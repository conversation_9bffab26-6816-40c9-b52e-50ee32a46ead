"use strict";exports.id=2087,exports.ids=[2087],exports.modules={44630:(e,t,n)=>{n.a(e,async(e,o)=>{try{n.d(t,{I:()=>c,Q:()=>l});var i=n(75263),a=n(34940),r=n(94612),s=e([i]);i=(s.then?(await s)():s)[0];class l{constructor(){this.defaultConfig={timeout:15e3,extractImages:!1,extractLinks:!0,onlyMainContent:!0,javascript:!1,stealth:!1,maxLength:8e3,userAgent:"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",headers:{Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8","Accept-Language":"en-US,en;q=0.5","Accept-Encoding":"gzip, deflate",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1"}},this.turndownService=new a.A({headingStyle:"atx",codeBlockStyle:"fenced",emDelimiter:"_"}),this.turndownService.addRule("removeScripts",{filter:["script","style","noscript"],replacement:()=>""})}async initialize(){console.log("✅ Simple web scraper ready (no browser needed)")}async scrapeUrl(e,t){let n={...this.defaultConfig,...t};try{if(console.log(`🔍 Scraping: ${e}`),this.isPdfUrl(e))return console.log(`⏭️ Skipping PDF file: ${e}`),{url:e,title:"PDF Document (Skipped)",content:"PDF documents are not supported by the web scraper",markdown:"",html:"",success:!1,wordCount:0,links:[],images:[],error:"PDF files are not supported for scraping"};let t=await Promise.race([r.A.get(e,{timeout:n.timeout,headers:{"User-Agent":n.userAgent,...n.headers},maxRedirects:5,validateStatus:e=>e<500}),new Promise((e,t)=>{setTimeout(()=>t(Error(`Request timeout after ${n.timeout}ms`)),n.timeout+5e3)})]),o=i.load(t.data),a=o("title").text().trim()||o("h1").first().text().trim()||o('meta[property="og:title"]').attr("content")||"Extracted Content";o("script, style, noscript, nav, header, footer, aside, .advertisement, .ads, .cookie-banner, .popup, .modal, .social-share").remove();let s="",l="";if(n.onlyMainContent){for(let e of["article",'[role="main"]',"main",".content",".main-content",".post-content",".entry-content",".article-content",".article-body",".post-body",".story-body","#content",".page-content"]){let t=o(e);if(t.length>0){let e=t.text().trim(),n=t.html()||"";e.length>s.length&&e.length>200&&(s=e,l=n)}}(!s||s.length<200)&&o("div, section, p").each((e,t)=>{let n=o(t).text().trim(),i=o(t).html()||"";n.length>s.length&&n.length>100&&(s=n,l=i)}),s||(s=o("body").text().trim(),l=o("body").html()||"")}else s=o("body").text().trim(),l=o("body").html()||"";let c=this.extractMetadata(o),g=this.extractStatistics(o),h=this.extractKeyInsights(o),u=n.extractLinks?this.extractLinks(o,e):[],d=n.extractImages?this.extractImages(o,e):[],p=this.cleanText(s),m=n.maxLength&&p.length>n.maxLength?p.substring(0,n.maxLength)+"...":p,k=this.turndownService.turndown(l),f=m.split(/\s+/).filter(e=>e.length>0).length;return console.log(`✅ Successfully scraped: ${e} (${f} words, ${m.length} chars)`),{url:e,title:a,content:m,markdown:k,html:l,success:!0,metadata:c,statistics:g,keyInsights:h,wordCount:f,links:u,images:d}}catch(n){let t=n instanceof Error?n.message:"Unknown error";return console.error(`❌ Failed to scrape ${e}:`,t),{url:e,title:"",content:"",markdown:"",html:"",success:!1,wordCount:0,links:[],images:[],error:`Failed to scrape ${e}: ${t}`}}}async scrapeMultipleUrls(e,t){let n=[];console.log(`🚀 Fast scraping ${e.length} URLs (concurrency: 3)`);for(let o=0;o<e.length;o+=3){let i=e.slice(o,o+3);console.log(`📦 Processing batch ${Math.floor(o/3)+1}/${Math.ceil(e.length/3)}`);let a=i.map(e=>this.scrapeUrl(e,t)),r=await Promise.all(a);n.push(...r),o+3<e.length&&(console.log("⏳ Waiting 800ms between batches..."),await new Promise(e=>setTimeout(e,800)))}let o=n.filter(e=>e.success).length;return console.log(`✅ Fast scraping completed: ${o}/${e.length} successful`),n}extractMetadata(e){return{description:e('meta[name="description"]').attr("content")||e('meta[property="og:description"]').attr("content"),author:e('meta[name="author"]').attr("content")||e('[rel="author"]').text().trim(),publishDate:e('meta[property="article:published_time"]').attr("content")||e("time").attr("datetime")||e("[datetime]").attr("datetime"),keywords:e('meta[name="keywords"]').attr("content")?.split(",").map(e=>e.trim()),ogTitle:e('meta[property="og:title"]').attr("content"),ogDescription:e('meta[property="og:description"]').attr("content"),ogImage:e('meta[property="og:image"]').attr("content")}}extractStatistics(e){let t=[];return e("*").each((n,o)=>{let i=e(o).text(),a=i.match(/\b\d{1,3}(,\d{3})*(\.\d+)?(%|k|M|B|million|billion|thousand)?\b/g);a&&a.forEach(e=>{let n=i.substring(Math.max(0,i.indexOf(e)-30),i.indexOf(e)+e.length+30).trim();n.length>10&&!t.includes(n)&&t.push(n)})}),t.slice(0,10)}extractKeyInsights(e){let t=[];return["blockquote",".highlight",".important",".key-point","strong","b","em","i"].forEach(n=>{e(n).each((n,o)=>{let i=e(o).text().trim();i.length>20&&i.length<200&&!t.includes(i)&&t.push(i)})}),t.slice(0,15)}extractLinks(e,t){let n=[];return e("a[href]").each((o,i)=>{let a=e(i).attr("href");if(a)try{let e=new URL(a,t).toString();n.includes(e)||n.push(e)}catch{}}),n.slice(0,50)}extractImages(e,t){let n=[];return e("img[src]").each((o,i)=>{let a=e(i).attr("src");if(a)try{let e=new URL(a,t).toString();n.includes(e)||n.push(e)}catch{}}),n.slice(0,20)}cleanText(e){return e.replace(/\s+/g," ").replace(/\n\s*\n/g,"\n\n").trim()}isPdfUrl(e){try{if(new URL(e).pathname.toLowerCase().endsWith(".pdf")||e.toLowerCase().includes(".pdf"))return!0;return!1}catch{return!1}}async close(){console.log("✅ Simple web scraper closed (no browser to close)")}}let c=new l;o()}catch(e){o(e)}})},93356:(e,t,n)=>{n.d(t,{p:()=>i});let o=new(n(37449)).ij("AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY");class i{constructor(e="gemini-2.5-flash-lite-preview-06-17"){this.model=o.getGenerativeModel({model:e})}updateModel(e){this.model=o.getGenerativeModel({model:e})}estimateTokenCount(e){return Math.ceil(e.length/4)}async generateContent(e,t={},n){let o=Date.now(),i=Math.random().toString(36).substr(2,9);console.log(`💎 Gemini Content Call Started`),console.log(`   📋 Call ID: ${i}`),console.log(`   🎬 Context: ${n||"General Content"}`),console.log(`   ⚙️ Model: ${this.model._model||"gemini-2.5-flash-lite-preview-06-17"}`),console.log(`   🌡️ Temperature: ${t.temperature||.7}`),console.log(`   📏 Max Tokens: ${t.maxOutputTokens||4e3}`),console.log(`   🎯 TopP: ${t.topP||.95}`),console.log(`   🔢 TopK: ${t.topK||40}`),console.log(`   📝 Prompt Length: ${e.length} chars`),t.thinkingConfig&&(console.log(`   🧠 Thinking Enabled:`),console.log(`      💭 Budget: ${t.thinkingConfig.thinkingBudget??"dynamic"}`),console.log(`      🔍 Include Thoughts: ${t.thinkingConfig.includeThoughts||!1}`));try{let a={temperature:t.temperature||.7,maxOutputTokens:t.maxOutputTokens||4e3,topP:t.topP||.95,topK:t.topK||40};t.thinkingConfig&&(a.thinkingConfig={},void 0!==t.thinkingConfig.thinkingBudget&&(a.thinkingConfig.thinkingBudget=t.thinkingConfig.thinkingBudget),void 0!==t.thinkingConfig.includeThoughts&&(a.thinkingConfig.includeThoughts=t.thinkingConfig.includeThoughts)),console.log(`   📤 Sending request to Gemini...`);let r=await this.model.generateContent({contents:[{role:"user",parts:[{text:e}]}],generationConfig:a}),s=await r.response,l=s.text(),c=[],g=0;if(t.thinkingConfig?.includeThoughts&&s.candidates?.[0]?.content?.parts)for(let e of s.candidates[0].content.parts)e.thought&&e.text&&c.push(e.text);s.usageMetadata?.thoughtsTokenCount&&(g=s.usageMetadata.thoughtsTokenCount);let h=this.estimateTokenCount(e),u=this.estimateTokenCount(l),d=Date.now()-o;console.log(`   ✅ Gemini Content Complete`),console.log(`   ⏱️ Duration: ${d}ms`),console.log(`   📊 Input Tokens: ${h} (estimated)`),console.log(`   📊 Output Tokens: ${u} (estimated)`),g>0&&(console.log(`   🧠 Thinking Tokens: ${g}`),console.log(`   💭 Thoughts Generated: ${c.length}`)),console.log(`   📄 Response Length: ${l.length} chars`);let p=4e-7*g;return console.log(`   💰 Estimated Cost: $${(1e-7*h+4e-7*u+p).toFixed(6)} (Flash-Lite 2025)`),n?.includes("YouTube")&&(console.log(`   🎬 YouTube Content Success - Call ${i}`),console.log(`   📺 Step: ${n}`)),{response:l,inputTokens:h,outputTokens:u,thoughtsTokenCount:g>0?g:void 0,thoughts:c.length>0?c:void 0}}catch(t){let e=Date.now()-o;throw console.error(`   ❌ Gemini Content Failed`),console.error(`   ⏱️ Failed after: ${e}ms`),console.error(`   📋 Call ID: ${i}`),console.error(`   🎬 Context: ${n||"General Content"}`),console.error(`   💥 Error:`,t),n?.includes("YouTube")&&(console.error(`   🎬 YouTube Content FAILED - Call ${i}`),console.error(`   📺 Failed Step: ${n}`)),Error(`Failed to generate content with Gemini: ${t}`)}}async generateContentWithThinking(e,t=-1,n=!0,o={},i){return this.generateContent(e,{...o,thinkingConfig:{thinkingBudget:t,includeThoughts:n}},i)}async generateContentWithMaxThinking(e,t={},n){return this.generateContentWithThinking(e,24576,!0,t,n)}async generateContentWithoutThinking(e,t={},n){return this.generateContent(e,{...t,thinkingConfig:{thinkingBudget:0,includeThoughts:!1}},n)}async generateBlogPost(e,t,n,o,i,a=!0){let r=`
You are a world-class professional content writer and subject matter expert. Create a comprehensive, engaging blog post about "${e}".

CONTENT REQUIREMENTS:
- Target word count: ${t} words
- Tone: ${n}
- Format: Professional markdown with proper headings, lists, and structure
- Include compelling hook and engaging introduction
- Use narrative storytelling and real-world examples
- Include strategic call-to-action at the end
- Write as a primary authoritative source
- Use confident, authoritative language (avoid hedging)

PROFESSIONAL WRITING STANDARDS:
- Start with an attention-grabbing hook (question, statistic, or bold statement)
- Create emotional connection with readers through storytelling
- Use scannable formatting with headings, subheadings, and bullet points
- Include actionable insights and practical advice
- Incorporate relevant statistics and data points
- Use active voice and strong verbs
- Create smooth transitions between sections
- End with a powerful conclusion and clear next steps

${i?.title?`Article Title: ${i.title}
`:""}
${i?.targetKeyword?`Target Keyword: ${i.targetKeyword} (use naturally throughout the content)
`:""}
${i?.targetAudience?`Target Audience: ${i.targetAudience} (tailor content for this audience)
`:""}
${i?.competitors?`Competitors to outperform: ${i.competitors} (create content that surpasses these sources)
`:""}

${o?`Research Data to incorporate:
${o}
`:""}

CONTENT STRUCTURE:
1. Compelling Hook (question, statistic, or bold statement)
2. Introduction with context and thesis
3. Main sections with clear headings and subheadings
4. Practical examples and case studies
5. Actionable takeaways and recommendations
6. Powerful conclusion with call-to-action

Create content that not only informs but also inspires action and provides exceptional value to readers. This should be the definitive resource on this topic.
`;return(await this.generateContent(r,{temperature:.7,maxOutputTokens:8e3,...a?{thinkingConfig:{thinkingBudget:-1,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async generateEmail(e,t,n,o,i=!1){let a=`
Create a professional email for the following:

Purpose: ${e}
Target Audience: ${t}
Tone: ${n}
Key Points to Include: ${o.join(", ")}

Requirements:
- Include compelling subject line
- Professional email structure (greeting, body, closing)
- Clear call-to-action
- Appropriate tone and language for the audience
- Concise but comprehensive

Format the response as:
Subject: [Subject Line]

[Email Body]
`;return(await this.generateContent(a,{temperature:.6,maxOutputTokens:1500,...i?{thinkingConfig:{thinkingBudget:512,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async generateTweet(e,t,n=!0,o=!1){let i=`
Create an engaging Twitter/X tweet about "${e}".

Style: ${t}
Include hashtags: ${n}

Requirements:
- Maximum 280 characters
- Engaging and shareable
- Include relevant emojis if appropriate
- ${n?"Include 2-3 relevant hashtags":"No hashtags"}
- Hook the reader's attention
- Encourage engagement (likes, retweets, replies)

Create a tweet that stands out in the feed and drives engagement.
`;return(await this.generateContent(i,{temperature:.8,maxOutputTokens:500,...o?{thinkingConfig:{thinkingBudget:256,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async extractKeywords(e,t=!1){let n=`
Extract the most important keywords from this topic for Google search: "${e}"

Requirements:
- If the topic is a single word or simple phrase, use it as the main keyword
- For complex topics, extract 3-5 key terms that best represent the topic
- Focus on the main concepts and important terms
- Use words that would be effective for Google search
- Return only the keywords separated by spaces, nothing else
- Do not include common words like "the", "and", "of", etc.
- Do not add words like "meaning", "definition", "example" unless they are part of the original topic
- Focus on specific, searchable terms from the original topic

Examples:
Topic: "magistral"
Keywords: magistral

Topic: "How to build a React application with TypeScript"
Keywords: React TypeScript application build development

Topic: "artificial intelligence in healthcare"
Keywords: artificial intelligence healthcare

Return only the keywords:
`;return(await this.generateContent(n,{temperature:.1,maxOutputTokens:50,...t?{thinkingConfig:{thinkingBudget:256,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async generateYouTubeScript(e,t,n,o,i=!0){let a=`
Create a YouTube video script about "${e}".

Video Duration: ${t}
Style: ${n}
Target Audience: ${o}

Requirements:
- Include compelling hook in first 15 seconds
- Clear structure with timestamps
- Engaging storytelling throughout
- Include call-to-action for likes, subscribes, comments
- Natural speaking rhythm and flow
- Include cues for visuals/graphics where appropriate
- End with strong conclusion and next video teaser

Format:
[HOOK - 0:00-0:15]
[INTRODUCTION - 0:15-0:45]
[MAIN CONTENT - Sections with timestamps]
[CONCLUSION & CTA - Final section]

Create a script that keeps viewers engaged throughout the entire video.
`;return(await this.generateContent(a,{temperature:.7,maxOutputTokens:5e3,...i?{thinkingConfig:{thinkingBudget:2048,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async extractKeywordsFromContent(e,t=!1){let n=`
Analyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:

Content:
${e.substring(0,3e3)}

Rules:
- Extract 8-12 high-value keywords and phrases
- Focus on terms that appear frequently and seem important
- Include both single keywords and 2-3 word phrases
- Prioritize terms that would be good for SEO targeting
- Separate keywords with commas
- Don't include common words like "the", "and", "or", etc.

Return only the keywords separated by commas:
`;return(await this.generateContent(n,{temperature:.2,maxOutputTokens:200,...t?{thinkingConfig:{thinkingBudget:1024,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}static calculateEnhancedInvincibleCost(e){let t=2e3+800*e.researchSources+6e3,n=Math.ceil(e.articleWordCount/.75),o=e.withThinking?Math.ceil(.2*n):0,i=1e-7*t,a=4e-7*n,r=4e-7*o;return{estimatedInputTokens:t,estimatedOutputTokens:n,estimatedThinkingTokens:o,totalCost:i+a+r,breakdown:{inputCost:i,outputCost:a,thinkingCost:r}}}}}};