exports.id=2403,exports.ids=[2403],exports.modules={7036:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},10022:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(75324).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},13861:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(75324).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15935:(e,t,s)=>{Promise.resolve().then(s.bind(s,84199))},16764:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(75324).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},31158:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(75324).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},42347:(e,t,s)=>{"use strict";s.d(t,{A:()=>g});var l=s(60687),i=s(43210),a=s(97905),r=s(28559),n=s(51361),c=s(48730),d=s(10022),o=s(13861),h=s(70615),x=s(31158),m=s(75324);let p=(0,m.A)("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]]);var b=s(97840);let v=(0,m.A)("Share",[["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["polyline",{points:"16 6 12 2 8 6",key:"m901s6"}],["line",{x1:"12",x2:"12",y1:"2",y2:"15",key:"1p0rca"}]]);var u=s(49014),j=s(85814),y=s.n(j);function g({title:e,content:t,wordCount:s}){let[m,j]=(0,i.useState)(!1),[g,f]=(0,i.useState)(!1),[w,N]=(0,i.useState)("0:00"),[k,A]=(0,i.useState)(0),P=s||t.split(/\s+/).filter(e=>e.length>0).length,C=Math.ceil(P/200),S=Math.ceil(P/150),H=async()=>{try{await navigator.clipboard.writeText(t),j(!0),setTimeout(()=>j(!1),2e3)}catch(e){console.error("Failed to copy text: ",e)}},M=()=>{let s=new Blob([t],{type:"text/plain"}),l=URL.createObjectURL(s),i=document.createElement("a");i.href=l,i.download=`${e.replace(/[^a-zA-Z0-9]/g,"-").toLowerCase()}-script.txt`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(l)},T=(e=>{let t=e.split(/\n\n+|\[.*?\]/).filter(e=>e.trim());return t.length<=1?[{title:"Script",content:e.trim()}]:t.map((e,t)=>{let s=e.trim();if(s.startsWith("[")&&s.endsWith("]"))return{title:s.slice(1,-1),content:""};let l=s.toLowerCase(),i=`Section ${t+1}`;return 0===t||l.includes("hook")||l.includes("intro")?i="\uD83C\uDFAC Introduction":l.includes("main")||l.includes("content")||l.includes("body")?i="\uD83D\uDCDD Main Content":l.includes("conclusion")||l.includes("outro")||l.includes("end")?i="\uD83C\uDFAF Conclusion":(l.includes("call to action")||l.includes("subscribe"))&&(i="\uD83D\uDD14 Call to Action"),{title:i,content:s}}).filter(e=>e.content)})(t);return(0,l.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900",children:[(0,l.jsx)("div",{className:"fixed inset-0 bg-[radial-gradient(ellipse_at_top,rgba(59,130,246,0.1),transparent_50%)]"}),(0,l.jsx)("div",{className:"fixed inset-0 bg-[radial-gradient(ellipse_at_bottom_right,rgba(239,68,68,0.1),transparent_50%)]"}),(0,l.jsx)(a.P.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"relative z-10 bg-slate-900/50 backdrop-blur-lg border-b border-slate-700/50",children:(0,l.jsx)("div",{className:"max-w-6xl mx-auto px-6 py-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,l.jsxs)(y(),{href:"/content",className:"flex items-center space-x-2 text-slate-400 hover:text-white transition-colors",children:[(0,l.jsx)(r.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"Back to Content"})]}),(0,l.jsx)("div",{className:"h-6 w-px bg-slate-600"}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center",children:(0,l.jsx)(n.A,{className:"w-5 h-5 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-xl font-bold text-white",children:e}),(0,l.jsx)("p",{className:"text-sm text-slate-400",children:"YouTube Script"})]})]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsxs)("div",{className:"hidden md:flex items-center space-x-6 text-sm text-slate-400",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(c.A,{className:"w-4 h-4"}),(0,l.jsxs)("span",{children:[C," min read"]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(d.A,{className:"w-4 h-4"}),(0,l.jsxs)("span",{children:[P.toLocaleString()," words"]})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)(o.A,{className:"w-4 h-4"}),(0,l.jsxs)("span",{children:["~",S," min video"]})]})]}),(0,l.jsx)("div",{className:"h-6 w-px bg-slate-600"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsxs)(a.P.button,{onClick:H,whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-white bg-slate-700 hover:bg-slate-600 rounded-lg transition-all",children:[(0,l.jsx)(h.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:m?"Copied!":"Copy"})]}),(0,l.jsxs)(a.P.button,{onClick:M,whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-3 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-all",children:[(0,l.jsx)(x.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"Download"})]})]})]})]})})}),(0,l.jsxs)("div",{className:"relative z-10 max-w-6xl mx-auto p-6",children:[(0,l.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-slate-800/50 backdrop-blur-lg border border-slate-700/50 rounded-2xl p-6 mb-8",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsxs)("h2",{className:"text-lg font-semibold text-white flex items-center space-x-2",children:[(0,l.jsx)(n.A,{className:"w-5 h-5 text-red-500"}),(0,l.jsx)("span",{children:"Video Preview Simulator"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsxs)(a.P.button,{onClick:()=>f(!g),whileHover:{scale:1.1},whileTap:{scale:.9},className:"flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all",children:[g?(0,l.jsx)(p,{className:"w-4 h-4"}):(0,l.jsx)(b.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:g?"Pause":"Play"})]}),(0,l.jsx)("button",{onClick:()=>{A(0),N("0:00"),f(!1)},className:"p-2 text-slate-400 hover:text-white transition-colors",title:"Reset",children:(0,l.jsx)(r.A,{className:"w-4 h-4"})})]})]}),(0,l.jsxs)("div",{className:"mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between text-sm text-slate-400 mb-2",children:[(0,l.jsx)("span",{children:w}),(0,l.jsxs)("span",{children:[Math.floor(S),":",String(S%1*60).padStart(2,"0")]})]}),(0,l.jsx)("div",{className:"w-full bg-slate-700 rounded-full h-2",children:(0,l.jsx)(a.P.div,{className:"bg-red-500 h-2 rounded-full",style:{width:`${k}%`},transition:{duration:.1}})})]}),(0,l.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center",children:[(0,l.jsxs)("div",{className:"bg-slate-700/50 rounded-lg p-3",children:[(0,l.jsx)("div",{className:"text-lg font-bold text-white",children:P.toLocaleString()}),(0,l.jsx)("div",{className:"text-sm text-slate-400",children:"Words"})]}),(0,l.jsxs)("div",{className:"bg-slate-700/50 rounded-lg p-3",children:[(0,l.jsxs)("div",{className:"text-lg font-bold text-white",children:["~",S,"min"]}),(0,l.jsx)("div",{className:"text-sm text-slate-400",children:"Estimated Length"})]}),(0,l.jsxs)("div",{className:"bg-slate-700/50 rounded-lg p-3",children:[(0,l.jsx)("div",{className:"text-lg font-bold text-white",children:T.length}),(0,l.jsx)("div",{className:"text-sm text-slate-400",children:"Sections"})]})]})]}),(0,l.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-slate-800/50 backdrop-blur-lg border border-slate-700/50 rounded-2xl overflow-hidden",children:[(0,l.jsxs)("div",{className:"p-6 border-b border-slate-700/50",children:[(0,l.jsx)("h2",{className:"text-xl font-bold text-white mb-2",children:"Script Content"}),(0,l.jsx)("p",{className:"text-slate-400",children:"Your complete YouTube script with clear sections and formatting"})]}),(0,l.jsx)("div",{className:"p-6",children:T.length>1?(0,l.jsx)("div",{className:"space-y-8",children:T.map((e,t)=>(0,l.jsxs)(a.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.1*t},className:"border-l-4 border-red-500 pl-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-white mb-3",children:e.title}),(0,l.jsx)("div",{className:"prose prose-invert max-w-none",children:(0,l.jsx)("div",{className:"text-slate-300 leading-relaxed whitespace-pre-wrap font-mono text-sm bg-slate-900/50 rounded-lg p-4",children:e.content})})]},t))}):(0,l.jsx)("div",{className:"prose prose-invert max-w-none",children:(0,l.jsx)("div",{className:"text-slate-300 leading-relaxed whitespace-pre-wrap font-mono text-sm bg-slate-900/50 rounded-lg p-6",children:t})})})]}),(0,l.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"mt-8 flex flex-wrap gap-4 justify-center",children:[(0,l.jsxs)(a.P.button,{onClick:H,whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-all font-medium",children:[(0,l.jsx)(h.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:m?"Copied to Clipboard!":"Copy Script"})]}),(0,l.jsxs)(a.P.button,{onClick:M,whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-all font-medium",children:[(0,l.jsx)(x.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"Download Script"})]}),(0,l.jsxs)(a.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-6 py-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-all font-medium",children:[(0,l.jsx)(v,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"Share Script"})]}),(0,l.jsxs)(a.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-6 py-3 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-all font-medium",children:[(0,l.jsx)(u.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{children:"Save for Later"})]})]})]})]})}},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(75324).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49014:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(75324).A)("Bookmark",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",key:"1fy3hk"}]])},51361:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(75324).A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},61135:()=>{},70615:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(75324).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},76183:(e,t,s)=>{Promise.resolve().then(s.bind(s,79025))},79025:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var l=s(60687),i=s(82136);function a({children:e}){return(0,l.jsx)(i.SessionProvider,{children:e})}},84199:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});let l=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx","default")},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,metadata:()=>n});var l=s(37413),i=s(7339),a=s.n(i);s(61135);var r=s(84199);let n={title:"Invincible - AI Content Generation Platform",description:"The ultimate content writing SaaS platform powered by advanced AI technology"};function c({children:e}){return(0,l.jsx)("html",{lang:"en",className:"dark",children:(0,l.jsx)("body",{className:`${a().className} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,suppressHydrationWarning:!0,children:(0,l.jsx)(r.default,{children:(0,l.jsx)("div",{className:"min-h-screen",children:e})})})})}},97840:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(75324).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])}};