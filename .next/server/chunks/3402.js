"use strict";exports.id=3402,exports.ids=[3402],exports.modules={13861:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(75324).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19587:(e,t)=>{function n(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"encodeURIPath",{enumerable:!0,get:function(){return n}})},30036:(e,t,n)=>{n.d(t,{default:()=>l.a});var r=n(49587),l=n.n(r)},47158:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(75324).A)("WandSparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]])},49587:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return l}});let r=n(59630)._(n(64963));function l(e,t){var n;let l={};"function"==typeof e&&(l.loader=e);let o={...l,...t};return(0,r.default)({...o,modules:null==(n=o.loadableGenerated)?void 0:n.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56085:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(75324).A)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},56780:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return l}});let r=n(81208);function l(e){let{reason:t,children:n}=e;throw Object.defineProperty(new r.BailoutToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}},64777:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadChunks",{enumerable:!0,get:function(){return a}});let r=n(60687),l=n(51215),o=n(29294),u=n(19587);function a(e){let{moduleIds:t}=e,n=o.workAsyncStorage.getStore();if(void 0===n)return null;let a=[];if(n.reactLoadableManifest&&t){let e=n.reactLoadableManifest;for(let n of t){if(!e[n])continue;let t=e[n].files;a.push(...t)}}return 0===a.length?null:(0,r.jsx)(r.Fragment,{children:a.map(e=>{let t=n.assetPrefix+"/_next/"+(0,u.encodeURIPath)(e);return e.endsWith(".css")?(0,r.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,l.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},64963:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let r=n(60687),l=n(43210),o=n(56780),u=n(64777);function a(e){return{default:e&&"default"in e?e.default:e}}let s={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},i=function(e){let t={...s,...e},n=(0,l.lazy)(()=>t.loader().then(a)),i=t.loading;function d(e){let a=i?(0,r.jsx)(i,{isLoading:!0,pastDelay:!0,error:null}):null,s=!t.ssr||!!t.loading,d=s?l.Suspense:l.Fragment,c=t.ssr?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(u.PreloadChunks,{moduleIds:t.modules}),(0,r.jsx)(n,{...e})]}):(0,r.jsx)(o.BailoutToCSR,{reason:"next/dynamic",children:(0,r.jsx)(n,{...e})});return(0,r.jsx)(d,{...s?{fallback:a}:{},children:c})}return d.displayName="LoadableComponent",d}},70615:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(75324).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},88920:(e,t,n)=>{n.d(t,{N:()=>x});var r=n(60687),l=n(43210),o=n(12157),u=n(72789),a=n(21279),s=n(32582);class i extends l.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function d({children:e,isPresent:t}){let n=(0,l.useId)(),o=(0,l.useRef)(null),u=(0,l.useRef)({width:0,height:0,top:0,left:0}),{nonce:a}=(0,l.useContext)(s.Q);return(0,l.useInsertionEffect)(()=>{let{width:e,height:r,top:l,left:s}=u.current;if(t||!o.current||!e||!r)return;o.current.dataset.motionPopId=n;let i=document.createElement("style");return a&&(i.nonce=a),document.head.appendChild(i),i.sheet&&i.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${r}px !important;
            top: ${l}px !important;
            left: ${s}px !important;
          }
        `),()=>{document.head.removeChild(i)}},[t]),(0,r.jsx)(i,{isPresent:t,childRef:o,sizeRef:u,children:l.cloneElement(e,{ref:o})})}let c=({children:e,initial:t,isPresent:n,onExitComplete:o,custom:s,presenceAffectsLayout:i,mode:c})=>{let p=(0,u.M)(f),h=(0,l.useId)(),m=(0,l.useCallback)(e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;o&&o()},[p,o]),y=(0,l.useMemo)(()=>({id:h,initial:t,isPresent:n,custom:s,onExitComplete:m,register:e=>(p.set(e,!1),()=>p.delete(e))}),i?[Math.random(),m]:[n,m]);return(0,l.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[n]),l.useEffect(()=>{n||p.size||!o||o()},[n]),"popLayout"===c&&(e=(0,r.jsx)(d,{isPresent:n,children:e})),(0,r.jsx)(a.t.Provider,{value:y,children:e})};function f(){return new Map}var p=n(86044);let h=e=>e.key||"";function m(e){let t=[];return l.Children.forEach(e,e=>{(0,l.isValidElement)(e)&&t.push(e)}),t}var y=n(15124);let x=({children:e,custom:t,initial:n=!0,onExitComplete:a,presenceAffectsLayout:s=!0,mode:i="sync",propagate:d=!1})=>{let[f,x]=(0,p.xQ)(d),b=(0,l.useMemo)(()=>m(e),[e]),j=d&&!f?[]:b.map(h),g=(0,l.useRef)(!0),v=(0,l.useRef)(b),M=(0,u.M)(()=>new Map),[k,P]=(0,l.useState)(b),[_,C]=(0,l.useState)(b);(0,y.E)(()=>{g.current=!1,v.current=b;for(let e=0;e<_.length;e++){let t=h(_[e]);j.includes(t)?M.delete(t):!0!==M.get(t)&&M.set(t,!1)}},[_,j.length,j.join("-")]);let R=[];if(b!==k){let e=[...b];for(let t=0;t<_.length;t++){let n=_[t],r=h(n);j.includes(r)||(e.splice(t,0,n),R.push(n))}"wait"===i&&R.length&&(e=R),C(m(e)),P(b);return}let{forceRender:E}=(0,l.useContext)(o.L);return(0,r.jsx)(r.Fragment,{children:_.map(e=>{let l=h(e),o=(!d||!!f)&&(b===_||j.includes(l));return(0,r.jsx)(c,{isPresent:o,initial:(!g.current||!!n)&&void 0,custom:o?void 0:t,presenceAffectsLayout:s,mode:i,onExitComplete:o?void 0:()=>{if(!M.has(l))return;M.set(l,!0);let e=!0;M.forEach(t=>{t||(e=!1)}),e&&(null==E||E(),C(v.current),d&&(null==x||x()),a&&a())},children:e},l)})})}}};