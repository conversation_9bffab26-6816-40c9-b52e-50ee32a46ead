"use strict";exports.id=5901,exports.ids=[5901],exports.modules={9865:(e,t,a)=>{a.d(t,{R:()=>o});var r=a(94612),i=a(99218);class n{constructor(){let e=["sd_5f872ba7dcf51fc00fc258abaff006aa","sd_7398534f0ba23be07bf5e47af32c8115","sd_f910aa820b3235291c439ba0fed0faaf","sd_211858172e68670ee607e143f11a9cd5","sd_077f1d631c97d0c84ba0e33ffc4165aa","sd_017aaee3312bd4d2285e2ce6f2f360c9","sd_682a66ee222fb692abae8103f387951d"],t=process.env.SUPADATA_API_KEY,a=t?[t,...e]:e;this.apiKeys=a,this.currentKeyIndex=0,this.keyQuotaStatus=new Map,this.lastRotationTime=new Date,console.log(`🔑 SupadataApiKeyRotator initialized with ${this.apiKeys.length} API keys`),this.apiKeys.forEach(e=>{this.keyQuotaStatus.set(e,{hitLimit:!1,errorCount:0})})}getCurrentApiKey(){let e=this.apiKeys[this.currentKeyIndex],t=this.keyQuotaStatus.get(e);return t?.hitLimit&&t.resetTime&&new Date<t.resetTime?(console.log(`🔄 Current Supadata key has hit limit, auto-rotating...`),this.rotateToNextValidKey(),this.getCurrentApiKey()):(t?.hitLimit&&t.resetTime&&new Date>=t.resetTime&&(this.keyQuotaStatus.set(e,{hitLimit:!1,errorCount:0}),console.log(`🔄 Supadata API key quota reset for key ending in ...${e.slice(-4)}`)),e)}rotateToNextValidKey(){let e=this.currentKeyIndex,t=0,a=2*this.apiKeys.length;do{this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,t++;let e=this.apiKeys[this.currentKeyIndex],r=this.keyQuotaStatus.get(e);if(!r?.hitLimit||r.resetTime&&new Date>=r.resetTime){console.log(`🔄 SUPADATA ROTATION: Switched to API key ${this.currentKeyIndex+1}/${this.apiKeys.length} (errors: ${r?.errorCount||0})`),this.lastRotationTime=new Date;return}if(t>=a){console.warn(`⚠️ Supadata rotation attempts exceeded, using current key anyway`);break}}while(this.currentKeyIndex!==e);console.warn("⚠️ All Supadata API keys have issues. Using least problematic key."),this.findLeastProblematicKey()}findLeastProblematicKey(){let e=0,t=1/0;this.apiKeys.forEach((a,r)=>{let i=this.keyQuotaStatus.get(a),n=i?.errorCount||0;i?.hitLimit&&i.resetTime&&new Date<i.resetTime&&(n+=1e3),n<t&&(t=n,e=r)}),this.currentKeyIndex=e,console.log(`🔄 Selected least problematic Supadata key: ${e+1}/${this.apiKeys.length} (score: ${t})`)}markKeyAsQuotaExceeded(e){let t=new Date;t.setHours(t.getHours()+24);let a=this.keyQuotaStatus.get(e)||{hitLimit:!1,errorCount:0};this.keyQuotaStatus.set(e,{hitLimit:!0,resetTime:t,errorCount:a.errorCount+1}),console.warn(`⚠️ Supadata API key ending in ...${e.slice(-4)} marked as exhausted (errors: ${a.errorCount+1}). Will reset at ${t.toISOString()}`),this.rotateToNextValidKey()}markKeyError(e,t="generic"){let a=this.keyQuotaStatus.get(e)||{hitLimit:!1,errorCount:0};this.keyQuotaStatus.set(e,{...a,errorCount:a.errorCount+1}),console.warn(`⚠️ Supadata API key ending in ...${e.slice(-4)} error count: ${a.errorCount+1} (${t})`),a.errorCount>=3&&(console.log(`🔄 Supadata key has high error count, rotating to different key`),this.rotateToNextValidKey())}forceRotate(){console.log(`🔄 SUPADATA FORCE ROTATION requested`),this.rotateToNextValidKey();let e=this.getCurrentApiKey();return console.log(`🔄 Supadata force rotation complete: now using key ending in ...${e.slice(-4)}`),e}instantRotate(e){console.log(`⚡ SUPADATA INSTANT ROTATION: ${e}`);let t=this.getCurrentApiKey();this.rotateToNextValidKey();let a=this.getCurrentApiKey();return console.log(`⚡ Supadata instant rotation: ...${t.slice(-4)} → ...${a.slice(-4)}`),a}getStatus(){let e=Array.from(this.keyQuotaStatus.values()).filter(e=>!e.hitLimit||e.resetTime&&new Date>=e.resetTime).length,t=this.apiKeys.map((e,t)=>{let a=this.keyQuotaStatus.get(e),r=!a?.hitLimit||a.resetTime&&new Date>=a.resetTime;return{keyId:`Supadata Key ${t+1} (...${e.slice(-4)})`,status:r?"active":"limited",errors:a?.errorCount||0}});return{totalKeys:this.apiKeys.length,currentKeyIndex:this.currentKeyIndex,availableKeys:e,lastRotation:this.lastRotationTime,keyHealthReport:t}}resetKeyErrors(e){let t=this.keyQuotaStatus.get(e);t&&(this.keyQuotaStatus.set(e,{...t,errorCount:0}),console.log(`🔄 Reset error count for Supadata key ending in ...${e.slice(-4)}`))}resetAllKeys(){console.log(`🔄 SUPADATA EMERGENCY RESET: Resetting all API keys`),this.apiKeys.forEach(e=>{this.keyQuotaStatus.set(e,{hitLimit:!1,errorCount:0})}),this.currentKeyIndex=0,console.log(`🔄 All Supadata keys reset, starting from key 1`)}handleSupadataError(e,t){if(!t.response)return this.markKeyError(e,"network_error"),!1;let a=t.response.status,r=t.response.data?.error||t.message||"Unknown error";switch(a){case 401:return console.warn(`🔑 Supadata API key invalid: ...${e.slice(-4)}`),this.markKeyAsQuotaExceeded(e),!0;case 429:return console.warn(`📈 Supadata API rate limit exceeded: ...${e.slice(-4)}`),this.markKeyAsQuotaExceeded(e),!0;case 402:return console.warn(`💳 Supadata API quota exceeded: ...${e.slice(-4)}`),this.markKeyAsQuotaExceeded(e),!0;case 403:return console.warn(`🚫 Supadata API access forbidden: ...${e.slice(-4)}`),this.markKeyError(e,"forbidden"),!0;case 404:return console.warn(`📹 Video not found or no transcripts available`),!1;case 500:case 502:case 503:return console.warn(`🔧 Supadata API server error (${a}): ...${e.slice(-4)}`),this.markKeyError(e,`server_error_${a}`),!0;default:return console.warn(`⚠️ Supadata API unexpected error (${a}): ${r}`),this.markKeyError(e,`unknown_${a}`),!1}}}class o{constructor(){this.innertube=null,this.apiKey=process.env.YOUTUBE_API_KEY||"AIzaSyCHK5v6aCrIXQAakOtAbJjPA1MEpDWEEMo",this.supadataRotator=new n,this.apiKey||console.warn("YouTube API key not configured"),console.log("✅ YouTube service initialized with Supadata API rotation")}async initInnerTube(){if(!this.innertube)try{console.log("\uD83D\uDD27 Initializing YouTube InnerTube client..."),this.innertube=await i.P5.create(),console.log("✅ InnerTube client initialized successfully")}catch(e){console.error("❌ Failed to initialize InnerTube client:",e),this.innertube=null}return this.innertube}async searchVideos(e,t=10){try{let a=await this.initInnerTube();if(a){let r=console.warn,i=console.error;try{console.log(`🔍 Searching via InnerTube: "${e}"`),console.warn=(...e)=>{let t=e.join(" ");t.includes("[YOUTUBEJS][Parser]")||t.includes("VideoSummaryContentView")||t.includes("VideoSummaryParagraphView")||r(...e)},console.error=(...e)=>{let t=e.join(" ");t.includes("[YOUTUBEJS][Parser]")||t.includes("VideoSummaryContentView")||t.includes("VideoSummaryParagraphView")||i(...e)};let n=await a.search(e,{type:"video"});if(n&&n.videos){let e=n.videos.slice(0,t).map(e=>({id:e.id,title:e.title?.text||e.title||"Unknown Title",description:e.description?.text||e.description||"",channelTitle:e.author?.name||e.channel?.name||"Unknown Channel",viewCount:e.view_count?.text||e.views?.text||"0",likeCount:e.like_count?.text||void 0,publishedAt:e.published?.text||new Date().toISOString(),duration:e.duration?.text||"0:00",thumbnailUrl:e.thumbnails?.[0]?.url||e.thumbnail?.url||""}));return console.log(`✅ InnerTube search found ${e.length} videos`),{videos:e,totalResults:e.length}}}catch(t){let e=t instanceof Error?t.message:String(t);e.includes("VideoSummaryContentView")||e.includes("Parser")?console.log("\uD83D\uDD04 YouTube parser issue detected, falling back to official API..."):console.warn("⚠️ InnerTube search failed, falling back to official API:",t)}finally{console.warn=r,console.error=i}}if(!this.apiKey)return this.getMockSearchResults(e);console.log(`🔍 Searching via YouTube Data API: "${e}"`);let i=await r.A.get("https://www.googleapis.com/youtube/v3/search",{params:{key:this.apiKey,q:e,part:"snippet",type:"video",maxResults:t,order:"relevance",videoDuration:"medium"}}),n=i.data.items.map(e=>e.id.videoId).join(","),o=(await r.A.get("https://www.googleapis.com/youtube/v3/videos",{params:{key:this.apiKey,id:n,part:"snippet,statistics,contentDetails"}})).data.items.map(e=>({id:e.id,title:e.snippet.title,description:e.snippet.description,channelTitle:e.snippet.channelTitle,viewCount:e.statistics.viewCount,likeCount:e.statistics.likeCount,publishedAt:e.snippet.publishedAt,duration:this.parseDuration(e.contentDetails.duration),thumbnailUrl:e.snippet.thumbnails.high.url}));return console.log(`✅ YouTube Data API search found ${o.length} videos`),{videos:o,totalResults:i.data.pageInfo.totalResults}}catch(e){throw console.error("YouTube search error:",e),Error("Failed to search YouTube videos")}}async extractCaptions(e,t="en"){try{console.log(`🎬 Extracting captions for video: ${e} using Supadata.ai API`);let a=await this.extractCaptionsSupadata(e,t);if(a.length>0)return a;console.log("\uD83D\uDD04 Supadata failed, trying InnerTube API...");let r=await this.extractCaptionsInnerTube(e,t);if(r.length>0)return r;return console.log("\uD83D\uDD04 InnerTube failed, trying YouTube Data API..."),this.extractCaptionsDataAPI(e,t)}catch(e){return console.error("\uD83D\uDCA5 Caption extraction error:",e),[{text:`[Caption extraction failed: ${e instanceof Error?e.message:"Unknown error"}]`,start:0,duration:5}]}}async extractCaptionsSupadata(e,t="en"){let a=null;for(let i=0;i<3;i++)try{let a=this.supadataRotator.getCurrentApiKey();console.log(`🌟 Using Supadata.ai API for video: ${e} (attempt ${i+1}/3, key: ...${a.slice(-4)})`);let n=await r.A.get("https://api.supadata.ai/v1/youtube/transcript",{params:{videoId:e,lang:t},headers:{"x-api-key":a,"Content-Type":"application/json"},timeout:3e4});if(!n.data||!n.data.content)return console.warn(`⚠️ No transcript content received from Supadata for ${e}`),[];let o=n.data.content.map(e=>({text:e.text||"",start:(e.offset||0)/1e3,duration:(e.duration||2e3)/1e3})).filter(e=>e.text.trim());if(o.length>0)return console.log(`✅ Successfully extracted ${o.length} caption segments via Supadata.ai (key: ...${a.slice(-4)})`),console.log(`📄 Language: ${n.data.lang||"unknown"}`),console.log(`🌍 Available languages: ${n.data.availableLangs?.join(", ")||"N/A"}`),console.log(`📋 Sample text: "${o[0].text.substring(0,100)}..."`),o;return console.warn(`⚠️ No valid caption segments found in Supadata response`),[]}catch(t){a=t;let e=this.supadataRotator.getCurrentApiKey();if(r.A.isAxiosError(t)){if(!this.supadataRotator.handleSupadataError(e,t)){console.log(`🛑 Supadata error indicates no retry needed: ${t.response?.status}`);break}if(i<2){console.log(`🔄 Retrying with rotated Supadata API key...`);continue}}else if(this.supadataRotator.markKeyError(e,"network_error"),console.error(`❌ Supadata request failed:`,t instanceof Error?t.message:String(t)),i<2){console.log(`🔄 Retrying with different Supadata API key...`);continue}}return console.error(`❌ All Supadata API attempts failed for video ${e}`),a&&r.A.isAxiosError(a)&&console.error(`📄 Final error: ${a.response?.status} - ${a.response?.statusText}`),[]}async extractCaptionsInnerTube(e,t="en"){try{console.log(`🔧 Using InnerTube API fallback for video: ${e}`);let a=await this.initInnerTube();if(!a)return console.warn("⚠️ InnerTube client not available"),[];let r=await a.getInfo(e);if(!r)return console.warn(`⚠️ Could not get video info for ${e}`),[];console.log(`📺 Video: "${r.basic_info?.title}"`);let i=r.captions?.caption_tracks||[];if(0===i.length)return console.warn(`⚠️ No caption tracks available for video ${e}`),[];console.log(`📝 Found ${i.length} caption track(s)`);let n=i.find(e=>e.language_code===t||e.language_code?.startsWith(t));n||(n=i.find(e=>e.language_code?.startsWith("en"))),n||(n=i[0]),console.log(`🎯 Selected track: ${n.name?.text||n.name} (${n.language_code})`);try{let t=await a.getTranscript(e,n.language_code);if(t?.content?.body?.initial_segments){let e=t.content.body.initial_segments.map(e=>({text:e.snippet?.text||"",start:e.start_ms?e.start_ms/1e3:0,duration:e.end_ms&&e.start_ms?(e.end_ms-e.start_ms)/1e3:2})).filter(e=>e.text.trim());if(e.length>0)return console.log(`✅ Successfully extracted ${e.length} caption segments via InnerTube`),e}}catch(e){console.warn("⚠️ InnerTube getTranscript method not available:",e instanceof Error?e.message:String(e))}return[]}catch(e){return console.error("\uD83D\uDCA5 InnerTube fallback error:",e),[]}}async extractCaptionsDataAPI(e,t="en"){try{if(console.log(`🔄 Using YouTube Data API fallback for video: ${e}`),!this.apiKey)return console.warn("⚠️ No YouTube API key available"),[];let a=await this.getCaptionTracks(e);if(0===a.length)return console.warn(`⚠️ No caption tracks available for video ${e}`),[];console.log(`📝 Found ${a.length} caption track(s) via Data API`);let r=a.find(e=>e.languageCode===t);r||(r=a.find(e=>e.languageCode.startsWith("en"))),r||(r=a[0]),console.log(`🎯 Selected caption track: ${r.name} (${r.languageCode})`);let i=await this.downloadCaptions(r.id);return i.length>0&&console.log(`✅ Successfully extracted ${i.length} caption segments via Data API`),i}catch(e){return console.error("\uD83D\uDCA5 Data API fallback error:",e),[{text:"[Captions require OAuth authentication - only video owners can download caption content via API]",start:0,duration:5}]}}async getCaptionTracks(e){try{return(await r.A.get("https://www.googleapis.com/youtube/v3/captions",{params:{key:this.apiKey,videoId:e,part:"snippet"}})).data.items.map(e=>({id:e.id,name:e.snippet.name,languageCode:e.snippet.language,kind:e.snippet.trackKind}))}catch(t){return console.error("Failed to get caption tracks:",t),(await this.getAvailableLanguagesFallback(e)).map((t,a)=>({id:`${e}_${t}_${a}`,name:`${t} (auto-generated)`,languageCode:t,kind:"standard"}))}}async downloadCaptions(e){try{let t=`https://www.googleapis.com/youtube/v3/captions/${e}`,a=await r.A.get(t,{params:{key:this.apiKey,tfmt:"ttml"}});return this.parseCaptionContent(a.data)}catch(e){if(console.error("Caption download failed:",e),r.A.isAxiosError(e)&&e.response?.status===403)return console.warn("\uD83D\uDD12 Caption download requires OAuth authentication (expected for most videos)"),[{text:"[Captions require OAuth authentication - only video owners can download caption content via API]",start:0,duration:5}];throw e}}parseCaptionContent(e){try{if(e.includes("<tt")||e.includes("<ttml"))return this.parseTTML(e);if(e.includes("--\x3e"))return this.parseSRT(e);if(e.includes("WEBVTT"))return this.parseVTT(e);if(e.trim())return[{text:e.trim(),start:0,duration:10}]}catch(e){console.error("Caption parsing error:",e)}return[]}parseTTML(e){let t=[];try{let a=e.match(/<p[^>]*begin="([^"]*)"[^>]*dur="([^"]*)"[^>]*>([\s\S]*?)<\/p>/g);if(a)for(let e of a){let a=e.match(/begin="([^"]*)"/),r=e.match(/dur="([^"]*)"/),i=e.match(/<p[^>]*>([\s\S]*?)<\/p>/);if(a&&r&&i){let e=this.parseTimeToSeconds(a[1]),n=this.parseTimeToSeconds(r[1]),o=i[1].replace(/<[^>]*>/g,"").trim();o&&t.push({text:o,start:e,duration:n})}}}catch(e){console.error("TTML parsing error:",e)}return t}parseSRT(e){let t=[];for(let a of e.split(/\n\s*\n/)){let e=a.trim().split("\n");if(e.length>=3){let a=e[1].match(/(\d{2}:\d{2}:\d{2},\d{3}) --> (\d{2}:\d{2}:\d{2},\d{3})/);if(a){let r=this.srtTimeToSeconds(a[1]),i=this.srtTimeToSeconds(a[2]),n=e.slice(2).join(" ").replace(/<[^>]*>/g,"").trim();n&&t.push({text:n,start:r,duration:i-r})}}}return t}parseVTT(e){let t=[],a=e.split("\n");for(let e=0;e<a.length;e++){let r=a[e].trim().match(/(\d{2}:\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}:\d{2}\.\d{3})/);if(r&&e+1<a.length){let i=this.vttTimeToSeconds(r[1]),n=this.vttTimeToSeconds(r[2]),o=a[e+1].replace(/<[^>]*>/g,"").trim();o&&t.push({text:o,start:i,duration:n-i})}}return t}parseTimeToSeconds(e){if(e.includes("s"))return parseFloat(e.replace("s",""));let t=e.split(":");return 3===t.length?3600*parseInt(t[0])+60*parseInt(t[1])+parseFloat(t[2]):0}srtTimeToSeconds(e){let[t,a]=e.split(","),[r,i,n]=t.split(":").map(Number);return 3600*r+60*i+n+Number(a)/1e3}vttTimeToSeconds(e){let[t,a,r]=e.split(":"),[i,n]=r.split(".");return 3600*Number(t)+60*Number(a)+Number(i)+Number(n)/1e3}async getAvailableLanguagesFallback(e){try{let t=`https://www.youtube.com/watch?v=${e}`,a=(await r.A.get(t,{headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}})).data.match(/ytInitialPlayerResponse\s*=\s*({.+?})\s*;\s*(?:var\s+(?:meta|head)|<\/script|\n)/);if(!a)return[];let i=JSON.parse(a[1]);return(i.captions?.playerCaptionsTracklistRenderer?.captionTracks||[]).map(e=>e.languageCode).filter(Boolean)}catch(e){return console.error("Failed to get available languages:",e),[]}}async getAvailableLanguages(e){return(await this.getCaptionTracks(e)).map(e=>e.languageCode)}parseDuration(e){let t=e.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);if(!t)return e;let a=parseInt(t[1]||"0"),r=parseInt(t[2]||"0"),i=parseInt(t[3]||"0");return a>0?`${a}:${r.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}`:`${r}:${i.toString().padStart(2,"0")}`}getMockSearchResults(e){return{videos:[{id:"dQw4w9WgXcQ",title:`How to ${e} - Complete Guide 2024 🚀`,description:"This is a comprehensive guide covering everything you need to know about the topic with real examples and practical tips.",channelTitle:"Tech Masterclass",viewCount:"1547823",likeCount:"89421",publishedAt:new Date(Date.now()-6048e5).toISOString(),duration:"12:34",thumbnailUrl:"https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg"},{id:"jNQXAC9IVRw",title:`${e} for Beginners - Everything You Need to Know ✨`,description:"Learn the fundamentals with step-by-step instructions, practical examples, and expert tips from industry professionals.",channelTitle:"Learning Hub Pro",viewCount:"892156",likeCount:"34521",publishedAt:new Date(Date.now()-12096e5).toISOString(),duration:"15:42",thumbnailUrl:"https://i.ytimg.com/vi/jNQXAC9IVRw/hqdefault.jpg"},{id:"ScMzIvxBSi4",title:`Advanced ${e} Techniques That Actually Work 💡`,description:"Discover professional strategies and advanced methods used by experts. Includes case studies and real-world applications.",channelTitle:"Pro Tips Channel",viewCount:"456789",likeCount:"23156",publishedAt:new Date(Date.now()-18144e5).toISOString(),duration:"18:27",thumbnailUrl:"https://i.ytimg.com/vi/ScMzIvxBSi4/hqdefault.jpg"}],totalResults:3}}combineCaptions(e){return e.map(e=>e.text).join(" ")}extractKeyMoments(e,t=30){let a=[],r=[],i=0,n=0;return e.forEach((o,s)=>{if(0===r.length&&(i=o.start),r.push(o),(n+=o.duration)>=t||s===e.length-1){let e=r.map(e=>e.text).join(" ").replace(/\s+/g," ").trim();if(e){let t=this.formatTimestamp(i);a.push({timestamp:t,text:e.length>120?e.substring(0,120)+"...":e,startTime:i})}r=[],n=0}}),a}formatTimestamp(e){let t=Math.floor(e/3600),a=Math.floor(e%3600/60),r=Math.floor(e%60);return t>0?`${t}:${a.toString().padStart(2,"0")}:${r.toString().padStart(2,"0")}`:`${a}:${r.toString().padStart(2,"0")}`}async searchAndExtractCaptions(e,t=5,a="en"){try{let r=await this.searchVideos(e,t),i=[];for(let e of r.videos)try{let t=await this.extractCaptions(e.id,a),r=this.combineCaptions(t);i.push({video:e,captions:t,transcript:r})}catch(t){console.warn(`Failed to extract captions for video ${e.id}:`,t),i.push({video:e,captions:[],transcript:""})}return i}catch(e){throw console.error("Search and extract captions failed:",e),e}}async getVideoMetadata(e){try{if(!this.apiKey)return null;let t=await r.A.get("https://www.googleapis.com/youtube/v3/videos",{params:{key:this.apiKey,id:e,part:"snippet,statistics,contentDetails"}});if(!t.data.items||0===t.data.items.length)return null;let a=t.data.items[0];return{id:a.id,title:a.snippet.title,description:a.snippet.description,channelTitle:a.snippet.channelTitle,viewCount:a.statistics.viewCount,likeCount:a.statistics.likeCount,publishedAt:a.snippet.publishedAt,duration:this.parseDuration(a.contentDetails.duration),thumbnailUrl:a.snippet.thumbnails.high.url}}catch(e){return console.error("Failed to get video metadata:",e),null}}getSupadataRotatorStatus(){return this.supadataRotator.getStatus()}forceSupadataKeyRotation(){return this.supadataRotator.forceRotate()}getCurrentSupadataKeyInfo(){let e=this.supadataRotator.getStatus(),t=this.supadataRotator.getCurrentApiKey(),a=e.keyHealthReport.find(e=>e.keyId.includes(t.slice(-4)));return{key:`...${t.slice(-4)}`,status:a?.status||"unknown",availableKeys:e.availableKeys}}resetSupadataKeys(){this.supadataRotator.resetAllKeys()}instantRotateSupadataKey(e){return this.supadataRotator.instantRotate(e)}}},12909:(e,t,a)=>{a.d(t,{N:()=>o});var r=a(16467),i=a(36344),n=a(31183);let o={adapter:(0,r.y)(n.z),providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"database"},callbacks:{session:async({session:e,user:t})=>(e?.user&&t&&(e.user.id=t.id),e),signIn:async({user:e,account:t,profile:a})=>!0},events:{async createUser({user:e}){try{await n.z.userSettings.findUnique({where:{userId:e.id}})?console.log(`ℹ️ User profile already exists for: ${e.email}`):(await n.z.user.update({where:{id:e.id},data:{firstName:e.name?.split(" ")[0]||"",lastName:e.name?.split(" ").slice(1).join(" ")||"",settings:{create:{}},subscription:{create:{plan:"free",status:"active"}},quotas:{create:[{quotaType:"blog_posts",totalLimit:5,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"emails",totalLimit:10,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"social_media",totalLimit:20,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"youtube_scripts",totalLimit:3,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"invincible_research",totalLimit:2,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}]}}}),console.log(`✅ Created complete user profile for: ${e.email}`))}catch(e){console.error("Error setting up user profile:",e)}}},pages:{signIn:"/auth/signin",error:"/auth/error"},debug:!1}},31183:(e,t,a)=>{a.d(t,{z:()=>i});var r=a(96330);let i=globalThis.prisma??new r.PrismaClient({log:["query"]})}};