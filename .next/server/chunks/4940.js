exports.id=4940,exports.ids=[4940],exports.modules={2672:(e,t,r)=>{"use strict";var n=r(62114),a=r(98113);function i(e,t){this._window=e,this._href=t}e.exports=i,i.prototype=Object.create(a.prototype,{constructor:{value:i},href:{get:function(){return this._href},set:function(e){this.assign(e)}},assign:{value:function(e){var t=new n(this._href).resolve(e);this._href=t}},replace:{value:function(e){this.assign(e)}},reload:{value:function(){this.assign(this.href)}},toString:{value:function(){return this.href}}})},6560:(e,t,r)=>{"use strict";e.exports=s;var n=r(81070),a=r(21939),i=r(87856);function s(e,t){i.call(this),this.nodeType=a.TEXT_NODE,this.ownerDocument=e,this._data=t,this._index=void 0}var o={get:function(){return this._data},set:function(e){(e=null==e?"":String(e))!==this._data&&(this._data=e,this.rooted&&this.ownerDocument.mutateValue(this),this.parentNode&&this.parentNode._textchangehook&&this.parentNode._textchangehook(this))}};s.prototype=Object.create(i.prototype,{nodeName:{value:"#text"},nodeValue:o,textContent:o,innerText:o,data:{get:o.get,set:function(e){o.set.call(this,null===e?"":String(e))}},splitText:{value:function(e){(e>this._data.length||e<0)&&n.IndexSizeError();var t=this._data.substring(e),r=this.ownerDocument.createTextNode(t);this.data=this.data.substring(0,e);var a=this.parentNode;return null!==a&&a.insertBefore(r,this.nextSibling),r}},wholeText:{get:function(){for(var e=this.textContent,t=this.nextSibling;t&&t.nodeType===a.TEXT_NODE;t=t.nextSibling)e+=t.textContent;return e}},replaceWholeText:{value:n.nyi},clone:{value:function(){return new s(this.ownerDocument,this._data)}}})},7650:(e,t,r)=>{"use strict";e.exports=l;var n=r(21939),a=r(26987),i=r(67472),s=r(61487),o=r(29519),c=r(81070);function l(e){i.call(this),this.nodeType=n.DOCUMENT_FRAGMENT_NODE,this.ownerDocument=e}l.prototype=Object.create(i.prototype,{nodeName:{value:"#document-fragment"},nodeValue:{get:function(){return null},set:function(){}},textContent:Object.getOwnPropertyDescriptor(s.prototype,"textContent"),innerText:Object.getOwnPropertyDescriptor(s.prototype,"innerText"),querySelector:{value:function(e){var t=this.querySelectorAll(e);return t.length?t[0]:null}},querySelectorAll:{value:function(e){var t=Object.create(this);t.isHTML=!0,t.getElementsByTagName=s.prototype.getElementsByTagName,t.nextElement=Object.getOwnPropertyDescriptor(s.prototype,"firstElementChild").get;var r=o(e,t);return r.item?r:new a(r)}},clone:{value:function(){return new l(this.ownerDocument)}},isEqual:{value:function(e){return!0}},innerHTML:{get:function(){return this.serialize()},set:c.nyi},outerHTML:{get:function(){return this.serialize()},set:c.nyi}})},8595:e=>{"use strict";e.exports=Object.create(null,{appCodeName:{value:"Mozilla"},appName:{value:"Netscape"},appVersion:{value:"4.0"},platform:{value:""},product:{value:"Gecko"},productSub:{value:"20100101"},userAgent:{value:""},vendor:{value:""},vendorSub:{value:""},taintEnabled:{value:function(){return!1}}})},11228:(e,t)=>{"use strict";t.isValidName=function(e){if(r.test(e)||l.test(e))return!0;if(!h.test(e)||!d.test(e))return!1;var t=e.match(p),n=e.match(f);return null!==n&&2*n.length===t.length},t.isValidQName=function(e){if(n.test(e)||u.test(e))return!0;if(!h.test(e)||!m.test(e))return!1;var t=e.match(p),r=e.match(f);return null!==r&&2*r.length===t.length};var r=/^[_:A-Za-z][-.:\w]+$/,n=/^([_A-Za-z][-.\w]+|[_A-Za-z][-.\w]+:[_A-Za-z][-.\w]+)$/,a="_A-Za-z\xc0-\xd6\xd8-\xf6\xf8-˿Ͱ-ͽͿ-῿‌-‍⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�",i="-._A-Za-z0-9\xb7\xc0-\xd6\xd8-\xf6\xf8-˿̀-ͽͿ-῿‌‍‿⁀⁰-↏Ⰰ-⿯、-퟿豈-﷏ﷰ-�",s="["+a+"]["+i+"]*",o=a+":",c=i+":",l=RegExp("^["+o+"]["+c+"]*$"),u=RegExp("^("+s+"|"+s+":"+s+")$"),h=/[\uD800-\uDB7F\uDC00-\uDFFF]/,p=/[\uD800-\uDB7F\uDC00-\uDFFF]/g,f=/[\uD800-\uDB7F][\uDC00-\uDFFF]/g;a+="\uD800-\uDB7F\uDC00-\uDFFF",i+="\uD800-\uDB7F\uDC00-\uDFFF",s="["+a+"]["+i+"]*";var d=RegExp("^["+(o=a+":")+"]["+(c=i+":")+"]*$"),m=RegExp("^("+s+"|"+s+":"+s+")$")},17415:(e,t,r)=>{"use strict";e.exports=c;var n=r(21939),a=r(26987),i=r(81070),s=i.HierarchyRequestError,o=i.NotFoundError;function c(){n.call(this)}c.prototype=Object.create(n.prototype,{hasChildNodes:{value:function(){return!1}},firstChild:{value:null},lastChild:{value:null},insertBefore:{value:function(e,t){if(!e.nodeType)throw TypeError("not a node");s()}},replaceChild:{value:function(e,t){if(!e.nodeType)throw TypeError("not a node");s()}},removeChild:{value:function(e){if(!e.nodeType)throw TypeError("not a node");o()}},removeChildren:{value:function(){}},childNodes:{get:function(){return this._childNodes||(this._childNodes=new a),this._childNodes}}})},19429:(e,t,r)=>{"use strict";var n=r(28636);function a(){n.call(this),this.view=null,this.detail=0}e.exports=a,a.prototype=Object.create(n.prototype,{constructor:{value:a},initUIEvent:{value:function(e,t,r,n,a){this.initEvent(e,t,r),this.view=n,this.detail=a}}})},20674:(e,t,r)=>{"use strict";var n=r(28636),a=r(49514),i=r(81070);function s(){}e.exports=s,s.prototype={addEventListener:function(e,t,r){if(t){void 0===r&&(r=!1),this._listeners||(this._listeners=Object.create(null)),this._listeners[e]||(this._listeners[e]=[]);for(var n=this._listeners[e],a=0,i=n.length;a<i;a++){var s=n[a];if(s.listener===t&&s.capture===r)return}var o={listener:t,capture:r};"function"==typeof t&&(o.f=t),n.push(o)}},removeEventListener:function(e,t,r){if(void 0===r&&(r=!1),this._listeners){var n=this._listeners[e];if(n)for(var a=0,i=n.length;a<i;a++){var s=n[a];if(s.listener===t&&s.capture===r)return void(1===n.length?this._listeners[e]=void 0:n.splice(a,1))}}},dispatchEvent:function(e){return this._dispatchEvent(e,!1)},_dispatchEvent:function(e,t){function r(e,t){var r=t.type,a=t.eventPhase;if(t.currentTarget=e,a!==n.CAPTURING_PHASE&&e._handlers&&e._handlers[r]){var i,s=e._handlers[r];if("function"==typeof s)i=s.call(t.currentTarget,t);else{var o=s.handleEvent;if("function"!=typeof o)throw TypeError("handleEvent property of event handler object isnot a function.");i=o.call(s,t)}"mouseover"===t.type?!0===i&&t.preventDefault():!1===i&&t.preventDefault()}var c=e._listeners&&e._listeners[r];if(c){c=c.slice();for(var l=0,u=c.length;l<u;l++){if(t._immediatePropagationStopped)return;var h=c[l];if((a!==n.CAPTURING_PHASE||h.capture)&&(a!==n.BUBBLING_PHASE||!h.capture))if(h.f)h.f.call(t.currentTarget,t);else{var p=h.listener.handleEvent;if("function"!=typeof p)throw TypeError("handleEvent property of event listener object is not a function.");p.call(h.listener,t)}}}}"boolean"!=typeof t&&(t=!1),(!e._initialized||e._dispatching)&&i.InvalidStateError(),e.isTrusted=t,e._dispatching=!0,e.target=this;for(var s=[],o=this.parentNode;o;o=o.parentNode)s.push(o);e.eventPhase=n.CAPTURING_PHASE;for(var c=s.length-1;c>=0&&(r(s[c],e),!e._propagationStopped);c--);if(e._propagationStopped||(e.eventPhase=n.AT_TARGET,r(this,e)),e.bubbles&&!e._propagationStopped){e.eventPhase=n.BUBBLING_PHASE;for(var l=0,u=s.length;l<u&&(r(s[l],e),!e._propagationStopped);l++);}if(e._dispatching=!1,e.eventPhase=n.AT_TARGET,e.currentTarget=null,t&&!e.defaultPrevented&&e instanceof a)switch(e.type){case"mousedown":this._armed={x:e.clientX,y:e.clientY,t:e.timeStamp};break;case"mouseout":case"mouseover":this._armed=null;break;case"mouseup":this._isClick(e)&&this._doClick(e),this._armed=null}return!e.defaultPrevented},_isClick:function(e){return null!==this._armed&&"mouseup"===e.type&&e.isTrusted&&0===e.button&&e.timeStamp-this._armed.t<1e3&&10>Math.abs(e.clientX-this._armed.x)&&10>Math.abs(e.clientY-this._armed.Y)},_doClick:function(e){if(!this._click_in_progress){this._click_in_progress=!0;for(var t=this;t&&!t._post_click_activation_steps;)t=t.parentNode;t&&t._pre_click_activation_steps&&t._pre_click_activation_steps();var r=this.ownerDocument.createEvent("MouseEvent");r.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,e.screenX,e.screenY,e.clientX,e.clientY,e.ctrlKey,e.altKey,e.shiftKey,e.metaKey,e.button,null);var n=this._dispatchEvent(r,!0);t&&(n?t._post_click_activation_steps&&t._post_click_activation_steps(r):t._cancelled_activation_steps&&t._cancelled_activation_steps())}},_setEventHandler:function(e,t){this._handlers||(this._handlers=Object.create(null)),this._handlers[e]=t},_getEventHandler:function(e){return this._handlers&&this._handlers[e]||null}}},21939:(e,t,r)=>{"use strict";e.exports=o;var n=r(20674),a=r(37070),i=r(37882),s=r(81070);function o(){n.call(this),this.parentNode=null,this._nextSibling=this._previousSibling=this,this._index=void 0}var c=o.ELEMENT_NODE=1,l=o.ATTRIBUTE_NODE=2,u=o.TEXT_NODE=3,h=o.CDATA_SECTION_NODE=4,p=o.ENTITY_REFERENCE_NODE=5,f=o.ENTITY_NODE=6,d=o.PROCESSING_INSTRUCTION_NODE=7,m=o.COMMENT_NODE=8,g=o.DOCUMENT_NODE=9,b=o.DOCUMENT_TYPE_NODE=10,v=o.DOCUMENT_FRAGMENT_NODE=11,E=o.NOTATION_NODE=12,y=o.DOCUMENT_POSITION_DISCONNECTED=1,T=o.DOCUMENT_POSITION_PRECEDING=2,_=o.DOCUMENT_POSITION_FOLLOWING=4,w=o.DOCUMENT_POSITION_CONTAINS=8,S=o.DOCUMENT_POSITION_CONTAINED_BY=16,N=o.DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC=32;o.prototype=Object.create(n.prototype,{baseURI:{get:s.nyi},parentElement:{get:function(){return this.parentNode&&this.parentNode.nodeType===c?this.parentNode:null}},hasChildNodes:{value:s.shouldOverride},firstChild:{get:s.shouldOverride},lastChild:{get:s.shouldOverride},isConnected:{get:function(){let e=this;for(;null!=e;){if(e.nodeType===o.DOCUMENT_NODE)return!0;null!=(e=e.parentNode)&&e.nodeType===o.DOCUMENT_FRAGMENT_NODE&&(e=e.host)}return!1}},previousSibling:{get:function(){var e=this.parentNode;return e&&this!==e.firstChild?this._previousSibling:null}},nextSibling:{get:function(){var e=this.parentNode,t=this._nextSibling;return e&&t!==e.firstChild?t:null}},textContent:{get:function(){return null},set:function(e){}},innerText:{get:function(){return null},set:function(e){}},_countChildrenOfType:{value:function(e){for(var t=0,r=this.firstChild;null!==r;r=r.nextSibling)r.nodeType===e&&t++;return t}},_ensureInsertValid:{value:function(e,t,r){var n,a;if(!e.nodeType)throw TypeError("not a node");switch(this.nodeType){case g:case v:case c:break;default:s.HierarchyRequestError()}switch(e.isAncestor(this)&&s.HierarchyRequestError(),(null!==t||!r)&&t.parentNode!==this&&s.NotFoundError(),e.nodeType){case v:case b:case c:case u:case d:case m:break;default:s.HierarchyRequestError()}if(this.nodeType===g)switch(e.nodeType){case u:s.HierarchyRequestError();break;case v:switch(e._countChildrenOfType(u)>0&&s.HierarchyRequestError(),e._countChildrenOfType(c)){case 0:break;case 1:if(null!==t)for(r&&t.nodeType===b&&s.HierarchyRequestError(),a=t.nextSibling;null!==a;a=a.nextSibling)a.nodeType===b&&s.HierarchyRequestError();n=this._countChildrenOfType(c),r?n>0&&s.HierarchyRequestError():(n>1||1===n&&t.nodeType!==c)&&s.HierarchyRequestError();break;default:s.HierarchyRequestError()}break;case c:if(null!==t)for(r&&t.nodeType===b&&s.HierarchyRequestError(),a=t.nextSibling;null!==a;a=a.nextSibling)a.nodeType===b&&s.HierarchyRequestError();n=this._countChildrenOfType(c),r?n>0&&s.HierarchyRequestError():(n>1||1===n&&t.nodeType!==c)&&s.HierarchyRequestError();break;case b:if(null===t)this._countChildrenOfType(c)&&s.HierarchyRequestError();else for(a=this.firstChild;null!==a&&a!==t;a=a.nextSibling)a.nodeType===c&&s.HierarchyRequestError();n=this._countChildrenOfType(b),r?n>0&&s.HierarchyRequestError():(n>1||1===n&&t.nodeType!==b)&&s.HierarchyRequestError()}else e.nodeType===b&&s.HierarchyRequestError()}},insertBefore:{value:function(e,t){this._ensureInsertValid(e,t,!0);var r=t;return r===e&&(r=e.nextSibling),this.doc.adoptNode(e),e._insertOrReplace(this,r,!1),e}},appendChild:{value:function(e){return this.insertBefore(e,null)}},_appendChild:{value:function(e){e._insertOrReplace(this,null,!1)}},removeChild:{value:function(e){if(!e.nodeType)throw TypeError("not a node");return e.parentNode!==this&&s.NotFoundError(),e.remove(),e}},replaceChild:{value:function(e,t){return this._ensureInsertValid(e,t,!1),e.doc!==this.doc&&this.doc.adoptNode(e),e._insertOrReplace(this,t,!0),t}},contains:{value:function(e){return null!==e&&(this===e||(this.compareDocumentPosition(e)&S)!=0)}},compareDocumentPosition:{value:function(e){if(this===e)return 0;if(this.doc!==e.doc||this.rooted!==e.rooted)return y+N;for(var t=[],r=[],n=this;null!==n;n=n.parentNode)t.push(n);for(n=e;null!==n;n=n.parentNode)r.push(n);if(t.reverse(),r.reverse(),t[0]!==r[0])return y+N;n=Math.min(t.length,r.length);for(var a=1;a<n;a++)if(t[a]!==r[a])if(t[a].index<r[a].index)return _;else return T;return t.length<r.length?_+S:T+w}},isSameNode:{value:function(e){return this===e}},isEqualNode:{value:function(e){if(!e||e.nodeType!==this.nodeType||!this.isEqual(e))return!1;for(var t=this.firstChild,r=e.firstChild;t&&r;t=t.nextSibling,r=r.nextSibling)if(!t.isEqualNode(r))return!1;return null===t&&null===r}},cloneNode:{value:function(e){var t=this.clone();if(e)for(var r=this.firstChild;null!==r;r=r.nextSibling)t._appendChild(r.cloneNode(!0));return t}},lookupPrefix:{value:function(e){var t;if(""===e||null==e)return null;switch(this.nodeType){case c:return this._lookupNamespacePrefix(e,this);case g:return(t=this.documentElement)?t.lookupPrefix(e):null;case f:case E:case v:case b:return null;case l:return(t=this.ownerElement)?t.lookupPrefix(e):null;default:return(t=this.parentElement)?t.lookupPrefix(e):null}}},lookupNamespaceURI:{value:function(e){var t;switch((""===e||void 0===e)&&(e=null),this.nodeType){case c:return s.shouldOverride();case g:return(t=this.documentElement)?t.lookupNamespaceURI(e):null;case f:case E:case b:case v:return null;case l:return(t=this.ownerElement)?t.lookupNamespaceURI(e):null;default:return(t=this.parentElement)?t.lookupNamespaceURI(e):null}}},isDefaultNamespace:{value:function(e){return(""===e||void 0===e)&&(e=null),this.lookupNamespaceURI(null)===e}},index:{get:function(){var e=this.parentNode;if(this===e.firstChild)return 0;var t=e.childNodes;if(void 0===this._index||t[this._index]!==this){for(var r=0;r<t.length;r++)t[r]._index=r;s.assert(t[this._index]===this)}return this._index}},isAncestor:{value:function(e){if(this.doc!==e.doc||this.rooted!==e.rooted)return!1;for(var t=e;t;t=t.parentNode)if(t===this)return!0;return!1}},ensureSameDoc:{value:function(e){null===e.ownerDocument?e.ownerDocument=this.doc:e.ownerDocument!==this.doc&&s.WrongDocumentError()}},removeChildren:{value:s.shouldOverride},_insertOrReplace:{value:function(e,t,r){this.nodeType===v&&this.rooted&&s.HierarchyRequestError(),e._childNodes&&(o=null===t?e._childNodes.length:t.index,this.parentNode===e&&this.index<o&&o--),r&&(t.rooted&&t.doc.mutateRemove(t),t.parentNode=null);var n=t;null===n&&(n=e.firstChild);var i=this.rooted&&e.rooted;if(this.nodeType===v){for(var o,c,l,u=[0,+!!r],h=this.firstChild;null!==h;h=l)l=h.nextSibling,u.push(h),h.parentNode=e;var p=u.length;if(r?a.replace(n,p>2?u[2]:null):p>2&&null!==n&&a.insertBefore(u[2],n),e._childNodes)for(u[0]=null===t?e._childNodes.length:t._index,e._childNodes.splice.apply(e._childNodes,u),c=2;c<p;c++)u[c]._index=u[0]+(c-2);else e._firstChild===t&&(p>2?e._firstChild=u[2]:r&&(e._firstChild=null));if(this._childNodes?this._childNodes.length=0:this._firstChild=null,e.rooted)for(e.modify(),c=2;c<p;c++)e.doc.mutateInsert(u[c])}else{if(t===this)return;i?this._remove():this.parentNode&&this.remove(),this.parentNode=e,r?(a.replace(n,this),e._childNodes?(this._index=o,e._childNodes[o]=this):e._firstChild===t&&(e._firstChild=this)):(null!==n&&a.insertBefore(this,n),e._childNodes?(this._index=o,e._childNodes.splice(o,0,this)):e._firstChild===t&&(e._firstChild=this)),i?(e.modify(),e.doc.mutateMove(this)):e.rooted&&(e.modify(),e.doc.mutateInsert(this))}}},lastModTime:{get:function(){return this._lastModTime||(this._lastModTime=this.doc.modclock),this._lastModTime}},modify:{value:function(){if(this.doc.modclock)for(var e=++this.doc.modclock,t=this;t;t=t.parentElement)t._lastModTime&&(t._lastModTime=e)}},doc:{get:function(){return this.ownerDocument||this}},rooted:{get:function(){return!!this._nid}},normalize:{value:function(){for(var e,t=this.firstChild;null!==t;t=e)if(e=t.nextSibling,t.normalize&&t.normalize(),t.nodeType===o.TEXT_NODE){if(""===t.nodeValue){this.removeChild(t);continue}var r=t.previousSibling;if(null===r)continue;r.nodeType===o.TEXT_NODE&&(r.appendData(t.nodeValue),this.removeChild(t))}}},serialize:{value:function(){if(this._innerHTML)return this._innerHTML;for(var e="",t=this.firstChild;null!==t;t=t.nextSibling)e+=i.serializeOne(t,this);return e}},outerHTML:{get:function(){return i.serializeOne(this,{nodeType:0})},set:s.nyi},ELEMENT_NODE:{value:c},ATTRIBUTE_NODE:{value:l},TEXT_NODE:{value:u},CDATA_SECTION_NODE:{value:h},ENTITY_REFERENCE_NODE:{value:p},ENTITY_NODE:{value:f},PROCESSING_INSTRUCTION_NODE:{value:d},COMMENT_NODE:{value:m},DOCUMENT_NODE:{value:g},DOCUMENT_TYPE_NODE:{value:b},DOCUMENT_FRAGMENT_NODE:{value:v},NOTATION_NODE:{value:E},DOCUMENT_POSITION_DISCONNECTED:{value:y},DOCUMENT_POSITION_PRECEDING:{value:T},DOCUMENT_POSITION_FOLLOWING:{value:_},DOCUMENT_POSITION_CONTAINS:{value:w},DOCUMENT_POSITION_CONTAINED_BY:{value:S},DOCUMENT_POSITION_IMPLEMENTATION_SPECIFIC:{value:N}})},24122:(e,t,r)=>{"use strict";e.exports=a;var n=r(81070);function a(e){this.element=e}Object.defineProperties(a.prototype,{length:{get:n.shouldOverride},item:{value:n.shouldOverride},getNamedItem:{value:function(e){return this.element.getAttributeNode(e)}},getNamedItemNS:{value:function(e,t){return this.element.getAttributeNodeNS(e,t)}},setNamedItem:{value:n.nyi},setNamedItemNS:{value:n.nyi},removeNamedItem:{value:function(e){var t=this.element.getAttributeNode(e);if(t)return this.element.removeAttribute(e),t;n.NotFoundError()}},removeNamedItemNS:{value:function(e,t){var r=this.element.getAttributeNodeNS(e,t);if(r)return this.element.removeAttributeNS(e,t),r;n.NotFoundError()}}})},26987:(e,t,r)=>{"use strict";var n;try{n=r(93639)}catch(e){n=r(78282)}e.exports=n},28636:e=>{"use strict";function t(e,r){if(this.type="",this.target=null,this.currentTarget=null,this.eventPhase=t.AT_TARGET,this.bubbles=!1,this.cancelable=!1,this.isTrusted=!1,this.defaultPrevented=!1,this.timeStamp=Date.now(),this._propagationStopped=!1,this._immediatePropagationStopped=!1,this._initialized=!0,this._dispatching=!1,e&&(this.type=e),r)for(var n in r)this[n]=r[n]}e.exports=t,t.CAPTURING_PHASE=1,t.AT_TARGET=2,t.BUBBLING_PHASE=3,t.prototype=Object.create(Object.prototype,{constructor:{value:t},stopPropagation:{value:function(){this._propagationStopped=!0}},stopImmediatePropagation:{value:function(){this._propagationStopped=!0,this._immediatePropagationStopped=!0}},preventDefault:{value:function(){this.cancelable&&(this.defaultPrevented=!0)}},initEvent:{value:function(e,t,r){this._initialized=!0,this._dispatching||(this._propagationStopped=!1,this._immediatePropagationStopped=!1,this.defaultPrevented=!1,this.isTrusted=!1,this.target=null,this.type=e,this.bubbles=t,this.cancelable=r)}}})},29519:(e,t)=>{"use strict";var r=Object.create(null,{location:{get:function(){throw Error("window.location is not supported.")}}}),n=function(e,t){return 2&e.compareDocumentPosition(t)?1:-1},a=function(e){for(;(e=e.nextSibling)&&1!==e.nodeType;);return e},i=function(e){for(;(e=e.previousSibling)&&1!==e.nodeType;);return e},s=function(e){if(e=e.firstChild)for(;1!==e.nodeType&&(e=e.nextSibling););return e},o=function(e){if(e=e.lastChild)for(;1!==e.nodeType&&(e=e.previousSibling););return e},c=function(e){if(!e.parentNode)return!1;var t=e.parentNode.nodeType;return 1===t||9===t},l=function(e){if(!e)return e;var t=e[0];return'"'===t||"'"===t?(e=e[e.length-1]===t?e.slice(1,-1):e.slice(1)).replace(y.str_escape,function(e){var t=/^\\(?:([0-9A-Fa-f]+)|([\r\n\f]+))/.exec(e);if(!t)return e.slice(1);if(t[2])return"";var r=parseInt(t[1],16);return String.fromCodePoint?String.fromCodePoint(r):String.fromCharCode(r)}):y.ident.test(e)?u(e):e},u=function(e){return e.replace(y.escape,function(e){var t=/^\\([0-9A-Fa-f]+)/.exec(e);if(!t)return e[1];var r=parseInt(t[1],16);return String.fromCodePoint?String.fromCodePoint(r):String.fromCharCode(r)})},h=Array.prototype.indexOf?Array.prototype.indexOf:function(e,t){for(var r=this.length;r--;)if(this[r]===t)return r;return -1},p=function(e,t){return new RegExp(y.inside.source.replace(/</g,e).replace(/>/g,t))},f=function(e,t,r){return new RegExp(e=(e=e.source).replace(t,r.source||r))},d=function(e,t){return e.replace(/^(?:\w+:\/\/|\/+)/,"").replace(/(?:\/+|\/*#.*?)$/,"").split("/",t).join("/")},m=function(e,t){var r,n=e.replace(/\s+/g,"");return"even"===n?n="2n+0":"odd"===n?n="2n+1":-1===n.indexOf("n")&&(n="0n"+n),{group:"-"===(r=/^([+-])?(\d+)?n([+-])?(\d+)?$/.exec(n))[1]?-(r[2]||1):+(r[2]||1),offset:r[4]?"-"===r[3]?-r[4]:+r[4]:0}},g=function(e,t,r){var n=m(e),l=n.group,u=n.offset,h=r?o:s,p=r?i:a;return function(e){if(c(e))for(var r=h(e.parentNode),n=0;r;){if(t(r,e)&&n++,r===e)return n-=u,l&&n?n%l==0&&n<0==l<0:!n;r=p(r)}}},b={"*":function(){return!0},type:function(e){return e=e.toLowerCase(),function(t){return t.nodeName.toLowerCase()===e}},attr:function(e,t,r,n){return t=v[t],function(a){var i;switch(e){case"for":i=a.htmlFor;break;case"class":""===(i=a.className)&&null==a.getAttribute("class")&&(i=null);break;case"href":case"src":i=a.getAttribute(e,2);break;case"title":i=a.getAttribute("title")||null;break;case"id":case"lang":case"dir":case"accessKey":case"hidden":case"tabIndex":case"style":if(a.getAttribute){i=a.getAttribute(e);break}default:if(a.hasAttribute&&!a.hasAttribute(e))break;i=null!=a[e]?a[e]:a.getAttribute&&a.getAttribute(e)}if(null!=i)return i+="",n&&(i=i.toLowerCase(),r=r.toLowerCase()),t(i,r)}},":first-child":function(e){return!i(e)&&c(e)},":last-child":function(e){return!a(e)&&c(e)},":only-child":function(e){return!i(e)&&!a(e)&&c(e)},":nth-child":function(e,t){return g(e,function(){return!0},t)},":nth-last-child":function(e){return b[":nth-child"](e,!0)},":root":function(e){return e.ownerDocument.documentElement===e},":empty":function(e){return!e.firstChild},":not":function(e){var t=k(e);return function(e){return!t(e)}},":first-of-type":function(e){if(c(e)){for(var t=e.nodeName;e=i(e);)if(e.nodeName===t)return;return!0}},":last-of-type":function(e){if(c(e)){for(var t=e.nodeName;e=a(e);)if(e.nodeName===t)return;return!0}},":only-of-type":function(e){return b[":first-of-type"](e)&&b[":last-of-type"](e)},":nth-of-type":function(e,t){return g(e,function(e,t){return e.nodeName===t.nodeName},t)},":nth-last-of-type":function(e){return b[":nth-of-type"](e,!0)},":checked":function(e){return!!(e.checked||e.selected)},":indeterminate":function(e){return!b[":checked"](e)},":enabled":function(e){return!e.disabled&&"hidden"!==e.type},":disabled":function(e){return!!e.disabled},":target":function(e){return e.id===r.location.hash.substring(1)},":focus":function(e){return e===e.ownerDocument.activeElement},":is":function(e){return k(e)},":matches":function(e){return b[":is"](e)},":nth-match":function(e,t){var r=e.split(/\s*,\s*/);return g(r.shift(),k(r.join(",")),t)},":nth-last-match":function(e){return b[":nth-match"](e,!0)},":links-here":function(e){return e+""==r.location+""},":lang":function(e){return function(t){for(;t;){if(t.lang)return 0===t.lang.indexOf(e);t=t.parentNode}}},":dir":function(e){return function(t){for(;t;){if(t.dir)return t.dir===e;t=t.parentNode}}},":scope":function(e,t){var r=t||e.ownerDocument;return 9===r.nodeType?e===r.documentElement:e===r},":any-link":function(e){return"string"==typeof e.href},":local-link":function(e){if(e.nodeName)return e.href&&e.host===r.location.host;var t=+e+1;return function(e){if(e.href){var n=r.location+"",a=e+"";return d(n,t)===d(a,t)}}},":default":function(e){return!!e.defaultSelected},":valid":function(e){return e.willValidate||e.validity&&e.validity.valid},":invalid":function(e){return!b[":valid"](e)},":in-range":function(e){return e.value>e.min&&e.value<=e.max},":out-of-range":function(e){return!b[":in-range"](e)},":required":function(e){return!!e.required},":optional":function(e){return!e.required},":read-only":function(e){if(e.readOnly)return!0;var t=e.getAttribute("contenteditable"),r=e.contentEditable,n=e.nodeName.toLowerCase();return((n="input"!==n&&"textarea"!==n)||e.disabled)&&null==t&&"true"!==r},":read-write":function(e){return!b[":read-only"](e)},":hover":function(){throw Error(":hover is not supported.")},":active":function(){throw Error(":active is not supported.")},":link":function(){throw Error(":link is not supported.")},":visited":function(){throw Error(":visited is not supported.")},":column":function(){throw Error(":column is not supported.")},":nth-column":function(){throw Error(":nth-column is not supported.")},":nth-last-column":function(){throw Error(":nth-last-column is not supported.")},":current":function(){throw Error(":current is not supported.")},":past":function(){throw Error(":past is not supported.")},":future":function(){throw Error(":future is not supported.")},":contains":function(e){return function(t){return -1!==(t.innerText||t.textContent||t.value||"").indexOf(e)}},":has":function(e){return function(t){return C(e,t).length>0}}},v={"-":function(){return!0},"=":function(e,t){return e===t},"*=":function(e,t){return -1!==e.indexOf(t)},"~=":function(e,t){var r,n,a,i;for(n=0;;n=r+1){if(-1===(r=e.indexOf(t,n)))return!1;if(a=e[r-1],i=e[r+t.length],(!a||" "===a)&&(!i||" "===i))return!0}},"|=":function(e,t){var r,n=e.indexOf(t);if(0===n)return"-"===(r=e[n+t.length])||!r},"^=":function(e,t){return 0===e.indexOf(t)},"$=":function(e,t){var r=e.lastIndexOf(t);return -1!==r&&r+t.length===e.length},"!=":function(e,t){return e!==t}},E={" ":function(e){return function(t){for(;t=t.parentNode;)if(e(t))return t}},">":function(e){return function(t){if(t=t.parentNode)return e(t)&&t}},"+":function(e){return function(t){if(t=i(t))return e(t)&&t}},"~":function(e){return function(t){for(;t=i(t);)if(e(t))return t}},noop:function(e){return function(t){return e(t)&&t}},ref:function(e,t){var r;function n(e){for(var t=e.ownerDocument.getElementsByTagName("*"),a=t.length;a--;)if(r=t[a],n.test(e))return r=null,!0;r=null}return n.combinator=function(n){if(r&&r.getAttribute){var a=r.getAttribute(t)||"";if("#"===a[0]&&(a=a.substring(1)),a===n.id&&e(r))return r}},n}},y={escape:/\\(?:[^0-9A-Fa-f\r\n]|[0-9A-Fa-f]{1,6}[\r\n\t ]?)/g,str_escape:/(escape)|\\(\n|\r\n?|\f)/g,nonascii:/[\u00A0-\uFFFF]/,cssid:/(?:(?!-?[0-9])(?:escape|nonascii|[-_a-zA-Z0-9])+)/,qname:/^ *(cssid|\*)/,simple:/^(?:([.#]cssid)|pseudo|attr)/,ref:/^ *\/(cssid)\/ */,combinator:/^(?: +([^ \w*.#\\]) +|( )+|([^ \w*.#\\]))(?! *$)/,attr:/^\[(cssid)(?:([^\w]?=)(inside))?\]/,pseudo:/^(:cssid)(?:\((inside)\))?/,inside:/(?:"(?:\\"|[^"])*"|'(?:\\'|[^'])*'|<[^"'>]*>|\\["'>]|[^"'>])*/,ident:/^(cssid)$/};y.cssid=f(y.cssid,"nonascii",y.nonascii),y.cssid=f(y.cssid,"escape",y.escape),y.qname=f(y.qname,"cssid",y.cssid),y.simple=f(y.simple,"cssid",y.cssid),y.ref=f(y.ref,"cssid",y.cssid),y.attr=f(y.attr,"cssid",y.cssid),y.pseudo=f(y.pseudo,"cssid",y.cssid),y.inside=f(y.inside,"[^\"'>]*",y.inside),y.attr=f(y.attr,"inside",p("\\[","\\]")),y.pseudo=f(y.pseudo,"inside",p("\\(","\\)")),y.simple=f(y.simple,"pseudo",y.pseudo),y.simple=f(y.simple,"attr",y.attr),y.ident=f(y.ident,"cssid",y.cssid),y.str_escape=f(y.str_escape,"escape",y.escape);var T=function(e){for(var t,r,n,a,i,s,o=e.replace(/^\s+|\s+$/g,""),c=[],l=[];o;){if(a=y.qname.exec(o))o=o.substring(a[0].length),n=u(a[1]),l.push(_(n,!0));else if(a=y.simple.exec(o))o=o.substring(a[0].length),n="*",l.push(_(n,!0)),l.push(_(a));else throw SyntaxError("Invalid selector.");for(;a=y.simple.exec(o);)o=o.substring(a[0].length),l.push(_(a));if("!"===o[0]&&(o=o.substring(1),(r=N()).qname=n,l.push(r.simple)),a=y.ref.exec(o)){o=o.substring(a[0].length),s=E.ref(w(l),u(a[1])),c.push(s.combinator),l=[];continue}if(a=y.combinator.exec(o)){if(o=o.substring(a[0].length),","===(i=a[1]||a[2]||a[3])){c.push(E.noop(w(l)));break}}else i="noop";if(!E[i])throw SyntaxError("Bad combinator.");c.push(E[i](w(l))),l=[]}return(t=S(c)).qname=n,t.sel=o,r&&(r.lname=t.qname,r.test=t,r.qname=r.qname,r.sel=t.sel,t=r),s&&(s.test=t,s.qname=t.qname,s.sel=t.sel,t=s),t},_=function(e,t){if(t)return"*"===e?b["*"]:b.type(e);if(e[1])return"."===e[1][0]?b.attr("class","~=",u(e[1].substring(1)),!1):b.attr("id","=",u(e[1].substring(1)),!1);if(e[2])return e[3]?b[u(e[2])](l(e[3])):b[u(e[2])];if(e[4]){var r=e[6],n=/["'\s]\s*I$/i.test(r);return n&&(r=r.replace(/\s*I$/i,"")),b.attr(u(e[4]),e[5]||"-",l(r),n)}throw SyntaxError("Unknown Selector.")},w=function(e){var t,r=e.length;return r<2?e[0]:function(n){if(n){for(t=0;t<r;t++)if(!e[t](n))return;return!0}}},S=function(e){return e.length<2?function(t){return!!e[0](t)}:function(t){for(var r=e.length;r--;)if(!(t=e[r](t)))return;return!0}},N=function(){var e;function t(r){for(var n=r.ownerDocument.getElementsByTagName(t.lname),a=n.length;a--;)if(t.test(n[a])&&e===r)return e=null,!0;e=null}return t.simple=function(t){return e=t,!0},t},k=function(e){for(var t=T(e),r=[t];t.sel;)t=T(t.sel),r.push(t);return r.length<2?t:function(e){for(var t=r.length,n=0;n<t;n++)if(r[n](e))return!0}},C=function(e,t){for(var r,a=[],i=T(e),s=t.getElementsByTagName(i.qname),o=0;r=s[o++];)i(r)&&a.push(r);if(i.sel){for(;i.sel;)for(i=T(i.sel),s=t.getElementsByTagName(i.qname),o=0;r=s[o++];)i(r)&&-1===h.call(a,r)&&a.push(r);a.sort(n)}return a};e.exports=t=function(e,t){var r,n;if(11!==t.nodeType&&-1===e.indexOf(" ")){if("#"===e[0]&&t.rooted&&/^#[A-Z_][-A-Z0-9_]*$/i.test(e)&&t.doc._hasMultipleElementsWithId&&(r=e.substring(1),!t.doc._hasMultipleElementsWithId(r)))return(n=t.doc.getElementById(r))?[n]:[];if("."===e[0]&&/^\.\w+$/.test(e))return t.getElementsByClassName(e.substring(1));if(/^\w+$/.test(e))return t.getElementsByTagName(e)}return C(e,t)},t.selectors=b,t.operators=v,t.combinators=E,t.matches=function(e,t){var r={sel:t};do if((r=T(r.sel))(e))return!0;while(r.sel);return!1}},31427:(e,t,r)=>{"use strict";var n=r(67237),a=r(75105);r(63427);var i=r(49205);t.createDOMImplementation=function(){return new n(null)},t.createDocument=function(e,t){if(e||t){var r=new a;return r.parse(e||"",!0),r.document()}return new n(null).createHTMLDocument("")},t.createIncrementalHTMLParser=function(){var e=new a;return{write:function(t){t.length>0&&e.parse(t,!1,function(){return!0})},end:function(t){e.parse(t||"",!0,function(){return!0})},process:function(t){return e.parse("",!1,t)},document:function(){return e.document()}}},t.createWindow=function(e,r){var n=t.createDocument(e);return void 0!==r&&(n._address=r),new i.Window(n)},t.impl=i},31878:(e,t,r)=>{"use strict";var n=r(81070);function a(e,t){this._getString=e,this._setString=t,this._length=0,this._lastStringValue="",this._update()}function i(e,t){var r,n=e._length;for(r=0,e._length=t.length;r<t.length;r++)e[r]=t[r];for(;r<n;r++)e[r]=void 0}function s(e){return""===(e=String(e))&&n.SyntaxError(),/[ \t\r\n\f]/.test(e)&&n.InvalidCharacterError(),e}function o(e){var t=e._getString();if(t===e._lastStringValue){for(var r=e._length,n=Array(r),a=0;a<r;a++)n[a]=e[a];return n}var i=t.replace(/(^[ \t\r\n\f]+)|([ \t\r\n\f]+$)/g,"");if(""===i)return[];var s=Object.create(null);return i.split(/[ \t\r\n\f]+/g).filter(function(e){var t="$"+e;return!s[t]&&(s[t]=!0,!0)})}e.exports=a,Object.defineProperties(a.prototype,{length:{get:function(){return this._length}},item:{value:function(e){var t=o(this);return e<0||e>=t.length?null:t[e]}},contains:{value:function(e){return e=String(e),o(this).indexOf(e)>-1}},add:{value:function(){for(var e=o(this),t=0,r=arguments.length;t<r;t++){var n=s(arguments[t]);0>e.indexOf(n)&&e.push(n)}this._update(e)}},remove:{value:function(){for(var e=o(this),t=0,r=arguments.length;t<r;t++){var n=s(arguments[t]),a=e.indexOf(n);a>-1&&e.splice(a,1)}this._update(e)}},toggle:{value:function(e,t){return(e=s(e),this.contains(e))?void 0!==t&&!1!==t||(this.remove(e),!1):(void 0===t||!0===t)&&(this.add(e),!0)}},replace:{value:function(e,t){""===String(t)&&n.SyntaxError(),e=s(e),t=s(t);var r=o(this),a=r.indexOf(e);if(a<0)return!1;var i=r.indexOf(t);return i<0?r[a]=t:a<i?(r[a]=t,r.splice(i,1)):r.splice(a,1),this._update(r),!0}},toString:{value:function(){return this._getString()}},value:{get:function(){return this._getString()},set:function(e){this._setString(e),this._update()}},_update:{value:function(e){e?(i(this,e),this._setString(e.join(" ").trim())):i(this,o(this)),this._lastStringValue=this._getString()}}})},34405:e=>{"use strict";var t={FILTER_ACCEPT:1,FILTER_REJECT:2,FILTER_SKIP:3,SHOW_ALL:0xffffffff,SHOW_ELEMENT:1,SHOW_ATTRIBUTE:2,SHOW_TEXT:4,SHOW_CDATA_SECTION:8,SHOW_ENTITY_REFERENCE:16,SHOW_ENTITY:32,SHOW_PROCESSING_INSTRUCTION:64,SHOW_COMMENT:128,SHOW_DOCUMENT:256,SHOW_DOCUMENT_TYPE:512,SHOW_DOCUMENT_FRAGMENT:1024,SHOW_NOTATION:2048};e.exports=t.constructor=t.prototype=t},34940:(e,t,r)=>{"use strict";function n(e,t){return Array(t+1).join(e)}r.d(t,{A:()=>x});var a,i=["ADDRESS","ARTICLE","ASIDE","AUDIO","BLOCKQUOTE","BODY","CANVAS","CENTER","DD","DIR","DIV","DL","DT","FIELDSET","FIGCAPTION","FIGURE","FOOTER","FORM","FRAMESET","H1","H2","H3","H4","H5","H6","HEADER","HGROUP","HR","HTML","ISINDEX","LI","MAIN","MENU","NAV","NOFRAMES","NOSCRIPT","OL","OUTPUT","P","PRE","SECTION","TABLE","TBODY","TD","TFOOT","TH","THEAD","TR","UL"];function s(e){return u(e,i)}var o=["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","META","PARAM","SOURCE","TRACK","WBR"];function c(e){return u(e,o)}var l=["A","TABLE","THEAD","TBODY","TFOOT","TH","TD","IFRAME","SCRIPT","AUDIO","VIDEO"];function u(e,t){return t.indexOf(e.nodeName)>=0}function h(e,t){return e.getElementsByTagName&&t.some(function(t){return e.getElementsByTagName(t).length})}var p={};function f(e){return e?e.replace(/(\n+\s*)+/g,"\n"):""}function d(e){for(var t in this.options=e,this._keep=[],this._remove=[],this.blankRule={replacement:e.blankReplacement},this.keepReplacement=e.keepReplacement,this.defaultRule={replacement:e.defaultReplacement},this.array=[],e.rules)this.array.push(e.rules[t])}function m(e,t,r){for(var n=0;n<e.length;n++){var a=e[n];if(function(e,t,r){var n=e.filter;if("string"==typeof n){if(n===t.nodeName.toLowerCase())return!0}else if(Array.isArray(n)){if(n.indexOf(t.nodeName.toLowerCase())>-1)return!0}else if("function"==typeof n){if(n.call(e,t,r))return!0}else throw TypeError("`filter` needs to be a string, array, or function")}(a,t,r))return a}}function g(e){var t=e.nextSibling||e.parentNode;return e.parentNode.removeChild(e),t}function b(e,t,r){return e&&e.parentNode===t||r(t)?t.nextSibling||t.parentNode:t.firstChild||t.nextSibling||t.parentNode}p.paragraph={filter:"p",replacement:function(e){return"\n\n"+e+"\n\n"}},p.lineBreak={filter:"br",replacement:function(e,t,r){return r.br+"\n"}},p.heading={filter:["h1","h2","h3","h4","h5","h6"],replacement:function(e,t,r){var a=Number(t.nodeName.charAt(1));if("setext"!==r.headingStyle||!(a<3))return"\n\n"+n("#",a)+" "+e+"\n\n";var i=n(1===a?"=":"-",e.length);return"\n\n"+e+"\n"+i+"\n\n"}},p.blockquote={filter:"blockquote",replacement:function(e){return"\n\n"+(e=(e=e.replace(/^\n+|\n+$/g,"")).replace(/^/gm,"> "))+"\n\n"}},p.list={filter:["ul","ol"],replacement:function(e,t){var r=t.parentNode;return"LI"===r.nodeName&&r.lastElementChild===t?"\n"+e:"\n\n"+e+"\n\n"}},p.listItem={filter:"li",replacement:function(e,t,r){e=e.replace(/^\n+/,"").replace(/\n+$/,"\n").replace(/\n/gm,"\n    ");var n=r.bulletListMarker+"   ",a=t.parentNode;if("OL"===a.nodeName){var i=a.getAttribute("start"),s=Array.prototype.indexOf.call(a.children,t);n=(i?Number(i)+s:s+1)+".  "}return n+e+(t.nextSibling&&!/\n$/.test(e)?"\n":"")}},p.indentedCodeBlock={filter:function(e,t){return"indented"===t.codeBlockStyle&&"PRE"===e.nodeName&&e.firstChild&&"CODE"===e.firstChild.nodeName},replacement:function(e,t,r){return"\n\n    "+t.firstChild.textContent.replace(/\n/g,"\n    ")+"\n\n"}},p.fencedCodeBlock={filter:function(e,t){return"fenced"===t.codeBlockStyle&&"PRE"===e.nodeName&&e.firstChild&&"CODE"===e.firstChild.nodeName},replacement:function(e,t,r){for(var a,i=((t.firstChild.getAttribute("class")||"").match(/language-(\S+)/)||[null,""])[1],s=t.firstChild.textContent,o=r.fence.charAt(0),c=3,l=RegExp("^"+o+"{3,}","gm");a=l.exec(s);)a[0].length>=c&&(c=a[0].length+1);var u=n(o,c);return"\n\n"+u+i+"\n"+s.replace(/\n$/,"")+"\n"+u+"\n\n"}},p.horizontalRule={filter:"hr",replacement:function(e,t,r){return"\n\n"+r.hr+"\n\n"}},p.inlineLink={filter:function(e,t){return"inlined"===t.linkStyle&&"A"===e.nodeName&&e.getAttribute("href")},replacement:function(e,t){var r=t.getAttribute("href");r&&(r=r.replace(/([()])/g,"\\$1"));var n=f(t.getAttribute("title"));return n&&(n=' "'+n.replace(/"/g,'\\"')+'"'),"["+e+"]("+r+n+")"}},p.referenceLink={filter:function(e,t){return"referenced"===t.linkStyle&&"A"===e.nodeName&&e.getAttribute("href")},replacement:function(e,t,r){var n,a,i=t.getAttribute("href"),s=f(t.getAttribute("title"));switch(s&&(s=' "'+s+'"'),r.linkReferenceStyle){case"collapsed":n="["+e+"][]",a="["+e+"]: "+i+s;break;case"shortcut":n="["+e+"]",a="["+e+"]: "+i+s;break;default:var o=this.references.length+1;n="["+e+"]["+o+"]",a="["+o+"]: "+i+s}return this.references.push(a),n},references:[],append:function(e){var t="";return this.references.length&&(t="\n\n"+this.references.join("\n")+"\n\n",this.references=[]),t}},p.emphasis={filter:["em","i"],replacement:function(e,t,r){return e.trim()?r.emDelimiter+e+r.emDelimiter:""}},p.strong={filter:["strong","b"],replacement:function(e,t,r){return e.trim()?r.strongDelimiter+e+r.strongDelimiter:""}},p.code={filter:function(e){var t=e.previousSibling||e.nextSibling,r="PRE"===e.parentNode.nodeName&&!t;return"CODE"===e.nodeName&&!r},replacement:function(e){if(!e)return"";e=e.replace(/\r?\n|\r/g," ");for(var t=/^`|^ .*?[^ ].* $|`$/.test(e)?" ":"",r="`",n=e.match(/`+/gm)||[];-1!==n.indexOf(r);)r+="`";return r+t+e+t+r}},p.image={filter:"img",replacement:function(e,t){var r=f(t.getAttribute("alt")),n=t.getAttribute("src")||"",a=f(t.getAttribute("title"));return n?"!["+r+"]("+n+(a?' "'+a+'"':"")+")":""}},d.prototype={add:function(e,t){this.array.unshift(t)},keep:function(e){this._keep.unshift({filter:e,replacement:this.keepReplacement})},remove:function(e){this._remove.unshift({filter:e,replacement:function(){return""}})},forNode:function(e){var t;return e.isBlank?this.blankRule:(t=m(this.array,e,this.options))||(t=m(this._keep,e,this.options))||(t=m(this._remove,e,this.options))?t:this.defaultRule},forEach:function(e){for(var t=0;t<this.array.length;t++)e(this.array[t],t)}};var v="undefined"!=typeof window?window:{},E=!function(){var e=v.DOMParser,t=!1;try{new e().parseFromString("","text/html")&&(t=!0)}catch(e){}return t}()?function(){var e=function(){},t=r(31427);return e.prototype.parseFromString=function(e){return t.createDocument(e)},e}():v.DOMParser;function y(e,t){var r;return!function(e){var t=e.element,r=e.isBlock,n=e.isVoid,a=e.isPre||function(e){return"PRE"===e.nodeName};if(!(!t.firstChild||a(t))){for(var i=null,s=!1,o=null,c=b(null,t,a);c!==t;){if(3===c.nodeType||4===c.nodeType){var l=c.data.replace(/[ \r\n\t]+/g," ");if((!i||/ $/.test(i.data))&&!s&&" "===l[0]&&(l=l.substr(1)),!l){c=g(c);continue}c.data=l,i=c}else if(1===c.nodeType)r(c)||"BR"===c.nodeName?(i&&(i.data=i.data.replace(/ $/,"")),i=null,s=!1):n(c)||a(c)?(i=null,s=!0):i&&(s=!1);else{c=g(c);continue}var u=b(o,c,a);o=c,c=u}i&&(i.data=i.data.replace(/ $/,""),i.data||g(i))}}({element:r="string"==typeof e?(a=a||new E).parseFromString('<x-turndown id="turndown-root">'+e+"</x-turndown>","text/html").getElementById("turndown-root"):e.cloneNode(!0),isBlock:s,isVoid:c,isPre:t.preformattedCode?T:null}),r}function T(e){return"PRE"===e.nodeName||"CODE"===e.nodeName}function _(e,t){var r;return e.isBlock=s(e),e.isCode="CODE"===e.nodeName||e.parentNode.isCode,e.isBlank=!c(r=e)&&!u(r,l)&&/^\s*$/i.test(r.textContent)&&!h(r,o)&&!h(r,l),e.flankingWhitespace=function(e,t){if(e.isBlock||t.preformattedCode&&e.isCode)return{leading:"",trailing:""};var r,n={leading:(r=e.textContent.match(/^(([ \t\r\n]*)(\s*))(?:(?=\S)[\s\S]*\S)?((\s*?)([ \t\r\n]*))$/))[1],leadingAscii:r[2],leadingNonAscii:r[3],trailing:r[4],trailingNonAscii:r[5],trailingAscii:r[6]};return n.leadingAscii&&w("left",e,t)&&(n.leading=n.leadingNonAscii),n.trailingAscii&&w("right",e,t)&&(n.trailing=n.trailingNonAscii),{leading:n.leading,trailing:n.trailing}}(e,t),e}function w(e,t,r){var n,a,i;return"left"===e?(n=t.previousSibling,a=/ $/):(n=t.nextSibling,a=/^ /),n&&(3===n.nodeType?i=a.test(n.nodeValue):r.preformattedCode&&"CODE"===n.nodeName?i=!1:1!==n.nodeType||s(n)||(i=a.test(n.textContent))),i}var S=Array.prototype.reduce,N=[[/\\/g,"\\\\"],[/\*/g,"\\*"],[/^-/g,"\\-"],[/^\+ /g,"\\+ "],[/^(=+)/g,"\\$1"],[/^(#{1,6}) /g,"\\$1 "],[/`/g,"\\`"],[/^~~~/g,"\\~~~"],[/\[/g,"\\["],[/\]/g,"\\]"],[/^>/g,"\\>"],[/_/g,"\\_"],[/^(\d+)\. /g,"$1\\. "]];function k(e){if(!(this instanceof k))return new k(e);this.options=function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)r.hasOwnProperty(n)&&(e[n]=r[n])}return e}({},{rules:p,headingStyle:"setext",hr:"* * *",bulletListMarker:"*",codeBlockStyle:"indented",fence:"```",emDelimiter:"_",strongDelimiter:"**",linkStyle:"inlined",linkReferenceStyle:"full",br:"  ",preformattedCode:!1,blankReplacement:function(e,t){return t.isBlock?"\n\n":""},keepReplacement:function(e,t){return t.isBlock?"\n\n"+t.outerHTML+"\n\n":t.outerHTML},defaultReplacement:function(e,t){return t.isBlock?"\n\n"+e+"\n\n":e}},e),this.rules=new d(this.options)}function C(e){var t=this;return S.call(e.childNodes,function(e,r){r=new _(r,t.options);var n="";return 3===r.nodeType?n=r.isCode?r.nodeValue:t.escape(r.nodeValue):1===r.nodeType&&(n=D.call(t,r)),L(e,n)},"")}function A(e){var t=this;return this.rules.forEach(function(r){"function"==typeof r.append&&(e=L(e,r.append(t.options)))}),e.replace(/^[\t\r\n]+/,"").replace(/[\t\r\n\s]+$/,"")}function D(e){var t=this.rules.forNode(e),r=C.call(this,e),n=e.flankingWhitespace;return(n.leading||n.trailing)&&(r=r.trim()),n.leading+t.replacement(r,e,this.options)+n.trailing}function L(e,t){var r=function(e){for(var t=e.length;t>0&&"\n"===e[t-1];)t--;return e.substring(0,t)}(e),n=t.replace(/^\n*/,""),a=Math.max(e.length-r.length,t.length-n.length);return r+"\n\n".substring(0,a)+n}k.prototype={turndown:function(e){var t;if(!(null!=(t=e)&&("string"==typeof t||t.nodeType&&(1===t.nodeType||9===t.nodeType||11===t.nodeType))))throw TypeError(e+" is not a string, or an element/document/fragment node.");return""===e?"":A.call(this,C.call(this,new y(e,this.options)))},use:function(e){if(Array.isArray(e))for(var t=0;t<e.length;t++)this.use(e[t]);else if("function"==typeof e)e(this);else throw TypeError("plugin must be a Function or an Array of Functions");return this},addRule:function(e,t){return this.rules.add(e,t),this},keep:function(e){return this.rules.keep(e),this},remove:function(e){return this.rules.remove(e),this},escape:function(e){return N.reduce(function(e,t){return e.replace(t[0],t[1])},e)}};let x=k},37070:(e,t,r)=>{"use strict";var n=r(81070),a=e.exports={valid:function(e){return n.assert(e,"list falsy"),n.assert(e._previousSibling,"previous falsy"),n.assert(e._nextSibling,"next falsy"),!0},insertBefore:function(e,t){n.assert(a.valid(e)&&a.valid(t));var r=e._previousSibling,i=t._previousSibling;e._previousSibling=i,r._nextSibling=t,i._nextSibling=e,t._previousSibling=r,n.assert(a.valid(e)&&a.valid(t))},replace:function(e,t){n.assert(a.valid(e)&&(null===t||a.valid(t))),null!==t&&a.insertBefore(t,e),a.remove(e),n.assert(a.valid(e)&&(null===t||a.valid(t)))},remove:function(e){n.assert(a.valid(e));var t=e._previousSibling;if(t!==e){var r=e._nextSibling;t._nextSibling=r,r._previousSibling=t,e._previousSibling=e._nextSibling=e,n.assert(a.valid(e))}}}},37882:(e,t,r)=>{"use strict";e.exports={serializeOne:function(e,t){var r,h,d="";switch(e.nodeType){case 1:var m=e.namespaceURI,g=m===a.HTML,b=g||m===a.SVG||m===a.MATHML?e.localName:e.tagName;d+="<"+b;for(var v=0,E=e._numattrs;v<E;v++){var y,T=e._attr(v);d+=" "+function(e){var t=e.namespaceURI;if(!t)return e.localName;if(t===a.XML)return"xml:"+e.localName;if(t===a.XLINK)return"xlink:"+e.localName;if(t===a.XMLNS)if("xmlns"===e.localName)return"xmlns";else return"xmlns:"+e.localName;return e.name}(T),void 0!==T.value&&(d+='="'+(y=T.value,l.test(y)?y.replace(l,e=>{switch(e){case"<":return"&lt;";case">":return"&gt;";case"&":return"&amp;";case'"':return"&quot;";case"\xa0":return"&nbsp;"}}):y)+'"')}if(d+=">",!(g&&s[b])){var _=e.serialize();i[b.toUpperCase()]&&(_=u(_,b)),g&&o[b]&&"\n"===_.charAt(0)&&(d+="\n"),d+=_,d+="</"+b+">"}break;case 3:case 4:i[h=1===t.nodeType&&t.namespaceURI===a.HTML?t.tagName:""]||"NOSCRIPT"===h&&t.ownerDocument._scripting_enabled?d+=e.data:d+=(r=e.data,c.test(r)?r.replace(c,e=>{switch(e){case"&":return"&amp;";case"<":return"&lt;";case">":return"&gt;";case"\xa0":return"&nbsp;"}}):r);break;case 8:d+="\x3c!--"+p(e.data)+"--\x3e";break;case 7:let w=f(e.data);d+="<?"+e.target+" "+w+"?>";break;case 10:d+="<!DOCTYPE "+e.name,d+=">";break;default:n.InvalidStateError()}return d},ɵescapeMatchingClosingTag:u,ɵescapeClosingCommentTag:p,ɵescapeProcessingInstructionContent:f};var n=r(81070),a=n.NAMESPACE,i={STYLE:!0,SCRIPT:!0,XMP:!0,IFRAME:!0,NOEMBED:!0,NOFRAMES:!0,PLAINTEXT:!0},s={area:!0,base:!0,basefont:!0,bgsound:!0,br:!0,col:!0,embed:!0,frame:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0},o={};let c=/[&<>\u00A0]/g,l=/[&"<>\u00A0]/g;function u(e,t){let r="</"+t;if(!e.toLowerCase().includes(r))return e;let n=[...e];for(let t of e.matchAll(RegExp(r,"ig")))n[t.index]="&lt;";return n.join("")}let h=/--!?>/;function p(e){return h.test(e)?e.replace(/(--\!?)>/g,"$1&gt;"):e}function f(e){return e.includes(">")?e.replaceAll(">","&gt;"):e}},39170:(e,t,r)=>{"use strict";e.exports=a;var n=r(21939);function a(e,t){this.root=e,this.filter=t,this.lastModTime=e.lastModTime,this.done=!1,this.cache=[],this.traverse()}a.prototype=Object.create(Object.prototype,{length:{get:function(){return this.checkcache(),this.done||this.traverse(),this.cache.length}},item:{value:function(e){return this.checkcache(),!this.done&&e>=this.cache.length&&this.traverse(),this.cache[e]}},checkcache:{value:function(){if(this.lastModTime!==this.root.lastModTime){for(var e=this.cache.length-1;e>=0;e--)this[e]=void 0;this.cache.length=0,this.done=!1,this.lastModTime=this.root.lastModTime}}},traverse:{value:function(e){var t;for(void 0!==e&&e++;null!==(t=this.next());)if(this[this.cache.length]=t,this.cache.push(t),e&&this.cache.length===e)return;this.done=!0}},next:{value:function(){var e,t=0===this.cache.length?this.root:this.cache[this.cache.length-1];for(e=t.nodeType===n.DOCUMENT_NODE?t.documentElement:t.nextElement(this.root);e;){if(this.filter(e))return e;e=e.nextElement(this.root)}return null}}})},40247:(e,t,r)=>{"use strict";var n=r(21939);e.exports={nextElementSibling:{get:function(){if(this.parentNode){for(var e=this.nextSibling;null!==e;e=e.nextSibling)if(e.nodeType===n.ELEMENT_NODE)return e}return null}},previousElementSibling:{get:function(){if(this.parentNode){for(var e=this.previousSibling;null!==e;e=e.previousSibling)if(e.nodeType===n.ELEMENT_NODE)return e}return null}}}},43562:(e,t,r)=>{"use strict";e.exports=i;var n=r(21939),a=r(87856);function i(e,t,r){a.call(this),this.nodeType=n.PROCESSING_INSTRUCTION_NODE,this.ownerDocument=e,this.target=t,this._data=r}var s={get:function(){return this._data},set:function(e){e=null==e?"":String(e),this._data=e,this.rooted&&this.ownerDocument.mutateValue(this)}};i.prototype=Object.create(a.prototype,{nodeName:{get:function(){return this.target}},nodeValue:s,textContent:s,innerText:s,data:{get:s.get,set:function(e){s.set.call(this,null===e?"":String(e))}},clone:{value:function(){return new i(this.ownerDocument,this.target,this._data)}},isEqual:{value:function(e){return this.target===e.target&&this._data===e._data}}})},44954:(e,t)=>{"use strict";function r(e){return e.replace(/[a-z][A-Z]/g,e=>e.charAt(0)+"-"+e.charAt(1)).toLowerCase()}Object.defineProperty(t,"__esModule",{value:!0}),t.hyphenate=t.parse=void 0,t.parse=function(e){let t=[],n=0,a=0,i=0,s=0,o=0,c=null;for(;n<e.length;)switch(e.charCodeAt(n++)){case 40:a++;break;case 41:a--;break;case 39:0===i?i=39:39===i&&92!==e.charCodeAt(n-1)&&(i=0);break;case 34:0===i?i=34:34===i&&92!==e.charCodeAt(n-1)&&(i=0);break;case 58:c||0!==a||0!==i||(c=r(e.substring(o,n-1).trim()),s=n);break;case 59:if(c&&s>0&&0===a&&0===i){let r=e.substring(s,n-1).trim();t.push(c,r),o=n,s=0,c=null}}if(c&&s){let r=e.slice(s).trim();t.push(c,r)}return t},t.hyphenate=r},46309:(e,t)=>{t.h=!globalThis.__domino_frozen__},48871:e=>{"use strict";e.exports={VALUE:1,ATTR:2,REMOVE_ATTR:3,REMOVE:4,MOVE:5,INSERT:6}},49205:(e,t,r)=>{"use strict";var n=r(81070);t=e.exports={CSSStyleDeclaration:r(75251),CharacterData:r(87856),Comment:r(51782),DOMException:r(82718),DOMImplementation:r(67237),DOMTokenList:r(31878),Document:r(52970),DocumentFragment:r(7650),DocumentType:r(93458),Element:r(61487),HTMLParser:r(75105),NamedNodeMap:r(24122),Node:r(21939),NodeList:r(26987),NodeFilter:r(34405),ProcessingInstruction:r(43562),Text:r(6560),Window:r(63427)},n.merge(t,r(69586)),n.merge(t,r(64538).elements),n.merge(t,r(52117).elements)},49514:(e,t,r)=>{"use strict";var n=r(19429);function a(){n.call(this),this.screenX=this.screenY=this.clientX=this.clientY=0,this.ctrlKey=this.altKey=this.shiftKey=this.metaKey=!1,this.button=0,this.buttons=1,this.relatedTarget=null}e.exports=a,a.prototype=Object.create(n.prototype,{constructor:{value:a},initMouseEvent:{value:function(e,t,r,n,a,i,s,o,c,l,u,h,p,f,d){switch(this.initEvent(e,t,r,n,a),this.screenX=i,this.screenY=s,this.clientX=o,this.clientY=c,this.ctrlKey=l,this.altKey=u,this.shiftKey=h,this.metaKey=p,this.button=f,f){case 0:this.buttons=1;break;case 1:this.buttons=4;break;case 2:this.buttons=2;break;default:this.buttons=0}this.relatedTarget=d}},getModifierState:{value:function(e){switch(e){case"Alt":return this.altKey;case"Control":return this.ctrlKey;case"Shift":return this.shiftKey;case"Meta":return this.metaKey;default:return!1}}}})},50353:e=>{"use strict";e.exports={setTimeout:setTimeout,clearTimeout:clearTimeout,setInterval:setInterval,clearInterval:clearInterval}},51782:(e,t,r)=>{"use strict";e.exports=i;var n=r(21939),a=r(87856);function i(e,t){a.call(this),this.nodeType=n.COMMENT_NODE,this.ownerDocument=e,this._data=t}var s={get:function(){return this._data},set:function(e){e=null==e?"":String(e),this._data=e,this.rooted&&this.ownerDocument.mutateValue(this)}};i.prototype=Object.create(a.prototype,{nodeName:{value:"#comment"},nodeValue:s,textContent:s,innerText:s,data:{get:s.get,set:function(e){s.set.call(this,null===e?"":String(e))}},clone:{value:function(){return new i(this.ownerDocument,this._data)}}})},52117:(e,t,r)=>{"use strict";var n=r(61487),a=r(56894),i=r(81070),s=r(75251),o=t.elements={},c=Object.create(null);function l(e){return a(e,u,o,c)}t.createElement=function(e,t,r){return new(c[t]||u)(e,t,r)};var u=l({superclass:n,name:"SVGElement",ctor:function(e,t,r){n.call(this,e,t,i.NAMESPACE.SVG,r)},props:{style:{get:function(){return this._style||(this._style=new s(this)),this._style}}}});l({name:"SVGSVGElement",ctor:function(e,t,r){u.call(this,e,t,r)},tag:"svg",props:{createSVGRect:{value:function(){return t.createElement(this.ownerDocument,"rect",null)}}}}),l({tags:["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignObject","g","glyph","glyphRef","hkern","image","line","linearGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"]})},52970:(e,t,r)=>{"use strict";e.exports=k;var n=r(21939),a=r(26987),i=r(67472),s=r(61487),o=r(6560),c=r(51782),l=r(28636),u=r(7650),h=r(43562),p=r(67237),f=r(77177),d=r(97901),m=r(34405),g=r(62114),b=r(29519),v=r(69586),E=r(11228),y=r(64538),T=r(52117),_=r(81070),w=r(48871),S=_.NAMESPACE,N=r(46309).h;function k(e,t){i.call(this),this.nodeType=n.DOCUMENT_NODE,this.isHTML=e,this._address=t||"about:blank",this.readyState="loading",this.implementation=new p(this),this.ownerDocument=null,this._contentType=e?"text/html":"application/xml",this.doctype=null,this.documentElement=null,this._templateDocCache=null,this._nodeIterators=null,this._nid=1,this._nextnid=2,this._nodes=[null,this],this.byId=Object.create(null),this.modclock=0}var C={event:"Event",customevent:"CustomEvent",uievent:"UIEvent",mouseevent:"MouseEvent"},A={events:"event",htmlevents:"event",mouseevents:"mouseevent",mutationevents:"mutationevent",uievents:"uievent"},D=function(e,t,r){return{get:function(){var n=e.call(this);return n?n[t]:r},set:function(r){var n=e.call(this);n&&(n[t]=r)}}};function L(e,t){var r,n,a;return""===e&&(e=null),E.isValidQName(t)||_.InvalidCharacterError(),r=null,n=t,(a=t.indexOf(":"))>=0&&(r=t.substring(0,a),n=t.substring(a+1)),null!==r&&null===e&&_.NamespaceError(),"xml"===r&&e!==S.XML&&_.NamespaceError(),("xmlns"===r||"xmlns"===t)&&e!==S.XMLNS&&_.NamespaceError(),e===S.XMLNS&&"xmlns"!==r&&"xmlns"!==t&&_.NamespaceError(),{namespace:e,prefix:r,localName:n}}function x(e,t){if(e&&e.isHTML){for(var r=e.firstChild;null!==r;r=r.nextSibling)if(r.nodeType===n.ELEMENT_NODE&&r.localName===t&&r.namespaceURI===S.HTML)return r}return null}function R(e){this.nodes=Object.create(null),this.nodes[e._nid]=e,this.length=1,this.firstNode=void 0}k.prototype=Object.create(i.prototype,{_setMutationHandler:{value:function(e){this.mutationHandler=e}},_dispatchRendererEvent:{value:function(e,t,r){var n=this._nodes[e];n&&n._dispatchEvent(new l(t,r),!0)}},nodeName:{value:"#document"},nodeValue:{get:function(){return null},set:function(){}},documentURI:{get:function(){return this._address},set:_.nyi},compatMode:{get:function(){return this._quirks?"BackCompat":"CSS1Compat"}},createTextNode:{value:function(e){return new o(this,String(e))}},createComment:{value:function(e){return new c(this,e)}},createDocumentFragment:{value:function(){return new u(this)}},createProcessingInstruction:{value:function(e,t){return E.isValidName(e)&&-1===t.indexOf("?>")||_.InvalidCharacterError(),new h(this,e,t)}},createAttribute:{value:function(e){return e=String(e),E.isValidName(e)||_.InvalidCharacterError(),this.isHTML&&(e=_.toASCIILowerCase(e)),new s._Attr(null,e,null,null,"")}},createAttributeNS:{value:function(e,t){var r=L(e=null==e||""===e?null:String(e),t=String(t));return new s._Attr(null,r.localName,r.prefix,r.namespace,"")}},createElement:{value:function(e){return(e=String(e),E.isValidName(e)||_.InvalidCharacterError(),this.isHTML)?(/[A-Z]/.test(e)&&(e=_.toASCIILowerCase(e)),y.createElement(this,e,null)):"application/xhtml+xml"===this.contentType?y.createElement(this,e,null):new s(this,e,null,null)},writable:N},createElementNS:{value:function(e,t){var r=L(e=null==e||""===e?null:String(e),t=String(t));return this._createElementNS(r.localName,r.namespace,r.prefix)},writable:N},_createElementNS:{value:function(e,t,r){return t===S.HTML?y.createElement(this,e,r):t===S.SVG?T.createElement(this,e,r):new s(this,e,t,r)}},createEvent:{value:function(e){var t=v[C[A[e=e.toLowerCase()]||e]];if(t){var r=new t;return r._initialized=!1,r}_.NotSupportedError()}},createTreeWalker:{value:function(e,t,r){if(!e)throw TypeError("root argument is required");if(!(e instanceof n))throw TypeError("root not a node");return new f(e,t=void 0===t?m.SHOW_ALL:+t,r=void 0===r?null:r)}},createNodeIterator:{value:function(e,t,r){if(!e)throw TypeError("root argument is required");if(!(e instanceof n))throw TypeError("root not a node");return new d(e,t=void 0===t?m.SHOW_ALL:+t,r=void 0===r?null:r)}},_attachNodeIterator:{value:function(e){this._nodeIterators||(this._nodeIterators=[]),this._nodeIterators.push(e)}},_detachNodeIterator:{value:function(e){var t=this._nodeIterators.indexOf(e);this._nodeIterators.splice(t,1)}},_preremoveNodeIterators:{value:function(e){this._nodeIterators&&this._nodeIterators.forEach(function(t){t._preremove(e)})}},_updateDocTypeElement:{value:function(){this.doctype=this.documentElement=null;for(var e=this.firstChild;null!==e;e=e.nextSibling)e.nodeType===n.DOCUMENT_TYPE_NODE?this.doctype=e:e.nodeType===n.ELEMENT_NODE&&(this.documentElement=e)}},insertBefore:{value:function(e,t){return n.prototype.insertBefore.call(this,e,t),this._updateDocTypeElement(),e}},replaceChild:{value:function(e,t){return n.prototype.replaceChild.call(this,e,t),this._updateDocTypeElement(),t}},removeChild:{value:function(e){return n.prototype.removeChild.call(this,e),this._updateDocTypeElement(),e}},getElementById:{value:function(e){var t=this.byId[e];return t?t instanceof R?t.getFirst():t:null}},_hasMultipleElementsWithId:{value:function(e){return this.byId[e]instanceof R}},getElementsByName:{value:s.prototype.getElementsByName},getElementsByTagName:{value:s.prototype.getElementsByTagName},getElementsByTagNameNS:{value:s.prototype.getElementsByTagNameNS},getElementsByClassName:{value:s.prototype.getElementsByClassName},adoptNode:{value:function(e){return e.nodeType===n.DOCUMENT_NODE&&_.NotSupportedError(),e.nodeType===n.ATTRIBUTE_NODE||(e.parentNode&&e.parentNode.removeChild(e),e.ownerDocument!==this&&function e(t,r){t.ownerDocument=r,t._lastModTime=void 0,Object.prototype.hasOwnProperty.call(t,"_tagName")&&(t._tagName=void 0);for(var n=t.firstChild;null!==n;n=n.nextSibling)e(n,r)}(e,this)),e}},importNode:{value:function(e,t){return this.adoptNode(e.cloneNode(t))},writable:N},origin:{get:function(){return null}},characterSet:{get:function(){return"UTF-8"}},contentType:{get:function(){return this._contentType}},URL:{get:function(){return this._address}},domain:{get:_.nyi,set:_.nyi},referrer:{get:_.nyi},cookie:{get:_.nyi,set:_.nyi},lastModified:{get:_.nyi},location:{get:function(){return this.defaultView?this.defaultView.location:null},set:_.nyi},_titleElement:{get:function(){return this.getElementsByTagName("title").item(0)||null}},title:{get:function(){var e=this._titleElement;return(e?e.textContent:"").replace(/[ \t\n\r\f]+/g," ").replace(/(^ )|( $)/g,"")},set:function(e){var t=this._titleElement,r=this.head;(t||r)&&(t||(t=this.createElement("title"),r.appendChild(t)),t.textContent=e)}},dir:D(function(){var e=this.documentElement;if(e&&"HTML"===e.tagName)return e},"dir",""),fgColor:D(function(){return this.body},"text",""),linkColor:D(function(){return this.body},"link",""),vlinkColor:D(function(){return this.body},"vLink",""),alinkColor:D(function(){return this.body},"aLink",""),bgColor:D(function(){return this.body},"bgColor",""),charset:{get:function(){return this.characterSet}},inputEncoding:{get:function(){return this.characterSet}},scrollingElement:{get:function(){return this._quirks?this.body:this.documentElement}},body:{get:function(){return x(this.documentElement,"body")},set:_.nyi},head:{get:function(){return x(this.documentElement,"head")}},images:{get:_.nyi},embeds:{get:_.nyi},plugins:{get:_.nyi},links:{get:_.nyi},forms:{get:_.nyi},scripts:{get:_.nyi},applets:{get:function(){return[]}},activeElement:{get:function(){return null}},innerHTML:{get:function(){return this.serialize()},set:_.nyi},outerHTML:{get:function(){return this.serialize()},set:_.nyi},write:{value:function(e){if(this.isHTML||_.InvalidStateError(),this._parser){this._parser;var t=arguments.join("");this._parser.parse(t)}}},writeln:{value:function(e){this.write(Array.prototype.join.call(arguments,"")+"\n")}},open:{value:function(){this.documentElement=null}},close:{value:function(){this.readyState="interactive",this._dispatchEvent(new l("readystatechange"),!0),this._dispatchEvent(new l("DOMContentLoaded"),!0),this.readyState="complete",this._dispatchEvent(new l("readystatechange"),!0),this.defaultView&&this.defaultView._dispatchEvent(new l("load"),!0)}},clone:{value:function(){var e=new k(this.isHTML,this._address);return e._quirks=this._quirks,e._contentType=this._contentType,e}},cloneNode:{value:function(e){var t=n.prototype.cloneNode.call(this,!1);if(e)for(var r=this.firstChild;null!==r;r=r.nextSibling)t._appendChild(t.importNode(r,!0));return t._updateDocTypeElement(),t}},isEqual:{value:function(e){return!0}},mutateValue:{value:function(e){this.mutationHandler&&this.mutationHandler({type:w.VALUE,target:e,data:e.data})}},mutateAttr:{value:function(e,t){this.mutationHandler&&this.mutationHandler({type:w.ATTR,target:e.ownerElement,attr:e})}},mutateRemoveAttr:{value:function(e){this.mutationHandler&&this.mutationHandler({type:w.REMOVE_ATTR,target:e.ownerElement,attr:e})}},mutateRemove:{value:function(e){this.mutationHandler&&this.mutationHandler({type:w.REMOVE,target:e.parentNode,node:e}),function e(t){if(t.nodeType===n.ELEMENT_NODE){var r=t.getAttribute("id");r&&t.ownerDocument.delId(r,t)}t.ownerDocument._nodes[t._nid]=void 0,t._nid=void 0;for(var a=t.firstChild;null!==a;a=a.nextSibling)e(a)}(e)}},mutateInsert:{value:function(e){(function e(t){if(t._nid=t.ownerDocument._nextnid++,t.ownerDocument._nodes[t._nid]=t,t.nodeType===n.ELEMENT_NODE){var r=t.getAttribute("id");r&&t.ownerDocument.addId(r,t),t._roothook&&t._roothook()}if(t.nodeType===n.ELEMENT_NODE)for(var a=t.firstChild;null!==a;a=a.nextSibling)e(a)})(e),this.mutationHandler&&this.mutationHandler({type:w.INSERT,target:e.parentNode,node:e})}},mutateMove:{value:function(e){this.mutationHandler&&this.mutationHandler({type:w.MOVE,target:e})}},addId:{value:function(e,t){var r=this.byId[e];r?(r instanceof R||(r=new R(r),this.byId[e]=r),r.add(t)):this.byId[e]=t}},delId:{value:function(e,t){var r=this.byId[e];_.assert(r),r instanceof R?(r.del(t),1===r.length&&(this.byId[e]=r.downgrade())):this.byId[e]=void 0}},_resolve:{value:function(e){return new g(this._documentBaseURL).resolve(e)}},_documentBaseURL:{get:function(){var e=this._address;"about:blank"===e&&(e="/");var t=this.querySelector("base[href]");return t?new g(e).resolve(t.getAttribute("href")):e}},_templateDoc:{get:function(){if(!this._templateDocCache){var e=new k(this.isHTML,this._address);this._templateDocCache=e._templateDocCache=e}return this._templateDocCache}},querySelector:{value:function(e){return b(e,this)[0]}},querySelectorAll:{value:function(e){var t=b(e,this);return t.item?t:new a(t)}}}),["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"].forEach(function(e){Object.defineProperty(k.prototype,"on"+e,{get:function(){return this._getEventHandler(e)},set:function(t){this._setEventHandler(e,t)}})}),R.prototype.add=function(e){this.nodes[e._nid]||(this.nodes[e._nid]=e,this.length++,this.firstNode=void 0)},R.prototype.del=function(e){this.nodes[e._nid]&&(delete this.nodes[e._nid],this.length--,this.firstNode=void 0)},R.prototype.getFirst=function(){if(!this.firstNode){var e;for(e in this.nodes)(void 0===this.firstNode||this.firstNode.compareDocumentPosition(this.nodes[e])&n.DOCUMENT_POSITION_PRECEDING)&&(this.firstNode=this.nodes[e])}return this.firstNode},R.prototype.downgrade=function(){if(1===this.length){var e;for(e in this.nodes)return this.nodes[e]}return this}},56894:(e,t,r)=>{"use strict";var n=r(69418),a=r(46309).h;function i(e,t,r,n){this.body=e,this.document=t,this.form=r,this.element=n}function s(e,t,r,n){var a=e.ownerDocument||Object.create(null),s=e.form||Object.create(null);e[t]=new i(n,a,s,e).build()}e.exports=function(e,t,r,i){var o=e.ctor;if(o){var c,l,u,h=e.props||{};if(e.attributes)for(var p in e.attributes){var f=e.attributes[p];("object"!=typeof f||Array.isArray(f))&&(f={type:f}),f.name||(f.name=p.toLowerCase()),h[p]=n.property(f)}h.constructor={value:o,writable:a},o.prototype=Object.create((e.superclass||t).prototype,h),e.events&&(c=o,l=e.events,u=c.prototype,l.forEach(function(e){Object.defineProperty(u,"on"+e,{get:function(){return this._getEventHandler(e)},set:function(t){this._setEventHandler(e,t)}}),n.registerChangeHandler(c,"on"+e,s)})),r[e.name]=o}else o=t;return(e.tags||e.tag&&[e.tag]||[]).forEach(function(e){i[e]=o}),o},i.prototype.build=function(){return()=>{}}},58349:e=>{"use strict";function t(e,t){for(e=e.parentNode;null!==e&&e!==t;e=e.parentNode)if(null!==e.nextSibling)return e.nextSibling;return null}function r(e){for(;e.lastChild;)e=e.lastChild;return e}e.exports={nextSkippingChildren:function(e,r){return e===r?null:null!==e.nextSibling?e.nextSibling:t(e,r)},nextAncestorSibling:t,next:function(e,r){var n;return null!==(n=e.firstChild)?n:e===r?null:null!==(n=e.nextSibling)?n:t(e,r)},previous:function(e,t){var n;return null!==(n=e.previousSibling)?r(n):(n=e.parentNode)===t?null:n},deepLastChild:r}},61487:(e,t,r)=>{"use strict";e.exports=E;var n=r(11228),a=r(81070),i=a.NAMESPACE,s=r(69418),o=r(21939),c=r(26987),l=r(37882),u=r(39170),h=r(82718),p=r(31878),f=r(29519),d=r(67472),m=r(76729),g=r(40247),b=r(24122),v=Object.create(null);function E(e,t,r,n){d.call(this),this.nodeType=o.ELEMENT_NODE,this.ownerDocument=e,this.localName=t,this.namespaceURI=r,this.prefix=n,this._tagName=void 0,this._attrsByQName=Object.create(null),this._attrsByLName=Object.create(null),this._attrKeys=[]}function y(e,t){if(e.nodeType===o.TEXT_NODE)t.push(e._data);else for(var r=0,n=e.childNodes.length;r<n;r++)y(e.childNodes[r],t)}function T(e,t,r,n,a){this.localName=t,this.prefix=null===r||""===r?null:""+r,this.namespaceURI=null===n||""===n?null:""+n,this.data=a,this._setOwnerElement(e)}function _(e){for(var t in b.call(this,e),e._attrsByQName)this[t]=e._attrsByQName[t];for(var r=0;r<e._attrKeys.length;r++)this[r]=e._attrsByLName[e._attrKeys[r]]}function w(e){this.element=e,this.updateCache()}function S(e){return function(t){return t.localName===e}}E.prototype=Object.create(d.prototype,{isHTML:{get:function(){return this.namespaceURI===i.HTML&&this.ownerDocument.isHTML}},tagName:{get:function(){if(void 0===this._tagName){var e;if(e=null===this.prefix?this.localName:this.prefix+":"+this.localName,this.isHTML){var t=v[e];t||(v[e]=t=a.toASCIIUpperCase(e)),e=t}this._tagName=e}return this._tagName}},nodeName:{get:function(){return this.tagName}},nodeValue:{get:function(){return null},set:function(){}},textContent:{get:function(){var e=[];return y(this,e),e.join("")},set:function(e){this.removeChildren(),null!=e&&""!==e&&this._appendChild(this.ownerDocument.createTextNode(e))}},innerText:{get:function(){var e=[];return y(this,e),e.join("").replace(/[ \t\n\f\r]+/g," ").trim()},set:function(e){this.removeChildren(),null!=e&&""!==e&&this._appendChild(this.ownerDocument.createTextNode(e))}},innerHTML:{get:function(){return this.serialize()},set:a.nyi},outerHTML:{get:function(){return l.serializeOne(this,{nodeType:0})},set:function(e){var t=this.ownerDocument,r=this.parentNode;if(null!==r){r.nodeType===o.DOCUMENT_NODE&&a.NoModificationAllowedError(),r.nodeType===o.DOCUMENT_FRAGMENT_NODE&&(r=r.ownerDocument.createElement("body"));var n=t.implementation.mozHTMLParser(t._address,r);n.parse(null===e?"":String(e),!0),this.replaceWith(n._asDocumentFragment())}}},_insertAdjacent:{value:function(e,t){var r=!1;switch(e){case"beforebegin":r=!0;case"afterend":var n=this.parentNode;if(null===n)return null;return n.insertBefore(t,r?this:this.nextSibling);case"afterbegin":r=!0;case"beforeend":return this.insertBefore(t,r?this.firstChild:null);default:return a.SyntaxError()}}},insertAdjacentElement:{value:function(e,t){if(t.nodeType!==o.ELEMENT_NODE)throw TypeError("not an element");return e=a.toASCIILowerCase(String(e)),this._insertAdjacent(e,t)}},insertAdjacentText:{value:function(e,t){var r=this.ownerDocument.createTextNode(t);e=a.toASCIILowerCase(String(e)),this._insertAdjacent(e,r)}},insertAdjacentHTML:{value:function(e,t){switch(e=a.toASCIILowerCase(String(e)),t=String(t),e){case"beforebegin":case"afterend":(null===(r=this.parentNode)||r.nodeType===o.DOCUMENT_NODE)&&a.NoModificationAllowedError();break;case"afterbegin":case"beforeend":r=this;break;default:a.SyntaxError()}r instanceof E&&(!r.ownerDocument.isHTML||"html"!==r.localName||r.namespaceURI!==i.HTML)||(r=r.ownerDocument.createElementNS(i.HTML,"body"));var r,n=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,r);n.parse(t,!0),this._insertAdjacent(e,n._asDocumentFragment())}},children:{get:function(){return this._children||(this._children=new w(this)),this._children}},attributes:{get:function(){return this._attributes||(this._attributes=new _(this)),this._attributes}},firstElementChild:{get:function(){for(var e=this.firstChild;null!==e;e=e.nextSibling)if(e.nodeType===o.ELEMENT_NODE)return e;return null}},lastElementChild:{get:function(){for(var e=this.lastChild;null!==e;e=e.previousSibling)if(e.nodeType===o.ELEMENT_NODE)return e;return null}},childElementCount:{get:function(){return this.children.length}},nextElement:{value:function(e){e||(e=this.ownerDocument.documentElement);var t=this.firstElementChild;if(!t){if(this===e)return null;t=this.nextElementSibling}if(t)return t;for(var r=this.parentElement;r&&r!==e;r=r.parentElement)if(t=r.nextElementSibling)return t;return null}},getElementsByTagName:{value:function(e){var t,r,n;return e?new u(this,"*"===e?function(){return!0}:this.isHTML?(r=e,(n=a.toASCIILowerCase(r))===r?S(r):function(e){return e.isHTML?e.localName===n:e.localName===r}):S(e)):new c}},getElementsByTagNameNS:{value:function(e,t){var r,n,a,i;return new u(this,"*"===e&&"*"===t?function(){return!0}:"*"===e?S(t):"*"===t?(n=e,function(e){return e.namespaceURI===n}):(a=e,i=t,function(e){return e.namespaceURI===a&&e.localName===i}))}},getElementsByClassName:{value:function(e){var t;return""===(e=String(e).trim())?new c:new u(this,(t=e=e.split(/[ \t\r\n\f]+/),function(e){return t.every(function(t){return e.classList.contains(t)})}))}},getElementsByName:{value:function(e){var t;return new u(this,(t=String(e),function(e){return e.namespaceURI===i.HTML&&e.getAttribute("name")===t}))}},clone:{value:function(){var e;e=this.namespaceURI!==i.HTML||this.prefix||!this.ownerDocument.isHTML?this.ownerDocument.createElementNS(this.namespaceURI,null!==this.prefix?this.prefix+":"+this.localName:this.localName):this.ownerDocument.createElement(this.localName);for(var t=0,r=this._attrKeys.length;t<r;t++){var n=this._attrKeys[t],a=this._attrsByLName[n].cloneNode();a._setOwnerElement(e),e._attrsByLName[n]=a,e._addQName(a)}return e._attrKeys=this._attrKeys.concat(),e}},isEqual:{value:function(e){if(this.localName!==e.localName||this.namespaceURI!==e.namespaceURI||this.prefix!==e.prefix||this._numattrs!==e._numattrs)return!1;for(var t=0,r=this._numattrs;t<r;t++){var n=this._attr(t);if(!e.hasAttributeNS(n.namespaceURI,n.localName)||e.getAttributeNS(n.namespaceURI,n.localName)!==n.value)return!1}return!0}},_lookupNamespacePrefix:{value:function(e,t){if(this.namespaceURI&&this.namespaceURI===e&&null!==this.prefix&&t.lookupNamespaceURI(this.prefix)===e)return this.prefix;for(var r=0,n=this._numattrs;r<n;r++){var a=this._attr(r);if("xmlns"===a.prefix&&a.value===e&&t.lookupNamespaceURI(a.localName)===e)return a.localName}var i=this.parentElement;return i?i._lookupNamespacePrefix(e,t):null}},lookupNamespaceURI:{value:function(e){if((""===e||void 0===e)&&(e=null),null!==this.namespaceURI&&this.prefix===e)return this.namespaceURI;for(var t=0,r=this._numattrs;t<r;t++){var n=this._attr(t);if(n.namespaceURI===i.XMLNS&&("xmlns"===n.prefix&&n.localName===e||null===e&&null===n.prefix&&"xmlns"===n.localName))return n.value||null}var a=this.parentElement;return a?a.lookupNamespaceURI(e):null}},getAttribute:{value:function(e){var t=this.getAttributeNode(e);return t?t.value:null}},getAttributeNS:{value:function(e,t){var r=this.getAttributeNodeNS(e,t);return r?r.value:null}},getAttributeNode:{value:function(e){e=String(e),/[A-Z]/.test(e)&&this.isHTML&&(e=a.toASCIILowerCase(e));var t=this._attrsByQName[e];return t?(Array.isArray(t)&&(t=t[0]),t):null}},getAttributeNodeNS:{value:function(e,t){return e=null==e?"":String(e),t=String(t),this._attrsByLName[e+"|"+t]||null}},hasAttribute:{value:function(e){return e=String(e),/[A-Z]/.test(e)&&this.isHTML&&(e=a.toASCIILowerCase(e)),void 0!==this._attrsByQName[e]}},hasAttributeNS:{value:function(e,t){var r=(e=null==e?"":String(e))+"|"+(t=String(t));return void 0!==this._attrsByLName[r]}},hasAttributes:{value:function(){return this._numattrs>0}},toggleAttribute:{value:function(e,t){return(e=String(e),n.isValidName(e)||a.InvalidCharacterError(),/[A-Z]/.test(e)&&this.isHTML&&(e=a.toASCIILowerCase(e)),void 0===this._attrsByQName[e])?(void 0===t||!0===t)&&(this._setAttribute(e,""),!0):void 0!==t&&!1!==t||(this.removeAttribute(e),!1)}},_setAttribute:{value:function(e,t){var r,n=this._attrsByQName[e];n?Array.isArray(n)&&(n=n[0]):(n=this._newattr(e),r=!0),n.value=t,this._attributes&&(this._attributes[e]=n),r&&this._newattrhook&&this._newattrhook(e,t)}},setAttribute:{value:function(e,t){e=String(e),n.isValidName(e)||a.InvalidCharacterError(),/[A-Z]/.test(e)&&this.isHTML&&(e=a.toASCIILowerCase(e)),this._setAttribute(e,String(t))}},_setAttributeNS:{value:function(e,t,r){var n,a,i,s=t.indexOf(":");s<0?(a=null,i=t):(a=t.substring(0,s),i=t.substring(s+1)),(""===e||void 0===e)&&(e=null);var o=(null===e?"":e)+"|"+i,c=this._attrsByLName[o];c||(c=new T(this,i,a,e),n=!0,this._attrsByLName[o]=c,this._attributes&&(this._attributes[this._attrKeys.length]=c),this._attrKeys.push(o),this._addQName(c)),c.value=r,n&&this._newattrhook&&this._newattrhook(t,r)}},setAttributeNS:{value:function(e,t,r){e=null==e||""===e?null:String(e),t=String(t),n.isValidQName(t)||a.InvalidCharacterError();var s=t.indexOf(":"),o=s<0?null:t.substring(0,s);(null!==o&&null===e||"xml"===o&&e!==i.XML||("xmlns"===t||"xmlns"===o)&&e!==i.XMLNS||e===i.XMLNS&&"xmlns"!==t&&"xmlns"!==o)&&a.NamespaceError(),this._setAttributeNS(e,t,String(r))}},setAttributeNode:{value:function(e){if(null!==e.ownerElement&&e.ownerElement!==this)throw new h(h.INUSE_ATTRIBUTE_ERR);var t=null,r=this._attrsByQName[e.name];if(r){if(Array.isArray(r)||(r=[r]),r.some(function(t){return t===e}))return e;if(null!==e.ownerElement)throw new h(h.INUSE_ATTRIBUTE_ERR);r.forEach(function(e){this.removeAttributeNode(e)},this),t=r[0]}return this.setAttributeNodeNS(e),t}},setAttributeNodeNS:{value:function(e){if(null!==e.ownerElement)throw new h(h.INUSE_ATTRIBUTE_ERR);var t=e.namespaceURI,r=(null===t?"":t)+"|"+e.localName,n=this._attrsByLName[r];return n&&this.removeAttributeNode(n),e._setOwnerElement(this),this._attrsByLName[r]=e,this._attributes&&(this._attributes[this._attrKeys.length]=e),this._attrKeys.push(r),this._addQName(e),this._newattrhook&&this._newattrhook(e.name,e.value),n||null}},removeAttribute:{value:function(e){e=String(e),/[A-Z]/.test(e)&&this.isHTML&&(e=a.toASCIILowerCase(e));var t=this._attrsByQName[e];if(t){Array.isArray(t)?t.length>2?t=t.shift():(this._attrsByQName[e]=t[1],t=t[0]):this._attrsByQName[e]=void 0;var r=t.namespaceURI,n=(null===r?"":r)+"|"+t.localName;this._attrsByLName[n]=void 0;var i=this._attrKeys.indexOf(n);this._attributes&&(Array.prototype.splice.call(this._attributes,i,1),this._attributes[e]=void 0),this._attrKeys.splice(i,1);var s=t.onchange;t._setOwnerElement(null),s&&s.call(t,this,t.localName,t.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(t)}}},removeAttributeNS:{value:function(e,t){var r=(e=null==e?"":String(e))+"|"+(t=String(t)),n=this._attrsByLName[r];if(n){this._attrsByLName[r]=void 0;var a=this._attrKeys.indexOf(r);this._attributes&&Array.prototype.splice.call(this._attributes,a,1),this._attrKeys.splice(a,1),this._removeQName(n);var i=n.onchange;n._setOwnerElement(null),i&&i.call(n,this,n.localName,n.value,null),this.rooted&&this.ownerDocument.mutateRemoveAttr(n)}}},removeAttributeNode:{value:function(e){var t=e.namespaceURI,r=(null===t?"":t)+"|"+e.localName;return this._attrsByLName[r]!==e&&a.NotFoundError(),this.removeAttributeNS(t,e.localName),e}},getAttributeNames:{value:function(){var e=this;return this._attrKeys.map(function(t){return e._attrsByLName[t].name})}},_getattr:{value:function(e){var t=this._attrsByQName[e];return t?t.value:null}},_setattr:{value:function(e,t){var r,n=this._attrsByQName[e];n||(n=this._newattr(e),r=!0),n.value=String(t),this._attributes&&(this._attributes[e]=n),r&&this._newattrhook&&this._newattrhook(e,t)}},_newattr:{value:function(e){var t=new T(this,e,null,null),r="|"+e;return this._attrsByQName[e]=t,this._attrsByLName[r]=t,this._attributes&&(this._attributes[this._attrKeys.length]=t),this._attrKeys.push(r),t}},_addQName:{value:function(e){var t=e.name,r=this._attrsByQName[t];r?Array.isArray(r)?r.push(e):this._attrsByQName[t]=[r,e]:this._attrsByQName[t]=e,this._attributes&&(this._attributes[t]=e)}},_removeQName:{value:function(e){var t=e.name,r=this._attrsByQName[t];if(Array.isArray(r)){var n=r.indexOf(e);a.assert(-1!==n),2===r.length?(this._attrsByQName[t]=r[1-n],this._attributes&&(this._attributes[t]=this._attrsByQName[t])):(r.splice(n,1),this._attributes&&this._attributes[t]===e&&(this._attributes[t]=r[0]))}else a.assert(r===e),this._attrsByQName[t]=void 0,this._attributes&&(this._attributes[t]=void 0)}},_numattrs:{get:function(){return this._attrKeys.length}},_attr:{value:function(e){return this._attrsByLName[this._attrKeys[e]]}},id:s.property({name:"id"}),className:s.property({name:"class"}),classList:{get:function(){var e=this;if(this._classList)return this._classList;var t=new p(function(){return e.className||""},function(t){e.className=t});return this._classList=t,t},set:function(e){this.className=e}},matches:{value:function(e){return f.matches(this,e)}},closest:{value:function(e){var t=this;do{if(t.matches&&t.matches(e))return t;t=t.parentElement||t.parentNode}while(null!==t&&t.nodeType===o.ELEMENT_NODE);return null}},querySelector:{value:function(e){return f(e,this)[0]}},querySelectorAll:{value:function(e){var t=f(e,this);return t.item?t:new c(t)}}}),Object.defineProperties(E.prototype,m),Object.defineProperties(E.prototype,g),s.registerChangeHandler(E,"id",function(e,t,r,n){e.rooted&&(r&&e.ownerDocument.delId(r,e),n&&e.ownerDocument.addId(n,e))}),s.registerChangeHandler(E,"class",function(e,t,r,n){e._classList&&e._classList._update()}),T.prototype=Object.create(Object.prototype,{ownerElement:{get:function(){return this._ownerElement}},_setOwnerElement:{value:function(e){this._ownerElement=e,null===this.prefix&&null===this.namespaceURI&&e?this.onchange=e._attributeChangeHandlers[this.localName]:this.onchange=null}},name:{get:function(){return this.prefix?this.prefix+":"+this.localName:this.localName}},specified:{get:function(){return!0}},value:{get:function(){return this.data},set:function(e){var t=this.data;(e=void 0===e?"":e+"")!==t&&(this.data=e,this.ownerElement&&(this.onchange&&this.onchange(this.ownerElement,this.localName,t,e),this.ownerElement.rooted&&this.ownerElement.ownerDocument.mutateAttr(this,t)))}},cloneNode:{value:function(e){return new T(null,this.localName,this.prefix,this.namespaceURI,this.data)}},nodeType:{get:function(){return o.ATTRIBUTE_NODE}},nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return this.value},set:function(e){this.value=e}},textContent:{get:function(){return this.value},set:function(e){null==e&&(e=""),this.value=e}},innerText:{get:function(){return this.value},set:function(e){null==e&&(e=""),this.value=e}}}),E._Attr=T,_.prototype=Object.create(b.prototype,{length:{get:function(){return this.element._attrKeys.length},set:function(){}},item:{value:function(e){return(e>>>=0)>=this.length?null:this.element._attrsByLName[this.element._attrKeys[e]]}}}),globalThis.Symbol?.iterator&&(_.prototype[globalThis.Symbol.iterator]=function(){var e=0,t=this.length,r=this;return{next:function(){return e<t?{value:r.item(e++)}:{done:!0}}}}),w.prototype=Object.create(Object.prototype,{length:{get:function(){return this.updateCache(),this.childrenByNumber.length}},item:{value:function(e){return this.updateCache(),this.childrenByNumber[e]||null}},namedItem:{value:function(e){return this.updateCache(),this.childrenByName[e]||null}},namedItems:{get:function(){return this.updateCache(),this.childrenByName}},updateCache:{value:function(){var e=/^(a|applet|area|embed|form|frame|frameset|iframe|img|object)$/;if(this.lastModTime!==this.element.lastModTime){this.lastModTime=this.element.lastModTime;for(var t=this.childrenByNumber&&this.childrenByNumber.length||0,r=0;r<t;r++)this[r]=void 0;this.childrenByNumber=[],this.childrenByName=Object.create(null);for(var n=this.element.firstChild;null!==n;n=n.nextSibling)if(n.nodeType===o.ELEMENT_NODE){this[this.childrenByNumber.length]=n,this.childrenByNumber.push(n);var a=n.getAttribute("id");a&&!this.childrenByName[a]&&(this.childrenByName[a]=n);var s=n.getAttribute("name");s&&this.element.namespaceURI===i.HTML&&e.test(this.element.localName)&&!this.childrenByName[s]&&(this.childrenByName[a]=n)}}}}})},62114:e=>{"use strict";function t(e){if(!e)return Object.create(t.prototype);this.url=e.replace(/^[ \t\n\r\f]+|[ \t\n\r\f]+$/g,"");var r=t.pattern.exec(this.url);if(r){if(r[2]&&(this.scheme=r[2]),r[4]){var n=r[4].match(t.userinfoPattern);if(n&&(this.username=n[1],this.password=n[3],r[4]=r[4].substring(n[0].length)),r[4].match(t.portPattern)){var a=r[4].lastIndexOf(":");this.host=r[4].substring(0,a),this.port=r[4].substring(a+1)}else this.host=r[4]}r[5]&&(this.path=r[5]),r[6]&&(this.query=r[7]),r[8]&&(this.fragment=r[9])}}e.exports=t,t.pattern=/^(([^:\/?#]+):)?(\/\/([^\/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/,t.userinfoPattern=/^([^@:]*)(:([^@]*))?@/,t.portPattern=/:\d+$/,t.authorityPattern=/^[^:\/?#]+:\/\//,t.hierarchyPattern=/^[^:\/?#]+:\//,t.percentEncode=function(e){var t=e.charCodeAt(0);if(t<256)return"%"+t.toString(16);throw Error("can't percent-encode codepoints > 255 yet")},t.prototype={constructor:t,isAbsolute:function(){return!!this.scheme},isAuthorityBased:function(){return t.authorityPattern.test(this.url)},isHierarchical:function(){return t.hierarchyPattern.test(this.url)},toString:function(){var e="";return void 0!==this.scheme&&(e+=this.scheme+":"),this.isAbsolute()&&(e+="//",(this.username||this.password)&&(e+=this.username||"",this.password&&(e+=":"+this.password),e+="@"),this.host&&(e+=this.host)),void 0!==this.port&&(e+=":"+this.port),void 0!==this.path&&(e+=this.path),void 0!==this.query&&(e+="?"+this.query),void 0!==this.fragment&&(e+="#"+this.fragment),e},resolve:function(e){var r=this,n=new t(e),a=new t;return void 0!==n.scheme?(a.scheme=n.scheme,a.username=n.username,a.password=n.password,a.host=n.host,a.port=n.port,a.path=i(n.path),a.query=n.query):(a.scheme=r.scheme,void 0!==n.host?(a.username=n.username,a.password=n.password,a.host=n.host,a.port=n.port,a.path=i(n.path),a.query=n.query):(a.username=r.username,a.password=r.password,a.host=r.host,a.port=r.port,n.path?("/"===n.path.charAt(0)?a.path=i(n.path):(a.path=function(e,t){if(void 0!==r.host&&!r.path)return"/"+t;var n=e.lastIndexOf("/");return -1===n?t:e.substring(0,n+1)+t}(r.path,n.path),a.path=i(a.path)),a.query=n.query):(a.path=r.path,void 0!==n.query?a.query=n.query:a.query=r.query))),a.fragment=n.fragment,a.toString();function i(e){if(!e)return e;for(var t="";e.length>0;){if("."===e||".."===e){e="";break}var r=e.substring(0,2),n=e.substring(0,3),a=e.substring(0,4);if("../"===n)e=e.substring(3);else if("./"===r)e=e.substring(2);else if("/./"===n)e="/"+e.substring(3);else if("/."===r&&2===e.length)e="/";else if("/../"===a||"/.."===n&&3===e.length)e="/"+e.substring(4),t=t.replace(/\/?[^\/]*$/,"");else{var i=e.match(/(\/?([^\/]*))/)[0];t+=i,e=e.substring(i.length)}}return t}}}},63427:(e,t,r)=>{"use strict";var n=r(67237),a=r(20674),i=r(2672),s=r(81070);function o(e){this.document=e||new n(null).createHTMLDocument(""),this.document._scripting_enabled=!0,this.document.defaultView=this,this.location=new i(this,this.document._address||"about:blank")}e.exports=o,o.prototype=Object.create(a.prototype,{console:{value:console},history:{value:{back:s.nyi,forward:s.nyi,go:s.nyi}},navigator:{value:r(8595)},window:{get:function(){return this}},self:{get:function(){return this}},frames:{get:function(){return this}},parent:{get:function(){return this}},top:{get:function(){return this}},length:{value:0},frameElement:{value:null},opener:{value:null},onload:{get:function(){return this._getEventHandler("load")},set:function(e){this._setEventHandler("load",e)}},getComputedStyle:{value:function(e){return e.style}}}),s.expose(r(50353),o),s.expose(r(49205),o)},64538:(e,t,r)=>{"use strict";var n=r(21939),a=r(61487),i=r(75251),s=r(81070),o=r(98113),c=r(56894),l=t.elements={},u=Object.create(null);function h(e){return c(e,b,l,u)}function p(e){return{get:function(){var t=this._getattr(e);if(null===t)return"";var r=this.doc._resolve(t);return null===r?t:r},set:function(t){this._setattr(e,t)}}}function f(e){return{get:function(){var t=this._getattr(e);return null===t?null:"use-credentials"===t.toLowerCase()?"use-credentials":"anonymous"},set:function(t){null==t?this.removeAttribute(e):this._setattr(e,t)}}}t.createElement=function(e,t,r){return new(u[t]||v)(e,t,r)};let d={type:["","no-referrer","no-referrer-when-downgrade","same-origin","origin","strict-origin","origin-when-cross-origin","strict-origin-when-cross-origin","unsafe-url"],missing:""};var m={A:!0,LINK:!0,BUTTON:!0,INPUT:!0,SELECT:!0,TEXTAREA:!0,COMMAND:!0},g=function(e,t,r){b.call(this,e,t,r),this._form=null},b=t.HTMLElement=h({superclass:a,name:"HTMLElement",ctor:function(e,t,r){a.call(this,e,t,s.NAMESPACE.HTML,r)},props:{dangerouslySetInnerHTML:{set:function(e){this._innerHTML=e}},innerHTML:{get:function(){return this.serialize()},set:function(e){var t=this.ownerDocument.implementation.mozHTMLParser(this.ownerDocument._address,this);t.parse(null===e?"":String(e),!0);for(var r=this instanceof u.template?this.content:this;r.hasChildNodes();)r.removeChild(r.firstChild);r.appendChild(t._asDocumentFragment())}},style:{get:function(){return this._style||(this._style=new i(this)),this._style},set:function(e){null==e&&(e=""),this._setattr("style",String(e))}},blur:{value:function(){}},focus:{value:function(){}},forceSpellCheck:{value:function(){}},click:{value:function(){if(!this._click_in_progress){this._click_in_progress=!0;try{this._pre_click_activation_steps&&this._pre_click_activation_steps();var e=this.ownerDocument.createEvent("MouseEvent");e.initMouseEvent("click",!0,!0,this.ownerDocument.defaultView,1,0,0,0,0,!1,!1,!1,!1,0,null),this.dispatchEvent(e)?this._post_click_activation_steps&&this._post_click_activation_steps(e):this._cancelled_activation_steps&&this._cancelled_activation_steps()}finally{this._click_in_progress=!1}}}},submit:{value:s.nyi}},attributes:{title:String,lang:String,dir:{type:["ltr","rtl","auto"],missing:""},draggable:{type:["true","false"],treatNullAsEmptyString:!0},spellcheck:{type:["true","false"],missing:""},enterKeyHint:{type:["enter","done","go","next","previous","search","send"],missing:""},autoCapitalize:{type:["off","on","none","sentences","words","characters"],missing:""},autoFocus:Boolean,accessKey:String,nonce:String,hidden:Boolean,translate:{type:["no","yes"],missing:""},tabIndex:{type:"long",default:function(){return this.tagName in m||this.contentEditable?0:-1}}},events:["abort","canplay","canplaythrough","change","click","contextmenu","cuechange","dblclick","drag","dragend","dragenter","dragleave","dragover","dragstart","drop","durationchange","emptied","ended","input","invalid","keydown","keypress","keyup","loadeddata","loadedmetadata","loadstart","mousedown","mousemove","mouseout","mouseover","mouseup","mousewheel","pause","play","playing","progress","ratechange","readystatechange","reset","seeked","seeking","select","show","stalled","submit","suspend","timeupdate","volumechange","waiting","blur","error","focus","load","scroll"]}),v=h({name:"HTMLUnknownElement",ctor:function(e,t,r){b.call(this,e,t,r)}}),E={form:{get:function(){return this._form}}};h({tag:"a",name:"HTMLAnchorElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{_post_click_activation_steps:{value:function(e){this.href&&(this.ownerDocument.defaultView.location=this.href)}}},attributes:{href:p,ping:String,download:String,target:String,rel:String,media:String,hreflang:String,type:String,referrerPolicy:d,coords:String,charset:String,name:String,rev:String,shape:String}}),o._inherit(u.a.prototype),h({tag:"area",name:"HTMLAreaElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{alt:String,target:String,download:String,rel:String,media:String,href:p,hreflang:String,type:String,shape:String,coords:String,ping:String,referrerPolicy:d,noHref:Boolean}}),o._inherit(u.area.prototype),h({tag:"br",name:"HTMLBRElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{clear:String}}),h({tag:"base",name:"HTMLBaseElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{target:String}}),h({tag:"body",name:"HTMLBodyElement",ctor:function(e,t,r){b.call(this,e,t,r)},events:["afterprint","beforeprint","beforeunload","blur","error","focus","hashchange","load","message","offline","online","pagehide","pageshow","popstate","resize","scroll","storage","unload"],attributes:{text:{type:String,treatNullAsEmptyString:!0},link:{type:String,treatNullAsEmptyString:!0},vLink:{type:String,treatNullAsEmptyString:!0},aLink:{type:String,treatNullAsEmptyString:!0},bgColor:{type:String,treatNullAsEmptyString:!0},background:String}}),h({tag:"button",name:"HTMLButtonElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:E,attributes:{name:String,value:String,disabled:Boolean,autofocus:Boolean,type:{type:["submit","reset","button","menu"],missing:"submit"},formTarget:String,formAction:p,formNoValidate:Boolean,formMethod:{type:["get","post","dialog"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""}}}),h({tag:"dl",name:"HTMLDListElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{compact:Boolean}}),h({tag:"data",name:"HTMLDataElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{value:String}}),h({tag:"datalist",name:"HTMLDataListElement",ctor:function(e,t,r){b.call(this,e,t,r)}}),h({tag:"details",name:"HTMLDetailsElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{open:Boolean}}),h({tag:"div",name:"HTMLDivElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String}}),h({tag:"embed",name:"HTMLEmbedElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{src:p,type:String,width:String,height:String,align:String,name:String}}),h({tag:"fieldset",name:"HTMLFieldSetElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:E,attributes:{disabled:Boolean,name:String}}),h({tag:"form",name:"HTMLFormElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{action:String,autocomplete:{type:["on","off"],missing:"on"},name:String,acceptCharset:{name:"accept-charset"},target:String,noValidate:Boolean,method:{type:["get","post","dialog"],invalid:"get",missing:"get"},enctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"},encoding:{name:"enctype",type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:"application/x-www-form-urlencoded"}}}),h({tag:"hr",name:"HTMLHRElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String,color:String,noShade:Boolean,size:String,width:String}}),h({tag:"head",name:"HTMLHeadElement",ctor:function(e,t,r){b.call(this,e,t,r)}}),h({tags:["h1","h2","h3","h4","h5","h6"],name:"HTMLHeadingElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String}}),h({tag:"html",name:"HTMLHtmlElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{xmlns:p,version:String}}),h({tag:"iframe",name:"HTMLIFrameElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{src:p,srcdoc:String,name:String,width:String,height:String,seamless:Boolean,allow:Boolean,allowFullscreen:Boolean,allowUserMedia:Boolean,allowPaymentRequest:Boolean,referrerPolicy:d,loading:{type:["eager","lazy"],treatNullAsEmptyString:!0},align:String,scrolling:String,frameBorder:String,longDesc:p,marginHeight:{type:String,treatNullAsEmptyString:!0},marginWidth:{type:String,treatNullAsEmptyString:!0}}}),h({tag:"img",name:"HTMLImageElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{alt:String,src:p,srcset:String,crossOrigin:f,useMap:String,isMap:Boolean,sizes:String,height:{type:"unsigned long",default:0},width:{type:"unsigned long",default:0},referrerPolicy:d,loading:{type:["eager","lazy"],missing:""},name:String,lowsrc:p,align:String,hspace:{type:"unsigned long",default:0},vspace:{type:"unsigned long",default:0},longDesc:p,border:{type:String,treatNullAsEmptyString:!0}}}),h({tag:"input",name:"HTMLInputElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:{form:E.form,_post_click_activation_steps:{value:function(e){if("checkbox"===this.type)this.checked=!this.checked;else if("radio"===this.type)for(var t=this.form.getElementsByName(this.name),r=t.length-1;r>=0;r--){var n=t[r];n.checked=n===this}}}},attributes:{name:String,disabled:Boolean,autofocus:Boolean,accept:String,alt:String,max:String,min:String,pattern:String,placeholder:String,step:String,dirName:String,defaultValue:{name:"value"},multiple:Boolean,required:Boolean,readOnly:Boolean,checked:Boolean,value:String,src:p,defaultChecked:{name:"checked",type:Boolean},size:{type:"unsigned long",default:20,min:1,setmin:1},width:{type:"unsigned long",min:0,setmin:0,default:0},height:{type:"unsigned long",min:0,setmin:0,default:0},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},autocomplete:String,type:{type:["text","hidden","search","tel","url","email","password","datetime","date","month","week","time","datetime-local","number","range","color","checkbox","radio","file","submit","image","reset","button"],missing:"text"},formTarget:String,formNoValidate:Boolean,formMethod:{type:["get","post"],invalid:"get",missing:""},formEnctype:{type:["application/x-www-form-urlencoded","multipart/form-data","text/plain"],invalid:"application/x-www-form-urlencoded",missing:""},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""},align:String,useMap:String}}),h({tag:"keygen",name:"HTMLKeygenElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:E,attributes:{name:String,disabled:Boolean,autofocus:Boolean,challenge:String,keytype:{type:["rsa"],missing:""}}}),h({tag:"li",name:"HTMLLIElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{value:{type:"long",default:0},type:String}}),h({tag:"label",name:"HTMLLabelElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:E,attributes:{htmlFor:{name:"for",type:String}}}),h({tag:"legend",name:"HTMLLegendElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String}}),h({tag:"link",name:"HTMLLinkElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{href:p,rel:String,media:String,hreflang:String,type:String,crossOrigin:f,nonce:String,integrity:String,referrerPolicy:d,imageSizes:String,imageSrcset:String,charset:String,rev:String,target:String}}),h({tag:"map",name:"HTMLMapElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{name:String}}),h({tag:"menu",name:"HTMLMenuElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{type:{type:["context","popup","toolbar"],missing:"toolbar"},label:String,compact:Boolean}}),h({tag:"meta",name:"HTMLMetaElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{name:String,content:String,httpEquiv:{name:"http-equiv",type:String},scheme:String}}),h({tag:"meter",name:"HTMLMeterElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:E}),h({tags:["ins","del"],name:"HTMLModElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{cite:p,dateTime:String}}),h({tag:"ol",name:"HTMLOListElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{_numitems:{get:function(){var e=0;return this.childNodes.forEach(function(t){t.nodeType===n.ELEMENT_NODE&&"LI"===t.tagName&&e++}),e}}},attributes:{type:String,reversed:Boolean,start:{type:"long",default:function(){return this.reversed?this._numitems:1}},compact:Boolean}}),h({tag:"object",name:"HTMLObjectElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:E,attributes:{data:p,type:String,name:String,useMap:String,typeMustMatch:Boolean,width:String,height:String,align:String,archive:String,code:String,declare:Boolean,hspace:{type:"unsigned long",default:0},standby:String,vspace:{type:"unsigned long",default:0},codeBase:p,codeType:String,border:{type:String,treatNullAsEmptyString:!0}}}),h({tag:"optgroup",name:"HTMLOptGroupElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{disabled:Boolean,label:String}}),h({tag:"option",name:"HTMLOptionElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{form:{get:function(){for(var e=this.parentNode;e&&e.nodeType===n.ELEMENT_NODE;){if("select"===e.localName)return e.form;e=e.parentNode}}},value:{get:function(){return this._getattr("value")||this.text},set:function(e){this._setattr("value",e)}},text:{get:function(){return this.textContent.replace(/[ \t\n\f\r]+/g," ").trim()},set:function(e){this.textContent=e}}},attributes:{disabled:Boolean,defaultSelected:{name:"selected",type:Boolean},label:String}}),h({tag:"output",name:"HTMLOutputElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:E,attributes:{name:String}}),h({tag:"p",name:"HTMLParagraphElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String}}),h({tag:"param",name:"HTMLParamElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{name:String,value:String,type:String,valueType:String}}),h({tags:["pre","listing","xmp"],name:"HTMLPreElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{width:{type:"long",default:0}}}),h({tag:"progress",name:"HTMLProgressElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:E,attributes:{max:{type:Number,float:!0,default:1,min:0}}}),h({tags:["q","blockquote"],name:"HTMLQuoteElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{cite:p}}),h({tag:"script",name:"HTMLScriptElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{text:{get:function(){for(var e="",t=0,r=this.childNodes.length;t<r;t++){var a=this.childNodes[t];a.nodeType===n.TEXT_NODE&&(e+=a._data)}return e},set:function(e){this.removeChildren(),null!==e&&""!==e&&this.appendChild(this.ownerDocument.createTextNode(e))}}},attributes:{src:p,type:String,charset:String,referrerPolicy:d,defer:Boolean,async:Boolean,nomodule:Boolean,crossOrigin:f,nonce:String,integrity:String}}),h({tag:"select",name:"HTMLSelectElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:{form:E.form,options:{get:function(){return this.getElementsByTagName("option")}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,multiple:Boolean,required:Boolean,size:{type:"unsigned long",default:0}}}),h({tag:"span",name:"HTMLSpanElement",ctor:function(e,t,r){b.call(this,e,t,r)}}),h({tag:"style",name:"HTMLStyleElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{media:String,type:String,scoped:Boolean}}),h({tag:"caption",name:"HTMLTableCaptionElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{align:String}}),h({name:"HTMLTableCellElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{colSpan:{type:"unsigned long",default:1},rowSpan:{type:"unsigned long",default:1},scope:{type:["row","col","rowgroup","colgroup"],missing:""},abbr:String,align:String,axis:String,height:String,width:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},noWrap:Boolean,vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),h({tags:["col","colgroup"],name:"HTMLTableColElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{span:{type:"limited unsigned long with fallback",default:1,min:1},align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,width:String}}),h({tag:"table",name:"HTMLTableElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,border:String,frame:String,rules:String,summary:String,width:String,bgColor:{type:String,treatNullAsEmptyString:!0},cellPadding:{type:String,treatNullAsEmptyString:!0},cellSpacing:{type:String,treatNullAsEmptyString:!0}}}),h({tag:"template",name:"HTMLTemplateElement",ctor:function(e,t,r){b.call(this,e,t,r),this._contentFragment=e._templateDoc.createDocumentFragment()},props:{content:{get:function(){return this._contentFragment}},serialize:{value:function(){return this.content.serialize()}}}}),h({tag:"tr",name:"HTMLTableRowElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{cells:{get:function(){return this.querySelectorAll("td,th")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String,bgColor:{type:String,treatNullAsEmptyString:!0}}}),h({tags:["thead","tfoot","tbody"],name:"HTMLTableSectionElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{rows:{get:function(){return this.getElementsByTagName("tr")}}},attributes:{align:String,ch:{name:"char",type:String},chOff:{name:"charoff",type:String},vAlign:String}}),h({tag:"textarea",name:"HTMLTextAreaElement",ctor:function(e,t,r){g.call(this,e,t,r)},props:{form:E.form,type:{get:function(){return"textarea"}},defaultValue:{get:function(){return this.textContent},set:function(e){this.textContent=e}},value:{get:function(){return this.defaultValue},set:function(e){this.defaultValue=e}},textLength:{get:function(){return this.value.length}}},attributes:{autocomplete:String,name:String,disabled:Boolean,autofocus:Boolean,placeholder:String,wrap:String,dirName:String,required:Boolean,readOnly:Boolean,rows:{type:"limited unsigned long with fallback",default:2},cols:{type:"limited unsigned long with fallback",default:20},maxLength:{type:"unsigned long",min:0,setmin:0,default:-1},minLength:{type:"unsigned long",min:0,setmin:0,default:-1},inputMode:{type:["verbatim","latin","latin-name","latin-prose","full-width-latin","kana","kana-name","katakana","numeric","tel","email","url"],missing:""}}}),h({tag:"time",name:"HTMLTimeElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{dateTime:String,pubDate:Boolean}}),h({tag:"title",name:"HTMLTitleElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{text:{get:function(){return this.textContent}}}}),h({tag:"ul",name:"HTMLUListElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{type:String,compact:Boolean}}),h({name:"HTMLMediaElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{src:p,crossOrigin:f,preload:{type:["metadata","none","auto",{value:"",alias:"auto"}],missing:"auto"},loop:Boolean,autoplay:Boolean,mediaGroup:String,controls:Boolean,defaultMuted:{name:"muted",type:Boolean}}}),h({name:"HTMLAudioElement",tag:"audio",superclass:l.HTMLMediaElement,ctor:function(e,t,r){l.HTMLMediaElement.call(this,e,t,r)}}),h({name:"HTMLVideoElement",tag:"video",superclass:l.HTMLMediaElement,ctor:function(e,t,r){l.HTMLMediaElement.call(this,e,t,r)},attributes:{poster:p,width:{type:"unsigned long",min:0,default:0},height:{type:"unsigned long",min:0,default:0}}}),h({tag:"td",name:"HTMLTableDataCellElement",superclass:l.HTMLTableCellElement,ctor:function(e,t,r){l.HTMLTableCellElement.call(this,e,t,r)}}),h({tag:"th",name:"HTMLTableHeaderCellElement",superclass:l.HTMLTableCellElement,ctor:function(e,t,r){l.HTMLTableCellElement.call(this,e,t,r)}}),h({tag:"frameset",name:"HTMLFrameSetElement",ctor:function(e,t,r){b.call(this,e,t,r)}}),h({tag:"frame",name:"HTMLFrameElement",ctor:function(e,t,r){b.call(this,e,t,r)}}),h({tag:"canvas",name:"HTMLCanvasElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{getContext:{value:s.nyi},probablySupportsContext:{value:s.nyi},setContext:{value:s.nyi},transferControlToProxy:{value:s.nyi},toDataURL:{value:s.nyi},toBlob:{value:s.nyi}},attributes:{width:{type:"unsigned long",default:300},height:{type:"unsigned long",default:150}}}),h({tag:"dialog",name:"HTMLDialogElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{show:{value:s.nyi},showModal:{value:s.nyi},close:{value:s.nyi}},attributes:{open:Boolean,returnValue:String}}),h({tag:"menuitem",name:"HTMLMenuItemElement",ctor:function(e,t,r){b.call(this,e,t,r)},props:{_label:{get:function(){var e=this._getattr("label");return null!==e&&""!==e?e:(e=this.textContent).replace(/[ \t\n\f\r]+/g," ").trim()}},label:{get:function(){var e=this._getattr("label");return null!==e?e:this._label},set:function(e){this._setattr("label",e)}}},attributes:{type:{type:["command","checkbox","radio"],missing:"command"},icon:p,disabled:Boolean,checked:Boolean,radiogroup:String,default:Boolean}}),h({tag:"source",name:"HTMLSourceElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{srcset:String,sizes:String,media:String,src:p,type:String,width:String,height:String}}),h({tag:"track",name:"HTMLTrackElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{src:p,srclang:String,label:String,default:Boolean,kind:{type:["subtitles","captions","descriptions","chapters","metadata"],missing:"subtitles",invalid:"metadata"}},props:{NONE:{get:function(){return 0}},LOADING:{get:function(){return 1}},LOADED:{get:function(){return 2}},ERROR:{get:function(){return 3}},readyState:{get:s.nyi},track:{get:s.nyi}}}),h({tag:"font",name:"HTMLFontElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{color:{type:String,treatNullAsEmptyString:!0},face:{type:String},size:{type:String}}}),h({tag:"dir",name:"HTMLDirectoryElement",ctor:function(e,t,r){b.call(this,e,t,r)},attributes:{compact:Boolean}}),h({tags:["abbr","address","article","aside","b","bdi","bdo","cite","content","code","dd","dfn","dt","em","figcaption","figure","footer","header","hgroup","i","kbd","main","mark","nav","noscript","rb","rp","rt","rtc","ruby","s","samp","section","small","strong","sub","summary","sup","u","var","wbr","acronym","basefont","big","center","nobr","noembed","noframes","plaintext","strike","tt"]})},67237:(e,t,r)=>{"use strict";e.exports=c;var n=r(52970),a=r(93458),i=r(75105),s=r(81070),o=r(11228);function c(e){this.contextObject=e}var l={xml:{"":!0,"1.0":!0,"2.0":!0},core:{"":!0,"2.0":!0},html:{"":!0,"1.0":!0,"2.0":!0},xhtml:{"":!0,"1.0":!0,"2.0":!0}};c.prototype={hasFeature:function(e,t){var r=l[(e||"").toLowerCase()];return r&&r[t||""]||!1},createDocumentType:function(e,t,r){return o.isValidQName(e)||s.InvalidCharacterError(),new a(this.contextObject,e,t,r)},createDocument:function(e,t,r){var a,i=new n(!1,null);return a=t?i.createElementNS(e,t):null,r&&i.appendChild(r),a&&i.appendChild(a),e===s.NAMESPACE.HTML?i._contentType="application/xhtml+xml":e===s.NAMESPACE.SVG?i._contentType="image/svg+xml":i._contentType="application/xml",i},createHTMLDocument:function(e){var t=new n(!0,null);t.appendChild(new a(t,"html"));var r=t.createElement("html");t.appendChild(r);var i=t.createElement("head");if(r.appendChild(i),void 0!==e){var s=t.createElement("title");i.appendChild(s),s.appendChild(t.createTextNode(e))}return r.appendChild(t.createElement("body")),t.modclock=1,t},mozSetOutputMutationHandler:function(e,t){e.mutationHandler=t},mozGetInputMutationHandler:function(e){s.nyi()},mozHTMLParser:i}},67472:(e,t,r)=>{"use strict";e.exports=i;var n=r(21939),a=r(26987);function i(){n.call(this),this._firstChild=this._childNodes=null}i.prototype=Object.create(n.prototype,{hasChildNodes:{value:function(){return this._childNodes?this._childNodes.length>0:null!==this._firstChild}},childNodes:{get:function(){return this._ensureChildNodes(),this._childNodes}},firstChild:{get:function(){return this._childNodes?0===this._childNodes.length?null:this._childNodes[0]:this._firstChild}},lastChild:{get:function(){var e,t=this._childNodes;return t?0===t.length?null:t[t.length-1]:null===(e=this._firstChild)?null:e._previousSibling}},_ensureChildNodes:{value:function(){if(!this._childNodes){var e=this._firstChild,t=e,r=this._childNodes=new a;if(e)do r.push(t),t=t._nextSibling;while(t!==e);this._firstChild=null}}},removeChildren:{value:function(){for(var e,t=this.rooted?this.ownerDocument:null,r=this.firstChild;null!==r;)r=(e=r).nextSibling,t&&t.mutateRemove(e),e.parentNode=null;this._childNodes?this._childNodes.length=0:this._firstChild=null,this.modify()}}})},69418:(e,t,r)=>{"use strict";var n=r(81070);t.property=function(e){if(Array.isArray(e.type)){var t,r,a,i,s,o,c,l,u=Object.create(null);e.type.forEach(function(e){u[e.value||e]=e.alias||e});var h=e.missing;void 0===h&&(h=null);var p=e.invalid;return void 0===p&&(p=h),{get:function(){var t=this._getattr(e.name);return null===t?h:void 0!==(t=u[t.toLowerCase()])?t:null!==p?p:t},set:function(t){this._setattr(e.name,t)}}}if(e.type===Boolean)return{get:function(){return this.hasAttribute(e.name)},set:function(t){t?this._setattr(e.name,""):this.removeAttribute(e.name)}};if(e.type===Number||"long"===e.type||"unsigned long"===e.type||"limited unsigned long with fallback"===e.type){return r="function"==typeof(t=e).default?t.default:"number"==typeof t.default?function(){return t.default}:function(){n.assert(!1,typeof t.default)},a="unsigned long"===t.type,i="long"===t.type,s="limited unsigned long with fallback"===t.type,o=t.min,c=t.max,l=t.setmin,void 0===o&&(a&&(o=0),i&&(o=-0x80000000),s&&(o=1)),void 0===c&&(a||i||s)&&(c=0x7fffffff),{get:function(){var e=this._getattr(t.name),n=t.float?parseFloat(e):parseInt(e,10);if(null===e||!isFinite(n)||void 0!==o&&n<o||void 0!==c&&n>c)return r.call(this);if(a||i||s){if(!/^[ \t\n\f\r]*[-+]?[0-9]/.test(e))return r.call(this);n|=0}return n},set:function(e){t.float||(e=Math.floor(e)),void 0!==l&&e<l&&n.IndexSizeError(t.name+" set to "+e),a?e=e<0||e>0x7fffffff?r.call(this):0|e:s?e=e<1||e>0x7fffffff?r.call(this):0|e:i&&(e=e<-0x80000000||e>0x7fffffff?r.call(this):0|e),this._setattr(t.name,String(e))}}}if(!e.type||e.type===String)return{get:function(){return this._getattr(e.name)||""},set:function(t){e.treatNullAsEmptyString&&null===t&&(t=""),this._setattr(e.name,t)}};if("function"==typeof e.type)return e.type(e.name,e);throw Error("Invalid attribute definition")},t.registerChangeHandler=function(e,t,r){var n=e.prototype;Object.prototype.hasOwnProperty.call(n,"_attributeChangeHandlers")||(n._attributeChangeHandlers=Object.create(n._attributeChangeHandlers||null)),n._attributeChangeHandlers[t]=r}},69586:(e,t,r)=>{"use strict";e.exports={Event:r(28636),UIEvent:r(19429),MouseEvent:r(49514),CustomEvent:r(95402)}},75105:(e,t,r)=>{"use strict";e.exports=ei;var n=r(52970),a=r(93458),i=r(21939),s=r(81070).NAMESPACE,o=r(64538),c=o.elements,l=Function.prototype.apply.bind(Array.prototype.push),u=[],h=/^HTML$|^-\/\/W3O\/\/DTD W3 HTML Strict 3\.0\/\/EN\/\/$|^-\/W3C\/DTD HTML 4\.0 Transitional\/EN$|^\+\/\/Silmaril\/\/dtd html Pro v0r11 19970101\/\/|^-\/\/AdvaSoft Ltd\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/AS\/\/DTD HTML 3\.0 asWedit \+ extensions\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML 2\.0 Strict\/\/|^-\/\/IETF\/\/DTD HTML 2\.0\/\/|^-\/\/IETF\/\/DTD HTML 2\.1E\/\/|^-\/\/IETF\/\/DTD HTML 3\.0\/\/|^-\/\/IETF\/\/DTD HTML 3\.2 Final\/\/|^-\/\/IETF\/\/DTD HTML 3\.2\/\/|^-\/\/IETF\/\/DTD HTML 3\/\/|^-\/\/IETF\/\/DTD HTML Level 0\/\/|^-\/\/IETF\/\/DTD HTML Level 1\/\/|^-\/\/IETF\/\/DTD HTML Level 2\/\/|^-\/\/IETF\/\/DTD HTML Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 0\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 1\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 2\/\/|^-\/\/IETF\/\/DTD HTML Strict Level 3\/\/|^-\/\/IETF\/\/DTD HTML Strict\/\/|^-\/\/IETF\/\/DTD HTML\/\/|^-\/\/Metrius\/\/DTD Metrius Presentational\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 2\.0 Tables\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML Strict\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 HTML\/\/|^-\/\/Microsoft\/\/DTD Internet Explorer 3\.0 Tables\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD HTML\/\/|^-\/\/Netscape Comm\. Corp\.\/\/DTD Strict HTML\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML 2\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended 1\.0\/\/|^-\/\/O'Reilly and Associates\/\/DTD HTML Extended Relaxed 1\.0\/\/|^-\/\/SoftQuad Software\/\/DTD HoTMetaL PRO 6\.0::19990601::extensions to HTML 4\.0\/\/|^-\/\/SoftQuad\/\/DTD HoTMetaL PRO 4\.0::19971010::extensions to HTML 4\.0\/\/|^-\/\/Spyglass\/\/DTD HTML 2\.0 Extended\/\/|^-\/\/SQ\/\/DTD HTML 2\.0 HoTMetaL \+ extensions\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava HTML\/\/|^-\/\/Sun Microsystems Corp\.\/\/DTD HotJava Strict HTML\/\/|^-\/\/W3C\/\/DTD HTML 3 1995-03-24\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Draft\/\/|^-\/\/W3C\/\/DTD HTML 3\.2 Final\/\/|^-\/\/W3C\/\/DTD HTML 3\.2\/\/|^-\/\/W3C\/\/DTD HTML 3\.2S Draft\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.0 Transitional\/\/|^-\/\/W3C\/\/DTD HTML Experimental 19960712\/\/|^-\/\/W3C\/\/DTD HTML Experimental 970421\/\/|^-\/\/W3C\/\/DTD W3 HTML\/\/|^-\/\/W3O\/\/DTD W3 HTML 3\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML 2\.0\/\/|^-\/\/WebTechs\/\/DTD Mozilla HTML\/\//i,p=/^-\/\/W3C\/\/DTD HTML 4\.01 Frameset\/\/|^-\/\/W3C\/\/DTD HTML 4\.01 Transitional\/\//i,f=/^-\/\/W3C\/\/DTD XHTML 1\.0 Frameset\/\/|^-\/\/W3C\/\/DTD XHTML 1\.0 Transitional\/\//i,d=Object.create(null);d[s.HTML]={__proto__:null,address:!0,applet:!0,area:!0,article:!0,aside:!0,base:!0,basefont:!0,bgsound:!0,blockquote:!0,body:!0,br:!0,button:!0,caption:!0,center:!0,col:!0,colgroup:!0,dd:!0,details:!0,dir:!0,div:!0,dl:!0,dt:!0,embed:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,frame:!0,frameset:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,head:!0,header:!0,hgroup:!0,hr:!0,html:!0,iframe:!0,img:!0,input:!0,li:!0,link:!0,listing:!0,main:!0,marquee:!0,menu:!0,meta:!0,nav:!0,noembed:!0,noframes:!0,noscript:!0,object:!0,ol:!0,p:!0,param:!0,plaintext:!0,pre:!0,script:!0,section:!0,select:!0,source:!0,style:!0,summary:!0,table:!0,tbody:!0,td:!0,template:!0,textarea:!0,tfoot:!0,th:!0,thead:!0,title:!0,tr:!0,track:!0,ul:!0,wbr:!0,xmp:!0},d[s.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0},d[s.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0};var m=Object.create(null);m[s.HTML]={__proto__:null,address:!0,div:!0,p:!0};var g=Object.create(null);g[s.HTML]={__proto__:null,dd:!0,dt:!0};var b=Object.create(null);b[s.HTML]={__proto__:null,table:!0,thead:!0,tbody:!0,tfoot:!0,tr:!0};var v=Object.create(null);v[s.HTML]={__proto__:null,dd:!0,dt:!0,li:!0,menuitem:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0};var E=Object.create(null);E[s.HTML]={__proto__:null,caption:!0,colgroup:!0,dd:!0,dt:!0,li:!0,optgroup:!0,option:!0,p:!0,rb:!0,rp:!0,rt:!0,rtc:!0,tbody:!0,td:!0,tfoot:!0,th:!0,thead:!0,tr:!0};var y=Object.create(null);y[s.HTML]={__proto__:null,table:!0,template:!0,html:!0};var T=Object.create(null);T[s.HTML]={__proto__:null,tbody:!0,tfoot:!0,thead:!0,template:!0,html:!0};var _=Object.create(null);_[s.HTML]={__proto__:null,tr:!0,template:!0,html:!0};var w=Object.create(null);w[s.HTML]={__proto__:null,button:!0,fieldset:!0,input:!0,keygen:!0,object:!0,output:!0,select:!0,textarea:!0,img:!0};var S=Object.create(null);S[s.HTML]={__proto__:null,applet:!0,caption:!0,html:!0,table:!0,td:!0,th:!0,marquee:!0,object:!0,template:!0},S[s.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0,"annotation-xml":!0},S[s.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var N=Object.create(S);N[s.HTML]=Object.create(S[s.HTML]),N[s.HTML].ol=!0,N[s.HTML].ul=!0;var k=Object.create(S);k[s.HTML]=Object.create(S[s.HTML]),k[s.HTML].button=!0;var C=Object.create(null);C[s.HTML]={__proto__:null,html:!0,table:!0,template:!0},Object.create(null)[s.HTML]={__proto__:null,optgroup:!0,option:!0};var A=Object.create(null);A[s.MATHML]={__proto__:null,mi:!0,mo:!0,mn:!0,ms:!0,mtext:!0};var D=Object.create(null);D[s.SVG]={__proto__:null,foreignObject:!0,desc:!0,title:!0};var L={__proto__:null,"xlink:actuate":s.XLINK,"xlink:arcrole":s.XLINK,"xlink:href":s.XLINK,"xlink:role":s.XLINK,"xlink:show":s.XLINK,"xlink:title":s.XLINK,"xlink:type":s.XLINK,"xml:base":s.XML,"xml:lang":s.XML,"xml:space":s.XML,xmlns:s.XMLNS,"xmlns:xlink":s.XMLNS},x={__proto__:null,attributename:"attributeName",attributetype:"attributeType",basefrequency:"baseFrequency",baseprofile:"baseProfile",calcmode:"calcMode",clippathunits:"clipPathUnits",diffuseconstant:"diffuseConstant",edgemode:"edgeMode",filterunits:"filterUnits",glyphref:"glyphRef",gradienttransform:"gradientTransform",gradientunits:"gradientUnits",kernelmatrix:"kernelMatrix",kernelunitlength:"kernelUnitLength",keypoints:"keyPoints",keysplines:"keySplines",keytimes:"keyTimes",lengthadjust:"lengthAdjust",limitingconeangle:"limitingConeAngle",markerheight:"markerHeight",markerunits:"markerUnits",markerwidth:"markerWidth",maskcontentunits:"maskContentUnits",maskunits:"maskUnits",numoctaves:"numOctaves",pathlength:"pathLength",patterncontentunits:"patternContentUnits",patterntransform:"patternTransform",patternunits:"patternUnits",pointsatx:"pointsAtX",pointsaty:"pointsAtY",pointsatz:"pointsAtZ",preservealpha:"preserveAlpha",preserveaspectratio:"preserveAspectRatio",primitiveunits:"primitiveUnits",refx:"refX",refy:"refY",repeatcount:"repeatCount",repeatdur:"repeatDur",requiredextensions:"requiredExtensions",requiredfeatures:"requiredFeatures",specularconstant:"specularConstant",specularexponent:"specularExponent",spreadmethod:"spreadMethod",startoffset:"startOffset",stddeviation:"stdDeviation",stitchtiles:"stitchTiles",surfacescale:"surfaceScale",systemlanguage:"systemLanguage",tablevalues:"tableValues",targetx:"targetX",targety:"targetY",textlength:"textLength",viewbox:"viewBox",viewtarget:"viewTarget",xchannelselector:"xChannelSelector",ychannelselector:"yChannelSelector",zoomandpan:"zoomAndPan"},R={__proto__:null,altglyph:"altGlyph",altglyphdef:"altGlyphDef",altglyphitem:"altGlyphItem",animatecolor:"animateColor",animatemotion:"animateMotion",animatetransform:"animateTransform",clippath:"clipPath",feblend:"feBlend",fecolormatrix:"feColorMatrix",fecomponenttransfer:"feComponentTransfer",fecomposite:"feComposite",feconvolvematrix:"feConvolveMatrix",fediffuselighting:"feDiffuseLighting",fedisplacementmap:"feDisplacementMap",fedistantlight:"feDistantLight",feflood:"feFlood",fefunca:"feFuncA",fefuncb:"feFuncB",fefuncg:"feFuncG",fefuncr:"feFuncR",fegaussianblur:"feGaussianBlur",feimage:"feImage",femerge:"feMerge",femergenode:"feMergeNode",femorphology:"feMorphology",feoffset:"feOffset",fepointlight:"fePointLight",fespecularlighting:"feSpecularLighting",fespotlight:"feSpotLight",fetile:"feTile",feturbulence:"feTurbulence",foreignobject:"foreignObject",glyphref:"glyphRef",lineargradient:"linearGradient",radialgradient:"radialGradient",textpath:"textPath"},M={__proto__:null,0:65533,128:8364,130:8218,131:402,132:8222,133:8230,134:8224,135:8225,136:710,137:8240,138:352,139:8249,140:338,142:381,145:8216,146:8217,147:8220,148:8221,149:8226,150:8211,151:8212,152:732,153:8482,154:353,155:8250,156:339,158:382,159:376},I={__proto__:null,AElig:198,"AElig;":198,AMP:38,"AMP;":38,Aacute:193,"Aacute;":193,"Abreve;":258,Acirc:194,"Acirc;":194,"Acy;":1040,"Afr;":[55349,56580],Agrave:192,"Agrave;":192,"Alpha;":913,"Amacr;":256,"And;":10835,"Aogon;":260,"Aopf;":[55349,56632],"ApplyFunction;":8289,Aring:197,"Aring;":197,"Ascr;":[55349,56476],"Assign;":8788,Atilde:195,"Atilde;":195,Auml:196,"Auml;":196,"Backslash;":8726,"Barv;":10983,"Barwed;":8966,"Bcy;":1041,"Because;":8757,"Bernoullis;":8492,"Beta;":914,"Bfr;":[55349,56581],"Bopf;":[55349,56633],"Breve;":728,"Bscr;":8492,"Bumpeq;":8782,"CHcy;":1063,COPY:169,"COPY;":169,"Cacute;":262,"Cap;":8914,"CapitalDifferentialD;":8517,"Cayleys;":8493,"Ccaron;":268,Ccedil:199,"Ccedil;":199,"Ccirc;":264,"Cconint;":8752,"Cdot;":266,"Cedilla;":184,"CenterDot;":183,"Cfr;":8493,"Chi;":935,"CircleDot;":8857,"CircleMinus;":8854,"CirclePlus;":8853,"CircleTimes;":8855,"ClockwiseContourIntegral;":8754,"CloseCurlyDoubleQuote;":8221,"CloseCurlyQuote;":8217,"Colon;":8759,"Colone;":10868,"Congruent;":8801,"Conint;":8751,"ContourIntegral;":8750,"Copf;":8450,"Coproduct;":8720,"CounterClockwiseContourIntegral;":8755,"Cross;":10799,"Cscr;":[55349,56478],"Cup;":8915,"CupCap;":8781,"DD;":8517,"DDotrahd;":10513,"DJcy;":1026,"DScy;":1029,"DZcy;":1039,"Dagger;":8225,"Darr;":8609,"Dashv;":10980,"Dcaron;":270,"Dcy;":1044,"Del;":8711,"Delta;":916,"Dfr;":[55349,56583],"DiacriticalAcute;":180,"DiacriticalDot;":729,"DiacriticalDoubleAcute;":733,"DiacriticalGrave;":96,"DiacriticalTilde;":732,"Diamond;":8900,"DifferentialD;":8518,"Dopf;":[55349,56635],"Dot;":168,"DotDot;":8412,"DotEqual;":8784,"DoubleContourIntegral;":8751,"DoubleDot;":168,"DoubleDownArrow;":8659,"DoubleLeftArrow;":8656,"DoubleLeftRightArrow;":8660,"DoubleLeftTee;":10980,"DoubleLongLeftArrow;":10232,"DoubleLongLeftRightArrow;":10234,"DoubleLongRightArrow;":10233,"DoubleRightArrow;":8658,"DoubleRightTee;":8872,"DoubleUpArrow;":8657,"DoubleUpDownArrow;":8661,"DoubleVerticalBar;":8741,"DownArrow;":8595,"DownArrowBar;":10515,"DownArrowUpArrow;":8693,"DownBreve;":785,"DownLeftRightVector;":10576,"DownLeftTeeVector;":10590,"DownLeftVector;":8637,"DownLeftVectorBar;":10582,"DownRightTeeVector;":10591,"DownRightVector;":8641,"DownRightVectorBar;":10583,"DownTee;":8868,"DownTeeArrow;":8615,"Downarrow;":8659,"Dscr;":[55349,56479],"Dstrok;":272,"ENG;":330,ETH:208,"ETH;":208,Eacute:201,"Eacute;":201,"Ecaron;":282,Ecirc:202,"Ecirc;":202,"Ecy;":1069,"Edot;":278,"Efr;":[55349,56584],Egrave:200,"Egrave;":200,"Element;":8712,"Emacr;":274,"EmptySmallSquare;":9723,"EmptyVerySmallSquare;":9643,"Eogon;":280,"Eopf;":[55349,56636],"Epsilon;":917,"Equal;":10869,"EqualTilde;":8770,"Equilibrium;":8652,"Escr;":8496,"Esim;":10867,"Eta;":919,Euml:203,"Euml;":203,"Exists;":8707,"ExponentialE;":8519,"Fcy;":1060,"Ffr;":[55349,56585],"FilledSmallSquare;":9724,"FilledVerySmallSquare;":9642,"Fopf;":[55349,56637],"ForAll;":8704,"Fouriertrf;":8497,"Fscr;":8497,"GJcy;":1027,GT:62,"GT;":62,"Gamma;":915,"Gammad;":988,"Gbreve;":286,"Gcedil;":290,"Gcirc;":284,"Gcy;":1043,"Gdot;":288,"Gfr;":[55349,56586],"Gg;":8921,"Gopf;":[55349,56638],"GreaterEqual;":8805,"GreaterEqualLess;":8923,"GreaterFullEqual;":8807,"GreaterGreater;":10914,"GreaterLess;":8823,"GreaterSlantEqual;":10878,"GreaterTilde;":8819,"Gscr;":[55349,56482],"Gt;":8811,"HARDcy;":1066,"Hacek;":711,"Hat;":94,"Hcirc;":292,"Hfr;":8460,"HilbertSpace;":8459,"Hopf;":8461,"HorizontalLine;":9472,"Hscr;":8459,"Hstrok;":294,"HumpDownHump;":8782,"HumpEqual;":8783,"IEcy;":1045,"IJlig;":306,"IOcy;":1025,Iacute:205,"Iacute;":205,Icirc:206,"Icirc;":206,"Icy;":1048,"Idot;":304,"Ifr;":8465,Igrave:204,"Igrave;":204,"Im;":8465,"Imacr;":298,"ImaginaryI;":8520,"Implies;":8658,"Int;":8748,"Integral;":8747,"Intersection;":8898,"InvisibleComma;":8291,"InvisibleTimes;":8290,"Iogon;":302,"Iopf;":[55349,56640],"Iota;":921,"Iscr;":8464,"Itilde;":296,"Iukcy;":1030,Iuml:207,"Iuml;":207,"Jcirc;":308,"Jcy;":1049,"Jfr;":[55349,56589],"Jopf;":[55349,56641],"Jscr;":[55349,56485],"Jsercy;":1032,"Jukcy;":1028,"KHcy;":1061,"KJcy;":1036,"Kappa;":922,"Kcedil;":310,"Kcy;":1050,"Kfr;":[55349,56590],"Kopf;":[55349,56642],"Kscr;":[55349,56486],"LJcy;":1033,LT:60,"LT;":60,"Lacute;":313,"Lambda;":923,"Lang;":10218,"Laplacetrf;":8466,"Larr;":8606,"Lcaron;":317,"Lcedil;":315,"Lcy;":1051,"LeftAngleBracket;":10216,"LeftArrow;":8592,"LeftArrowBar;":8676,"LeftArrowRightArrow;":8646,"LeftCeiling;":8968,"LeftDoubleBracket;":10214,"LeftDownTeeVector;":10593,"LeftDownVector;":8643,"LeftDownVectorBar;":10585,"LeftFloor;":8970,"LeftRightArrow;":8596,"LeftRightVector;":10574,"LeftTee;":8867,"LeftTeeArrow;":8612,"LeftTeeVector;":10586,"LeftTriangle;":8882,"LeftTriangleBar;":10703,"LeftTriangleEqual;":8884,"LeftUpDownVector;":10577,"LeftUpTeeVector;":10592,"LeftUpVector;":8639,"LeftUpVectorBar;":10584,"LeftVector;":8636,"LeftVectorBar;":10578,"Leftarrow;":8656,"Leftrightarrow;":8660,"LessEqualGreater;":8922,"LessFullEqual;":8806,"LessGreater;":8822,"LessLess;":10913,"LessSlantEqual;":10877,"LessTilde;":8818,"Lfr;":[55349,56591],"Ll;":8920,"Lleftarrow;":8666,"Lmidot;":319,"LongLeftArrow;":10229,"LongLeftRightArrow;":10231,"LongRightArrow;":10230,"Longleftarrow;":10232,"Longleftrightarrow;":10234,"Longrightarrow;":10233,"Lopf;":[55349,56643],"LowerLeftArrow;":8601,"LowerRightArrow;":8600,"Lscr;":8466,"Lsh;":8624,"Lstrok;":321,"Lt;":8810,"Map;":10501,"Mcy;":1052,"MediumSpace;":8287,"Mellintrf;":8499,"Mfr;":[55349,56592],"MinusPlus;":8723,"Mopf;":[55349,56644],"Mscr;":8499,"Mu;":924,"NJcy;":1034,"Nacute;":323,"Ncaron;":327,"Ncedil;":325,"Ncy;":1053,"NegativeMediumSpace;":8203,"NegativeThickSpace;":8203,"NegativeThinSpace;":8203,"NegativeVeryThinSpace;":8203,"NestedGreaterGreater;":8811,"NestedLessLess;":8810,"NewLine;":10,"Nfr;":[55349,56593],"NoBreak;":8288,"NonBreakingSpace;":160,"Nopf;":8469,"Not;":10988,"NotCongruent;":8802,"NotCupCap;":8813,"NotDoubleVerticalBar;":8742,"NotElement;":8713,"NotEqual;":8800,"NotEqualTilde;":[8770,824],"NotExists;":8708,"NotGreater;":8815,"NotGreaterEqual;":8817,"NotGreaterFullEqual;":[8807,824],"NotGreaterGreater;":[8811,824],"NotGreaterLess;":8825,"NotGreaterSlantEqual;":[10878,824],"NotGreaterTilde;":8821,"NotHumpDownHump;":[8782,824],"NotHumpEqual;":[8783,824],"NotLeftTriangle;":8938,"NotLeftTriangleBar;":[10703,824],"NotLeftTriangleEqual;":8940,"NotLess;":8814,"NotLessEqual;":8816,"NotLessGreater;":8824,"NotLessLess;":[8810,824],"NotLessSlantEqual;":[10877,824],"NotLessTilde;":8820,"NotNestedGreaterGreater;":[10914,824],"NotNestedLessLess;":[10913,824],"NotPrecedes;":8832,"NotPrecedesEqual;":[10927,824],"NotPrecedesSlantEqual;":8928,"NotReverseElement;":8716,"NotRightTriangle;":8939,"NotRightTriangleBar;":[10704,824],"NotRightTriangleEqual;":8941,"NotSquareSubset;":[8847,824],"NotSquareSubsetEqual;":8930,"NotSquareSuperset;":[8848,824],"NotSquareSupersetEqual;":8931,"NotSubset;":[8834,8402],"NotSubsetEqual;":8840,"NotSucceeds;":8833,"NotSucceedsEqual;":[10928,824],"NotSucceedsSlantEqual;":8929,"NotSucceedsTilde;":[8831,824],"NotSuperset;":[8835,8402],"NotSupersetEqual;":8841,"NotTilde;":8769,"NotTildeEqual;":8772,"NotTildeFullEqual;":8775,"NotTildeTilde;":8777,"NotVerticalBar;":8740,"Nscr;":[55349,56489],Ntilde:209,"Ntilde;":209,"Nu;":925,"OElig;":338,Oacute:211,"Oacute;":211,Ocirc:212,"Ocirc;":212,"Ocy;":1054,"Odblac;":336,"Ofr;":[55349,56594],Ograve:210,"Ograve;":210,"Omacr;":332,"Omega;":937,"Omicron;":927,"Oopf;":[55349,56646],"OpenCurlyDoubleQuote;":8220,"OpenCurlyQuote;":8216,"Or;":10836,"Oscr;":[55349,56490],Oslash:216,"Oslash;":216,Otilde:213,"Otilde;":213,"Otimes;":10807,Ouml:214,"Ouml;":214,"OverBar;":8254,"OverBrace;":9182,"OverBracket;":9140,"OverParenthesis;":9180,"PartialD;":8706,"Pcy;":1055,"Pfr;":[55349,56595],"Phi;":934,"Pi;":928,"PlusMinus;":177,"Poincareplane;":8460,"Popf;":8473,"Pr;":10939,"Precedes;":8826,"PrecedesEqual;":10927,"PrecedesSlantEqual;":8828,"PrecedesTilde;":8830,"Prime;":8243,"Product;":8719,"Proportion;":8759,"Proportional;":8733,"Pscr;":[55349,56491],"Psi;":936,QUOT:34,"QUOT;":34,"Qfr;":[55349,56596],"Qopf;":8474,"Qscr;":[55349,56492],"RBarr;":10512,REG:174,"REG;":174,"Racute;":340,"Rang;":10219,"Rarr;":8608,"Rarrtl;":10518,"Rcaron;":344,"Rcedil;":342,"Rcy;":1056,"Re;":8476,"ReverseElement;":8715,"ReverseEquilibrium;":8651,"ReverseUpEquilibrium;":10607,"Rfr;":8476,"Rho;":929,"RightAngleBracket;":10217,"RightArrow;":8594,"RightArrowBar;":8677,"RightArrowLeftArrow;":8644,"RightCeiling;":8969,"RightDoubleBracket;":10215,"RightDownTeeVector;":10589,"RightDownVector;":8642,"RightDownVectorBar;":10581,"RightFloor;":8971,"RightTee;":8866,"RightTeeArrow;":8614,"RightTeeVector;":10587,"RightTriangle;":8883,"RightTriangleBar;":10704,"RightTriangleEqual;":8885,"RightUpDownVector;":10575,"RightUpTeeVector;":10588,"RightUpVector;":8638,"RightUpVectorBar;":10580,"RightVector;":8640,"RightVectorBar;":10579,"Rightarrow;":8658,"Ropf;":8477,"RoundImplies;":10608,"Rrightarrow;":8667,"Rscr;":8475,"Rsh;":8625,"RuleDelayed;":10740,"SHCHcy;":1065,"SHcy;":1064,"SOFTcy;":1068,"Sacute;":346,"Sc;":10940,"Scaron;":352,"Scedil;":350,"Scirc;":348,"Scy;":1057,"Sfr;":[55349,56598],"ShortDownArrow;":8595,"ShortLeftArrow;":8592,"ShortRightArrow;":8594,"ShortUpArrow;":8593,"Sigma;":931,"SmallCircle;":8728,"Sopf;":[55349,56650],"Sqrt;":8730,"Square;":9633,"SquareIntersection;":8851,"SquareSubset;":8847,"SquareSubsetEqual;":8849,"SquareSuperset;":8848,"SquareSupersetEqual;":8850,"SquareUnion;":8852,"Sscr;":[55349,56494],"Star;":8902,"Sub;":8912,"Subset;":8912,"SubsetEqual;":8838,"Succeeds;":8827,"SucceedsEqual;":10928,"SucceedsSlantEqual;":8829,"SucceedsTilde;":8831,"SuchThat;":8715,"Sum;":8721,"Sup;":8913,"Superset;":8835,"SupersetEqual;":8839,"Supset;":8913,THORN:222,"THORN;":222,"TRADE;":8482,"TSHcy;":1035,"TScy;":1062,"Tab;":9,"Tau;":932,"Tcaron;":356,"Tcedil;":354,"Tcy;":1058,"Tfr;":[55349,56599],"Therefore;":8756,"Theta;":920,"ThickSpace;":[8287,8202],"ThinSpace;":8201,"Tilde;":8764,"TildeEqual;":8771,"TildeFullEqual;":8773,"TildeTilde;":8776,"Topf;":[55349,56651],"TripleDot;":8411,"Tscr;":[55349,56495],"Tstrok;":358,Uacute:218,"Uacute;":218,"Uarr;":8607,"Uarrocir;":10569,"Ubrcy;":1038,"Ubreve;":364,Ucirc:219,"Ucirc;":219,"Ucy;":1059,"Udblac;":368,"Ufr;":[55349,56600],Ugrave:217,"Ugrave;":217,"Umacr;":362,"UnderBar;":95,"UnderBrace;":9183,"UnderBracket;":9141,"UnderParenthesis;":9181,"Union;":8899,"UnionPlus;":8846,"Uogon;":370,"Uopf;":[55349,56652],"UpArrow;":8593,"UpArrowBar;":10514,"UpArrowDownArrow;":8645,"UpDownArrow;":8597,"UpEquilibrium;":10606,"UpTee;":8869,"UpTeeArrow;":8613,"Uparrow;":8657,"Updownarrow;":8661,"UpperLeftArrow;":8598,"UpperRightArrow;":8599,"Upsi;":978,"Upsilon;":933,"Uring;":366,"Uscr;":[55349,56496],"Utilde;":360,Uuml:220,"Uuml;":220,"VDash;":8875,"Vbar;":10987,"Vcy;":1042,"Vdash;":8873,"Vdashl;":10982,"Vee;":8897,"Verbar;":8214,"Vert;":8214,"VerticalBar;":8739,"VerticalLine;":124,"VerticalSeparator;":10072,"VerticalTilde;":8768,"VeryThinSpace;":8202,"Vfr;":[55349,56601],"Vopf;":[55349,56653],"Vscr;":[55349,56497],"Vvdash;":8874,"Wcirc;":372,"Wedge;":8896,"Wfr;":[55349,56602],"Wopf;":[55349,56654],"Wscr;":[55349,56498],"Xfr;":[55349,56603],"Xi;":926,"Xopf;":[55349,56655],"Xscr;":[55349,56499],"YAcy;":1071,"YIcy;":1031,"YUcy;":1070,Yacute:221,"Yacute;":221,"Ycirc;":374,"Ycy;":1067,"Yfr;":[55349,56604],"Yopf;":[55349,56656],"Yscr;":[55349,56500],"Yuml;":376,"ZHcy;":1046,"Zacute;":377,"Zcaron;":381,"Zcy;":1047,"Zdot;":379,"ZeroWidthSpace;":8203,"Zeta;":918,"Zfr;":8488,"Zopf;":8484,"Zscr;":[55349,56501],aacute:225,"aacute;":225,"abreve;":259,"ac;":8766,"acE;":[8766,819],"acd;":8767,acirc:226,"acirc;":226,acute:180,"acute;":180,"acy;":1072,aelig:230,"aelig;":230,"af;":8289,"afr;":[55349,56606],agrave:224,"agrave;":224,"alefsym;":8501,"aleph;":8501,"alpha;":945,"amacr;":257,"amalg;":10815,amp:38,"amp;":38,"and;":8743,"andand;":10837,"andd;":10844,"andslope;":10840,"andv;":10842,"ang;":8736,"ange;":10660,"angle;":8736,"angmsd;":8737,"angmsdaa;":10664,"angmsdab;":10665,"angmsdac;":10666,"angmsdad;":10667,"angmsdae;":10668,"angmsdaf;":10669,"angmsdag;":10670,"angmsdah;":10671,"angrt;":8735,"angrtvb;":8894,"angrtvbd;":10653,"angsph;":8738,"angst;":197,"angzarr;":9084,"aogon;":261,"aopf;":[55349,56658],"ap;":8776,"apE;":10864,"apacir;":10863,"ape;":8778,"apid;":8779,"apos;":39,"approx;":8776,"approxeq;":8778,aring:229,"aring;":229,"ascr;":[55349,56502],"ast;":42,"asymp;":8776,"asympeq;":8781,atilde:227,"atilde;":227,auml:228,"auml;":228,"awconint;":8755,"awint;":10769,"bNot;":10989,"backcong;":8780,"backepsilon;":1014,"backprime;":8245,"backsim;":8765,"backsimeq;":8909,"barvee;":8893,"barwed;":8965,"barwedge;":8965,"bbrk;":9141,"bbrktbrk;":9142,"bcong;":8780,"bcy;":1073,"bdquo;":8222,"becaus;":8757,"because;":8757,"bemptyv;":10672,"bepsi;":1014,"bernou;":8492,"beta;":946,"beth;":8502,"between;":8812,"bfr;":[55349,56607],"bigcap;":8898,"bigcirc;":9711,"bigcup;":8899,"bigodot;":10752,"bigoplus;":10753,"bigotimes;":10754,"bigsqcup;":10758,"bigstar;":9733,"bigtriangledown;":9661,"bigtriangleup;":9651,"biguplus;":10756,"bigvee;":8897,"bigwedge;":8896,"bkarow;":10509,"blacklozenge;":10731,"blacksquare;":9642,"blacktriangle;":9652,"blacktriangledown;":9662,"blacktriangleleft;":9666,"blacktriangleright;":9656,"blank;":9251,"blk12;":9618,"blk14;":9617,"blk34;":9619,"block;":9608,"bne;":[61,8421],"bnequiv;":[8801,8421],"bnot;":8976,"bopf;":[55349,56659],"bot;":8869,"bottom;":8869,"bowtie;":8904,"boxDL;":9559,"boxDR;":9556,"boxDl;":9558,"boxDr;":9555,"boxH;":9552,"boxHD;":9574,"boxHU;":9577,"boxHd;":9572,"boxHu;":9575,"boxUL;":9565,"boxUR;":9562,"boxUl;":9564,"boxUr;":9561,"boxV;":9553,"boxVH;":9580,"boxVL;":9571,"boxVR;":9568,"boxVh;":9579,"boxVl;":9570,"boxVr;":9567,"boxbox;":10697,"boxdL;":9557,"boxdR;":9554,"boxdl;":9488,"boxdr;":9484,"boxh;":9472,"boxhD;":9573,"boxhU;":9576,"boxhd;":9516,"boxhu;":9524,"boxminus;":8863,"boxplus;":8862,"boxtimes;":8864,"boxuL;":9563,"boxuR;":9560,"boxul;":9496,"boxur;":9492,"boxv;":9474,"boxvH;":9578,"boxvL;":9569,"boxvR;":9566,"boxvh;":9532,"boxvl;":9508,"boxvr;":9500,"bprime;":8245,"breve;":728,brvbar:166,"brvbar;":166,"bscr;":[55349,56503],"bsemi;":8271,"bsim;":8765,"bsime;":8909,"bsol;":92,"bsolb;":10693,"bsolhsub;":10184,"bull;":8226,"bullet;":8226,"bump;":8782,"bumpE;":10926,"bumpe;":8783,"bumpeq;":8783,"cacute;":263,"cap;":8745,"capand;":10820,"capbrcup;":10825,"capcap;":10827,"capcup;":10823,"capdot;":10816,"caps;":[8745,65024],"caret;":8257,"caron;":711,"ccaps;":10829,"ccaron;":269,ccedil:231,"ccedil;":231,"ccirc;":265,"ccups;":10828,"ccupssm;":10832,"cdot;":267,cedil:184,"cedil;":184,"cemptyv;":10674,cent:162,"cent;":162,"centerdot;":183,"cfr;":[55349,56608],"chcy;":1095,"check;":10003,"checkmark;":10003,"chi;":967,"cir;":9675,"cirE;":10691,"circ;":710,"circeq;":8791,"circlearrowleft;":8634,"circlearrowright;":8635,"circledR;":174,"circledS;":9416,"circledast;":8859,"circledcirc;":8858,"circleddash;":8861,"cire;":8791,"cirfnint;":10768,"cirmid;":10991,"cirscir;":10690,"clubs;":9827,"clubsuit;":9827,"colon;":58,"colone;":8788,"coloneq;":8788,"comma;":44,"commat;":64,"comp;":8705,"compfn;":8728,"complement;":8705,"complexes;":8450,"cong;":8773,"congdot;":10861,"conint;":8750,"copf;":[55349,56660],"coprod;":8720,copy:169,"copy;":169,"copysr;":8471,"crarr;":8629,"cross;":10007,"cscr;":[55349,56504],"csub;":10959,"csube;":10961,"csup;":10960,"csupe;":10962,"ctdot;":8943,"cudarrl;":10552,"cudarrr;":10549,"cuepr;":8926,"cuesc;":8927,"cularr;":8630,"cularrp;":10557,"cup;":8746,"cupbrcap;":10824,"cupcap;":10822,"cupcup;":10826,"cupdot;":8845,"cupor;":10821,"cups;":[8746,65024],"curarr;":8631,"curarrm;":10556,"curlyeqprec;":8926,"curlyeqsucc;":8927,"curlyvee;":8910,"curlywedge;":8911,curren:164,"curren;":164,"curvearrowleft;":8630,"curvearrowright;":8631,"cuvee;":8910,"cuwed;":8911,"cwconint;":8754,"cwint;":8753,"cylcty;":9005,"dArr;":8659,"dHar;":10597,"dagger;":8224,"daleth;":8504,"darr;":8595,"dash;":8208,"dashv;":8867,"dbkarow;":10511,"dblac;":733,"dcaron;":271,"dcy;":1076,"dd;":8518,"ddagger;":8225,"ddarr;":8650,"ddotseq;":10871,deg:176,"deg;":176,"delta;":948,"demptyv;":10673,"dfisht;":10623,"dfr;":[55349,56609],"dharl;":8643,"dharr;":8642,"diam;":8900,"diamond;":8900,"diamondsuit;":9830,"diams;":9830,"die;":168,"digamma;":989,"disin;":8946,"div;":247,divide:247,"divide;":247,"divideontimes;":8903,"divonx;":8903,"djcy;":1106,"dlcorn;":8990,"dlcrop;":8973,"dollar;":36,"dopf;":[55349,56661],"dot;":729,"doteq;":8784,"doteqdot;":8785,"dotminus;":8760,"dotplus;":8724,"dotsquare;":8865,"doublebarwedge;":8966,"downarrow;":8595,"downdownarrows;":8650,"downharpoonleft;":8643,"downharpoonright;":8642,"drbkarow;":10512,"drcorn;":8991,"drcrop;":8972,"dscr;":[55349,56505],"dscy;":1109,"dsol;":10742,"dstrok;":273,"dtdot;":8945,"dtri;":9663,"dtrif;":9662,"duarr;":8693,"duhar;":10607,"dwangle;":10662,"dzcy;":1119,"dzigrarr;":10239,"eDDot;":10871,"eDot;":8785,eacute:233,"eacute;":233,"easter;":10862,"ecaron;":283,"ecir;":8790,ecirc:234,"ecirc;":234,"ecolon;":8789,"ecy;":1101,"edot;":279,"ee;":8519,"efDot;":8786,"efr;":[55349,56610],"eg;":10906,egrave:232,"egrave;":232,"egs;":10902,"egsdot;":10904,"el;":10905,"elinters;":9191,"ell;":8467,"els;":10901,"elsdot;":10903,"emacr;":275,"empty;":8709,"emptyset;":8709,"emptyv;":8709,"emsp13;":8196,"emsp14;":8197,"emsp;":8195,"eng;":331,"ensp;":8194,"eogon;":281,"eopf;":[55349,56662],"epar;":8917,"eparsl;":10723,"eplus;":10865,"epsi;":949,"epsilon;":949,"epsiv;":1013,"eqcirc;":8790,"eqcolon;":8789,"eqsim;":8770,"eqslantgtr;":10902,"eqslantless;":10901,"equals;":61,"equest;":8799,"equiv;":8801,"equivDD;":10872,"eqvparsl;":10725,"erDot;":8787,"erarr;":10609,"escr;":8495,"esdot;":8784,"esim;":8770,"eta;":951,eth:240,"eth;":240,euml:235,"euml;":235,"euro;":8364,"excl;":33,"exist;":8707,"expectation;":8496,"exponentiale;":8519,"fallingdotseq;":8786,"fcy;":1092,"female;":9792,"ffilig;":64259,"fflig;":64256,"ffllig;":64260,"ffr;":[55349,56611],"filig;":64257,"fjlig;":[102,106],"flat;":9837,"fllig;":64258,"fltns;":9649,"fnof;":402,"fopf;":[55349,56663],"forall;":8704,"fork;":8916,"forkv;":10969,"fpartint;":10765,frac12:189,"frac12;":189,"frac13;":8531,frac14:188,"frac14;":188,"frac15;":8533,"frac16;":8537,"frac18;":8539,"frac23;":8532,"frac25;":8534,frac34:190,"frac34;":190,"frac35;":8535,"frac38;":8540,"frac45;":8536,"frac56;":8538,"frac58;":8541,"frac78;":8542,"frasl;":8260,"frown;":8994,"fscr;":[55349,56507],"gE;":8807,"gEl;":10892,"gacute;":501,"gamma;":947,"gammad;":989,"gap;":10886,"gbreve;":287,"gcirc;":285,"gcy;":1075,"gdot;":289,"ge;":8805,"gel;":8923,"geq;":8805,"geqq;":8807,"geqslant;":10878,"ges;":10878,"gescc;":10921,"gesdot;":10880,"gesdoto;":10882,"gesdotol;":10884,"gesl;":[8923,65024],"gesles;":10900,"gfr;":[55349,56612],"gg;":8811,"ggg;":8921,"gimel;":8503,"gjcy;":1107,"gl;":8823,"glE;":10898,"gla;":10917,"glj;":10916,"gnE;":8809,"gnap;":10890,"gnapprox;":10890,"gne;":10888,"gneq;":10888,"gneqq;":8809,"gnsim;":8935,"gopf;":[55349,56664],"grave;":96,"gscr;":8458,"gsim;":8819,"gsime;":10894,"gsiml;":10896,gt:62,"gt;":62,"gtcc;":10919,"gtcir;":10874,"gtdot;":8919,"gtlPar;":10645,"gtquest;":10876,"gtrapprox;":10886,"gtrarr;":10616,"gtrdot;":8919,"gtreqless;":8923,"gtreqqless;":10892,"gtrless;":8823,"gtrsim;":8819,"gvertneqq;":[8809,65024],"gvnE;":[8809,65024],"hArr;":8660,"hairsp;":8202,"half;":189,"hamilt;":8459,"hardcy;":1098,"harr;":8596,"harrcir;":10568,"harrw;":8621,"hbar;":8463,"hcirc;":293,"hearts;":9829,"heartsuit;":9829,"hellip;":8230,"hercon;":8889,"hfr;":[55349,56613],"hksearow;":10533,"hkswarow;":10534,"hoarr;":8703,"homtht;":8763,"hookleftarrow;":8617,"hookrightarrow;":8618,"hopf;":[55349,56665],"horbar;":8213,"hscr;":[55349,56509],"hslash;":8463,"hstrok;":295,"hybull;":8259,"hyphen;":8208,iacute:237,"iacute;":237,"ic;":8291,icirc:238,"icirc;":238,"icy;":1080,"iecy;":1077,iexcl:161,"iexcl;":161,"iff;":8660,"ifr;":[55349,56614],igrave:236,"igrave;":236,"ii;":8520,"iiiint;":10764,"iiint;":8749,"iinfin;":10716,"iiota;":8489,"ijlig;":307,"imacr;":299,"image;":8465,"imagline;":8464,"imagpart;":8465,"imath;":305,"imof;":8887,"imped;":437,"in;":8712,"incare;":8453,"infin;":8734,"infintie;":10717,"inodot;":305,"int;":8747,"intcal;":8890,"integers;":8484,"intercal;":8890,"intlarhk;":10775,"intprod;":10812,"iocy;":1105,"iogon;":303,"iopf;":[55349,56666],"iota;":953,"iprod;":10812,iquest:191,"iquest;":191,"iscr;":[55349,56510],"isin;":8712,"isinE;":8953,"isindot;":8949,"isins;":8948,"isinsv;":8947,"isinv;":8712,"it;":8290,"itilde;":297,"iukcy;":1110,iuml:239,"iuml;":239,"jcirc;":309,"jcy;":1081,"jfr;":[55349,56615],"jmath;":567,"jopf;":[55349,56667],"jscr;":[55349,56511],"jsercy;":1112,"jukcy;":1108,"kappa;":954,"kappav;":1008,"kcedil;":311,"kcy;":1082,"kfr;":[55349,56616],"kgreen;":312,"khcy;":1093,"kjcy;":1116,"kopf;":[55349,56668],"kscr;":[55349,56512],"lAarr;":8666,"lArr;":8656,"lAtail;":10523,"lBarr;":10510,"lE;":8806,"lEg;":10891,"lHar;":10594,"lacute;":314,"laemptyv;":10676,"lagran;":8466,"lambda;":955,"lang;":10216,"langd;":10641,"langle;":10216,"lap;":10885,laquo:171,"laquo;":171,"larr;":8592,"larrb;":8676,"larrbfs;":10527,"larrfs;":10525,"larrhk;":8617,"larrlp;":8619,"larrpl;":10553,"larrsim;":10611,"larrtl;":8610,"lat;":10923,"latail;":10521,"late;":10925,"lates;":[10925,65024],"lbarr;":10508,"lbbrk;":10098,"lbrace;":123,"lbrack;":91,"lbrke;":10635,"lbrksld;":10639,"lbrkslu;":10637,"lcaron;":318,"lcedil;":316,"lceil;":8968,"lcub;":123,"lcy;":1083,"ldca;":10550,"ldquo;":8220,"ldquor;":8222,"ldrdhar;":10599,"ldrushar;":10571,"ldsh;":8626,"le;":8804,"leftarrow;":8592,"leftarrowtail;":8610,"leftharpoondown;":8637,"leftharpoonup;":8636,"leftleftarrows;":8647,"leftrightarrow;":8596,"leftrightarrows;":8646,"leftrightharpoons;":8651,"leftrightsquigarrow;":8621,"leftthreetimes;":8907,"leg;":8922,"leq;":8804,"leqq;":8806,"leqslant;":10877,"les;":10877,"lescc;":10920,"lesdot;":10879,"lesdoto;":10881,"lesdotor;":10883,"lesg;":[8922,65024],"lesges;":10899,"lessapprox;":10885,"lessdot;":8918,"lesseqgtr;":8922,"lesseqqgtr;":10891,"lessgtr;":8822,"lesssim;":8818,"lfisht;":10620,"lfloor;":8970,"lfr;":[55349,56617],"lg;":8822,"lgE;":10897,"lhard;":8637,"lharu;":8636,"lharul;":10602,"lhblk;":9604,"ljcy;":1113,"ll;":8810,"llarr;":8647,"llcorner;":8990,"llhard;":10603,"lltri;":9722,"lmidot;":320,"lmoust;":9136,"lmoustache;":9136,"lnE;":8808,"lnap;":10889,"lnapprox;":10889,"lne;":10887,"lneq;":10887,"lneqq;":8808,"lnsim;":8934,"loang;":10220,"loarr;":8701,"lobrk;":10214,"longleftarrow;":10229,"longleftrightarrow;":10231,"longmapsto;":10236,"longrightarrow;":10230,"looparrowleft;":8619,"looparrowright;":8620,"lopar;":10629,"lopf;":[55349,56669],"loplus;":10797,"lotimes;":10804,"lowast;":8727,"lowbar;":95,"loz;":9674,"lozenge;":9674,"lozf;":10731,"lpar;":40,"lparlt;":10643,"lrarr;":8646,"lrcorner;":8991,"lrhar;":8651,"lrhard;":10605,"lrm;":8206,"lrtri;":8895,"lsaquo;":8249,"lscr;":[55349,56513],"lsh;":8624,"lsim;":8818,"lsime;":10893,"lsimg;":10895,"lsqb;":91,"lsquo;":8216,"lsquor;":8218,"lstrok;":322,lt:60,"lt;":60,"ltcc;":10918,"ltcir;":10873,"ltdot;":8918,"lthree;":8907,"ltimes;":8905,"ltlarr;":10614,"ltquest;":10875,"ltrPar;":10646,"ltri;":9667,"ltrie;":8884,"ltrif;":9666,"lurdshar;":10570,"luruhar;":10598,"lvertneqq;":[8808,65024],"lvnE;":[8808,65024],"mDDot;":8762,macr:175,"macr;":175,"male;":9794,"malt;":10016,"maltese;":10016,"map;":8614,"mapsto;":8614,"mapstodown;":8615,"mapstoleft;":8612,"mapstoup;":8613,"marker;":9646,"mcomma;":10793,"mcy;":1084,"mdash;":8212,"measuredangle;":8737,"mfr;":[55349,56618],"mho;":8487,micro:181,"micro;":181,"mid;":8739,"midast;":42,"midcir;":10992,middot:183,"middot;":183,"minus;":8722,"minusb;":8863,"minusd;":8760,"minusdu;":10794,"mlcp;":10971,"mldr;":8230,"mnplus;":8723,"models;":8871,"mopf;":[55349,56670],"mp;":8723,"mscr;":[55349,56514],"mstpos;":8766,"mu;":956,"multimap;":8888,"mumap;":8888,"nGg;":[8921,824],"nGt;":[8811,8402],"nGtv;":[8811,824],"nLeftarrow;":8653,"nLeftrightarrow;":8654,"nLl;":[8920,824],"nLt;":[8810,8402],"nLtv;":[8810,824],"nRightarrow;":8655,"nVDash;":8879,"nVdash;":8878,"nabla;":8711,"nacute;":324,"nang;":[8736,8402],"nap;":8777,"napE;":[10864,824],"napid;":[8779,824],"napos;":329,"napprox;":8777,"natur;":9838,"natural;":9838,"naturals;":8469,nbsp:160,"nbsp;":160,"nbump;":[8782,824],"nbumpe;":[8783,824],"ncap;":10819,"ncaron;":328,"ncedil;":326,"ncong;":8775,"ncongdot;":[10861,824],"ncup;":10818,"ncy;":1085,"ndash;":8211,"ne;":8800,"neArr;":8663,"nearhk;":10532,"nearr;":8599,"nearrow;":8599,"nedot;":[8784,824],"nequiv;":8802,"nesear;":10536,"nesim;":[8770,824],"nexist;":8708,"nexists;":8708,"nfr;":[55349,56619],"ngE;":[8807,824],"nge;":8817,"ngeq;":8817,"ngeqq;":[8807,824],"ngeqslant;":[10878,824],"nges;":[10878,824],"ngsim;":8821,"ngt;":8815,"ngtr;":8815,"nhArr;":8654,"nharr;":8622,"nhpar;":10994,"ni;":8715,"nis;":8956,"nisd;":8954,"niv;":8715,"njcy;":1114,"nlArr;":8653,"nlE;":[8806,824],"nlarr;":8602,"nldr;":8229,"nle;":8816,"nleftarrow;":8602,"nleftrightarrow;":8622,"nleq;":8816,"nleqq;":[8806,824],"nleqslant;":[10877,824],"nles;":[10877,824],"nless;":8814,"nlsim;":8820,"nlt;":8814,"nltri;":8938,"nltrie;":8940,"nmid;":8740,"nopf;":[55349,56671],not:172,"not;":172,"notin;":8713,"notinE;":[8953,824],"notindot;":[8949,824],"notinva;":8713,"notinvb;":8951,"notinvc;":8950,"notni;":8716,"notniva;":8716,"notnivb;":8958,"notnivc;":8957,"npar;":8742,"nparallel;":8742,"nparsl;":[11005,8421],"npart;":[8706,824],"npolint;":10772,"npr;":8832,"nprcue;":8928,"npre;":[10927,824],"nprec;":8832,"npreceq;":[10927,824],"nrArr;":8655,"nrarr;":8603,"nrarrc;":[10547,824],"nrarrw;":[8605,824],"nrightarrow;":8603,"nrtri;":8939,"nrtrie;":8941,"nsc;":8833,"nsccue;":8929,"nsce;":[10928,824],"nscr;":[55349,56515],"nshortmid;":8740,"nshortparallel;":8742,"nsim;":8769,"nsime;":8772,"nsimeq;":8772,"nsmid;":8740,"nspar;":8742,"nsqsube;":8930,"nsqsupe;":8931,"nsub;":8836,"nsubE;":[10949,824],"nsube;":8840,"nsubset;":[8834,8402],"nsubseteq;":8840,"nsubseteqq;":[10949,824],"nsucc;":8833,"nsucceq;":[10928,824],"nsup;":8837,"nsupE;":[10950,824],"nsupe;":8841,"nsupset;":[8835,8402],"nsupseteq;":8841,"nsupseteqq;":[10950,824],"ntgl;":8825,ntilde:241,"ntilde;":241,"ntlg;":8824,"ntriangleleft;":8938,"ntrianglelefteq;":8940,"ntriangleright;":8939,"ntrianglerighteq;":8941,"nu;":957,"num;":35,"numero;":8470,"numsp;":8199,"nvDash;":8877,"nvHarr;":10500,"nvap;":[8781,8402],"nvdash;":8876,"nvge;":[8805,8402],"nvgt;":[62,8402],"nvinfin;":10718,"nvlArr;":10498,"nvle;":[8804,8402],"nvlt;":[60,8402],"nvltrie;":[8884,8402],"nvrArr;":10499,"nvrtrie;":[8885,8402],"nvsim;":[8764,8402],"nwArr;":8662,"nwarhk;":10531,"nwarr;":8598,"nwarrow;":8598,"nwnear;":10535,"oS;":9416,oacute:243,"oacute;":243,"oast;":8859,"ocir;":8858,ocirc:244,"ocirc;":244,"ocy;":1086,"odash;":8861,"odblac;":337,"odiv;":10808,"odot;":8857,"odsold;":10684,"oelig;":339,"ofcir;":10687,"ofr;":[55349,56620],"ogon;":731,ograve:242,"ograve;":242,"ogt;":10689,"ohbar;":10677,"ohm;":937,"oint;":8750,"olarr;":8634,"olcir;":10686,"olcross;":10683,"oline;":8254,"olt;":10688,"omacr;":333,"omega;":969,"omicron;":959,"omid;":10678,"ominus;":8854,"oopf;":[55349,56672],"opar;":10679,"operp;":10681,"oplus;":8853,"or;":8744,"orarr;":8635,"ord;":10845,"order;":8500,"orderof;":8500,ordf:170,"ordf;":170,ordm:186,"ordm;":186,"origof;":8886,"oror;":10838,"orslope;":10839,"orv;":10843,"oscr;":8500,oslash:248,"oslash;":248,"osol;":8856,otilde:245,"otilde;":245,"otimes;":8855,"otimesas;":10806,ouml:246,"ouml;":246,"ovbar;":9021,"par;":8741,para:182,"para;":182,"parallel;":8741,"parsim;":10995,"parsl;":11005,"part;":8706,"pcy;":1087,"percnt;":37,"period;":46,"permil;":8240,"perp;":8869,"pertenk;":8241,"pfr;":[55349,56621],"phi;":966,"phiv;":981,"phmmat;":8499,"phone;":9742,"pi;":960,"pitchfork;":8916,"piv;":982,"planck;":8463,"planckh;":8462,"plankv;":8463,"plus;":43,"plusacir;":10787,"plusb;":8862,"pluscir;":10786,"plusdo;":8724,"plusdu;":10789,"pluse;":10866,plusmn:177,"plusmn;":177,"plussim;":10790,"plustwo;":10791,"pm;":177,"pointint;":10773,"popf;":[55349,56673],pound:163,"pound;":163,"pr;":8826,"prE;":10931,"prap;":10935,"prcue;":8828,"pre;":10927,"prec;":8826,"precapprox;":10935,"preccurlyeq;":8828,"preceq;":10927,"precnapprox;":10937,"precneqq;":10933,"precnsim;":8936,"precsim;":8830,"prime;":8242,"primes;":8473,"prnE;":10933,"prnap;":10937,"prnsim;":8936,"prod;":8719,"profalar;":9006,"profline;":8978,"profsurf;":8979,"prop;":8733,"propto;":8733,"prsim;":8830,"prurel;":8880,"pscr;":[55349,56517],"psi;":968,"puncsp;":8200,"qfr;":[55349,56622],"qint;":10764,"qopf;":[55349,56674],"qprime;":8279,"qscr;":[55349,56518],"quaternions;":8461,"quatint;":10774,"quest;":63,"questeq;":8799,quot:34,"quot;":34,"rAarr;":8667,"rArr;":8658,"rAtail;":10524,"rBarr;":10511,"rHar;":10596,"race;":[8765,817],"racute;":341,"radic;":8730,"raemptyv;":10675,"rang;":10217,"rangd;":10642,"range;":10661,"rangle;":10217,raquo:187,"raquo;":187,"rarr;":8594,"rarrap;":10613,"rarrb;":8677,"rarrbfs;":10528,"rarrc;":10547,"rarrfs;":10526,"rarrhk;":8618,"rarrlp;":8620,"rarrpl;":10565,"rarrsim;":10612,"rarrtl;":8611,"rarrw;":8605,"ratail;":10522,"ratio;":8758,"rationals;":8474,"rbarr;":10509,"rbbrk;":10099,"rbrace;":125,"rbrack;":93,"rbrke;":10636,"rbrksld;":10638,"rbrkslu;":10640,"rcaron;":345,"rcedil;":343,"rceil;":8969,"rcub;":125,"rcy;":1088,"rdca;":10551,"rdldhar;":10601,"rdquo;":8221,"rdquor;":8221,"rdsh;":8627,"real;":8476,"realine;":8475,"realpart;":8476,"reals;":8477,"rect;":9645,reg:174,"reg;":174,"rfisht;":10621,"rfloor;":8971,"rfr;":[55349,56623],"rhard;":8641,"rharu;":8640,"rharul;":10604,"rho;":961,"rhov;":1009,"rightarrow;":8594,"rightarrowtail;":8611,"rightharpoondown;":8641,"rightharpoonup;":8640,"rightleftarrows;":8644,"rightleftharpoons;":8652,"rightrightarrows;":8649,"rightsquigarrow;":8605,"rightthreetimes;":8908,"ring;":730,"risingdotseq;":8787,"rlarr;":8644,"rlhar;":8652,"rlm;":8207,"rmoust;":9137,"rmoustache;":9137,"rnmid;":10990,"roang;":10221,"roarr;":8702,"robrk;":10215,"ropar;":10630,"ropf;":[55349,56675],"roplus;":10798,"rotimes;":10805,"rpar;":41,"rpargt;":10644,"rppolint;":10770,"rrarr;":8649,"rsaquo;":8250,"rscr;":[55349,56519],"rsh;":8625,"rsqb;":93,"rsquo;":8217,"rsquor;":8217,"rthree;":8908,"rtimes;":8906,"rtri;":9657,"rtrie;":8885,"rtrif;":9656,"rtriltri;":10702,"ruluhar;":10600,"rx;":8478,"sacute;":347,"sbquo;":8218,"sc;":8827,"scE;":10932,"scap;":10936,"scaron;":353,"sccue;":8829,"sce;":10928,"scedil;":351,"scirc;":349,"scnE;":10934,"scnap;":10938,"scnsim;":8937,"scpolint;":10771,"scsim;":8831,"scy;":1089,"sdot;":8901,"sdotb;":8865,"sdote;":10854,"seArr;":8664,"searhk;":10533,"searr;":8600,"searrow;":8600,sect:167,"sect;":167,"semi;":59,"seswar;":10537,"setminus;":8726,"setmn;":8726,"sext;":10038,"sfr;":[55349,56624],"sfrown;":8994,"sharp;":9839,"shchcy;":1097,"shcy;":1096,"shortmid;":8739,"shortparallel;":8741,shy:173,"shy;":173,"sigma;":963,"sigmaf;":962,"sigmav;":962,"sim;":8764,"simdot;":10858,"sime;":8771,"simeq;":8771,"simg;":10910,"simgE;":10912,"siml;":10909,"simlE;":10911,"simne;":8774,"simplus;":10788,"simrarr;":10610,"slarr;":8592,"smallsetminus;":8726,"smashp;":10803,"smeparsl;":10724,"smid;":8739,"smile;":8995,"smt;":10922,"smte;":10924,"smtes;":[10924,65024],"softcy;":1100,"sol;":47,"solb;":10692,"solbar;":9023,"sopf;":[55349,56676],"spades;":9824,"spadesuit;":9824,"spar;":8741,"sqcap;":8851,"sqcaps;":[8851,65024],"sqcup;":8852,"sqcups;":[8852,65024],"sqsub;":8847,"sqsube;":8849,"sqsubset;":8847,"sqsubseteq;":8849,"sqsup;":8848,"sqsupe;":8850,"sqsupset;":8848,"sqsupseteq;":8850,"squ;":9633,"square;":9633,"squarf;":9642,"squf;":9642,"srarr;":8594,"sscr;":[55349,56520],"ssetmn;":8726,"ssmile;":8995,"sstarf;":8902,"star;":9734,"starf;":9733,"straightepsilon;":1013,"straightphi;":981,"strns;":175,"sub;":8834,"subE;":10949,"subdot;":10941,"sube;":8838,"subedot;":10947,"submult;":10945,"subnE;":10955,"subne;":8842,"subplus;":10943,"subrarr;":10617,"subset;":8834,"subseteq;":8838,"subseteqq;":10949,"subsetneq;":8842,"subsetneqq;":10955,"subsim;":10951,"subsub;":10965,"subsup;":10963,"succ;":8827,"succapprox;":10936,"succcurlyeq;":8829,"succeq;":10928,"succnapprox;":10938,"succneqq;":10934,"succnsim;":8937,"succsim;":8831,"sum;":8721,"sung;":9834,sup1:185,"sup1;":185,sup2:178,"sup2;":178,sup3:179,"sup3;":179,"sup;":8835,"supE;":10950,"supdot;":10942,"supdsub;":10968,"supe;":8839,"supedot;":10948,"suphsol;":10185,"suphsub;":10967,"suplarr;":10619,"supmult;":10946,"supnE;":10956,"supne;":8843,"supplus;":10944,"supset;":8835,"supseteq;":8839,"supseteqq;":10950,"supsetneq;":8843,"supsetneqq;":10956,"supsim;":10952,"supsub;":10964,"supsup;":10966,"swArr;":8665,"swarhk;":10534,"swarr;":8601,"swarrow;":8601,"swnwar;":10538,szlig:223,"szlig;":223,"target;":8982,"tau;":964,"tbrk;":9140,"tcaron;":357,"tcedil;":355,"tcy;":1090,"tdot;":8411,"telrec;":8981,"tfr;":[55349,56625],"there4;":8756,"therefore;":8756,"theta;":952,"thetasym;":977,"thetav;":977,"thickapprox;":8776,"thicksim;":8764,"thinsp;":8201,"thkap;":8776,"thksim;":8764,thorn:254,"thorn;":254,"tilde;":732,times:215,"times;":215,"timesb;":8864,"timesbar;":10801,"timesd;":10800,"tint;":8749,"toea;":10536,"top;":8868,"topbot;":9014,"topcir;":10993,"topf;":[55349,56677],"topfork;":10970,"tosa;":10537,"tprime;":8244,"trade;":8482,"triangle;":9653,"triangledown;":9663,"triangleleft;":9667,"trianglelefteq;":8884,"triangleq;":8796,"triangleright;":9657,"trianglerighteq;":8885,"tridot;":9708,"trie;":8796,"triminus;":10810,"triplus;":10809,"trisb;":10701,"tritime;":10811,"trpezium;":9186,"tscr;":[55349,56521],"tscy;":1094,"tshcy;":1115,"tstrok;":359,"twixt;":8812,"twoheadleftarrow;":8606,"twoheadrightarrow;":8608,"uArr;":8657,"uHar;":10595,uacute:250,"uacute;":250,"uarr;":8593,"ubrcy;":1118,"ubreve;":365,ucirc:251,"ucirc;":251,"ucy;":1091,"udarr;":8645,"udblac;":369,"udhar;":10606,"ufisht;":10622,"ufr;":[55349,56626],ugrave:249,"ugrave;":249,"uharl;":8639,"uharr;":8638,"uhblk;":9600,"ulcorn;":8988,"ulcorner;":8988,"ulcrop;":8975,"ultri;":9720,"umacr;":363,uml:168,"uml;":168,"uogon;":371,"uopf;":[55349,56678],"uparrow;":8593,"updownarrow;":8597,"upharpoonleft;":8639,"upharpoonright;":8638,"uplus;":8846,"upsi;":965,"upsih;":978,"upsilon;":965,"upuparrows;":8648,"urcorn;":8989,"urcorner;":8989,"urcrop;":8974,"uring;":367,"urtri;":9721,"uscr;":[55349,56522],"utdot;":8944,"utilde;":361,"utri;":9653,"utrif;":9652,"uuarr;":8648,uuml:252,"uuml;":252,"uwangle;":10663,"vArr;":8661,"vBar;":10984,"vBarv;":10985,"vDash;":8872,"vangrt;":10652,"varepsilon;":1013,"varkappa;":1008,"varnothing;":8709,"varphi;":981,"varpi;":982,"varpropto;":8733,"varr;":8597,"varrho;":1009,"varsigma;":962,"varsubsetneq;":[8842,65024],"varsubsetneqq;":[10955,65024],"varsupsetneq;":[8843,65024],"varsupsetneqq;":[10956,65024],"vartheta;":977,"vartriangleleft;":8882,"vartriangleright;":8883,"vcy;":1074,"vdash;":8866,"vee;":8744,"veebar;":8891,"veeeq;":8794,"vellip;":8942,"verbar;":124,"vert;":124,"vfr;":[55349,56627],"vltri;":8882,"vnsub;":[8834,8402],"vnsup;":[8835,8402],"vopf;":[55349,56679],"vprop;":8733,"vrtri;":8883,"vscr;":[55349,56523],"vsubnE;":[10955,65024],"vsubne;":[8842,65024],"vsupnE;":[10956,65024],"vsupne;":[8843,65024],"vzigzag;":10650,"wcirc;":373,"wedbar;":10847,"wedge;":8743,"wedgeq;":8793,"weierp;":8472,"wfr;":[55349,56628],"wopf;":[55349,56680],"wp;":8472,"wr;":8768,"wreath;":8768,"wscr;":[55349,56524],"xcap;":8898,"xcirc;":9711,"xcup;":8899,"xdtri;":9661,"xfr;":[55349,56629],"xhArr;":10234,"xharr;":10231,"xi;":958,"xlArr;":10232,"xlarr;":10229,"xmap;":10236,"xnis;":8955,"xodot;":10752,"xopf;":[55349,56681],"xoplus;":10753,"xotime;":10754,"xrArr;":10233,"xrarr;":10230,"xscr;":[55349,56525],"xsqcup;":10758,"xuplus;":10756,"xutri;":9651,"xvee;":8897,"xwedge;":8896,yacute:253,"yacute;":253,"yacy;":1103,"ycirc;":375,"ycy;":1099,yen:165,"yen;":165,"yfr;":[55349,56630],"yicy;":1111,"yopf;":[55349,56682],"yscr;":[55349,56526],"yucy;":1102,yuml:255,"yuml;":255,"zacute;":378,"zcaron;":382,"zcy;":1079,"zdot;":380,"zeetrf;":8488,"zeta;":950,"zfr;":[55349,56631],"zhcy;":1078,"zigrarr;":8669,"zopf;":[55349,56683],"zscr;":[55349,56527],"zwj;":8205,"zwnj;":8204},O=/(A(?:Elig;?|MP;?|acute;?|breve;|c(?:irc;?|y;)|fr;|grave;?|lpha;|macr;|nd;|o(?:gon;|pf;)|pplyFunction;|ring;?|s(?:cr;|sign;)|tilde;?|uml;?)|B(?:a(?:ckslash;|r(?:v;|wed;))|cy;|e(?:cause;|rnoullis;|ta;)|fr;|opf;|reve;|scr;|umpeq;)|C(?:Hcy;|OPY;?|a(?:cute;|p(?:;|italDifferentialD;)|yleys;)|c(?:aron;|edil;?|irc;|onint;)|dot;|e(?:dilla;|nterDot;)|fr;|hi;|ircle(?:Dot;|Minus;|Plus;|Times;)|lo(?:ckwiseContourIntegral;|seCurly(?:DoubleQuote;|Quote;))|o(?:lon(?:;|e;)|n(?:gruent;|int;|tourIntegral;)|p(?:f;|roduct;)|unterClockwiseContourIntegral;)|ross;|scr;|up(?:;|Cap;))|D(?:D(?:;|otrahd;)|Jcy;|Scy;|Zcy;|a(?:gger;|rr;|shv;)|c(?:aron;|y;)|el(?:;|ta;)|fr;|i(?:a(?:critical(?:Acute;|Do(?:t;|ubleAcute;)|Grave;|Tilde;)|mond;)|fferentialD;)|o(?:pf;|t(?:;|Dot;|Equal;)|uble(?:ContourIntegral;|Do(?:t;|wnArrow;)|L(?:eft(?:Arrow;|RightArrow;|Tee;)|ong(?:Left(?:Arrow;|RightArrow;)|RightArrow;))|Right(?:Arrow;|Tee;)|Up(?:Arrow;|DownArrow;)|VerticalBar;)|wn(?:Arrow(?:;|Bar;|UpArrow;)|Breve;|Left(?:RightVector;|TeeVector;|Vector(?:;|Bar;))|Right(?:TeeVector;|Vector(?:;|Bar;))|Tee(?:;|Arrow;)|arrow;))|s(?:cr;|trok;))|E(?:NG;|TH;?|acute;?|c(?:aron;|irc;?|y;)|dot;|fr;|grave;?|lement;|m(?:acr;|pty(?:SmallSquare;|VerySmallSquare;))|o(?:gon;|pf;)|psilon;|qu(?:al(?:;|Tilde;)|ilibrium;)|s(?:cr;|im;)|ta;|uml;?|x(?:ists;|ponentialE;))|F(?:cy;|fr;|illed(?:SmallSquare;|VerySmallSquare;)|o(?:pf;|rAll;|uriertrf;)|scr;)|G(?:Jcy;|T;?|amma(?:;|d;)|breve;|c(?:edil;|irc;|y;)|dot;|fr;|g;|opf;|reater(?:Equal(?:;|Less;)|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|scr;|t;)|H(?:ARDcy;|a(?:cek;|t;)|circ;|fr;|ilbertSpace;|o(?:pf;|rizontalLine;)|s(?:cr;|trok;)|ump(?:DownHump;|Equal;))|I(?:Ecy;|Jlig;|Ocy;|acute;?|c(?:irc;?|y;)|dot;|fr;|grave;?|m(?:;|a(?:cr;|ginaryI;)|plies;)|n(?:t(?:;|e(?:gral;|rsection;))|visible(?:Comma;|Times;))|o(?:gon;|pf;|ta;)|scr;|tilde;|u(?:kcy;|ml;?))|J(?:c(?:irc;|y;)|fr;|opf;|s(?:cr;|ercy;)|ukcy;)|K(?:Hcy;|Jcy;|appa;|c(?:edil;|y;)|fr;|opf;|scr;)|L(?:Jcy;|T;?|a(?:cute;|mbda;|ng;|placetrf;|rr;)|c(?:aron;|edil;|y;)|e(?:ft(?:A(?:ngleBracket;|rrow(?:;|Bar;|RightArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|Right(?:Arrow;|Vector;)|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;|rightarrow;)|ss(?:EqualGreater;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;))|fr;|l(?:;|eftarrow;)|midot;|o(?:ng(?:Left(?:Arrow;|RightArrow;)|RightArrow;|left(?:arrow;|rightarrow;)|rightarrow;)|pf;|wer(?:LeftArrow;|RightArrow;))|s(?:cr;|h;|trok;)|t;)|M(?:ap;|cy;|e(?:diumSpace;|llintrf;)|fr;|inusPlus;|opf;|scr;|u;)|N(?:Jcy;|acute;|c(?:aron;|edil;|y;)|e(?:gative(?:MediumSpace;|Thi(?:ckSpace;|nSpace;)|VeryThinSpace;)|sted(?:GreaterGreater;|LessLess;)|wLine;)|fr;|o(?:Break;|nBreakingSpace;|pf;|t(?:;|C(?:ongruent;|upCap;)|DoubleVerticalBar;|E(?:lement;|qual(?:;|Tilde;)|xists;)|Greater(?:;|Equal;|FullEqual;|Greater;|Less;|SlantEqual;|Tilde;)|Hump(?:DownHump;|Equal;)|Le(?:ftTriangle(?:;|Bar;|Equal;)|ss(?:;|Equal;|Greater;|Less;|SlantEqual;|Tilde;))|Nested(?:GreaterGreater;|LessLess;)|Precedes(?:;|Equal;|SlantEqual;)|R(?:everseElement;|ightTriangle(?:;|Bar;|Equal;))|S(?:quareSu(?:bset(?:;|Equal;)|perset(?:;|Equal;))|u(?:bset(?:;|Equal;)|cceeds(?:;|Equal;|SlantEqual;|Tilde;)|perset(?:;|Equal;)))|Tilde(?:;|Equal;|FullEqual;|Tilde;)|VerticalBar;))|scr;|tilde;?|u;)|O(?:Elig;|acute;?|c(?:irc;?|y;)|dblac;|fr;|grave;?|m(?:acr;|ega;|icron;)|opf;|penCurly(?:DoubleQuote;|Quote;)|r;|s(?:cr;|lash;?)|ti(?:lde;?|mes;)|uml;?|ver(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;))|P(?:artialD;|cy;|fr;|hi;|i;|lusMinus;|o(?:incareplane;|pf;)|r(?:;|ecedes(?:;|Equal;|SlantEqual;|Tilde;)|ime;|o(?:duct;|portion(?:;|al;)))|s(?:cr;|i;))|Q(?:UOT;?|fr;|opf;|scr;)|R(?:Barr;|EG;?|a(?:cute;|ng;|rr(?:;|tl;))|c(?:aron;|edil;|y;)|e(?:;|verse(?:E(?:lement;|quilibrium;)|UpEquilibrium;))|fr;|ho;|ight(?:A(?:ngleBracket;|rrow(?:;|Bar;|LeftArrow;))|Ceiling;|Do(?:ubleBracket;|wn(?:TeeVector;|Vector(?:;|Bar;)))|Floor;|T(?:ee(?:;|Arrow;|Vector;)|riangle(?:;|Bar;|Equal;))|Up(?:DownVector;|TeeVector;|Vector(?:;|Bar;))|Vector(?:;|Bar;)|arrow;)|o(?:pf;|undImplies;)|rightarrow;|s(?:cr;|h;)|uleDelayed;)|S(?:H(?:CHcy;|cy;)|OFTcy;|acute;|c(?:;|aron;|edil;|irc;|y;)|fr;|hort(?:DownArrow;|LeftArrow;|RightArrow;|UpArrow;)|igma;|mallCircle;|opf;|q(?:rt;|uare(?:;|Intersection;|Su(?:bset(?:;|Equal;)|perset(?:;|Equal;))|Union;))|scr;|tar;|u(?:b(?:;|set(?:;|Equal;))|c(?:ceeds(?:;|Equal;|SlantEqual;|Tilde;)|hThat;)|m;|p(?:;|erset(?:;|Equal;)|set;)))|T(?:HORN;?|RADE;|S(?:Hcy;|cy;)|a(?:b;|u;)|c(?:aron;|edil;|y;)|fr;|h(?:e(?:refore;|ta;)|i(?:ckSpace;|nSpace;))|ilde(?:;|Equal;|FullEqual;|Tilde;)|opf;|ripleDot;|s(?:cr;|trok;))|U(?:a(?:cute;?|rr(?:;|ocir;))|br(?:cy;|eve;)|c(?:irc;?|y;)|dblac;|fr;|grave;?|macr;|n(?:der(?:B(?:ar;|rac(?:e;|ket;))|Parenthesis;)|ion(?:;|Plus;))|o(?:gon;|pf;)|p(?:Arrow(?:;|Bar;|DownArrow;)|DownArrow;|Equilibrium;|Tee(?:;|Arrow;)|arrow;|downarrow;|per(?:LeftArrow;|RightArrow;)|si(?:;|lon;))|ring;|scr;|tilde;|uml;?)|V(?:Dash;|bar;|cy;|dash(?:;|l;)|e(?:e;|r(?:bar;|t(?:;|ical(?:Bar;|Line;|Separator;|Tilde;))|yThinSpace;))|fr;|opf;|scr;|vdash;)|W(?:circ;|edge;|fr;|opf;|scr;)|X(?:fr;|i;|opf;|scr;)|Y(?:Acy;|Icy;|Ucy;|acute;?|c(?:irc;|y;)|fr;|opf;|scr;|uml;)|Z(?:Hcy;|acute;|c(?:aron;|y;)|dot;|e(?:roWidthSpace;|ta;)|fr;|opf;|scr;)|a(?:acute;?|breve;|c(?:;|E;|d;|irc;?|ute;?|y;)|elig;?|f(?:;|r;)|grave;?|l(?:e(?:fsym;|ph;)|pha;)|m(?:a(?:cr;|lg;)|p;?)|n(?:d(?:;|and;|d;|slope;|v;)|g(?:;|e;|le;|msd(?:;|a(?:a;|b;|c;|d;|e;|f;|g;|h;))|rt(?:;|vb(?:;|d;))|s(?:ph;|t;)|zarr;))|o(?:gon;|pf;)|p(?:;|E;|acir;|e;|id;|os;|prox(?:;|eq;))|ring;?|s(?:cr;|t;|ymp(?:;|eq;))|tilde;?|uml;?|w(?:conint;|int;))|b(?:Not;|a(?:ck(?:cong;|epsilon;|prime;|sim(?:;|eq;))|r(?:vee;|wed(?:;|ge;)))|brk(?:;|tbrk;)|c(?:ong;|y;)|dquo;|e(?:caus(?:;|e;)|mptyv;|psi;|rnou;|t(?:a;|h;|ween;))|fr;|ig(?:c(?:ap;|irc;|up;)|o(?:dot;|plus;|times;)|s(?:qcup;|tar;)|triangle(?:down;|up;)|uplus;|vee;|wedge;)|karow;|l(?:a(?:ck(?:lozenge;|square;|triangle(?:;|down;|left;|right;))|nk;)|k(?:1(?:2;|4;)|34;)|ock;)|n(?:e(?:;|quiv;)|ot;)|o(?:pf;|t(?:;|tom;)|wtie;|x(?:D(?:L;|R;|l;|r;)|H(?:;|D;|U;|d;|u;)|U(?:L;|R;|l;|r;)|V(?:;|H;|L;|R;|h;|l;|r;)|box;|d(?:L;|R;|l;|r;)|h(?:;|D;|U;|d;|u;)|minus;|plus;|times;|u(?:L;|R;|l;|r;)|v(?:;|H;|L;|R;|h;|l;|r;)))|prime;|r(?:eve;|vbar;?)|s(?:cr;|emi;|im(?:;|e;)|ol(?:;|b;|hsub;))|u(?:ll(?:;|et;)|mp(?:;|E;|e(?:;|q;))))|c(?:a(?:cute;|p(?:;|and;|brcup;|c(?:ap;|up;)|dot;|s;)|r(?:et;|on;))|c(?:a(?:ps;|ron;)|edil;?|irc;|ups(?:;|sm;))|dot;|e(?:dil;?|mptyv;|nt(?:;|erdot;|))|fr;|h(?:cy;|eck(?:;|mark;)|i;)|ir(?:;|E;|c(?:;|eq;|le(?:arrow(?:left;|right;)|d(?:R;|S;|ast;|circ;|dash;)))|e;|fnint;|mid;|scir;)|lubs(?:;|uit;)|o(?:lon(?:;|e(?:;|q;))|m(?:ma(?:;|t;)|p(?:;|fn;|le(?:ment;|xes;)))|n(?:g(?:;|dot;)|int;)|p(?:f;|rod;|y(?:;|sr;|)))|r(?:arr;|oss;)|s(?:cr;|u(?:b(?:;|e;)|p(?:;|e;)))|tdot;|u(?:darr(?:l;|r;)|e(?:pr;|sc;)|larr(?:;|p;)|p(?:;|brcap;|c(?:ap;|up;)|dot;|or;|s;)|r(?:arr(?:;|m;)|ly(?:eq(?:prec;|succ;)|vee;|wedge;)|ren;?|vearrow(?:left;|right;))|vee;|wed;)|w(?:conint;|int;)|ylcty;)|d(?:Arr;|Har;|a(?:gger;|leth;|rr;|sh(?:;|v;))|b(?:karow;|lac;)|c(?:aron;|y;)|d(?:;|a(?:gger;|rr;)|otseq;)|e(?:g;?|lta;|mptyv;)|f(?:isht;|r;)|har(?:l;|r;)|i(?:am(?:;|ond(?:;|suit;)|s;)|e;|gamma;|sin;|v(?:;|ide(?:;|ontimes;|)|onx;))|jcy;|lc(?:orn;|rop;)|o(?:llar;|pf;|t(?:;|eq(?:;|dot;)|minus;|plus;|square;)|ublebarwedge;|wn(?:arrow;|downarrows;|harpoon(?:left;|right;)))|r(?:bkarow;|c(?:orn;|rop;))|s(?:c(?:r;|y;)|ol;|trok;)|t(?:dot;|ri(?:;|f;))|u(?:arr;|har;)|wangle;|z(?:cy;|igrarr;))|e(?:D(?:Dot;|ot;)|a(?:cute;?|ster;)|c(?:aron;|ir(?:;|c;?)|olon;|y;)|dot;|e;|f(?:Dot;|r;)|g(?:;|rave;?|s(?:;|dot;))|l(?:;|inters;|l;|s(?:;|dot;))|m(?:acr;|pty(?:;|set;|v;)|sp(?:1(?:3;|4;)|;))|n(?:g;|sp;)|o(?:gon;|pf;)|p(?:ar(?:;|sl;)|lus;|si(?:;|lon;|v;))|q(?:c(?:irc;|olon;)|s(?:im;|lant(?:gtr;|less;))|u(?:als;|est;|iv(?:;|DD;))|vparsl;)|r(?:Dot;|arr;)|s(?:cr;|dot;|im;)|t(?:a;|h;?)|u(?:ml;?|ro;)|x(?:cl;|ist;|p(?:ectation;|onentiale;)))|f(?:allingdotseq;|cy;|emale;|f(?:ilig;|l(?:ig;|lig;)|r;)|ilig;|jlig;|l(?:at;|lig;|tns;)|nof;|o(?:pf;|r(?:all;|k(?:;|v;)))|partint;|r(?:a(?:c(?:1(?:2;?|3;|4;?|5;|6;|8;)|2(?:3;|5;)|3(?:4;?|5;|8;)|45;|5(?:6;|8;)|78;)|sl;)|own;)|scr;)|g(?:E(?:;|l;)|a(?:cute;|mma(?:;|d;)|p;)|breve;|c(?:irc;|y;)|dot;|e(?:;|l;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|l;))|l(?:;|es;)))|fr;|g(?:;|g;)|imel;|jcy;|l(?:;|E;|a;|j;)|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|opf;|rave;|s(?:cr;|im(?:;|e;|l;))|t(?:;|c(?:c;|ir;)|dot;|lPar;|quest;|r(?:a(?:pprox;|rr;)|dot;|eq(?:less;|qless;)|less;|sim;)|)|v(?:ertneqq;|nE;))|h(?:Arr;|a(?:irsp;|lf;|milt;|r(?:dcy;|r(?:;|cir;|w;)))|bar;|circ;|e(?:arts(?:;|uit;)|llip;|rcon;)|fr;|ks(?:earow;|warow;)|o(?:arr;|mtht;|ok(?:leftarrow;|rightarrow;)|pf;|rbar;)|s(?:cr;|lash;|trok;)|y(?:bull;|phen;))|i(?:acute;?|c(?:;|irc;?|y;)|e(?:cy;|xcl;?)|f(?:f;|r;)|grave;?|i(?:;|i(?:int;|nt;)|nfin;|ota;)|jlig;|m(?:a(?:cr;|g(?:e;|line;|part;)|th;)|of;|ped;)|n(?:;|care;|fin(?:;|tie;)|odot;|t(?:;|cal;|e(?:gers;|rcal;)|larhk;|prod;))|o(?:cy;|gon;|pf;|ta;)|prod;|quest;?|s(?:cr;|in(?:;|E;|dot;|s(?:;|v;)|v;))|t(?:;|ilde;)|u(?:kcy;|ml;?))|j(?:c(?:irc;|y;)|fr;|math;|opf;|s(?:cr;|ercy;)|ukcy;)|k(?:appa(?:;|v;)|c(?:edil;|y;)|fr;|green;|hcy;|jcy;|opf;|scr;)|l(?:A(?:arr;|rr;|tail;)|Barr;|E(?:;|g;)|Har;|a(?:cute;|emptyv;|gran;|mbda;|ng(?:;|d;|le;)|p;|quo;?|rr(?:;|b(?:;|fs;)|fs;|hk;|lp;|pl;|sim;|tl;)|t(?:;|ail;|e(?:;|s;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|quo(?:;|r;)|r(?:dhar;|ushar;)|sh;)|e(?:;|ft(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|leftarrows;|right(?:arrow(?:;|s;)|harpoons;|squigarrow;)|threetimes;)|g;|q(?:;|q;|slant;)|s(?:;|cc;|dot(?:;|o(?:;|r;))|g(?:;|es;)|s(?:approx;|dot;|eq(?:gtr;|qgtr;)|gtr;|sim;)))|f(?:isht;|loor;|r;)|g(?:;|E;)|h(?:ar(?:d;|u(?:;|l;))|blk;)|jcy;|l(?:;|arr;|corner;|hard;|tri;)|m(?:idot;|oust(?:;|ache;))|n(?:E;|ap(?:;|prox;)|e(?:;|q(?:;|q;))|sim;)|o(?:a(?:ng;|rr;)|brk;|ng(?:left(?:arrow;|rightarrow;)|mapsto;|rightarrow;)|oparrow(?:left;|right;)|p(?:ar;|f;|lus;)|times;|w(?:ast;|bar;)|z(?:;|enge;|f;))|par(?:;|lt;)|r(?:arr;|corner;|har(?:;|d;)|m;|tri;)|s(?:aquo;|cr;|h;|im(?:;|e;|g;)|q(?:b;|uo(?:;|r;))|trok;)|t(?:;|c(?:c;|ir;)|dot;|hree;|imes;|larr;|quest;|r(?:Par;|i(?:;|e;|f;))|)|ur(?:dshar;|uhar;)|v(?:ertneqq;|nE;))|m(?:DDot;|a(?:cr;?|l(?:e;|t(?:;|ese;))|p(?:;|sto(?:;|down;|left;|up;))|rker;)|c(?:omma;|y;)|dash;|easuredangle;|fr;|ho;|i(?:cro;?|d(?:;|ast;|cir;|dot;?)|nus(?:;|b;|d(?:;|u;)))|l(?:cp;|dr;)|nplus;|o(?:dels;|pf;)|p;|s(?:cr;|tpos;)|u(?:;|ltimap;|map;))|n(?:G(?:g;|t(?:;|v;))|L(?:eft(?:arrow;|rightarrow;)|l;|t(?:;|v;))|Rightarrow;|V(?:Dash;|dash;)|a(?:bla;|cute;|ng;|p(?:;|E;|id;|os;|prox;)|tur(?:;|al(?:;|s;)))|b(?:sp;?|ump(?:;|e;))|c(?:a(?:p;|ron;)|edil;|ong(?:;|dot;)|up;|y;)|dash;|e(?:;|Arr;|ar(?:hk;|r(?:;|ow;))|dot;|quiv;|s(?:ear;|im;)|xist(?:;|s;))|fr;|g(?:E;|e(?:;|q(?:;|q;|slant;)|s;)|sim;|t(?:;|r;))|h(?:Arr;|arr;|par;)|i(?:;|s(?:;|d;)|v;)|jcy;|l(?:Arr;|E;|arr;|dr;|e(?:;|ft(?:arrow;|rightarrow;)|q(?:;|q;|slant;)|s(?:;|s;))|sim;|t(?:;|ri(?:;|e;)))|mid;|o(?:pf;|t(?:;|in(?:;|E;|dot;|v(?:a;|b;|c;))|ni(?:;|v(?:a;|b;|c;))|))|p(?:ar(?:;|allel;|sl;|t;)|olint;|r(?:;|cue;|e(?:;|c(?:;|eq;))))|r(?:Arr;|arr(?:;|c;|w;)|ightarrow;|tri(?:;|e;))|s(?:c(?:;|cue;|e;|r;)|hort(?:mid;|parallel;)|im(?:;|e(?:;|q;))|mid;|par;|qsu(?:be;|pe;)|u(?:b(?:;|E;|e;|set(?:;|eq(?:;|q;)))|cc(?:;|eq;)|p(?:;|E;|e;|set(?:;|eq(?:;|q;)))))|t(?:gl;|ilde;?|lg;|riangle(?:left(?:;|eq;)|right(?:;|eq;)))|u(?:;|m(?:;|ero;|sp;))|v(?:Dash;|Harr;|ap;|dash;|g(?:e;|t;)|infin;|l(?:Arr;|e;|t(?:;|rie;))|r(?:Arr;|trie;)|sim;)|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|near;))|o(?:S;|a(?:cute;?|st;)|c(?:ir(?:;|c;?)|y;)|d(?:ash;|blac;|iv;|ot;|sold;)|elig;|f(?:cir;|r;)|g(?:on;|rave;?|t;)|h(?:bar;|m;)|int;|l(?:arr;|c(?:ir;|ross;)|ine;|t;)|m(?:acr;|ega;|i(?:cron;|d;|nus;))|opf;|p(?:ar;|erp;|lus;)|r(?:;|arr;|d(?:;|er(?:;|of;)|f;?|m;?)|igof;|or;|slope;|v;)|s(?:cr;|lash;?|ol;)|ti(?:lde;?|mes(?:;|as;))|uml;?|vbar;)|p(?:ar(?:;|a(?:;|llel;|)|s(?:im;|l;)|t;)|cy;|er(?:cnt;|iod;|mil;|p;|tenk;)|fr;|h(?:i(?:;|v;)|mmat;|one;)|i(?:;|tchfork;|v;)|l(?:an(?:ck(?:;|h;)|kv;)|us(?:;|acir;|b;|cir;|d(?:o;|u;)|e;|mn;?|sim;|two;))|m;|o(?:intint;|pf;|und;?)|r(?:;|E;|ap;|cue;|e(?:;|c(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;))|ime(?:;|s;)|n(?:E;|ap;|sim;)|o(?:d;|f(?:alar;|line;|surf;)|p(?:;|to;))|sim;|urel;)|s(?:cr;|i;)|uncsp;)|q(?:fr;|int;|opf;|prime;|scr;|u(?:at(?:ernions;|int;)|est(?:;|eq;)|ot;?))|r(?:A(?:arr;|rr;|tail;)|Barr;|Har;|a(?:c(?:e;|ute;)|dic;|emptyv;|ng(?:;|d;|e;|le;)|quo;?|rr(?:;|ap;|b(?:;|fs;)|c;|fs;|hk;|lp;|pl;|sim;|tl;|w;)|t(?:ail;|io(?:;|nals;)))|b(?:arr;|brk;|r(?:ac(?:e;|k;)|k(?:e;|sl(?:d;|u;))))|c(?:aron;|e(?:dil;|il;)|ub;|y;)|d(?:ca;|ldhar;|quo(?:;|r;)|sh;)|e(?:al(?:;|ine;|part;|s;)|ct;|g;?)|f(?:isht;|loor;|r;)|h(?:ar(?:d;|u(?:;|l;))|o(?:;|v;))|i(?:ght(?:arrow(?:;|tail;)|harpoon(?:down;|up;)|left(?:arrows;|harpoons;)|rightarrows;|squigarrow;|threetimes;)|ng;|singdotseq;)|l(?:arr;|har;|m;)|moust(?:;|ache;)|nmid;|o(?:a(?:ng;|rr;)|brk;|p(?:ar;|f;|lus;)|times;)|p(?:ar(?:;|gt;)|polint;)|rarr;|s(?:aquo;|cr;|h;|q(?:b;|uo(?:;|r;)))|t(?:hree;|imes;|ri(?:;|e;|f;|ltri;))|uluhar;|x;)|s(?:acute;|bquo;|c(?:;|E;|a(?:p;|ron;)|cue;|e(?:;|dil;)|irc;|n(?:E;|ap;|sim;)|polint;|sim;|y;)|dot(?:;|b;|e;)|e(?:Arr;|ar(?:hk;|r(?:;|ow;))|ct;?|mi;|swar;|tm(?:inus;|n;)|xt;)|fr(?:;|own;)|h(?:arp;|c(?:hcy;|y;)|ort(?:mid;|parallel;)|y;?)|i(?:gma(?:;|f;|v;)|m(?:;|dot;|e(?:;|q;)|g(?:;|E;)|l(?:;|E;)|ne;|plus;|rarr;))|larr;|m(?:a(?:llsetminus;|shp;)|eparsl;|i(?:d;|le;)|t(?:;|e(?:;|s;)))|o(?:ftcy;|l(?:;|b(?:;|ar;))|pf;)|pa(?:des(?:;|uit;)|r;)|q(?:c(?:ap(?:;|s;)|up(?:;|s;))|su(?:b(?:;|e;|set(?:;|eq;))|p(?:;|e;|set(?:;|eq;)))|u(?:;|ar(?:e;|f;)|f;))|rarr;|s(?:cr;|etmn;|mile;|tarf;)|t(?:ar(?:;|f;)|r(?:aight(?:epsilon;|phi;)|ns;))|u(?:b(?:;|E;|dot;|e(?:;|dot;)|mult;|n(?:E;|e;)|plus;|rarr;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;)))|cc(?:;|approx;|curlyeq;|eq;|n(?:approx;|eqq;|sim;)|sim;)|m;|ng;|p(?:1;?|2;?|3;?|;|E;|d(?:ot;|sub;)|e(?:;|dot;)|hs(?:ol;|ub;)|larr;|mult;|n(?:E;|e;)|plus;|s(?:et(?:;|eq(?:;|q;)|neq(?:;|q;))|im;|u(?:b;|p;))))|w(?:Arr;|ar(?:hk;|r(?:;|ow;))|nwar;)|zlig;?)|t(?:a(?:rget;|u;)|brk;|c(?:aron;|edil;|y;)|dot;|elrec;|fr;|h(?:e(?:re(?:4;|fore;)|ta(?:;|sym;|v;))|i(?:ck(?:approx;|sim;)|nsp;)|k(?:ap;|sim;)|orn;?)|i(?:lde;|mes(?:;|b(?:;|ar;)|d;|)|nt;)|o(?:ea;|p(?:;|bot;|cir;|f(?:;|ork;))|sa;)|prime;|r(?:ade;|i(?:angle(?:;|down;|left(?:;|eq;)|q;|right(?:;|eq;))|dot;|e;|minus;|plus;|sb;|time;)|pezium;)|s(?:c(?:r;|y;)|hcy;|trok;)|w(?:ixt;|ohead(?:leftarrow;|rightarrow;)))|u(?:Arr;|Har;|a(?:cute;?|rr;)|br(?:cy;|eve;)|c(?:irc;?|y;)|d(?:arr;|blac;|har;)|f(?:isht;|r;)|grave;?|h(?:ar(?:l;|r;)|blk;)|l(?:c(?:orn(?:;|er;)|rop;)|tri;)|m(?:acr;|l;?)|o(?:gon;|pf;)|p(?:arrow;|downarrow;|harpoon(?:left;|right;)|lus;|si(?:;|h;|lon;)|uparrows;)|r(?:c(?:orn(?:;|er;)|rop;)|ing;|tri;)|scr;|t(?:dot;|ilde;|ri(?:;|f;))|u(?:arr;|ml;?)|wangle;)|v(?:Arr;|Bar(?:;|v;)|Dash;|a(?:ngrt;|r(?:epsilon;|kappa;|nothing;|p(?:hi;|i;|ropto;)|r(?:;|ho;)|s(?:igma;|u(?:bsetneq(?:;|q;)|psetneq(?:;|q;)))|t(?:heta;|riangle(?:left;|right;))))|cy;|dash;|e(?:e(?:;|bar;|eq;)|llip;|r(?:bar;|t;))|fr;|ltri;|nsu(?:b;|p;)|opf;|prop;|rtri;|s(?:cr;|u(?:bn(?:E;|e;)|pn(?:E;|e;)))|zigzag;)|w(?:circ;|e(?:d(?:bar;|ge(?:;|q;))|ierp;)|fr;|opf;|p;|r(?:;|eath;)|scr;)|x(?:c(?:ap;|irc;|up;)|dtri;|fr;|h(?:Arr;|arr;)|i;|l(?:Arr;|arr;)|map;|nis;|o(?:dot;|p(?:f;|lus;)|time;)|r(?:Arr;|arr;)|s(?:cr;|qcup;)|u(?:plus;|tri;)|vee;|wedge;)|y(?:ac(?:ute;?|y;)|c(?:irc;|y;)|en;?|fr;|icy;|opf;|scr;|u(?:cy;|ml;?))|z(?:acute;|c(?:aron;|y;)|dot;|e(?:etrf;|ta;)|fr;|hcy;|igrarr;|opf;|scr;|w(?:j;|nj;)))|[\s\S]/g,q=/[^\r"&\u0000]+/g,H=/[^\r'&\u0000]+/g,B=/[^\r\t\n\f &>\u0000]+/g,P=/[^\r\t\n\f \/>A-Z\u0000]+/g,U=/[^\r\t\n\f \/=>A-Z\u0000]+/g,F=/[^\]\r\u0000\uffff]*/g,V=/[^&<\r\u0000\uffff]*/g,j=/[^<\r\u0000\uffff]*/g,G=/[^\r\u0000\uffff]*/g,z=/(?:(\/)?([a-z]+)>)|[\s\S]/g,W=/(?:([-a-z]+)[ \t\n\f]*=[ \t\n\f]*('[^'&\r\u0000]*'|"[^"&\r\u0000]*"|[^\t\n\r\f "&'\u0000>][^&> \t\n\r\f\u0000]*[ \t\n\f]))|[\s\S]/g,K=/[^\x09\x0A\x0C\x0D\x20]/,X=/[^\x09\x0A\x0C\x0D\x20]/g,Y=/[^\x00\x09\x0A\x0C\x0D\x20]/,Q=/^[\x09\x0A\x0C\x0D\x20]+/,$=/\x00/g;function Z(e){if(e.length<16384)return String.fromCharCode.apply(String,e);for(var t="",r=0;r<e.length;r+=16384)t+=String.fromCharCode.apply(String,e.slice(r,r+16384));return t}function J(e,t){if("string"==typeof t)return e.namespaceURI===s.HTML&&e.localName===t;var r=t[e.namespaceURI];return r&&r[e.localName]}function ee(e){if(J(e,D))return!0;if(e.namespaceURI===s.MATHML&&"annotation-xml"===e.localName){var t=e.getAttribute("encoding");if(t&&(t=t.toLowerCase()),"text/html"===t||"application/xhtml+xml"===t)return!0}return!1}function et(e){for(var t=0,r=e.length;t<r;t++)e[t][0]in x&&(e[t][0]=x[e[t][0]])}function er(e){for(var t=0,r=e.length;t<r;t++)if("definitionurl"===e[t][0]){e[t][0]="definitionURL";break}}function en(e){for(var t=0,r=e.length;t<r;t++)e[t][0]in L&&e[t].push(L[e[t][0]])}function ea(e,t){for(var r=0,n=e.length;r<n;r++){var a=e[r][0],i=e[r][1];t.hasAttribute(a)||t._setAttribute(a,i)}}function ei(e,t,r){var v,E,S,N,k=null,C=0,D=0,L=!1,x=!1,es=0,eo=[],ec="",el=!0,eu=0,eh=tc,ep="",ef="",ed=[],em="",eg="",eb=[],ev=[],eE=[],ey=[],eT=[],e_=!1,ew=function(e,t,r,n){switch(e){case 1:if(0===(t=t.replace(Q,"")).length)return;break;case 4:eB._appendChild(eB.createComment(t));return;case 5:var i=t;eB.appendChild(new a(eB,i,r,n)),eM||"html"!==i.toLowerCase()||h.test(r)||n&&"http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd"===n.toLowerCase()||void 0===n&&p.test(r)?eB._quirks=!0:(f.test(r)||void 0!==n&&p.test(r))&&(eB._limitedQuirks=!0),ew=rS;return}eB._quirks=!0,(ew=rS)(e,t,r,n)},eS=null,eN=[],ek=new ei.ElementStack,eC=new ei.ActiveFormattingElements,eA=void 0!==t,eD=null,eL=null,ex=!0;t&&(ex=t.ownerDocument._scripting_enabled),r&&!1===r.scripting_enabled&&(ex=!1);var eR=!0,eM=!1,eI=[],eO=!1,eq=!1,eH={document:function(){return eB},_asDocumentFragment:function(){for(var e=eB.createDocumentFragment(),t=eB.firstChild;t.hasChildNodes();)e.appendChild(t.firstChild);return e},pause:function(){eu++},resume:function(){eu--,this.parse("")},parse:function(e,t,r){var n;return eu>0?(ec+=e,!0):(0===es?(ec&&(e=ec+e,ec=""),t&&(e+="￿",L=!0),k=e,C=e.length,D=0,el&&(el=!1,65279===k.charCodeAt(0)&&(D=1)),es++,n=eF(r),ec=k.substring(D,C)):(es++,eo.push(k,C,D),k=e,C=e.length,D=0,eF(),n=!1,ec=k.substring(D,C),D=eo.pop(),C=eo.pop(),k=eo.pop(),ec&&(C=(k=ec+k.substring(D)).length,D=0,ec="")),es--,n)}},eB=new n(!0,e);if(eB._parser=eH,eB._scripting_enabled=ex,t){if(t.ownerDocument._quirks&&(eB._quirks=!0),t.ownerDocument._limitedQuirks&&(eB._limitedQuirks=!0),t.namespaceURI===s.HTML)switch(t.localName){case"title":case"textarea":eh=tl;break;case"style":case"xmp":case"iframe":case"noembed":case"noframes":case"script":case"plaintext":eh=tp}var eP=eB.createElement("html");eB._appendChild(eP),ek.push(eP),t instanceof c.HTMLTemplateElement&&eN.push(rP),tt();for(var eU=t;null!==eU;eU=eU.parentElement)if(eU instanceof c.HTMLFormElement){eL=eU;break}}function eF(e){for(var t,r,n,a;D<C;){if(eu>0||e&&e())return!0;switch(typeof eh.lookahead){case"undefined":if(t=k.charCodeAt(D++),x&&(x=!1,10===t)){D++;continue}switch(t){case 13:D<C?10===k.charCodeAt(D)&&D++:x=!0,eh(10);break;case 65535:if(L&&D===C){eh(-1);break}default:eh(t)}break;case"number":t=k.charCodeAt(D);var i=eh.lookahead,s=!0;if(i<0&&(s=!1,i=-i),i<C-D)r=s?k.substring(D,D+i):null,a=!1;else{if(!L)return!0;r=s?k.substring(D,C):null,a=!0,65535===t&&D===C-1&&(t=-1)}eh(t,r,a);break;case"string":t=k.charCodeAt(D),n=eh.lookahead;var o=k.indexOf(n,D);if(-1!==o)r=k.substring(D,o+n.length),a=!1;else{if(!L)return!0;r=k.substring(D,C),65535===t&&D===C-1&&(t=-1),a=!0}eh(t,r,a)}}return!1}function eV(e,t){for(var r=0;r<eT.length;r++)if(eT[r][0]===e)return;void 0!==t?eT.push([e,t]):eT.push([e])}function ej(){e_=!0,ep="",eT.length=0}function eG(){ed.length=0}function ez(){eb.length=0}function eW(){ev.length=0,eE=null,ey=null}function eK(){ey=[]}function eX(){eM=!0}function eY(){if(eI.length>0){var e=Z(eI);if(eI.length=0,eq&&(eq=!1,"\n"===e[0]&&(e=e.substring(1)),0===e.length))return;e1(1,e),eO=!1}eq=!1}function eQ(e){e.lastIndex=D-1;var t=e.exec(k);if(t&&t.index===D-1)return t=t[0],D+=t.length-1,L&&D===C&&(t=t.slice(0,-1),D--),t;throw Error("should never happen")}function e$(e){e.lastIndex=D-1;var t,r=e.exec(k)[0];return!!r&&(t=r,eI.length>0&&eY(),eq&&(eq=!1,"\n"===t[0]&&(t=t.substring(1)),0===t.length)||e1(1,t),D+=r.length-1,!0)}function eZ(){if(e_)e1(3,ep);else{var e=ep;ep="",ef=e,e1(2,e,eT)}}function eJ(){e1(5,Z(ev),eE?Z(eE):void 0,ey?Z(ey):void 0)}function e0(){eY(),ew(-1),eB.modclock=1}var e1=eH.insertToken=function(e,t,r,n){eY();var a=ek.top;a&&a.namespaceURI!==s.HTML?2!==e&&1!==e?rz(e,t,r,n):J(a,A)&&(1===e||2===e&&"mglyph"!==t&&"malignmark"!==t)||2===e&&"svg"===t&&a.namespaceURI===s.MATHML&&"annotation-xml"===a.localName||ee(a)?(N=!0,ew(e,t,r,n),N=!1):rz(e,t,r,n):ew(e,t,r,n)};function e8(e){var t=ek.top;e6&&J(t,b)?te(function(t){return t.createComment(e)}):(t instanceof c.HTMLTemplateElement&&(t=t.content),t._appendChild(t.ownerDocument.createComment(e)))}function e5(e){var t=ek.top;if(e6&&J(t,b))te(function(t){return t.createTextNode(e)});else{t instanceof c.HTMLTemplateElement&&(t=t.content);var r=t.lastChild;r&&r.nodeType===i.TEXT_NODE?r.appendData(e):t._appendChild(t.ownerDocument.createTextNode(e))}}function e9(e,t,r){var n=o.createElement(e,t,null);if(r)for(var a=0,i=r.length;a<i;a++)n._setAttribute(r[a][0],r[a][1]);return n}var e6=!1;function e2(e,t){var r=e7(function(r){return e9(r,e,t)});return J(r,w)&&(r._form=eL),r}function e7(e){var t;return e6&&J(ek.top,b)?t=te(e):ek.top instanceof c.HTMLTemplateElement?(t=e(ek.top.content.ownerDocument),ek.top.content._appendChild(t)):(t=e(ek.top.ownerDocument),ek.top._appendChild(t)),ek.push(t),t}function e3(e,t,r){return e7(function(n){var a=n._createElementNS(e,r,null);if(t)for(var i=0,s=t.length;i<s;i++){var o=t[i];2===o.length?a._setAttribute(o[0],o[1]):a._setAttributeNS(o[2],o[0],o[1])}return a})}function e4(e){for(var t=ek.elements.length-1;t>=0;t--)if(ek.elements[t]instanceof e)return t;return -1}function te(e){var t,r,n,a,s=-1,o=-1;return(s=e4(c.HTMLTableElement),(o=e4(c.HTMLTemplateElement))>=0&&(s<0||o>s)?r=ek.elements[o]:s>=0&&((r=ek.elements[s].parentNode)?n=ek.elements[s]:r=ek.elements[s-1]),r||(r=ek.elements[0]),r instanceof c.HTMLTemplateElement&&(r=r.content),(a=e(r.ownerDocument)).nodeType===i.TEXT_NODE&&(t=n?n.previousSibling:r.lastChild)&&t.nodeType===i.TEXT_NODE)?(t.appendData(a.data),a):(n?r.insertBefore(a,n):r._appendChild(a),a)}function tt(){for(var e=!1,r=ek.elements.length-1;r>=0;r--){var n=ek.elements[r];if(0===r&&(e=!0,eA&&(n=t)),n.namespaceURI===s.HTML){var a=n.localName;switch(a){case"select":for(var i=r;i>0;){var o=ek.elements[--i];if(o instanceof c.HTMLTemplateElement)break;if(o instanceof c.HTMLTableElement){ew=rB;return}}ew=rH;return;case"tr":ew=rO;return;case"tbody":case"tfoot":case"thead":ew=rI;return;case"caption":ew=rR;return;case"colgroup":ew=rM;return;case"table":ew=rL;return;case"template":ew=eN[eN.length-1];return;case"body":ew=rA;return;case"frameset":ew=rF;return;case"html":ew=null===eD?rN:rC;return;default:if(!e){if("head"===a){ew=rk;return}if("td"===a||"th"===a){ew=rq;return}}}}if(e){ew=rA;return}}}function tr(e,t){e2(e,t),eh=tu,eS=ew,ew=rD}function tn(e,t){return{elt:e9(e,eC.list[t].localName,eC.attrs[t]),attrs:eC.attrs[t]}}function ta(){if(0!==eC.list.length){var e=eC.list[eC.list.length-1];if(e!==eC.MARKER&&-1===ek.elements.lastIndexOf(e)){for(var t=eC.list.length-2;t>=0&&(e=eC.list[t])!==eC.MARKER&&-1===ek.elements.lastIndexOf(e);t--);for(t+=1;t<eC.list.length;t++){var r=e7(function(e){return tn(e,t).elt});eC.list[t]=r}}}}var ti={localName:"BM"};function ts(){delete eB._parser,ek.elements.length=0,eB.defaultView&&eB.defaultView.dispatchEvent(new c.Event("load",{}))}function to(e,t){eh=t,D--}function tc(e){switch(e){case 38:v=tc,eh=rm;break;case 60:if(function(){if(D===C)return!1;z.lastIndex=D;var e=z.exec(k);if(!e)throw Error("should never happen");var t=e[2];return!!t&&(e[1]?(D+=t.length+2,e1(3,t)):(D+=t.length+1,ef=t,e1(2,t,u)),!0)}())break;eh=tf;break;case 0:eI.push(e),eO=!0;break;case -1:e0();break;default:e$(V)||eI.push(e)}}function tl(e){switch(e){case 38:v=tl,eh=rm;break;case 60:eh=tg;break;case 0:eI.push(65533),eO=!0;break;case -1:e0();break;default:eI.push(e)}}function tu(e){switch(e){case 60:eh=tE;break;case 0:eI.push(65533);break;case -1:e0();break;default:e$(j)||eI.push(e)}}function th(e){switch(e){case 60:eh=t_;break;case 0:eI.push(65533);break;case -1:e0();break;default:e$(j)||eI.push(e)}}function tp(e){switch(e){case 0:eI.push(65533);break;case -1:e0();break;default:e$(G)||eI.push(e)}}function tf(e){switch(e){case 33:eh=tY;break;case 47:eh=td;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:e_=!1,ep="",eT.length=0,to(e,tm);break;case 63:to(e,tX);break;default:eI.push(60),to(e,tc)}}function td(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ej(),to(e,tm);break;case 62:eh=tc;break;case -1:eI.push(60),eI.push(47),e0();break;default:to(e,tX)}}function tm(e){switch(e){case 9:case 10:case 12:case 32:eh=tP;break;case 47:eh=tK;break;case 62:eh=tc,eZ();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ep+=String.fromCharCode(e+32);break;case 0:ep+=String.fromCharCode(65533);break;case -1:e0();break;default:ep+=eQ(P)}}function tg(e){47===e?(eG(),eh=tb):(eI.push(60),to(e,tl))}function tb(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ej(),to(e,tv);break;default:eI.push(60),eI.push(47),to(e,tl)}}function tv(e){switch(e){case 9:case 10:case 12:case 32:if(ef===ep){eh=tP;return}break;case 47:if(ef===ep){eh=tK;return}break;case 62:if(ef===ep){eh=tc,eZ();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ep+=String.fromCharCode(e+32),ed.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ep+=String.fromCharCode(e),ed.push(e);return}eI.push(60),eI.push(47),l(eI,ed),to(e,tl)}function tE(e){47===e?(eG(),eh=ty):(eI.push(60),to(e,tu))}function ty(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ej(),to(e,tT);break;default:eI.push(60),eI.push(47),to(e,tu)}}function tT(e){switch(e){case 9:case 10:case 12:case 32:if(ef===ep){eh=tP;return}break;case 47:if(ef===ep){eh=tK;return}break;case 62:if(ef===ep){eh=tc,eZ();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ep+=String.fromCharCode(e+32),ed.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ep+=String.fromCharCode(e),ed.push(e);return}eI.push(60),eI.push(47),l(eI,ed),to(e,tu)}function t_(e){switch(e){case 47:eG(),eh=tw;break;case 33:eh=tN,eI.push(60),eI.push(33);break;default:eI.push(60),to(e,th)}}function tw(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ej(),to(e,tS);break;default:eI.push(60),eI.push(47),to(e,th)}}function tS(e){switch(e){case 9:case 10:case 12:case 32:if(ef===ep){eh=tP;return}break;case 47:if(ef===ep){eh=tK;return}break;case 62:if(ef===ep){eh=tc,eZ();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ep+=String.fromCharCode(e+32),ed.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ep+=String.fromCharCode(e),ed.push(e);return}eI.push(60),eI.push(47),l(eI,ed),to(e,th)}function tN(e){45===e?(eh=tk,eI.push(45)):to(e,th)}function tk(e){45===e?(eh=tD,eI.push(45)):to(e,th)}function tC(e){switch(e){case 45:eh=tA,eI.push(45);break;case 60:eh=tL;break;case 0:eI.push(65533);break;case -1:e0();break;default:eI.push(e)}}function tA(e){switch(e){case 45:eh=tD,eI.push(45);break;case 60:eh=tL;break;case 0:eh=tC,eI.push(65533);break;case -1:e0();break;default:eh=tC,eI.push(e)}}function tD(e){switch(e){case 45:eI.push(45);break;case 60:eh=tL;break;case 62:eh=th,eI.push(62);break;case 0:eh=tC,eI.push(65533);break;case -1:e0();break;default:eh=tC,eI.push(e)}}function tL(e){switch(e){case 47:eG(),eh=tx;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:eG(),eI.push(60),to(e,tM);break;default:eI.push(60),to(e,tC)}}function tx(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ej(),to(e,tR);break;default:eI.push(60),eI.push(47),to(e,tC)}}function tR(e){switch(e){case 9:case 10:case 12:case 32:if(ef===ep){eh=tP;return}break;case 47:if(ef===ep){eh=tK;return}break;case 62:if(ef===ep){eh=tc,eZ();return}break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ep+=String.fromCharCode(e+32),ed.push(e);return;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ep+=String.fromCharCode(e),ed.push(e);return}eI.push(60),eI.push(47),l(eI,ed),to(e,tC)}function tM(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:eh="script"===Z(ed)?tI:tC,eI.push(e);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ed.push(e+32),eI.push(e);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ed.push(e),eI.push(e);break;default:to(e,tC)}}function tI(e){switch(e){case 45:eh=tO,eI.push(45);break;case 60:eh=tH,eI.push(60);break;case 0:eI.push(65533);break;case -1:e0();break;default:eI.push(e)}}function tO(e){switch(e){case 45:eh=tq,eI.push(45);break;case 60:eh=tH,eI.push(60);break;case 0:eh=tI,eI.push(65533);break;case -1:e0();break;default:eh=tI,eI.push(e)}}function tq(e){switch(e){case 45:eI.push(45);break;case 60:eh=tH,eI.push(60);break;case 62:eh=th,eI.push(62);break;case 0:eh=tI,eI.push(65533);break;case -1:e0();break;default:eh=tI,eI.push(e)}}function tH(e){47===e?(eG(),eh=tB,eI.push(47)):to(e,tI)}function tB(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:eh="script"===Z(ed)?tC:tI,eI.push(e);break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ed.push(e+32),eI.push(e);break;case 97:case 98:case 99:case 100:case 101:case 102:case 103:case 104:case 105:case 106:case 107:case 108:case 109:case 110:case 111:case 112:case 113:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:ed.push(e),eI.push(e);break;default:to(e,tI)}}function tP(e){switch(e){case 9:case 10:case 12:case 32:break;case 47:eh=tK;break;case 62:eh=tc,eZ();break;case -1:e0();break;case 61:em=""+String.fromCharCode(e),eh=tU;break;default:if(function(){W.lastIndex=D-1;var e=W.exec(k);if(!e)throw Error("should never happen");var t=e[1];if(!t)return!1;var r=e[2],n=r.length;switch(r[0]){case'"':case"'":r=r.substring(1,n-1),D+=e[0].length-1,eh=tW;break;default:eh=tP,D+=e[0].length-1,r=r.substring(0,n-1)}for(var a=0;a<eT.length;a++)if(eT[a][0]===t)return!0;return eT.push([t,r]),!0}())break;em="",to(e,tU)}}function tU(e){switch(e){case 9:case 10:case 12:case 32:case 47:case 62:case -1:to(e,tF);break;case 61:eh=tV;break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:em+=String.fromCharCode(e+32);break;case 0:em+=String.fromCharCode(65533);break;default:em+=eQ(U)}}function tF(e){switch(e){case 9:case 10:case 12:case 32:break;case 47:eV(em),eh=tK;break;case 61:eh=tV;break;case 62:eh=tc,eV(em),eZ();break;case -1:eV(em),e0();break;default:eV(em),em="",to(e,tU)}}function tV(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:eg="",eh=tj;break;case 39:eg="",eh=tG;break;default:eg="",to(e,tz)}}function tj(e){switch(e){case 34:eV(em,eg),eh=tW;break;case 38:v=tj,eh=rm;break;case 0:eg+=String.fromCharCode(65533);break;case -1:e0();break;case 10:eg+=String.fromCharCode(e);break;default:eg+=eQ(q)}}function tG(e){switch(e){case 39:eV(em,eg),eh=tW;break;case 38:v=tG,eh=rm;break;case 0:eg+=String.fromCharCode(65533);break;case -1:e0();break;case 10:eg+=String.fromCharCode(e);break;default:eg+=eQ(H)}}function tz(e){switch(e){case 9:case 10:case 12:case 32:eV(em,eg),eh=tP;break;case 38:v=tz,eh=rm;break;case 62:eV(em,eg),eh=tc,eZ();break;case 0:eg+=String.fromCharCode(65533);break;case -1:D--,eh=tc;break;default:eg+=eQ(B)}}function tW(e){switch(e){case 9:case 10:case 12:case 32:eh=tP;break;case 47:eh=tK;break;case 62:eh=tc,eZ();break;case -1:e0();break;default:to(e,tP)}}function tK(e){switch(e){case 62:eh=tc,e_?e1(3,ep,null,!0):e1(2,ep,eT,!0);break;case -1:e0();break;default:to(e,tP)}}function tX(e,t,r){var n=t.length;r?D+=n-1:D+=n;var a=t.substring(0,n-1);a=(a=(a=a.replace(/\u0000/g,"�")).replace(/\u000D\u000A/g,"\n")).replace(/\u000D/g,"\n"),e1(4,a),eh=tc}function tY(e,t,r){if("-"===t[0]&&"-"===t[1]){D+=2,ez(),eh=tQ;return}"DOCTYPE"===t.toUpperCase()?(D+=7,eh=t2):"[CDATA["===t&&ek.top&&"http://www.w3.org/1999/xhtml"!==ek.top.namespaceURI?(D+=7,eh=rp):eh=tX}function tQ(e){switch(ez(),e){case 45:eh=t$;break;case 62:eh=tc,e1(4,Z(eb));break;default:to(e,tZ)}}function t$(e){switch(e){case 45:eh=t9;break;case 62:eh=tc,e1(4,Z(eb));break;case -1:e1(4,Z(eb)),e0();break;default:eb.push(45),to(e,tZ)}}function tZ(e){switch(e){case 60:eb.push(e),eh=tJ;break;case 45:eh=t5;break;case 0:eb.push(65533);break;case -1:e1(4,Z(eb)),e0();break;default:eb.push(e)}}function tJ(e){switch(e){case 33:eb.push(e),eh=t0;break;case 60:eb.push(e);break;default:to(e,tZ)}}function t0(e){45===e?eh=t1:to(e,tZ)}function t1(e){45===e?eh=t8:to(e,t5)}function t8(e){to(e,t9)}function t5(e){switch(e){case 45:eh=t9;break;case -1:e1(4,Z(eb)),e0();break;default:eb.push(45),to(e,tZ)}}function t9(e){switch(e){case 62:eh=tc,e1(4,Z(eb));break;case 33:eh=t6;break;case 45:eb.push(45);break;case -1:e1(4,Z(eb)),e0();break;default:eb.push(45),eb.push(45),to(e,tZ)}}function t6(e){switch(e){case 45:eb.push(45),eb.push(45),eb.push(33),eh=t5;break;case 62:eh=tc,e1(4,Z(eb));break;case -1:e1(4,Z(eb)),e0();break;default:eb.push(45),eb.push(45),eb.push(33),to(e,tZ)}}function t2(e){switch(e){case 9:case 10:case 12:case 32:eh=t7;break;case -1:eW(),eX(),eJ(),e0();break;default:to(e,t7)}}function t7(e){switch(e){case 9:case 10:case 12:case 32:break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:eW(),ev.push(e+32),eh=t3;break;case 0:eW(),ev.push(65533),eh=t3;break;case 62:eW(),eX(),eh=tc,eJ();break;case -1:eW(),eX(),eJ(),e0();break;default:eW(),ev.push(e),eh=t3}}function t3(e){switch(e){case 9:case 10:case 12:case 32:eh=t4;break;case 62:eh=tc,eJ();break;case 65:case 66:case 67:case 68:case 69:case 70:case 71:case 72:case 73:case 74:case 75:case 76:case 77:case 78:case 79:case 80:case 81:case 82:case 83:case 84:case 85:case 86:case 87:case 88:case 89:case 90:ev.push(e+32);break;case 0:ev.push(65533);break;case -1:eX(),eJ(),e0();break;default:ev.push(e)}}function t4(e,t,r){switch(e){case 9:case 10:case 12:case 32:D+=1;break;case 62:eh=tc,D+=1,eJ();break;case -1:eX(),eJ(),e0();break;default:"PUBLIC"===(t=t.toUpperCase())?(D+=6,eh=re):"SYSTEM"===t?(D+=6,eh=rs):(eX(),eh=rh)}}function re(e){switch(e){case 9:case 10:case 12:case 32:eh=rt;break;case 34:eE=[],eh=rr;break;case 39:eE=[],eh=rn;break;case 62:eX(),eh=tc,eJ();break;case -1:eX(),eJ(),e0();break;default:eX(),eh=rh}}function rt(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:eE=[],eh=rr;break;case 39:eE=[],eh=rn;break;case 62:eX(),eh=tc,eJ();break;case -1:eX(),eJ(),e0();break;default:eX(),eh=rh}}function rr(e){switch(e){case 34:eh=ra;break;case 0:eE.push(65533);break;case 62:eX(),eh=tc,eJ();break;case -1:eX(),eJ(),e0();break;default:eE.push(e)}}function rn(e){switch(e){case 39:eh=ra;break;case 0:eE.push(65533);break;case 62:eX(),eh=tc,eJ();break;case -1:eX(),eJ(),e0();break;default:eE.push(e)}}function ra(e){switch(e){case 9:case 10:case 12:case 32:eh=ri;break;case 62:eh=tc,eJ();break;case 34:eK(),eh=rc;break;case 39:eK(),eh=rl;break;case -1:eX(),eJ(),e0();break;default:eX(),eh=rh}}function ri(e){switch(e){case 9:case 10:case 12:case 32:break;case 62:eh=tc,eJ();break;case 34:eK(),eh=rc;break;case 39:eK(),eh=rl;break;case -1:eX(),eJ(),e0();break;default:eX(),eh=rh}}function rs(e){switch(e){case 9:case 10:case 12:case 32:eh=ro;break;case 34:eK(),eh=rc;break;case 39:eK(),eh=rl;break;case 62:eX(),eh=tc,eJ();break;case -1:eX(),eJ(),e0();break;default:eX(),eh=rh}}function ro(e){switch(e){case 9:case 10:case 12:case 32:break;case 34:eK(),eh=rc;break;case 39:eK(),eh=rl;break;case 62:eX(),eh=tc,eJ();break;case -1:eX(),eJ(),e0();break;default:eX(),eh=rh}}function rc(e){switch(e){case 34:eh=ru;break;case 0:ey.push(65533);break;case 62:eX(),eh=tc,eJ();break;case -1:eX(),eJ(),e0();break;default:ey.push(e)}}function rl(e){switch(e){case 39:eh=ru;break;case 0:ey.push(65533);break;case 62:eX(),eh=tc,eJ();break;case -1:eX(),eJ(),e0();break;default:ey.push(e)}}function ru(e){switch(e){case 9:case 10:case 12:case 32:break;case 62:eh=tc,eJ();break;case -1:eX(),eJ(),e0();break;default:eh=rh}}function rh(e){switch(e){case 62:eh=tc,eJ();break;case -1:eJ(),e0()}}function rp(e){switch(e){case 93:eh=rf;break;case -1:e0();break;case 0:eO=!0;default:e$(F)||eI.push(e)}}function rf(e){93===e?eh=rd:(eI.push(93),to(e,rp))}function rd(e){switch(e){case 93:eI.push(93);break;case 62:eY(),eh=tc;break;default:eI.push(93),eI.push(93),to(e,rp)}}function rm(e){switch(eG(),ed.push(38),e){case 9:case 10:case 12:case 32:case 60:case 38:case -1:to(e,rw);break;case 35:ed.push(e),eh=rb;break;default:to(e,rg)}}function rg(e){O.lastIndex=D;var t=O.exec(k);if(!t)throw Error("should never happen");var r=t[1];if(!r){eh=rw;return}switch(D+=r.length,l(ed,function(e){for(var t=[],r=0;r<e.length;r++)t[r]=e.charCodeAt(r);return t}(r)),v){case tj:case tG:case tz:if(";"!==r[r.length-1]&&/[=A-Za-z0-9]/.test(k[D])){eh=rw;return}}eG();var n=I[r];"number"==typeof n?ed.push(n):l(ed,n),eh=rw}function rb(e){switch(E=0,e){case 120:case 88:ed.push(e),eh=rv;break;default:to(e,rE)}}function rv(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:case 65:case 66:case 67:case 68:case 69:case 70:case 97:case 98:case 99:case 100:case 101:case 102:to(e,ry);break;default:to(e,rw)}}function rE(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:to(e,rT);break;default:to(e,rw)}}function ry(e){switch(e){case 65:case 66:case 67:case 68:case 69:case 70:E*=16,E+=e-55;break;case 97:case 98:case 99:case 100:case 101:case 102:E*=16,E+=e-87;break;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:E*=16,E+=e-48;break;case 59:eh=r_;break;default:to(e,r_)}}function rT(e){switch(e){case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:E*=10,E+=e-48;break;case 59:eh=r_;break;default:to(e,r_)}}function r_(e){E in M?E=M[E]:(E>1114111||E>=55296&&E<57344)&&(E=65533),eG(),E<=65535?ed.push(E):(E-=65536,ed.push(55296+(E>>10)),ed.push(56320+(1023&E))),to(e,rw)}function rw(e){switch(v){case tj:case tG:case tz:eg+=Z(ed);break;default:l(eI,ed)}to(e,v)}function rS(e,t,r,n){var a;switch(e){case 1:if(0===(t=t.replace(Q,"")).length)return;break;case 5:return;case 4:eB._appendChild(eB.createComment(t));return;case 2:if("html"===t){a=e9(eB,t,r),ek.push(a),eB.appendChild(a),ew=rN;return}break;case 3:switch(t){case"html":case"head":case"body":case"br":break;default:return}}a=e9(eB,"html",null),ek.push(a),eB.appendChild(a),(ew=rN)(e,t,r,n)}function rN(e,t,r,n){switch(e){case 1:if(0===(t=t.replace(Q,"")).length)return;break;case 5:return;case 4:e8(t);return;case 2:switch(t){case"html":rA(e,t,r,n);return;case"head":eD=e2(t,r),ew=rk;return}break;case 3:switch(t){case"html":case"head":case"body":case"br":break;default:return}}rN(2,"head",null),ew(e,t,r,n)}function rk(e,t,r,n){switch(e){case 1:var a=t.match(Q);if(a&&(e5(a[0]),t=t.substring(a[0].length)),0===t.length)return;break;case 4:e8(t);return;case 5:return;case 2:switch(t){case"html":rA(e,t,r,n);return;case"meta":case"base":case"basefont":case"bgsound":case"link":e2(t,r),ek.pop();return;case"title":e2(t,r),eh=tl,eS=ew,ew=rD;return;case"noscript":if(!ex){e2(t,r),ew=function e(t,r,n,a){switch(t){case 5:return;case 4:rk(t,r);return;case 1:var i=r.match(Q);if(i&&(rk(t,i[0]),r=r.substring(i[0].length)),0===r.length)return;break;case 2:switch(r){case"html":rA(t,r,n,a);return;case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"style":rk(t,r,n);return;case"head":case"noscript":return}break;case 3:switch(r){case"noscript":ek.pop(),ew=rk;return;case"br":break;default:return}}e(3,"noscript",null),ew(t,r,n,a)};return}case"noframes":case"style":tr(t,r);return;case"script":e7(function(e){var n=e9(e,t,r);return n._parser_inserted=!0,n._force_async=!1,eA&&(n._already_started=!0),eY(),n}),eh=th,eS=ew,ew=rD;return;case"template":e2(t,r),eC.insertMarker(),eR=!1,ew=rP,eN.push(ew);return;case"head":return}break;case 3:switch(t){case"head":ek.pop(),ew=rC;return;case"body":case"html":case"br":break;case"template":if(!ek.contains("template"))return;ek.generateImpliedEndTags(null,"thorough"),ek.popTag("template"),eC.clearToMarker(),eN.pop(),tt();return;default:return}}rk(3,"head",null),ew(e,t,r,n)}function rC(e,t,r,n){switch(e){case 1:var a=t.match(Q);if(a&&(e5(a[0]),t=t.substring(a[0].length)),0===t.length)return;break;case 4:e8(t);return;case 5:return;case 2:switch(t){case"html":rA(e,t,r,n);return;case"body":e2(t,r),eR=!1,ew=rA;return;case"frameset":e2(t,r),ew=rF;return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":ek.push(eD),rk(2,t,r),ek.removeElement(eD);return;case"head":return}break;case 3:switch(t){case"template":return rk(e,t,r,n);case"body":case"html":case"br":break;default:return}}rC(2,"body",null),eR=!0,ew(e,t,r,n)}function rA(e,t,r,n){var a,i,o,l;switch(e){case 1:if(eO&&0===(t=t.replace($,"")).length)return;eR&&K.test(t)&&(eR=!1),ta(),e5(t);return;case 5:return;case 4:e8(t);return;case -1:if(eN.length)return rP(e);ts();return;case 2:switch(t){case"html":if(ek.contains("template"))return;ea(r,ek.elements[0]);return;case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":rk(2,t,r);return;case"body":if(!(a=ek.elements[1])||!(a instanceof c.HTMLBodyElement)||ek.contains("template"))return;eR=!1,ea(r,a);return;case"frameset":if(!eR||!(a=ek.elements[1])||!(a instanceof c.HTMLBodyElement))return;for(a.parentNode&&a.parentNode.removeChild(a);!(ek.top instanceof c.HTMLHtmlElement);)ek.pop();e2(t,r),ew=rF;return;case"address":case"article":case"aside":case"blockquote":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"main":case"nav":case"ol":case"p":case"section":case"summary":case"ul":ek.inButtonScope("p")&&rA(3,"p"),e2(t,r);return;case"menu":ek.inButtonScope("p")&&rA(3,"p"),J(ek.top,"menuitem")&&ek.pop(),e2(t,r);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":ek.inButtonScope("p")&&rA(3,"p"),ek.top instanceof c.HTMLHeadingElement&&ek.pop(),e2(t,r);return;case"pre":case"listing":ek.inButtonScope("p")&&rA(3,"p"),e2(t,r),eq=!0,eR=!1;return;case"form":if(eL&&!ek.contains("template"))return;ek.inButtonScope("p")&&rA(3,"p"),l=e2(t,r),ek.contains("template")||(eL=l);return;case"li":for(eR=!1,i=ek.elements.length-1;i>=0;i--){if((o=ek.elements[i])instanceof c.HTMLLIElement){rA(3,"li");break}if(J(o,d)&&!J(o,m))break}ek.inButtonScope("p")&&rA(3,"p"),e2(t,r);return;case"dd":case"dt":for(eR=!1,i=ek.elements.length-1;i>=0;i--){if(J(o=ek.elements[i],g)){rA(3,o.localName);break}if(J(o,d)&&!J(o,m))break}ek.inButtonScope("p")&&rA(3,"p"),e2(t,r);return;case"plaintext":ek.inButtonScope("p")&&rA(3,"p"),e2(t,r),eh=tp;return;case"button":ek.inScope("button")?(rA(3,"button"),ew(e,t,r,n)):(ta(),e2(t,r),eR=!1);return;case"a":var u=eC.findElementByTag("a");u&&(rA(3,t),eC.remove(u),ek.removeElement(u));case"b":case"big":case"code":case"em":case"font":case"i":case"s":case"small":case"strike":case"strong":case"tt":case"u":ta(),eC.push(e2(t,r),r);return;case"nobr":ta(),ek.inScope(t)&&(rA(3,t),ta()),eC.push(e2(t,r),r);return;case"applet":case"marquee":case"object":ta(),e2(t,r),eC.insertMarker(),eR=!1;return;case"table":!eB._quirks&&ek.inButtonScope("p")&&rA(3,"p"),e2(t,r),eR=!1,ew=rL;return;case"area":case"br":case"embed":case"img":case"keygen":case"wbr":ta(),e2(t,r),ek.pop(),eR=!1;return;case"input":ta(),l=e2(t,r),ek.pop();var h=l.getAttribute("type");h&&"hidden"===h.toLowerCase()||(eR=!1);return;case"param":case"source":case"track":e2(t,r),ek.pop();return;case"hr":ek.inButtonScope("p")&&rA(3,"p"),J(ek.top,"menuitem")&&ek.pop(),e2(t,r),ek.pop(),eR=!1;return;case"image":rA(2,"img",r,n);return;case"textarea":e2(t,r),eq=!0,eR=!1,eh=tl,eS=ew,ew=rD;return;case"xmp":ek.inButtonScope("p")&&rA(3,"p"),ta(),eR=!1,tr(t,r);return;case"iframe":eR=!1,tr(t,r);return;case"noembed":tr(t,r);return;case"select":ta(),e2(t,r),eR=!1,ew=ew===rL||ew===rR||ew===rI||ew===rO||ew===rq?rB:rH;return;case"optgroup":case"option":ek.top instanceof c.HTMLOptionElement&&rA(3,"option"),ta(),e2(t,r);return;case"menuitem":J(ek.top,"menuitem")&&ek.pop(),ta(),e2(t,r);return;case"rb":case"rtc":ek.inScope("ruby")&&ek.generateImpliedEndTags(),e2(t,r);return;case"rp":case"rt":ek.inScope("ruby")&&ek.generateImpliedEndTags("rtc"),e2(t,r);return;case"math":ta(),er(r),en(r),e3(t,r,s.MATHML),n&&ek.pop();return;case"svg":ta(),et(r),en(r),e3(t,r,s.SVG),n&&ek.pop();return;case"caption":case"col":case"colgroup":case"frame":case"head":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}ta(),e2(t,r);return;case 3:switch(t){case"template":rk(3,t,r);return;case"body":if(!ek.inScope("body"))return;ew=rU;return;case"html":if(!ek.inScope("body"))return;(ew=rU)(e,t,r);return;case"address":case"article":case"aside":case"blockquote":case"button":case"center":case"details":case"dialog":case"dir":case"div":case"dl":case"fieldset":case"figcaption":case"figure":case"footer":case"header":case"hgroup":case"listing":case"main":case"menu":case"nav":case"ol":case"pre":case"section":case"summary":case"ul":if(!ek.inScope(t))return;ek.generateImpliedEndTags(),ek.popTag(t);return;case"form":if(ek.contains("template")){if(!ek.inScope("form"))return;ek.generateImpliedEndTags(),ek.popTag("form")}else{var p=eL;if(eL=null,!p||!ek.elementInScope(p))return;ek.generateImpliedEndTags(),ek.removeElement(p)}return;case"p":ek.inButtonScope(t)?(ek.generateImpliedEndTags(t),ek.popTag(t)):(rA(2,t,null),ew(e,t,r,n));return;case"li":if(!ek.inListItemScope(t))return;ek.generateImpliedEndTags(t),ek.popTag(t);return;case"dd":case"dt":if(!ek.inScope(t))return;ek.generateImpliedEndTags(t),ek.popTag(t);return;case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":if(!ek.elementTypeInScope(c.HTMLHeadingElement))return;ek.generateImpliedEndTags(),ek.popElementType(c.HTMLHeadingElement);return;case"sarcasm":break;case"a":case"b":case"big":case"code":case"em":case"font":case"i":case"nobr":case"s":case"small":case"strike":case"strong":case"tt":case"u":if(function(e){if(J(ek.top,e)&&-1===eC.indexOf(ek.top))return ek.pop(),!0;for(var t=0;t<8;){t++;var r=eC.findElementByTag(e);if(!r)return!1;var n=ek.elements.lastIndexOf(r);if(-1===n){eC.remove(r);break}if(!ek.elementInScope(r))break;for(var a,i=null,s=n+1;s<ek.elements.length;s++)if(J(ek.elements[s],d)){i=ek.elements[s],a=s;break}if(i){var o,l=ek.elements[n-1];eC.insertAfter(r,ti);for(var u=i,h=i,p=a,f=0;f++,(u=ek.elements[--p])!==r;){if(o=eC.indexOf(u),f>3&&-1!==o&&(eC.remove(u),o=-1),-1===o){ek.removeElement(u);continue}var m=tn(l.ownerDocument,o);eC.replace(u,m.elt,m.attrs),ek.elements[p]=m.elt,u=m.elt,h===i&&(eC.remove(ti),eC.insertAfter(m.elt,ti)),u._appendChild(h),h=u}e6&&J(l,b)?te(function(){return h}):l instanceof c.HTMLTemplateElement?l.content._appendChild(h):l._appendChild(h);for(var g=tn(i.ownerDocument,eC.indexOf(r));i.hasChildNodes();)g.elt._appendChild(i.firstChild);i._appendChild(g.elt),eC.remove(r),eC.replace(ti,g.elt,g.attrs),ek.removeElement(r);var v=ek.elements.lastIndexOf(i);ek.elements.splice(v+1,0,g.elt)}else{ek.popElement(r),eC.remove(r);break}}return!0}(t))return;break;case"applet":case"marquee":case"object":if(!ek.inScope(t))return;ek.generateImpliedEndTags(),ek.popTag(t),eC.clearToMarker();return;case"br":rA(2,t,null);return}for(i=ek.elements.length-1;i>=0;i--){if(J(o=ek.elements[i],t)){ek.generateImpliedEndTags(t),ek.popElement(o);break}if(J(o,d))break}return}}function rD(e,t,r,n){switch(e){case 1:e5(t);return;case -1:ek.top instanceof c.HTMLScriptElement&&(ek.top._already_started=!0),ek.pop(),(ew=eS)(e);return;case 3:ek.pop(),ew=eS;return;default:return}}function rL(e,t,r,n){switch(e){case 1:if(N)return void rA(e,t,r,n);if(J(ek.top,b)){S=[],eS=ew,(ew=rx)(e,t,r,n);return}break;case 4:e8(t);return;case 5:return;case 2:switch(t){case"caption":ek.clearToContext(y),eC.insertMarker(),e2(t,r),ew=rR;return;case"colgroup":ek.clearToContext(y),e2(t,r),ew=rM;return;case"col":rL(2,"colgroup",null),ew(e,t,r,n);return;case"tbody":case"tfoot":case"thead":ek.clearToContext(y),e2(t,r),ew=rI;return;case"td":case"th":case"tr":rL(2,"tbody",null),ew(e,t,r,n);return;case"table":if(!ek.inTableScope(t))return;rL(3,t),ew(e,t,r,n);return;case"style":case"script":case"template":rk(e,t,r,n);return;case"input":if("hidden"!==function(e){for(var t=0,r=e.length;t<r;t++)if("type"===e[t][0])return e[t][1].toLowerCase();return null}(r))break;e2(t,r),ek.pop();return;case"form":if(eL||ek.contains("template"))return;eL=e2(t,r),ek.popElement(eL);return}break;case 3:switch(t){case"table":if(!ek.inTableScope(t))return;ek.popTag(t),tt();return;case"body":case"caption":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return;case"template":rk(e,t,r,n);return}break;case -1:rA(e,t,r,n);return}e6=!0,rA(e,t,r,n),e6=!1}function rx(e,t,r,n){if(1===e)eO&&0===(t=t.replace($,"")).length||S.push(t);else{var a=S.join("");S.length=0,K.test(a)?(e6=!0,rA(1,a),e6=!1):e5(a),(ew=eS)(e,t,r,n)}}function rR(e,t,r,n){function a(){return!!ek.inTableScope("caption")&&(ek.generateImpliedEndTags(),ek.popTag("caption"),eC.clearToMarker(),ew=rL,!0)}switch(e){case 2:switch(t){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":a()&&ew(e,t,r,n);return}break;case 3:switch(t){case"caption":a();return;case"table":a()&&ew(e,t,r,n);return;case"body":case"col":case"colgroup":case"html":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":return}}rA(e,t,r,n)}function rM(e,t,r,n){switch(e){case 1:var a=t.match(Q);if(a&&(e5(a[0]),t=t.substring(a[0].length)),0===t.length)return;break;case 4:e8(t);return;case 5:return;case 2:switch(t){case"html":rA(e,t,r,n);return;case"col":e2(t,r),ek.pop();return;case"template":rk(e,t,r,n);return}break;case 3:switch(t){case"colgroup":if(!J(ek.top,"colgroup"))return;ek.pop(),ew=rL;return;case"col":return;case"template":rk(e,t,r,n);return}break;case -1:rA(e,t,r,n);return}J(ek.top,"colgroup")&&(rM(3,"colgroup"),ew(e,t,r,n))}function rI(e,t,r,n){function a(){(ek.inTableScope("tbody")||ek.inTableScope("thead")||ek.inTableScope("tfoot"))&&(ek.clearToContext(T),rI(3,ek.top.localName,null),ew(e,t,r,n))}switch(e){case 2:switch(t){case"tr":ek.clearToContext(T),e2(t,r),ew=rO;return;case"th":case"td":rI(2,"tr",null),ew(e,t,r,n);return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":a();return}break;case 3:switch(t){case"table":a();return;case"tbody":case"tfoot":case"thead":ek.inTableScope(t)&&(ek.clearToContext(T),ek.pop(),ew=rL);return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":case"tr":return}}rL(e,t,r,n)}function rO(e,t,r,n){function a(){return!!ek.inTableScope("tr")&&(ek.clearToContext(_),ek.pop(),ew=rI,!0)}switch(e){case 2:switch(t){case"th":case"td":ek.clearToContext(_),e2(t,r),ew=rq,eC.insertMarker();return;case"caption":case"col":case"colgroup":case"tbody":case"tfoot":case"thead":case"tr":a()&&ew(e,t,r,n);return}break;case 3:switch(t){case"tr":a();return;case"table":a()&&ew(e,t,r,n);return;case"tbody":case"tfoot":case"thead":ek.inTableScope(t)&&a()&&ew(e,t,r,n);return;case"body":case"caption":case"col":case"colgroup":case"html":case"td":case"th":return}}rL(e,t,r,n)}function rq(e,t,r,n){switch(e){case 2:switch(t){case"caption":case"col":case"colgroup":case"tbody":case"td":case"tfoot":case"th":case"thead":case"tr":ek.inTableScope("td")?(rq(3,"td"),ew(e,t,r,n)):ek.inTableScope("th")&&(rq(3,"th"),ew(e,t,r,n));return}break;case 3:switch(t){case"td":case"th":if(!ek.inTableScope(t))return;ek.generateImpliedEndTags(),ek.popTag(t),eC.clearToMarker(),ew=rO;return;case"body":case"caption":case"col":case"colgroup":case"html":return;case"table":case"tbody":case"tfoot":case"thead":case"tr":if(!ek.inTableScope(t))return;rq(3,ek.inTableScope("td")?"td":"th"),ew(e,t,r,n);return}}rA(e,t,r,n)}function rH(e,t,r,n){switch(e){case 1:if(eO&&0===(t=t.replace($,"")).length)return;e5(t);return;case 4:e8(t);return;case 5:return;case -1:rA(e,t,r,n);return;case 2:switch(t){case"html":rA(e,t,r,n);return;case"option":ek.top instanceof c.HTMLOptionElement&&rH(3,t),e2(t,r);return;case"optgroup":ek.top instanceof c.HTMLOptionElement&&rH(3,"option"),ek.top instanceof c.HTMLOptGroupElement&&rH(3,t),e2(t,r);return;case"select":rH(3,t);return;case"input":case"keygen":case"textarea":if(!ek.inSelectScope("select"))return;rH(3,"select"),ew(e,t,r,n);return;case"script":case"template":rk(e,t,r,n);return}break;case 3:switch(t){case"optgroup":ek.top instanceof c.HTMLOptionElement&&ek.elements[ek.elements.length-2]instanceof c.HTMLOptGroupElement&&rH(3,"option"),ek.top instanceof c.HTMLOptGroupElement&&ek.pop();return;case"option":ek.top instanceof c.HTMLOptionElement&&ek.pop();return;case"select":if(!ek.inSelectScope(t))return;ek.popTag(t),tt();return;case"template":rk(e,t,r,n);return}}}function rB(e,t,r,n){switch(t){case"caption":case"table":case"tbody":case"tfoot":case"thead":case"tr":case"td":case"th":switch(e){case 2:rB(3,"select"),ew(e,t,r,n);return;case 3:ek.inTableScope(t)&&(rB(3,"select"),ew(e,t,r,n));return}}rH(e,t,r,n)}function rP(e,t,r,n){function a(a){ew=a,eN[eN.length-1]=ew,ew(e,t,r,n)}switch(e){case 1:case 4:case 5:rA(e,t,r,n);return;case -1:ek.contains("template")?(ek.popTag("template"),eC.clearToMarker(),eN.pop(),tt(),ew(e,t,r,n)):ts();return;case 2:switch(t){case"base":case"basefont":case"bgsound":case"link":case"meta":case"noframes":case"script":case"style":case"template":case"title":rk(e,t,r,n);return;case"caption":case"colgroup":case"tbody":case"tfoot":case"thead":a(rL);return;case"col":a(rM);return;case"tr":a(rI);return;case"td":case"th":a(rO);return}a(rA);return;case 3:if("template"!==t)return;return void rk(e,t,r,n)}}function rU(e,t,r,n){switch(e){case 1:if(K.test(t))break;rA(e,t);return;case 4:ek.elements[0]._appendChild(eB.createComment(t));return;case 5:return;case -1:ts();return;case 2:if("html"===t)return void rA(e,t,r,n);break;case 3:if("html"===t){if(eA)return;ew=rj;return}}(ew=rA)(e,t,r,n)}function rF(e,t,r,n){switch(e){case 1:(t=t.replace(X,"")).length>0&&e5(t);return;case 4:e8(t);return;case 5:return;case -1:ts();return;case 2:switch(t){case"html":rA(e,t,r,n);return;case"frameset":e2(t,r);return;case"frame":e2(t,r),ek.pop();return;case"noframes":rk(e,t,r,n);return}break;case 3:if("frameset"===t){if(eA&&ek.top instanceof c.HTMLHtmlElement)return;ek.pop(),eA||ek.top instanceof c.HTMLFrameSetElement||(ew=rV);return}}}function rV(e,t,r,n){switch(e){case 1:(t=t.replace(X,"")).length>0&&e5(t);return;case 4:e8(t);return;case 5:return;case -1:ts();return;case 2:switch(t){case"html":rA(e,t,r,n);return;case"noframes":rk(e,t,r,n);return}break;case 3:if("html"===t){ew=rG;return}}}function rj(e,t,r,n){switch(e){case 1:if(K.test(t))break;rA(e,t,r,n);return;case 4:eB._appendChild(eB.createComment(t));return;case 5:rA(e,t,r,n);return;case -1:ts();return;case 2:if("html"===t)return void rA(e,t,r,n)}(ew=rA)(e,t,r,n)}function rG(e,t,r,n){switch(e){case 1:(t=t.replace(X,"")).length>0&&rA(e,t,r,n);return;case 4:eB._appendChild(eB.createComment(t));return;case 5:rA(e,t,r,n);return;case -1:ts();return;case 2:switch(t){case"html":rA(e,t,r,n);return;case"noframes":rk(e,t,r,n);return}}}function rz(e,r,n,a){switch(e){case 1:eR&&Y.test(r)&&(eR=!1),eO&&(r=r.replace($,"�")),e5(r);return;case 4:e8(r);return;case 5:return;case 2:switch(r){case"font":if(!function(e){for(var t=0,r=e.length;t<r;t++)switch(e[t][0]){case"color":case"face":case"size":return!0}return!1}(n))break;case"b":case"big":case"blockquote":case"body":case"br":case"center":case"code":case"dd":case"div":case"dl":case"dt":case"em":case"embed":case"h1":case"h2":case"h3":case"h4":case"h5":case"h6":case"head":case"hr":case"i":case"img":case"li":case"listing":case"menu":case"meta":case"nobr":case"ol":case"p":case"pre":case"ruby":case"s":case"small":case"span":case"strong":case"strike":case"sub":case"sup":case"table":case"tt":case"u":case"ul":case"var":if(eA)break;do ek.pop(),o=ek.top;while(o.namespaceURI!==s.HTML&&!J(o,A)&&!ee(o));e1(e,r,n,a);return}if((o=1===ek.elements.length&&eA?t:ek.top).namespaceURI===s.MATHML)er(n);else if(o.namespaceURI===s.SVG){var i;r=(i=r)in R?R[i]:i,et(n)}en(n),e3(r,n,o.namespaceURI),a&&("script"===r&&(o.namespaceURI,s.SVG),ek.pop());return;case 3:if(o=ek.top,"script"===r&&o.namespaceURI===s.SVG&&"script"===o.localName)ek.pop();else for(var o,c=ek.elements.length-1,l=ek.elements[c];;){if(l.localName.toLowerCase()===r){ek.popElement(l);break}if((l=ek.elements[--c]).namespaceURI===s.HTML){ew(e,r,n,a);break}}return}}return tX.lookahead=">",tY.lookahead=7,t4.lookahead=6,rg.lookahead=-32,eH.testTokenizer=function(e,t,r,n){var a=[];switch(t){case"PCDATA state":eh=tc;break;case"RCDATA state":eh=tl;break;case"RAWTEXT state":eh=tu;break;case"PLAINTEXT state":eh=tp}if(r&&(ef=r),e1=function(e,t,r,n){switch(eY(),e){case 1:a.length>0&&"Character"===a[a.length-1][0]?a[a.length-1][1]+=t:a.push(["Character",t]);break;case 4:a.push(["Comment",t]);break;case 5:a.push(["DOCTYPE",t,void 0===r?null:r,void 0===n?null:n,!eM]);break;case 2:for(var i=Object.create(null),s=0;s<r.length;s++){var o=r[s];1===o.length?i[o[0]]="":i[o[0]]=o[1]}var c=["StartTag",t,i];n&&c.push(!0),a.push(c);break;case 3:a.push(["EndTag",t])}},n){for(var i=0;i<e.length;i++)this.parse(e[i]);this.parse("",!0)}else this.parse(e,!0);return a},eH}ei.ElementStack=function(){this.elements=[],this.top=null},ei.ElementStack.prototype.push=function(e){this.elements.push(e),this.top=e},ei.ElementStack.prototype.pop=function(e){this.elements.pop(),this.top=this.elements[this.elements.length-1]},ei.ElementStack.prototype.popTag=function(e){for(var t=this.elements.length-1;t>0&&!J(this.elements[t],e);t--);this.elements.length=t,this.top=this.elements[t-1]},ei.ElementStack.prototype.popElementType=function(e){for(var t=this.elements.length-1;t>0&&!(this.elements[t]instanceof e);t--);this.elements.length=t,this.top=this.elements[t-1]},ei.ElementStack.prototype.popElement=function(e){for(var t=this.elements.length-1;t>0&&this.elements[t]!==e;t--);this.elements.length=t,this.top=this.elements[t-1]},ei.ElementStack.prototype.removeElement=function(e){if(this.top===e)this.pop();else{var t=this.elements.lastIndexOf(e);-1!==t&&this.elements.splice(t,1)}},ei.ElementStack.prototype.clearToContext=function(e){for(var t=this.elements.length-1;t>0&&!J(this.elements[t],e);t--);this.elements.length=t+1,this.top=this.elements[t]},ei.ElementStack.prototype.contains=function(e){return this.inSpecificScope(e,Object.create(null))},ei.ElementStack.prototype.inSpecificScope=function(e,t){for(var r=this.elements.length-1;r>=0;r--){var n=this.elements[r];if(J(n,e))return!0;if(J(n,t))break}return!1},ei.ElementStack.prototype.elementInSpecificScope=function(e,t){for(var r=this.elements.length-1;r>=0;r--){var n=this.elements[r];if(n===e)return!0;if(J(n,t))break}return!1},ei.ElementStack.prototype.elementTypeInSpecificScope=function(e,t){for(var r=this.elements.length-1;r>=0;r--){var n=this.elements[r];if(n instanceof e)return!0;if(J(n,t))break}return!1},ei.ElementStack.prototype.inScope=function(e){return this.inSpecificScope(e,S)},ei.ElementStack.prototype.elementInScope=function(e){return this.elementInSpecificScope(e,S)},ei.ElementStack.prototype.elementTypeInScope=function(e){return this.elementTypeInSpecificScope(e,S)},ei.ElementStack.prototype.inButtonScope=function(e){return this.inSpecificScope(e,k)},ei.ElementStack.prototype.inListItemScope=function(e){return this.inSpecificScope(e,N)},ei.ElementStack.prototype.inTableScope=function(e){return this.inSpecificScope(e,C)},ei.ElementStack.prototype.inSelectScope=function(e){for(var t=this.elements.length-1;t>=0;t--){var r=this.elements[t];if(r.namespaceURI!==s.HTML)break;var n=r.localName;if(n===e)return!0;if("optgroup"!==n&&"option"!==n)break}return!1},ei.ElementStack.prototype.generateImpliedEndTags=function(e,t){for(var r=t?E:v,n=this.elements.length-1;n>=0;n--){var a=this.elements[n];if(e&&J(a,e)||!J(this.elements[n],r))break}this.elements.length=n+1,this.top=this.elements[n]},ei.ActiveFormattingElements=function(){this.list=[],this.attrs=[]},ei.ActiveFormattingElements.prototype.MARKER={localName:"|"},ei.ActiveFormattingElements.prototype.insertMarker=function(){this.list.push(this.MARKER),this.attrs.push(this.MARKER)},ei.ActiveFormattingElements.prototype.push=function(e,t){for(var r=0,n=this.list.length-1;n>=0&&this.list[n]!==this.MARKER;n--)if(function(e,t,r){if(e.localName!==t.localName||e._numattrs!==r.length)return!1;for(var n=0,a=r.length;n<a;n++){var i=r[n][0],s=r[n][1];if(!e.hasAttribute(i)||e.getAttribute(i)!==s)return!1}return!0}(e,this.list[n],this.attrs[n])&&3==++r){this.list.splice(n,1),this.attrs.splice(n,1);break}this.list.push(e);for(var a=[],i=0;i<t.length;i++)a[i]=t[i];this.attrs.push(a)},ei.ActiveFormattingElements.prototype.clearToMarker=function(){for(var e=this.list.length-1;e>=0&&this.list[e]!==this.MARKER;e--);e<0&&(e=0),this.list.length=e,this.attrs.length=e},ei.ActiveFormattingElements.prototype.findElementByTag=function(e){for(var t=this.list.length-1;t>=0;t--){var r=this.list[t];if(r===this.MARKER)break;if(r.localName===e)return r}return null},ei.ActiveFormattingElements.prototype.indexOf=function(e){return this.list.lastIndexOf(e)},ei.ActiveFormattingElements.prototype.remove=function(e){var t=this.list.lastIndexOf(e);-1!==t&&(this.list.splice(t,1),this.attrs.splice(t,1))},ei.ActiveFormattingElements.prototype.replace=function(e,t,r){var n=this.list.lastIndexOf(e);-1!==n&&(this.list[n]=t,this.attrs[n]=r)},ei.ActiveFormattingElements.prototype.insertAfter=function(e,t){var r=this.list.lastIndexOf(e);-1!==r&&(this.list.splice(r,0,t),this.attrs.splice(r,0,t))}},75251:(e,t,r)=>{"use strict";let{parse:n}=r(44954);function a(e){return e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase()}function i(e){this._element=e}e.exports=function(e){return new Proxy(new i(e),{get:function(e,t){return t in e?e[t]:e.getPropertyValue(a(t))},has:function(e,t){return!0},set:function(e,t,r){return t in e?e[t]=r:e.setProperty(a(t),r??void 0),!0}})};let s="!important";function o(e){let t={property:{},priority:{}};if(!e)return t;let r=n(e);if(r.length<2)return t;for(let e=0;e<r.length;e+=2){let n=r[e],a=r[e+1];a.endsWith(s)&&(t.priority[n]="important",a=a.slice(0,-s.length).trim()),t.property[n]=a}return t}var c={};i.prototype=Object.create(Object.prototype,{_parsed:{get:function(){if(!this._parsedStyles||this.cssText!==this._lastParsedText){var e=this.cssText;this._parsedStyles=o(e),this._lastParsedText=e,delete this._names}return this._parsedStyles}},_serialize:{value:function(){var e=this._parsed,t="";for(var r in e.property)t&&(t+=" "),t+=r+": "+e.property[r],e.priority[r]&&(t+=" !"+e.priority[r]),t+=";";this.cssText=t,this._lastParsedText=t,delete this._names}},cssText:{get:function(){return this._element.getAttribute("style")},set:function(e){this._element.setAttribute("style",e)}},length:{get:function(){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names.length}},item:{value:function(e){return this._names||(this._names=Object.getOwnPropertyNames(this._parsed.property)),this._names[e]}},getPropertyValue:{value:function(e){return e=e.toLowerCase(),this._parsed.property[e]||""}},getPropertyPriority:{value:function(e){return e=e.toLowerCase(),this._parsed.priority[e]||""}},setProperty:{value:function(e,t,r){if(e=e.toLowerCase(),null==t&&(t=""),null==r&&(r=""),t!==c&&(t=""+t),""===(t=t.trim()))return void this.removeProperty(e);if(""===r||r===c||/^important$/i.test(r)){var n=this._parsed;if(t===c){if(!n.property[e])return;""!==r?n.priority[e]="important":delete n.priority[e]}else{if(-1!==t.indexOf(";"))return;var a=o(e+":"+t);if(0===Object.getOwnPropertyNames(a.property).length||0!==Object.getOwnPropertyNames(a.priority).length)return;for(var i in a.property)n.property[i]=a.property[i],r!==c&&(""!==r?n.priority[i]="important":n.priority[i]&&delete n.priority[i])}this._serialize()}}},setPropertyValue:{value:function(e,t){return this.setProperty(e,t,c)}},setPropertyPriority:{value:function(e,t){return this.setProperty(e,c,t)}},removeProperty:{value:function(e){e=e.toLowerCase();var t=this._parsed;e in t.property&&(delete t.property[e],delete t.priority[e],this._serialize())}}})},76729:(e,t,r)=>{"use strict";var n=r(21939),a=r(37070),i=function(e,t){for(var r=e.createDocumentFragment(),a=0;a<t.length;a++){var i=t[a],s=i instanceof n;r.appendChild(s?i:e.createTextNode(String(i)))}return r};e.exports={after:{value:function(){var e=Array.prototype.slice.call(arguments),t=this.parentNode,r=this.nextSibling;if(null!==t){for(;r&&e.some(function(e){return e===r});)r=r.nextSibling;var n=i(this.doc,e);t.insertBefore(n,r)}}},before:{value:function(){var e=Array.prototype.slice.call(arguments),t=this.parentNode,r=this.previousSibling;if(null!==t){for(;r&&e.some(function(e){return e===r});)r=r.previousSibling;var n=i(this.doc,e),a=r?r.nextSibling:t.firstChild;t.insertBefore(n,a)}}},remove:{value:function(){null!==this.parentNode&&(this.doc&&(this.doc._preremoveNodeIterators(this),this.rooted&&this.doc.mutateRemove(this)),this._remove(),this.parentNode=null)}},_remove:{value:function(){var e=this.parentNode;null!==e&&(e._childNodes?e._childNodes.splice(this.index,1):e._firstChild===this&&(this._nextSibling===this?e._firstChild=null:e._firstChild=this._nextSibling),a.remove(this),e.modify())}},replaceWith:{value:function(){var e=Array.prototype.slice.call(arguments),t=this.parentNode,r=this.nextSibling;if(null!==t){for(;r&&e.some(function(e){return e===r});)r=r.nextSibling;var n=i(this.doc,e);this.parentNode===t?t.replaceChild(n,this):t.insertBefore(n,r)}}}}},77177:(e,t,r)=>{"use strict";e.exports=h;var n=r(21939),a=r(34405),i=r(58349),s=r(81070),o={first:"firstChild",last:"lastChild",next:"firstChild",previous:"lastChild"},c={first:"nextSibling",last:"previousSibling",next:"nextSibling",previous:"previousSibling"};function l(e,t){var r,n,i,s,l;for(n=e._currentNode[o[t]];null!==n;){if((s=e._internalFilter(n))===a.FILTER_ACCEPT)return e._currentNode=n,n;if(s===a.FILTER_SKIP&&null!==(r=n[o[t]])){n=r;continue}for(;null!==n;){if(null!==(l=n[c[t]])){n=l;break}if(null===(i=n.parentNode)||i===e.root||i===e._currentNode)return null;n=i}}return null}function u(e,t){var r,n,i;if((r=e._currentNode)===e.root)return null;for(;;){for(i=r[c[t]];null!==i;){if(r=i,(n=e._internalFilter(r))===a.FILTER_ACCEPT)return e._currentNode=r,r;i=r[o[t]],(n===a.FILTER_REJECT||null===i)&&(i=r[c[t]])}if(null===(r=r.parentNode)||r===e.root||e._internalFilter(r)===a.FILTER_ACCEPT)return null}}function h(e,t,r){e&&e.nodeType||s.NotSupportedError(),this._root=e,this._whatToShow=Number(t)||0,this._filter=r||null,this._active=!1,this._currentNode=e}Object.defineProperties(h.prototype,{root:{get:function(){return this._root}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},currentNode:{get:function(){return this._currentNode},set:function(e){if(!(e instanceof n))throw TypeError("Not a Node");this._currentNode=e}},_internalFilter:{value:function(e){var t,r;if(this._active&&s.InvalidStateError(),!(1<<e.nodeType-1&this._whatToShow))return a.FILTER_SKIP;if(null===(r=this._filter))t=a.FILTER_ACCEPT;else{this._active=!0;try{t="function"==typeof r?r(e):r.acceptNode(e)}finally{this._active=!1}}return+t}},parentNode:{value:function(){for(var e=this._currentNode;e!==this.root&&null!==(e=e.parentNode);)if(this._internalFilter(e)===a.FILTER_ACCEPT)return this._currentNode=e,e;return null}},firstChild:{value:function(){return l(this,"first")}},lastChild:{value:function(){return l(this,"last")}},previousSibling:{value:function(){return u(this,"previous")}},nextSibling:{value:function(){return u(this,"next")}},previousNode:{value:function(){var e,t,r,n;for(e=this._currentNode;e!==this._root;){for(r=e.previousSibling;r;r=e.previousSibling)if(e=r,(t=this._internalFilter(e))!==a.FILTER_REJECT){for(n=e.lastChild;n&&(e=n,(t=this._internalFilter(e))!==a.FILTER_REJECT);n=e.lastChild);if(t===a.FILTER_ACCEPT)return this._currentNode=e,e}if(e===this.root||null===e.parentNode)break;if(e=e.parentNode,this._internalFilter(e)===a.FILTER_ACCEPT)return this._currentNode=e,e}return null}},nextNode:{value:function(){var e,t,r,n;e=this._currentNode,t=a.FILTER_ACCEPT;e:for(;;){for(r=e.firstChild;r;r=e.firstChild){if(e=r,(t=this._internalFilter(e))===a.FILTER_ACCEPT)return this._currentNode=e,e;if(t===a.FILTER_REJECT)break}for(n=i.nextSkippingChildren(e,this.root);n;n=i.nextSkippingChildren(e,this.root)){if(e=n,(t=this._internalFilter(e))===a.FILTER_ACCEPT)return this._currentNode=e,e;if(t===a.FILTER_SKIP)continue e}return null}}},toString:{value:function(){return"[object TreeWalker]"}}})},78282:e=>{"use strict";function t(e){return this[e]||null}e.exports=function(e){return e||(e=[]),e.item=t,e}},81070:(e,t,r)=>{"use strict";var n=r(82718),a=r(46309).h;t.NAMESPACE={HTML:"http://www.w3.org/1999/xhtml",XML:"http://www.w3.org/XML/1998/namespace",XMLNS:"http://www.w3.org/2000/xmlns/",MATHML:"http://www.w3.org/1998/Math/MathML",SVG:"http://www.w3.org/2000/svg",XLINK:"http://www.w3.org/1999/xlink"},t.IndexSizeError=function(){throw new n(n.INDEX_SIZE_ERR)},t.HierarchyRequestError=function(){throw new n(n.HIERARCHY_REQUEST_ERR)},t.WrongDocumentError=function(){throw new n(n.WRONG_DOCUMENT_ERR)},t.InvalidCharacterError=function(){throw new n(n.INVALID_CHARACTER_ERR)},t.NoModificationAllowedError=function(){throw new n(n.NO_MODIFICATION_ALLOWED_ERR)},t.NotFoundError=function(){throw new n(n.NOT_FOUND_ERR)},t.NotSupportedError=function(){throw new n(n.NOT_SUPPORTED_ERR)},t.InvalidStateError=function(){throw new n(n.INVALID_STATE_ERR)},t.SyntaxError=function(){throw new n(n.SYNTAX_ERR)},t.InvalidModificationError=function(){throw new n(n.INVALID_MODIFICATION_ERR)},t.NamespaceError=function(){throw new n(n.NAMESPACE_ERR)},t.InvalidAccessError=function(){throw new n(n.INVALID_ACCESS_ERR)},t.TypeMismatchError=function(){throw new n(n.TYPE_MISMATCH_ERR)},t.SecurityError=function(){throw new n(n.SECURITY_ERR)},t.NetworkError=function(){throw new n(n.NETWORK_ERR)},t.AbortError=function(){throw new n(n.ABORT_ERR)},t.UrlMismatchError=function(){throw new n(n.URL_MISMATCH_ERR)},t.QuotaExceededError=function(){throw new n(n.QUOTA_EXCEEDED_ERR)},t.TimeoutError=function(){throw new n(n.TIMEOUT_ERR)},t.InvalidNodeTypeError=function(){throw new n(n.INVALID_NODE_TYPE_ERR)},t.DataCloneError=function(){throw new n(n.DATA_CLONE_ERR)},t.nyi=function(){throw Error("NotYetImplemented")},t.shouldOverride=function(){throw Error("Abstract function; should be overriding in subclass.")},t.assert=function(e,t){if(!e)throw Error("Assertion failed: "+(t||"")+"\n"+Error().stack)},t.expose=function(e,t){for(var r in e)Object.defineProperty(t.prototype,r,{value:e[r],writable:a})},t.merge=function(e,t){for(var r in t)e[r]=t[r]},t.documentOrder=function(e,t){return 3-(6&e.compareDocumentPosition(t))},t.toASCIILowerCase=function(e){return e.replace(/[A-Z]+/g,function(e){return e.toLowerCase()})},t.toASCIIUpperCase=function(e){return e.replace(/[a-z]+/g,function(e){return e.toUpperCase()})}},82718:e=>{"use strict";e.exports=a;var t=[null,"INDEX_SIZE_ERR",null,"HIERARCHY_REQUEST_ERR","WRONG_DOCUMENT_ERR","INVALID_CHARACTER_ERR",null,"NO_MODIFICATION_ALLOWED_ERR","NOT_FOUND_ERR","NOT_SUPPORTED_ERR","INUSE_ATTRIBUTE_ERR","INVALID_STATE_ERR","SYNTAX_ERR","INVALID_MODIFICATION_ERR","NAMESPACE_ERR","INVALID_ACCESS_ERR",null,"TYPE_MISMATCH_ERR","SECURITY_ERR","NETWORK_ERR","ABORT_ERR","URL_MISMATCH_ERR","QUOTA_EXCEEDED_ERR","TIMEOUT_ERR","INVALID_NODE_TYPE_ERR","DATA_CLONE_ERR"],r=[null,"INDEX_SIZE_ERR (1): the index is not in the allowed range",null,"HIERARCHY_REQUEST_ERR (3): the operation would yield an incorrect nodes model","WRONG_DOCUMENT_ERR (4): the object is in the wrong Document, a call to importNode is required","INVALID_CHARACTER_ERR (5): the string contains invalid characters",null,"NO_MODIFICATION_ALLOWED_ERR (7): the object can not be modified","NOT_FOUND_ERR (8): the object can not be found here","NOT_SUPPORTED_ERR (9): this operation is not supported","INUSE_ATTRIBUTE_ERR (10): setAttributeNode called on owned Attribute","INVALID_STATE_ERR (11): the object is in an invalid state","SYNTAX_ERR (12): the string did not match the expected pattern","INVALID_MODIFICATION_ERR (13): the object can not be modified in this way","NAMESPACE_ERR (14): the operation is not allowed by Namespaces in XML","INVALID_ACCESS_ERR (15): the object does not support the operation or argument",null,"TYPE_MISMATCH_ERR (17): the type of the object does not match the expected type","SECURITY_ERR (18): the operation is insecure","NETWORK_ERR (19): a network error occurred","ABORT_ERR (20): the user aborted an operation","URL_MISMATCH_ERR (21): the given URL does not match another URL","QUOTA_EXCEEDED_ERR (22): the quota has been exceeded","TIMEOUT_ERR (23): a timeout occurred","INVALID_NODE_TYPE_ERR (24): the supplied node is invalid or has an invalid ancestor for this operation","DATA_CLONE_ERR (25): the object can not be cloned."],n={INDEX_SIZE_ERR:1,DOMSTRING_SIZE_ERR:2,HIERARCHY_REQUEST_ERR:3,WRONG_DOCUMENT_ERR:4,INVALID_CHARACTER_ERR:5,NO_DATA_ALLOWED_ERR:6,NO_MODIFICATION_ALLOWED_ERR:7,NOT_FOUND_ERR:8,NOT_SUPPORTED_ERR:9,INUSE_ATTRIBUTE_ERR:10,INVALID_STATE_ERR:11,SYNTAX_ERR:12,INVALID_MODIFICATION_ERR:13,NAMESPACE_ERR:14,INVALID_ACCESS_ERR:15,VALIDATION_ERR:16,TYPE_MISMATCH_ERR:17,SECURITY_ERR:18,NETWORK_ERR:19,ABORT_ERR:20,URL_MISMATCH_ERR:21,QUOTA_EXCEEDED_ERR:22,TIMEOUT_ERR:23,INVALID_NODE_TYPE_ERR:24,DATA_CLONE_ERR:25};function a(e){Error.call(this),Error.captureStackTrace(this,this.constructor),this.code=e,this.message=r[e],this.name=t[e]}for(var i in a.prototype.__proto__=Error.prototype,n){var s={value:n[i]};Object.defineProperty(a,i,s),Object.defineProperty(a.prototype,i,s)}},87856:(e,t,r)=>{"use strict";e.exports=o;var n=r(17415),a=r(81070),i=r(76729),s=r(40247);function o(){n.call(this)}o.prototype=Object.create(n.prototype,{substringData:{value:function(e,t){if(arguments.length<2)throw TypeError("Not enough arguments");return t>>>=0,((e>>>=0)>this.data.length||e<0||t<0)&&a.IndexSizeError(),this.data.substring(e,e+t)}},appendData:{value:function(e){if(arguments.length<1)throw TypeError("Not enough arguments");this.data+=String(e)}},insertData:{value:function(e,t){return this.replaceData(e,0,t)}},deleteData:{value:function(e,t){return this.replaceData(e,t,"")}},replaceData:{value:function(e,t,r){var n=this.data,i=n.length;e>>>=0,t>>>=0,r=String(r),(e>i||e<0)&&a.IndexSizeError(),e+t>i&&(t=i-e);var s=n.substring(0,e),o=n.substring(e+t);this.data=s+r+o}},isEqual:{value:function(e){return this._data===e._data}},length:{get:function(){return this.data.length}}}),Object.defineProperties(o.prototype,i),Object.defineProperties(o.prototype,s)},93458:(e,t,r)=>{"use strict";e.exports=s;var n=r(21939),a=r(17415),i=r(76729);function s(e,t,r,i){a.call(this),this.nodeType=n.DOCUMENT_TYPE_NODE,this.ownerDocument=e||null,this.name=t,this.publicId=r||"",this.systemId=i||""}s.prototype=Object.create(a.prototype,{nodeName:{get:function(){return this.name}},nodeValue:{get:function(){return null},set:function(){}},clone:{value:function(){return new s(this.ownerDocument,this.name,this.publicId,this.systemId)}},isEqual:{value:function(e){return this.name===e.name&&this.publicId===e.publicId&&this.systemId===e.systemId}}}),Object.defineProperties(s.prototype,i)},93639:e=>{"use strict";e.exports=class extends Array{constructor(e){if(super(e&&e.length||0),e)for(var t in e)this[t]=e[t]}item(e){return this[e]||null}}},95402:(e,t,r)=>{"use strict";e.exports=a;var n=r(28636);function a(e,t){n.call(this,e,t)}a.prototype=Object.create(n.prototype,{constructor:{value:a}})},97901:(e,t,r)=>{"use strict";e.exports=c;var n=r(34405),a=r(58349),i=r(81070);function s(e,t){for(;t;t=t.parentNode)if(e===t)return!0;return!1}function o(e,t){var r,i,s,o;for(r=e._referenceNode,i=e._pointerBeforeReferenceNode;;){if(i===t)i=!i;else if(s=r,o=e._root,null===(r=t?a.next(s,o):s===o?null:a.previous(s,null)))return null;if(e._internalFilter(r)===n.FILTER_ACCEPT)break}return e._referenceNode=r,e._pointerBeforeReferenceNode=i,r}function c(e,t,r){e&&e.nodeType||i.NotSupportedError(),this._root=e,this._referenceNode=e,this._pointerBeforeReferenceNode=!0,this._whatToShow=Number(t)||0,this._filter=r||null,this._active=!1,e.doc._attachNodeIterator(this)}Object.defineProperties(c.prototype,{root:{get:function(){return this._root}},referenceNode:{get:function(){return this._referenceNode}},pointerBeforeReferenceNode:{get:function(){return this._pointerBeforeReferenceNode}},whatToShow:{get:function(){return this._whatToShow}},filter:{get:function(){return this._filter}},_internalFilter:{value:function(e){var t,r;if(this._active&&i.InvalidStateError(),!(1<<e.nodeType-1&this._whatToShow))return n.FILTER_SKIP;if(null===(r=this._filter))t=n.FILTER_ACCEPT;else{this._active=!0;try{t="function"==typeof r?r(e):r.acceptNode(e)}finally{this._active=!1}}return+t}},_preremove:{value:function(e){if(!s(e,this._root)&&s(e,this._referenceNode)){if(this._pointerBeforeReferenceNode){for(var t,r=e;r.lastChild;)r=r.lastChild;if(r=a.next(r,this.root)){this._referenceNode=r;return}this._pointerBeforeReferenceNode=!1}if(null===e.previousSibling)this._referenceNode=e.parentNode;else for(this._referenceNode=e.previousSibling,t=this._referenceNode.lastChild;t;t=this._referenceNode.lastChild)this._referenceNode=t}}},nextNode:{value:function(){return o(this,!0)}},previousNode:{value:function(){return o(this,!1)}},detach:{value:function(){}},toString:{value:function(){return"[object NodeIterator]"}}})},98113:(e,t,r)=>{"use strict";var n=r(62114);function a(){}e.exports=a,a.prototype=Object.create(Object.prototype,{_url:{get:function(){return new n(this.href)}},protocol:{get:function(){var e=this._url;return e&&e.scheme?e.scheme+":":":"},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&(e=(e=e.replace(/:+$/,"")).replace(/[^-+\.a-zA-Z0-9]/g,n.percentEncode)).length>0&&(r.scheme=e,t=r.toString()),this.href=t}},host:{get:function(){var e=this._url;return e.isAbsolute()&&e.isAuthorityBased()?e.host+(e.port?":"+e.port:""):""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&r.isAuthorityBased()&&(e=e.replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,n.percentEncode)).length>0&&(r.host=e,delete r.port,t=r.toString()),this.href=t}},hostname:{get:function(){var e=this._url;return e.isAbsolute()&&e.isAuthorityBased()?e.host:""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&r.isAuthorityBased()&&(e=(e=e.replace(/^\/+/,"")).replace(/[^-+\._~!$&'()*,;:=a-zA-Z0-9]/g,n.percentEncode)).length>0&&(r.host=e,t=r.toString()),this.href=t}},port:{get:function(){var e=this._url;return e.isAbsolute()&&e.isAuthorityBased()&&void 0!==e.port?e.port:""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&r.isAuthorityBased()&&(0===(e=(e=(e=""+e).replace(/[^0-9].*$/,"")).replace(/^0+/,"")).length&&(e="0"),65535>=parseInt(e,10)&&(r.port=e,t=r.toString())),this.href=t}},pathname:{get:function(){var e=this._url;return e.isAbsolute()&&e.isHierarchical()?e.path:""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&r.isHierarchical()&&("/"!==e.charAt(0)&&(e="/"+e),r.path=e=e.replace(/[^-+\._~!$&'()*,;:=@\/a-zA-Z0-9]/g,n.percentEncode),t=r.toString()),this.href=t}},search:{get:function(){var e=this._url;return e.isAbsolute()&&e.isHierarchical()&&void 0!==e.query?"?"+e.query:""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&r.isHierarchical()&&("?"===e.charAt(0)&&(e=e.substring(1)),r.query=e=e.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,n.percentEncode),t=r.toString()),this.href=t}},hash:{get:function(){var e=this._url;return null==e||null==e.fragment||""===e.fragment?"":"#"+e.fragment},set:function(e){var t=this.href,r=new n(t);"#"===e.charAt(0)&&(e=e.substring(1)),r.fragment=e=e.replace(/[^-+\._~!$&'()*,;:=@\/?a-zA-Z0-9]/g,n.percentEncode),t=r.toString(),this.href=t}},username:{get:function(){return this._url.username||""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&(r.username=e=e.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\:]/g,n.percentEncode),t=r.toString()),this.href=t}},password:{get:function(){return this._url.password||""},set:function(e){var t=this.href,r=new n(t);r.isAbsolute()&&(""===e?r.password=null:r.password=e=e.replace(/[\x00-\x1F\x7F-\uFFFF "#<>?`\/@\\]/g,n.percentEncode),t=r.toString()),this.href=t}},origin:{get:function(){var e=this._url;if(null==e)return"";var t=function(t){var r=[e.scheme,e.host,+e.port||t];return r[0]+"://"+r[1]+(r[2]===t?"":":"+r[2])};switch(e.scheme){case"ftp":return t(21);case"gopher":return t(70);case"http":case"ws":return t(80);case"https":case"wss":return t(443);default:return e.scheme+"://"}}}}),a._inherit=function(e){Object.getOwnPropertyNames(a.prototype).forEach(function(t){if("constructor"!==t&&"href"!==t){var r=Object.getOwnPropertyDescriptor(a.prototype,t);Object.defineProperty(e,t,r)}})}}};