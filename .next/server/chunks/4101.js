"use strict";exports.id=4101,exports.ids=[4101],exports.modules={2030:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function e(t,n){let r=t[0],i=n[0];if(Array.isArray(r)&&Array.isArray(i)){if(r[0]!==i[0]||r[2]!==i[2])return!0}else if(r!==i)return!0;if(t[4])return!n[4];if(n[4])return!0;let a=Object.values(t[1])[0],o=Object.values(n[1])[0];return!a||!o||e(a,o)}}}),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},2255:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"pathHasPrefix",{enumerable:!0,get:function(){return i}});let r=n(19169);function i(e,t){if("string"!=typeof e)return!1;let{pathname:n}=(0,r.parsePath)(e);return n===t||n.startsWith(t+"/")}},5144:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PromiseQueue",{enumerable:!0,get:function(){return u}});let r=n(51499),i=n(38919);var a=i._("_maxConcurrency"),o=i._("_runningCount"),s=i._("_queue"),l=i._("_processNext");class u{enqueue(e){let t,n,i=new Promise((e,r)=>{t=e,n=r}),a=async()=>{try{r._(this,o)[o]++;let n=await e();t(n)}catch(e){n(e)}finally{r._(this,o)[o]--,r._(this,l)[l]()}};return r._(this,s)[s].push({promiseFn:i,task:a}),r._(this,l)[l](),i}bump(e){let t=r._(this,s)[s].findIndex(t=>t.promiseFn===e);if(t>-1){let e=r._(this,s)[s].splice(t,1)[0];r._(this,s)[s].unshift(e),r._(this,l)[l](!0)}}constructor(e=5){Object.defineProperty(this,l,{value:c}),Object.defineProperty(this,a,{writable:!0,value:void 0}),Object.defineProperty(this,o,{writable:!0,value:void 0}),Object.defineProperty(this,s,{writable:!0,value:void 0}),r._(this,a)[a]=e,r._(this,o)[o]=0,r._(this,s)[s]=[]}}function c(e){if(void 0===e&&(e=!1),(r._(this,o)[o]<r._(this,a)[a]||e)&&r._(this,s)[s].length>0){var t;null==(t=r._(this,s)[s].shift())||t.task()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},5334:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DYNAMIC_STALETIME_MS:function(){return d},STATIC_STALETIME_MS:function(){return f},createSeededPrefetchCacheEntry:function(){return u},getOrCreatePrefetchCacheEntry:function(){return l},prunePrefetchCache:function(){return h}});let r=n(59008),i=n(59154),a=n(75076);function o(e,t,n){let r=e.pathname;return(t&&(r+=e.search),n)?""+n+"%"+r:r}function s(e,t,n){return o(e,t===i.PrefetchKind.FULL,n)}function l(e){let{url:t,nextUrl:n,tree:r,prefetchCache:a,kind:s,allowAliasing:l=!0}=e,u=function(e,t,n,r,a){for(let s of(void 0===t&&(t=i.PrefetchKind.TEMPORARY),[n,null])){let n=o(e,!0,s),l=o(e,!1,s),u=e.search?n:l,c=r.get(u);if(c&&a){if(c.url.pathname===e.pathname&&c.url.search!==e.search)return{...c,aliased:!0};return c}let h=r.get(l);if(a&&e.search&&t!==i.PrefetchKind.FULL&&h&&!h.key.includes("%"))return{...h,aliased:!0}}if(t!==i.PrefetchKind.FULL&&a){for(let t of r.values())if(t.url.pathname===e.pathname&&!t.key.includes("%"))return{...t,aliased:!0}}}(t,s,n,a,l);return u?(u.status=p(u),u.kind!==i.PrefetchKind.FULL&&s===i.PrefetchKind.FULL&&u.data.then(e=>{if(!(Array.isArray(e.flightData)&&e.flightData.some(e=>e.isRootRender&&null!==e.seedData)))return c({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:null!=s?s:i.PrefetchKind.TEMPORARY})}),s&&u.kind===i.PrefetchKind.TEMPORARY&&(u.kind=s),u):c({tree:r,url:t,nextUrl:n,prefetchCache:a,kind:s||i.PrefetchKind.TEMPORARY})}function u(e){let{nextUrl:t,tree:n,prefetchCache:r,url:a,data:o,kind:l}=e,u=o.couldBeIntercepted?s(a,l,t):s(a,l),c={treeAtTimeOfPrefetch:n,data:Promise.resolve(o),kind:l,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:o.staleTime,key:u,status:i.PrefetchCacheEntryStatus.fresh,url:a};return r.set(u,c),c}function c(e){let{url:t,kind:n,tree:o,nextUrl:l,prefetchCache:u}=e,c=s(t,n),h=a.prefetchQueue.enqueue(()=>(0,r.fetchServerResponse)(t,{flightRouterState:o,nextUrl:l,prefetchKind:n}).then(e=>{let n;if(e.couldBeIntercepted&&(n=function(e){let{url:t,nextUrl:n,prefetchCache:r,existingCacheKey:i}=e,a=r.get(i);if(!a)return;let o=s(t,a.kind,n);return r.set(o,{...a,key:o}),r.delete(i),o}({url:t,existingCacheKey:c,nextUrl:l,prefetchCache:u})),e.prerendered){let t=u.get(null!=n?n:c);t&&(t.kind=i.PrefetchKind.FULL,-1!==e.staleTime&&(t.staleTime=e.staleTime))}return e})),d={treeAtTimeOfPrefetch:o,data:h,kind:n,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:c,status:i.PrefetchCacheEntryStatus.fresh,url:t};return u.set(c,d),d}function h(e){for(let[t,n]of e)p(n)===i.PrefetchCacheEntryStatus.expired&&e.delete(t)}let d=1e3*Number("0"),f=1e3*Number("300");function p(e){let{kind:t,prefetchTime:n,lastUsedTime:r,staleTime:a}=e;return -1!==a?Date.now()<n+a?i.PrefetchCacheEntryStatus.fresh:i.PrefetchCacheEntryStatus.stale:Date.now()<(null!=r?r:n)+d?r?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.fresh:t===i.PrefetchKind.AUTO&&Date.now()<n+f?i.PrefetchCacheEntryStatus.stale:t===i.PrefetchKind.FULL&&Date.now()<n+f?i.PrefetchCacheEntryStatus.reusable:i.PrefetchCacheEntryStatus.expired}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6361:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"assignLocation",{enumerable:!0,get:function(){return i}});let r=n(96127);function i(e,t){if(e.startsWith(".")){let n=t.origin+t.pathname;return new URL((n.endsWith("/")?n:n+"/")+e)}return new URL((0,r.addBasePath)(e),t.href)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7044:(e,t,n)=>{n.d(t,{B:()=>r});let r="undefined"!=typeof window},8830:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"reducer",{enumerable:!0,get:function(){return r}}),n(59154),n(25232),n(29651),n(28627),n(78866),n(75076),n(97936),n(35429);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},9707:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addSearchParamsToPageSegments:function(){return h},handleAliasedPrefetchEntry:function(){return c}});let r=n(83913),i=n(89752),a=n(86770),o=n(57391),s=n(33123),l=n(33898),u=n(59435);function c(e,t,n,c,d){let f,p=t.tree,m=t.cache,g=(0,o.createHrefFromUrl)(c);if("string"==typeof n)return!1;for(let t of n){if(!function e(t){if(!t)return!1;let n=t[2];if(t[3])return!0;for(let t in n)if(e(n[t]))return!0;return!1}(t.seedData))continue;let n=t.tree;n=h(n,Object.fromEntries(c.searchParams));let{seedData:o,isRootRender:u,pathToSegment:d}=t,y=["",...d];n=h(n,Object.fromEntries(c.searchParams));let v=(0,a.applyRouterStatePatchToTree)(y,p,n,g),b=(0,i.createEmptyCacheNode)();if(u&&o){let t=o[1];b.loading=o[3],b.rsc=t,function e(t,n,i,a,o){if(0!==Object.keys(a[1]).length)for(let l in a[1]){let u,c=a[1][l],h=c[0],d=(0,s.createRouterCacheKey)(h),f=null!==o&&void 0!==o[2][l]?o[2][l]:null;if(null!==f){let e=f[1],n=f[3];u={lazyData:null,rsc:h.includes(r.PAGE_SEGMENT_KEY)?null:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else u={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let p=n.parallelRoutes.get(l);p?p.set(d,u):n.parallelRoutes.set(l,new Map([[d,u]])),e(t,u,i,c,f)}}(e,b,m,n,o)}else b.rsc=m.rsc,b.prefetchRsc=m.prefetchRsc,b.loading=m.loading,b.parallelRoutes=new Map(m.parallelRoutes),(0,l.fillCacheWithNewSubTreeDataButOnlyLoading)(e,b,m,t);v&&(p=v,m=b,f=!0)}return!!f&&(d.patchedTree=p,d.cache=m,d.canonicalUrl=g,d.hashFragment=c.hash,(0,u.handleMutable)(t,d))}function h(e,t){let[n,i,...a]=e;if(n.includes(r.PAGE_SEGMENT_KEY))return[(0,r.addSearchParamsIfPageSegment)(n,t),i,...a];let o={};for(let[e,n]of Object.entries(i))o[e]=h(n,t);return[n,o,...a]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},12157:(e,t,n)=>{n.d(t,{L:()=>r});let r=(0,n(43210).createContext)({})},15124:(e,t,n)=>{n.d(t,{E:()=>i});var r=n(43210);let i=n(7044).B?r.useLayoutEffect:r.useEffect},18468:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let o=a.length<=2,[s,l]=a,u=(0,r.createRouterCacheKey)(l),c=n.parallelRoutes.get(s);if(!c)return;let h=t.parallelRoutes.get(s);if(h&&h!==c||(h=new Map(c),t.parallelRoutes.set(s,h)),o)return void h.delete(u);let d=c.get(u),f=h.get(u);f&&d&&(f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes)},h.set(u,f)),e(f,d,(0,i.getNextFlightSegmentPath)(a)))}}});let r=n(33123),i=n(74007);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},19169:(e,t)=>{function n(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parsePath",{enumerable:!0,get:function(){return n}})},21279:(e,t,n)=>{n.d(t,{t:()=>r});let r=(0,n(43210).createContext)(null)},22308:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{addRefreshMarkerToActiveParallelSegments:function(){return function e(t,n){let[r,i,,o]=t;for(let s in r.includes(a.PAGE_SEGMENT_KEY)&&"refresh"!==o&&(t[2]=n,t[3]="refresh"),i)e(i[s],n)}},refreshInactiveParallelSegments:function(){return o}});let r=n(56928),i=n(59008),a=n(83913);async function o(e){let t=new Set;await s({...e,rootTree:e.updatedTree,fetchedSegments:t})}async function s(e){let{navigatedAt:t,state:n,updatedTree:a,updatedCache:o,includeNextUrl:l,fetchedSegments:u,rootTree:c=a,canonicalUrl:h}=e,[,d,f,p]=a,m=[];if(f&&f!==h&&"refresh"===p&&!u.has(f)){u.add(f);let e=(0,i.fetchServerResponse)(new URL(f,location.origin),{flightRouterState:[c[0],c[1],c[2],"refetch"],nextUrl:l?n.nextUrl:null}).then(e=>{let{flightData:n}=e;if("string"!=typeof n)for(let e of n)(0,r.applyFlightData)(t,o,o,e)});m.push(e)}for(let e in d){let r=s({navigatedAt:t,state:n,updatedTree:d[e],updatedCache:o,includeNextUrl:l,fetchedSegments:u,rootTree:c,canonicalUrl:h});m.push(r)}await Promise.all(m)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},24642:(e,t)=>{function n(e){let t=parseInt(e.slice(0,2),16),n=t>>1&63,r=Array(6);for(let e=0;e<6;e++){let t=n>>5-e&1;r[e]=1===t}return{type:1==(t>>7&1)?"use-cache":"server-action",usedArgs:r,hasRestArgs:1==(1&t)}}function r(e,t){let n=Array(e.length);for(let r=0;r<e.length;r++)(r<6&&t.usedArgs[r]||r>=6&&t.hasRestArgs)&&(n[r]=e[r]);return n}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{extractInfoFromServerReferenceId:function(){return n},omitUnusedArgs:function(){return r}})},25232:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{handleExternalUrl:function(){return b},navigateReducer:function(){return function e(t,n){let{url:T,isExternalUrl:x,navigateType:w,shouldScroll:R,allowAliasing:E}=n,S={},{hash:_}=T,A=(0,i.createHrefFromUrl)(T),M="push"===w;if((0,g.prunePrefetchCache)(t.prefetchCache),S.preserveCustomHistoryState=!1,S.pendingPush=M,x)return b(t,S,T.toString(),M);if(document.getElementById("__next-page-redirect"))return b(t,S,A,M);let j=(0,g.getOrCreatePrefetchCacheEntry)({url:T,nextUrl:t.nextUrl,tree:t.tree,prefetchCache:t.prefetchCache,allowAliasing:E}),{treeAtTimeOfPrefetch:O,data:C}=j;return d.prefetchQueue.bump(C),C.then(d=>{let{flightData:g,canonicalUrl:x,postponed:w}=d,E=Date.now(),C=!1;if(j.lastUsedTime||(j.lastUsedTime=E,C=!0),j.aliased){let r=(0,v.handleAliasedPrefetchEntry)(E,t,g,T,S);return!1===r?e(t,{...n,allowAliasing:!1}):r}if("string"==typeof g)return b(t,S,g,M);let D=x?(0,i.createHrefFromUrl)(x):A;if(_&&t.canonicalUrl.split("#",1)[0]===D.split("#",1)[0])return S.onlyHashChange=!0,S.canonicalUrl=D,S.shouldScroll=R,S.hashFragment=_,S.scrollableSegments=[],(0,c.handleMutable)(t,S);let L=t.tree,k=t.cache,V=[];for(let e of g){let{pathToSegment:n,seedData:i,head:c,isHeadPartial:d,isRootRender:g}=e,v=e.tree,x=["",...n],R=(0,o.applyRouterStatePatchToTree)(x,L,v,A);if(null===R&&(R=(0,o.applyRouterStatePatchToTree)(x,O,v,A)),null!==R){if(i&&g&&w){let e=(0,m.startPPRNavigation)(E,k,L,v,i,c,d,!1,V);if(null!==e){if(null===e.route)return b(t,S,A,M);R=e.route;let n=e.node;null!==n&&(S.cache=n);let i=e.dynamicRequestTree;if(null!==i){let n=(0,r.fetchServerResponse)(T,{flightRouterState:i,nextUrl:t.nextUrl});(0,m.listenForDynamicRequest)(e,n)}}else R=v}else{if((0,l.isNavigatingToNewRootLayout)(L,R))return b(t,S,A,M);let r=(0,f.createEmptyCacheNode)(),i=!1;for(let t of(j.status!==u.PrefetchCacheEntryStatus.stale||C?i=(0,h.applyFlightData)(E,k,r,e,j):(i=function(e,t,n,r){let i=!1;for(let a of(e.rsc=t.rsc,e.prefetchRsc=t.prefetchRsc,e.loading=t.loading,e.parallelRoutes=new Map(t.parallelRoutes),P(r).map(e=>[...n,...e])))(0,y.clearCacheNodeDataForSegmentPath)(e,t,a),i=!0;return i}(r,k,n,v),j.lastUsedTime=E),(0,s.shouldHardNavigate)(x,L)?(r.rsc=k.rsc,r.prefetchRsc=k.prefetchRsc,(0,a.invalidateCacheBelowFlightSegmentPath)(r,k,n),S.cache=r):i&&(S.cache=r,k=r),P(v))){let e=[...n,...t];e[e.length-1]!==p.DEFAULT_SEGMENT_KEY&&V.push(e)}}L=R}}return S.patchedTree=L,S.canonicalUrl=D,S.scrollableSegments=V,S.hashFragment=_,S.shouldScroll=R,(0,c.handleMutable)(t,S)},()=>t)}}});let r=n(59008),i=n(57391),a=n(18468),o=n(86770),s=n(65951),l=n(2030),u=n(59154),c=n(59435),h=n(56928),d=n(75076),f=n(89752),p=n(83913),m=n(65956),g=n(5334),y=n(97464),v=n(9707);function b(e,t,n,r){return t.mpaNavigation=!0,t.canonicalUrl=n,t.pendingPush=r,t.scrollableSegments=void 0,(0,c.handleMutable)(e,t)}function P(e){let t=[],[n,r]=e;if(0===Object.keys(r).length)return[[n]];for(let[e,i]of Object.entries(r))for(let r of P(i))""===n?t.push([e,...r]):t.push([n,e,...r]);return t}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25942:(e,t,n)=>{function r(e){return e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeBasePath",{enumerable:!0,get:function(){return r}}),n(26736),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},26736:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hasBasePath",{enumerable:!0,get:function(){return i}});let r=n(2255);function i(e){return(0,r.pathHasPrefix)(e,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},28627:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"restoreReducer",{enumerable:!0,get:function(){return a}});let r=n(57391),i=n(70642);function a(e,t){var n;let{url:a,tree:o}=t,s=(0,r.createHrefFromUrl)(a),l=o||e.tree,u=e.cache;return{canonicalUrl:s,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:e.focusAndScrollRef,cache:u,prefetchCache:e.prefetchCache,tree:l,nextUrl:null!=(n=(0,i.extractPathFromFlightRouterState)(l))?n:a.pathname}}n(65956),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},29651:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverPatchReducer",{enumerable:!0,get:function(){return c}});let r=n(57391),i=n(86770),a=n(2030),o=n(25232),s=n(56928),l=n(59435),u=n(89752);function c(e,t){let{serverResponse:{flightData:n,canonicalUrl:c},navigatedAt:h}=t,d={};if(d.preserveCustomHistoryState=!1,"string"==typeof n)return(0,o.handleExternalUrl)(e,d,n,e.pushRef.pendingPush);let f=e.tree,p=e.cache;for(let t of n){let{segmentPath:n,tree:l}=t,m=(0,i.applyRouterStatePatchToTree)(["",...n],f,l,e.canonicalUrl);if(null===m)return e;if((0,a.isNavigatingToNewRootLayout)(f,m))return(0,o.handleExternalUrl)(e,d,e.canonicalUrl,e.pushRef.pendingPush);let g=c?(0,r.createHrefFromUrl)(c):void 0;g&&(d.canonicalUrl=g);let y=(0,u.createEmptyCacheNode)();(0,s.applyFlightData)(h,p,y,t),d.patchedTree=m,d.cache=y,p=y,f=m}return(0,l.handleMutable)(e,d)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},30195:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return a},formatWithValidation:function(){return s},urlObjectKeys:function(){return o}});let r=n(84441)._(n(76715)),i=/https?|ftp|gopher|file/;function a(e){let{auth:t,hostname:n}=e,a=e.protocol||"",o=e.pathname||"",s=e.hash||"",l=e.query||"",u=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?u=t+e.host:n&&(u=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(u+=":"+e.port)),l&&"object"==typeof l&&(l=String(r.urlQueryToSearchParams(l)));let c=e.search||l&&"?"+l||"";return a&&!a.endsWith(":")&&(a+=":"),e.slashes||(!a||i.test(a))&&!1!==u?(u="//"+(u||""),o&&"/"!==o[0]&&(o="/"+o)):u||(u=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+a+u+(o=o.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let o=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return a(e)}},32582:(e,t,n)=>{n.d(t,{Q:()=>r});let r=(0,n(43210).createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"})},32708:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},33898:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{fillCacheWithNewSubTreeData:function(){return l},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return u}});let r=n(34400),i=n(41500),a=n(33123),o=n(83913);function s(e,t,n,s,l,u){let{segmentPath:c,seedData:h,tree:d,head:f}=s,p=t,m=n;for(let t=0;t<c.length;t+=2){let n=c[t],s=c[t+1],g=t===c.length-2,y=(0,a.createRouterCacheKey)(s),v=m.parallelRoutes.get(n);if(!v)continue;let b=p.parallelRoutes.get(n);b&&b!==v||(b=new Map(v),p.parallelRoutes.set(n,b));let P=v.get(y),T=b.get(y);if(g){if(h&&(!T||!T.lazyData||T===P)){let t=h[0],n=h[1],a=h[3];T={lazyData:null,rsc:u||t!==o.PAGE_SEGMENT_KEY?n:null,prefetchRsc:null,head:null,prefetchHead:null,loading:a,parallelRoutes:u&&P?new Map(P.parallelRoutes):new Map,navigatedAt:e},P&&u&&(0,r.invalidateCacheByRouterState)(T,P,d),u&&(0,i.fillLazyItemsTillLeafWithHead)(e,T,P,d,h,f,l),b.set(y,T)}continue}T&&P&&(T===P&&(T={lazyData:T.lazyData,rsc:T.rsc,prefetchRsc:T.prefetchRsc,head:T.head,prefetchHead:T.prefetchHead,parallelRoutes:new Map(T.parallelRoutes),loading:T.loading},b.set(y,T)),p=T,m=P)}}function l(e,t,n,r,i){s(e,t,n,r,i,!0)}function u(e,t,n,r,i){s(e,t,n,r,i,!1)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},34400:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return i}});let r=n(33123);function i(e,t,n){for(let i in n[1]){let a=n[1][i][0],o=(0,r.createRouterCacheKey)(a),s=t.parallelRoutes.get(i);if(s){let t=new Map(s);t.delete(o),e.parallelRoutes.set(i,t)}}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},35416:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{HTML_LIMITED_BOT_UA_RE:function(){return r.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return a},getBotType:function(){return l},isBot:function(){return s}});let r=n(95796),i=/Googlebot|Google-PageRenderer|AdsBot-Google|googleweblight|Storebot-Google/i,a=r.HTML_LIMITED_BOT_UA_RE.source;function o(e){return r.HTML_LIMITED_BOT_UA_RE.test(e)}function s(e){return i.test(e)||o(e)}function l(e){return i.test(e)?"dom":o(e)?"html":void 0}},35429:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"serverActionReducer",{enumerable:!0,get:function(){return M}});let r=n(11264),i=n(11448),a=n(91563),o=n(59154),s=n(6361),l=n(57391),u=n(25232),c=n(86770),h=n(2030),d=n(59435),f=n(41500),p=n(89752),m=n(68214),g=n(96493),y=n(22308),v=n(74007),b=n(36875),P=n(97860),T=n(5334),x=n(25942),w=n(26736),R=n(24642);n(50593);let{createFromFetch:E,createTemporaryReferenceSet:S,encodeReply:_}=n(19357);async function A(e,t,n){let o,l,{actionId:u,actionArgs:c}=n,h=S(),d=(0,R.extractInfoFromServerReferenceId)(u),f="use-cache"===d.type?(0,R.omitUnusedArgs)(c,d):c,p=await _(f,{temporaryReferences:h}),m=await fetch("",{method:"POST",headers:{Accept:a.RSC_CONTENT_TYPE_HEADER,[a.ACTION_HEADER]:u,[a.NEXT_ROUTER_STATE_TREE_HEADER]:(0,v.prepareFlightRouterStateForRequest)(e.tree),...{},...t?{[a.NEXT_URL]:t}:{}},body:p}),g=m.headers.get("x-action-redirect"),[y,b]=(null==g?void 0:g.split(";"))||[];switch(b){case"push":o=P.RedirectType.push;break;case"replace":o=P.RedirectType.replace;break;default:o=void 0}let T=!!m.headers.get(a.NEXT_IS_PRERENDER_HEADER);try{let e=JSON.parse(m.headers.get("x-action-revalidated")||"[[],0,0]");l={paths:e[0]||[],tag:!!e[1],cookie:e[2]}}catch(e){l={paths:[],tag:!1,cookie:!1}}let x=y?(0,s.assignLocation)(y,new URL(e.canonicalUrl,window.location.href)):void 0,w=m.headers.get("content-type");if(null==w?void 0:w.startsWith(a.RSC_CONTENT_TYPE_HEADER)){let e=await E(Promise.resolve(m),{callServer:r.callServer,findSourceMapURL:i.findSourceMapURL,temporaryReferences:h});return y?{actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:x,redirectType:o,revalidatedParts:l,isPrerender:T}:{actionResult:e.a,actionFlightData:(0,v.normalizeFlightData)(e.f),redirectLocation:x,redirectType:o,revalidatedParts:l,isPrerender:T}}if(m.status>=400)throw Object.defineProperty(Error("text/plain"===w?await m.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return{redirectLocation:x,redirectType:o,revalidatedParts:l,isPrerender:T}}function M(e,t){let{resolve:n,reject:r}=t,i={},a=e.tree;i.preserveCustomHistoryState=!1;let s=e.nextUrl&&(0,m.hasInterceptionRouteInCurrentTree)(e.tree)?e.nextUrl:null,v=Date.now();return A(e,s,t).then(async m=>{let R,{actionResult:E,actionFlightData:S,redirectLocation:_,redirectType:A,isPrerender:M,revalidatedParts:j}=m;if(_&&(A===P.RedirectType.replace?(e.pushRef.pendingPush=!1,i.pendingPush=!1):(e.pushRef.pendingPush=!0,i.pendingPush=!0),i.canonicalUrl=R=(0,l.createHrefFromUrl)(_,!1)),!S)return(n(E),_)?(0,u.handleExternalUrl)(e,i,_.href,e.pushRef.pendingPush):e;if("string"==typeof S)return n(E),(0,u.handleExternalUrl)(e,i,S,e.pushRef.pendingPush);let O=j.paths.length>0||j.tag||j.cookie;for(let r of S){let{tree:o,seedData:l,head:d,isRootRender:m}=r;if(!m)return console.log("SERVER ACTION APPLY FAILED"),n(E),e;let b=(0,c.applyRouterStatePatchToTree)([""],a,o,R||e.canonicalUrl);if(null===b)return n(E),(0,g.handleSegmentMismatch)(e,t,o);if((0,h.isNavigatingToNewRootLayout)(a,b))return n(E),(0,u.handleExternalUrl)(e,i,R||e.canonicalUrl,e.pushRef.pendingPush);if(null!==l){let t=l[1],n=(0,p.createEmptyCacheNode)();n.rsc=t,n.prefetchRsc=null,n.loading=l[3],(0,f.fillLazyItemsTillLeafWithHead)(v,n,void 0,o,l,d,void 0),i.cache=n,i.prefetchCache=new Map,O&&await (0,y.refreshInactiveParallelSegments)({navigatedAt:v,state:e,updatedTree:b,updatedCache:n,includeNextUrl:!!s,canonicalUrl:i.canonicalUrl||e.canonicalUrl})}i.patchedTree=b,a=b}return _&&R?(O||((0,T.createSeededPrefetchCacheEntry)({url:_,data:{flightData:S,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:e.tree,prefetchCache:e.prefetchCache,nextUrl:e.nextUrl,kind:M?o.PrefetchKind.FULL:o.PrefetchKind.AUTO}),i.prefetchCache=e.prefetchCache),r((0,b.getRedirectError)((0,w.hasBasePath)(R)?(0,x.removeBasePath)(R):R,A||P.RedirectType.push))):n(E),(0,d.handleMutable)(e,i)},t=>(r(t),e))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},38919:(e,t,n)=>{n.r(t),n.d(t,{_:()=>i});var r=0;function i(e){return"__private_"+r+++"_"+e}},41500:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function e(t,n,a,o,s,l,u){if(0===Object.keys(o[1]).length){n.head=l;return}for(let c in o[1]){let h,d=o[1][c],f=d[0],p=(0,r.createRouterCacheKey)(f),m=null!==s&&void 0!==s[2][c]?s[2][c]:null;if(a){let r=a.parallelRoutes.get(c);if(r){let a,o=(null==u?void 0:u.kind)==="auto"&&u.status===i.PrefetchCacheEntryStatus.reusable,s=new Map(r),h=s.get(p);a=null!==m?{lazyData:null,rsc:m[1],prefetchRsc:null,head:null,prefetchHead:null,loading:m[3],parallelRoutes:new Map(null==h?void 0:h.parallelRoutes),navigatedAt:t}:o&&h?{lazyData:h.lazyData,rsc:h.rsc,prefetchRsc:h.prefetchRsc,head:h.head,prefetchHead:h.prefetchHead,parallelRoutes:new Map(h.parallelRoutes),loading:h.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==h?void 0:h.parallelRoutes),loading:null,navigatedAt:t},s.set(p,a),e(t,a,h,d,m||null,l,u),n.parallelRoutes.set(c,s);continue}}if(null!==m){let e=m[1],n=m[3];h={lazyData:null,rsc:e,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:n,navigatedAt:t}}else h={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:t};let g=n.parallelRoutes.get(c);g?g.set(p,h):n.parallelRoutes.set(c,new Map([[p,h]])),e(t,h,void 0,d,m,l,u)}}}});let r=n(33123),i=n(59154);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44397:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"findHeadInCache",{enumerable:!0,get:function(){return i}});let r=n(33123);function i(e,t){return function e(t,n,i){if(0===Object.keys(n).length)return[t,i];let a=Object.keys(n).filter(e=>"children"!==e);for(let o of("children"in n&&a.unshift("children"),a)){let[a,s]=n[o],l=t.parallelRoutes.get(o);if(!l)continue;let u=(0,r.createRouterCacheKey)(a),c=l.get(u);if(!c)continue;let h=e(c,s,i+"/"+u);if(h)return h}return null}(e,t,"")}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},50593:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{NavigationResultTag:function(){return h},PrefetchPriority:function(){return d},cancelPrefetchTask:function(){return l},createCacheKey:function(){return c},getCurrentCacheVersion:function(){return o},navigate:function(){return i},prefetch:function(){return r},reschedulePrefetchTask:function(){return u},revalidateEntireCache:function(){return a},schedulePrefetchTask:function(){return s}});let n=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},r=n,i=n,a=n,o=n,s=n,l=n,u=n,c=n;var h=function(e){return e[e.MPA=0]="MPA",e[e.Success=1]="Success",e[e.NoOp=2]="NoOp",e[e.Async=3]="Async",e}({}),d=function(e){return e[e.Intent=2]="Intent",e[e.Default=1]="Default",e[e.Background=0]="Background",e}({});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51499:(e,t,n)=>{function r(e,t){if(!Object.prototype.hasOwnProperty.call(e,t))throw TypeError("attempted to use private field on non-instance");return e}n.r(t),n.d(t,{_:()=>r})},53038:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return i}});let r=n(43210);function i(e,t){let n=(0,r.useRef)(null),i=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=i.current;t&&(i.current=null,t())}else e&&(n.current=a(e,r)),t&&(i.current=a(t,r))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},54674:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return a}});let r=n(84949),i=n(19169),a=e=>{if(!e.startsWith("/"))return e;let{pathname:t,query:n,hash:a}=(0,i.parsePath)(e);return""+(0,r.removeTrailingSlash)(t)+n+a};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},56928:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyFlightData",{enumerable:!0,get:function(){return a}});let r=n(41500),i=n(33898);function a(e,t,n,a,o){let{tree:s,seedData:l,head:u,isRootRender:c}=a;if(null===l)return!1;if(c){let i=l[1];n.loading=l[3],n.rsc=i,n.prefetchRsc=null,(0,r.fillLazyItemsTillLeafWithHead)(e,n,t,s,l,u,o)}else n.rsc=t.rsc,n.prefetchRsc=t.prefetchRsc,n.parallelRoutes=new Map(t.parallelRoutes),n.loading=t.loading,(0,i.fillCacheWithNewSubTreeData)(e,n,t,a,o);return!0}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},59435:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleMutable",{enumerable:!0,get:function(){return a}});let r=n(70642);function i(e){return void 0!==e}function a(e,t){var n,a;let o=null==(n=t.shouldScroll)||n,s=e.nextUrl;if(i(t.patchedTree)){let n=(0,r.computeChangedPath)(e.tree,t.patchedTree);n?s=n:s||(s=e.canonicalUrl)}return{canonicalUrl:i(t.canonicalUrl)?t.canonicalUrl===e.canonicalUrl?e.canonicalUrl:t.canonicalUrl:e.canonicalUrl,pushRef:{pendingPush:i(t.pendingPush)?t.pendingPush:e.pushRef.pendingPush,mpaNavigation:i(t.mpaNavigation)?t.mpaNavigation:e.pushRef.mpaNavigation,preserveCustomHistoryState:i(t.preserveCustomHistoryState)?t.preserveCustomHistoryState:e.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!o&&(!!i(null==t?void 0:t.scrollableSegments)||e.focusAndScrollRef.apply),onlyHashChange:t.onlyHashChange||!1,hashFragment:o?t.hashFragment&&""!==t.hashFragment?decodeURIComponent(t.hashFragment.slice(1)):e.focusAndScrollRef.hashFragment:null,segmentPaths:o?null!=(a=null==t?void 0:t.scrollableSegments)?a:e.focusAndScrollRef.segmentPaths:[]},cache:t.cache?t.cache:e.cache,prefetchCache:t.prefetchCache?t.prefetchCache:e.prefetchCache,tree:i(t.patchedTree)?t.patchedTree:e.tree,nextUrl:s}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},61794:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return a}});let r=n(79289),i=n(26736);function a(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,i.hasBasePath)(n.pathname)}catch(e){return!1}}},63690:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createMutableActionQueue:function(){return p},dispatchNavigateAction:function(){return y},dispatchTraverseAction:function(){return v},getCurrentAppRouterState:function(){return m},publicAppRouterInstance:function(){return b}});let r=n(59154),i=n(8830),a=n(43210),o=n(91992);n(50593);let s=n(19129),l=n(96127),u=n(89752),c=n(75076),h=n(73406);function d(e,t){null!==e.pending&&(e.pending=e.pending.next,null!==e.pending?f({actionQueue:e,action:e.pending,setState:t}):e.needsRefresh&&(e.needsRefresh=!1,e.dispatch({type:r.ACTION_REFRESH,origin:window.location.origin},t)))}async function f(e){let{actionQueue:t,action:n,setState:r}=e,i=t.state;t.pending=n;let a=n.payload,s=t.action(i,a);function l(e){n.discarded||(t.state=e,d(t,r),n.resolve(e))}(0,o.isThenable)(s)?s.then(l,e=>{d(t,r),n.reject(e)}):l(s)}function p(e,t){let n={state:e,dispatch:(e,t)=>(function(e,t,n){let i={resolve:n,reject:()=>{}};if(t.type!==r.ACTION_RESTORE){let e=new Promise((e,t)=>{i={resolve:e,reject:t}});(0,a.startTransition)(()=>{n(e)})}let o={payload:t,next:null,resolve:i.resolve,reject:i.reject};null===e.pending?(e.last=o,f({actionQueue:e,action:o,setState:n})):t.type===r.ACTION_NAVIGATE||t.type===r.ACTION_RESTORE?(e.pending.discarded=!0,o.next=e.pending.next,e.pending.payload.type===r.ACTION_SERVER_ACTION&&(e.needsRefresh=!0),f({actionQueue:e,action:o,setState:n})):(null!==e.last&&(e.last.next=o),e.last=o)})(n,e,t),action:async(e,t)=>(0,i.reducer)(e,t),pending:null,last:null,onRouterTransitionStart:null!==t&&"function"==typeof t.onRouterTransitionStart?t.onRouterTransitionStart:null};return n}function m(){return null}function g(){return null}function y(e,t,n,i){let a=new URL((0,l.addBasePath)(e),location.href);(0,h.setLinkForCurrentNavigation)(i);(0,s.dispatchAppRouterAction)({type:r.ACTION_NAVIGATE,url:a,isExternalUrl:(0,u.isExternalURL)(a),locationSearch:location.search,shouldScroll:n,navigateType:t,allowAliasing:!0})}function v(e,t){(0,s.dispatchAppRouterAction)({type:r.ACTION_RESTORE,url:new URL(e),tree:t})}let b={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(e,t)=>{let n=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),i=(0,u.createPrefetchURL)(e);if(null!==i){var a;(0,c.prefetchReducer)(n.state,{type:r.ACTION_PREFETCH,url:i,kind:null!=(a=null==t?void 0:t.kind)?a:r.PrefetchKind.FULL})}},replace:(e,t)=>{(0,a.startTransition)(()=>{var n;y(e,"replace",null==(n=null==t?void 0:t.scroll)||n,null)})},push:(e,t)=>{(0,a.startTransition)(()=>{var n;y(e,"push",null==(n=null==t?void 0:t.scroll)||n,null)})},refresh:()=>{(0,a.startTransition)(()=>{(0,s.dispatchAppRouterAction)({type:r.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65951:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"shouldHardNavigate",{enumerable:!0,get:function(){return function e(t,n){let[a,o]=n,[s,l]=t;return(0,i.matchSegment)(s,a)?!(t.length<=2)&&e((0,r.getNextFlightSegmentPath)(t),o[l]):!!Array.isArray(s)}}});let r=n(74007),i=n(14077);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},65956:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{abortTask:function(){return p},listenForDynamicRequest:function(){return f},startPPRNavigation:function(){return u},updateCacheNodeOnPopstateRestoration:function(){return function e(t,n){let r=n[1],i=t.parallelRoutes,o=new Map(i);for(let t in r){let n=r[t],s=n[0],l=(0,a.createRouterCacheKey)(s),u=i.get(t);if(void 0!==u){let r=u.get(l);if(void 0!==r){let i=e(r,n),a=new Map(u);a.set(l,i),o.set(t,a)}}}let s=t.rsc,l=y(s)&&"pending"===s.status;return{lazyData:null,rsc:s,head:t.head,prefetchHead:l?t.prefetchHead:[null,null],prefetchRsc:l?t.prefetchRsc:null,loading:t.loading,parallelRoutes:o,navigatedAt:t.navigatedAt}}}});let r=n(83913),i=n(14077),a=n(33123),o=n(2030),s=n(5334),l={route:null,node:null,dynamicRequestTree:null,children:null};function u(e,t,n,o,s,u,d,f,p){return function e(t,n,o,s,u,d,f,p,m,g,y){let v=o[1],b=s[1],P=null!==d?d[2]:null;u||!0===s[4]&&(u=!0);let T=n.parallelRoutes,x=new Map(T),w={},R=null,E=!1,S={};for(let n in b){let o,s=b[n],h=v[n],d=T.get(n),_=null!==P?P[n]:null,A=s[0],M=g.concat([n,A]),j=(0,a.createRouterCacheKey)(A),O=void 0!==h?h[0]:void 0,C=void 0!==d?d.get(j):void 0;if(null!==(o=A===r.DEFAULT_SEGMENT_KEY?void 0!==h?{route:h,node:null,dynamicRequestTree:null,children:null}:c(t,h,s,C,u,void 0!==_?_:null,f,p,M,y):m&&0===Object.keys(s[1]).length?c(t,h,s,C,u,void 0!==_?_:null,f,p,M,y):void 0!==h&&void 0!==O&&(0,i.matchSegment)(A,O)&&void 0!==C&&void 0!==h?e(t,C,h,s,u,_,f,p,m,M,y):c(t,h,s,C,u,void 0!==_?_:null,f,p,M,y))){if(null===o.route)return l;null===R&&(R=new Map),R.set(n,o);let e=o.node;if(null!==e){let t=new Map(d);t.set(j,e),x.set(n,t)}let t=o.route;w[n]=t;let r=o.dynamicRequestTree;null!==r?(E=!0,S[n]=r):S[n]=t}else w[n]=s,S[n]=s}if(null===R)return null;let _={lazyData:null,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,loading:n.loading,parallelRoutes:x,navigatedAt:t};return{route:h(s,w),node:_,dynamicRequestTree:E?h(s,S):null,children:R}}(e,t,n,o,!1,s,u,d,f,[],p)}function c(e,t,n,r,i,u,c,f,p,m){return!i&&(void 0===t||(0,o.isNavigatingToNewRootLayout)(t,n))?l:function e(t,n,r,i,o,l,u,c){let f,p,m,g,y=n[1],v=0===Object.keys(y).length;if(void 0!==r&&r.navigatedAt+s.DYNAMIC_STALETIME_MS>t)f=r.rsc,p=r.loading,m=r.head,g=r.navigatedAt;else if(null===i)return d(t,n,null,o,l,u,c);else if(f=i[1],p=i[3],m=v?o:null,g=t,i[4]||l&&v)return d(t,n,i,o,l,u,c);let b=null!==i?i[2]:null,P=new Map,T=void 0!==r?r.parallelRoutes:null,x=new Map(T),w={},R=!1;if(v)c.push(u);else for(let n in y){let r=y[n],i=null!==b?b[n]:null,s=null!==T?T.get(n):void 0,h=r[0],d=u.concat([n,h]),f=(0,a.createRouterCacheKey)(h),p=e(t,r,void 0!==s?s.get(f):void 0,i,o,l,d,c);P.set(n,p);let m=p.dynamicRequestTree;null!==m?(R=!0,w[n]=m):w[n]=r;let g=p.node;if(null!==g){let e=new Map;e.set(f,g),x.set(n,e)}}return{route:n,node:{lazyData:null,rsc:f,prefetchRsc:null,head:m,prefetchHead:null,loading:p,parallelRoutes:x,navigatedAt:g},dynamicRequestTree:R?h(n,w):null,children:P}}(e,n,r,u,c,f,p,m)}function h(e,t){let n=[e[0],t];return 2 in e&&(n[2]=e[2]),3 in e&&(n[3]=e[3]),4 in e&&(n[4]=e[4]),n}function d(e,t,n,r,i,o,s){let l=h(t,t[1]);return l[3]="refetch",{route:t,node:function e(t,n,r,i,o,s,l){let u=n[1],c=null!==r?r[2]:null,h=new Map;for(let n in u){let r=u[n],d=null!==c?c[n]:null,f=r[0],p=s.concat([n,f]),m=(0,a.createRouterCacheKey)(f),g=e(t,r,void 0===d?null:d,i,o,p,l),y=new Map;y.set(m,g),h.set(n,y)}let d=0===h.size;d&&l.push(s);let f=null!==r?r[1]:null,p=null!==r?r[3]:null;return{lazyData:null,parallelRoutes:h,prefetchRsc:void 0!==f?f:null,prefetchHead:d?i:[null,null],loading:void 0!==p?p:null,rsc:v(),head:d?v():null,navigatedAt:t}}(e,t,n,r,i,o,s),dynamicRequestTree:l,children:null}}function f(e,t){t.then(t=>{let{flightData:n}=t;if("string"!=typeof n){for(let t of n){let{segmentPath:n,tree:r,seedData:o,head:s}=t;o&&function(e,t,n,r,o){let s=e;for(let e=0;e<t.length;e+=2){let n=t[e],r=t[e+1],a=s.children;if(null!==a){let e=a.get(n);if(void 0!==e){let t=e.route[0];if((0,i.matchSegment)(r,t)){s=e;continue}}}return}!function e(t,n,r,o){if(null===t.dynamicRequestTree)return;let s=t.children,l=t.node;if(null===s){null!==l&&(function e(t,n,r,o,s){let l=n[1],u=r[1],c=o[2],h=t.parallelRoutes;for(let t in l){let n=l[t],r=u[t],o=c[t],d=h.get(t),f=n[0],p=(0,a.createRouterCacheKey)(f),g=void 0!==d?d.get(p):void 0;void 0!==g&&(void 0!==r&&(0,i.matchSegment)(f,r[0])&&null!=o?e(g,n,r,o,s):m(n,g,null))}let d=t.rsc,f=o[1];null===d?t.rsc=f:y(d)&&d.resolve(f);let p=t.head;y(p)&&p.resolve(s)}(l,t.route,n,r,o),t.dynamicRequestTree=null);return}let u=n[1],c=r[2];for(let t in n){let n=u[t],r=c[t],a=s.get(t);if(void 0!==a){let t=a.route[0];if((0,i.matchSegment)(n[0],t)&&null!=r)return e(a,n,r,o)}}}(s,n,r,o)}(e,n,r,o,s)}p(e,null)}},t=>{p(e,t)})}function p(e,t){let n=e.node;if(null===n)return;let r=e.children;if(null===r)m(e.route,n,t);else for(let e of r.values())p(e,t);e.dynamicRequestTree=null}function m(e,t,n){let r=e[1],i=t.parallelRoutes;for(let e in r){let t=r[e],o=i.get(e);if(void 0===o)continue;let s=t[0],l=(0,a.createRouterCacheKey)(s),u=o.get(l);void 0!==u&&m(t,u,n)}let o=t.rsc;y(o)&&(null===n?o.resolve(null):o.reject(n));let s=t.head;y(s)&&s.resolve(null)}let g=Symbol();function y(e){return e&&e.tag===g}function v(){let e,t,n=new Promise((n,r)=>{e=n,t=r});return n.status="pending",n.resolve=t=>{"pending"===n.status&&(n.status="fulfilled",n.value=t,e(t))},n.reject=e=>{"pending"===n.status&&(n.status="rejected",n.reason=e,t(e))},n.tag=g,n}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},70642:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{computeChangedPath:function(){return c},extractPathFromFlightRouterState:function(){return u},getSelectedParams:function(){return function e(t,n){for(let r of(void 0===n&&(n={}),Object.values(t[1]))){let t=r[0],a=Array.isArray(t),o=a?t[1]:t;!o||o.startsWith(i.PAGE_SEGMENT_KEY)||(a&&("c"===t[2]||"oc"===t[2])?n[t[0]]=t[1].split("/"):a&&(n[t[0]]=t[1]),n=e(r,n))}return n}}});let r=n(72859),i=n(83913),a=n(14077),o=e=>"/"===e[0]?e.slice(1):e,s=e=>"string"==typeof e?"children"===e?"":e:e[1];function l(e){return e.reduce((e,t)=>""===(t=o(t))||(0,i.isGroupSegment)(t)?e:e+"/"+t,"")||"/"}function u(e){var t;let n=Array.isArray(e[0])?e[0][1]:e[0];if(n===i.DEFAULT_SEGMENT_KEY||r.INTERCEPTION_ROUTE_MARKERS.some(e=>n.startsWith(e)))return;if(n.startsWith(i.PAGE_SEGMENT_KEY))return"";let a=[s(n)],o=null!=(t=e[1])?t:{},c=o.children?u(o.children):void 0;if(void 0!==c)a.push(c);else for(let[e,t]of Object.entries(o)){if("children"===e)continue;let n=u(t);void 0!==n&&a.push(n)}return l(a)}function c(e,t){let n=function e(t,n){let[i,o]=t,[l,c]=n,h=s(i),d=s(l);if(r.INTERCEPTION_ROUTE_MARKERS.some(e=>h.startsWith(e)||d.startsWith(e)))return"";if(!(0,a.matchSegment)(i,l)){var f;return null!=(f=u(n))?f:""}for(let t in o)if(c[t]){let n=e(o[t],c[t]);if(null!==n)return s(l)+"/"+n}return null}(e,t);return null==n||"/"===n?n:l(n.split("/"))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},72789:(e,t,n)=>{n.d(t,{M:()=>i});var r=n(43210);function i(e){let t=(0,r.useRef)(null);return null===t.current&&(t.current=e()),t.current}},73406:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{IDLE_LINK_STATUS:function(){return u},PENDING_LINK_STATUS:function(){return l},mountFormInstance:function(){return v},mountLinkInstance:function(){return y},onLinkVisibilityChanged:function(){return P},onNavigationIntent:function(){return T},pingVisibleLinks:function(){return w},setLinkForCurrentNavigation:function(){return c},unmountLinkForCurrentNavigation:function(){return h},unmountPrefetchableInstance:function(){return b}}),n(63690);let r=n(89752),i=n(59154),a=n(50593),o=n(43210),s=null,l={pending:!0},u={pending:!1};function c(e){(0,o.startTransition)(()=>{null==s||s.setOptimisticLinkStatus(u),null==e||e.setOptimisticLinkStatus(l),s=e})}function h(e){s===e&&(s=null)}let d="function"==typeof WeakMap?new WeakMap:new Map,f=new Set,p="function"==typeof IntersectionObserver?new IntersectionObserver(function(e){for(let t of e){let e=t.intersectionRatio>0;P(t.target,e)}},{rootMargin:"200px"}):null;function m(e,t){void 0!==d.get(e)&&b(e),d.set(e,t),null!==p&&p.observe(e)}function g(e){try{return(0,r.createPrefetchURL)(e)}catch(t){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),null}}function y(e,t,n,r,i,a){if(i){let i=g(t);if(null!==i){let t={router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:a};return m(e,t),t}}return{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:null,setOptimisticLinkStatus:a}}function v(e,t,n,r){let i=g(t);null!==i&&m(e,{router:n,kind:r,isVisible:!1,wasHoveredOrTouched:!1,prefetchTask:null,cacheVersion:-1,prefetchHref:i.href,setOptimisticLinkStatus:null})}function b(e){let t=d.get(e);if(void 0!==t){d.delete(e),f.delete(t);let n=t.prefetchTask;null!==n&&(0,a.cancelPrefetchTask)(n)}null!==p&&p.unobserve(e)}function P(e,t){let n=d.get(e);void 0!==n&&(n.isVisible=t,t?f.add(n):f.delete(n),x(n))}function T(e,t){let n=d.get(e);void 0!==n&&void 0!==n&&(n.wasHoveredOrTouched=!0,x(n))}function x(e){let t=e.prefetchTask;if(!e.isVisible){null!==t&&(0,a.cancelPrefetchTask)(t);return}}function w(e,t){let n=(0,a.getCurrentCacheVersion)();for(let r of f){let o=r.prefetchTask;if(null!==o&&r.cacheVersion===n&&o.key.nextUrl===e&&o.treeAtTimeOfPrefetch===t)continue;null!==o&&(0,a.cancelPrefetchTask)(o);let s=(0,a.createCacheKey)(r.prefetchHref,e),l=r.wasHoveredOrTouched?a.PrefetchPriority.Intent:a.PrefetchPriority.Default;r.prefetchTask=(0,a.schedulePrefetchTask)(s,t,r.kind===i.PrefetchKind.FULL,l),r.cacheVersion=(0,a.getCurrentCacheVersion)()}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75076:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{prefetchQueue:function(){return a},prefetchReducer:function(){return o}});let r=n(5144),i=n(5334),a=new r.PromiseQueue(5),o=function(e,t){(0,i.prunePrefetchCache)(e.prefetchCache);let{url:n}=t;return(0,i.getOrCreatePrefetchCacheEntry)({url:n,nextUrl:e.nextUrl,prefetchCache:e.prefetchCache,kind:t.kind,tree:e.tree,allowAliasing:!0}),e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},75324:(e,t,n)=>{n.d(t,{A:()=>o});var r=n(43210),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=(e,t)=>{let n=(0,r.forwardRef)(({color:n="currentColor",size:o=24,strokeWidth:s=2,absoluteStrokeWidth:l,className:u="",children:c,...h},d)=>(0,r.createElement)("svg",{ref:d,...i,width:o,height:o,stroke:n,strokeWidth:l?24*Number(s)/Number(o):s,className:["lucide",`lucide-${a(e)}`,u].join(" "),...h},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(c)?c:[c]]));return n.displayName=`${e}`,n}},76715:(e,t)=>{function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,i]of Object.entries(e))if(Array.isArray(i))for(let e of i)t.append(n,r(e));else t.set(n,r(i));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return i}})},77022:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AppRouterAnnouncer",{enumerable:!0,get:function(){return o}});let r=n(43210),i=n(51215),a="next-route-announcer";function o(e){let{tree:t}=e,[n,o]=(0,r.useState)(null);(0,r.useEffect)(()=>(o(function(){var e;let t=document.getElementsByName(a)[0];if(null==t||null==(e=t.shadowRoot)?void 0:e.childNodes[0])return t.shadowRoot.childNodes[0];{let e=document.createElement(a);e.style.cssText="position:absolute";let t=document.createElement("div");return t.ariaLive="assertive",t.id="__next-route-announcer__",t.role="alert",t.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",e.attachShadow({mode:"open"}).appendChild(t),document.body.appendChild(e),t}}()),()=>{let e=document.getElementsByTagName(a)[0];(null==e?void 0:e.isConnected)&&document.body.removeChild(e)}),[]);let[s,l]=(0,r.useState)(""),u=(0,r.useRef)(void 0);return(0,r.useEffect)(()=>{let e="";if(document.title)e=document.title;else{let t=document.querySelector("h1");t&&(e=t.innerText||t.textContent||"")}void 0!==u.current&&u.current!==e&&l(e),u.current=e},[t]),n?(0,i.createPortal)(s,n):null}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},78866:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"refreshReducer",{enumerable:!0,get:function(){return p}});let r=n(59008),i=n(57391),a=n(86770),o=n(2030),s=n(25232),l=n(59435),u=n(41500),c=n(89752),h=n(96493),d=n(68214),f=n(22308);function p(e,t){let{origin:n}=t,p={},m=e.canonicalUrl,g=e.tree;p.preserveCustomHistoryState=!1;let y=(0,c.createEmptyCacheNode)(),v=(0,d.hasInterceptionRouteInCurrentTree)(e.tree);y.lazyData=(0,r.fetchServerResponse)(new URL(m,n),{flightRouterState:[g[0],g[1],g[2],"refetch"],nextUrl:v?e.nextUrl:null});let b=Date.now();return y.lazyData.then(async n=>{let{flightData:r,canonicalUrl:c}=n;if("string"==typeof r)return(0,s.handleExternalUrl)(e,p,r,e.pushRef.pendingPush);for(let n of(y.lazyData=null,r)){let{tree:r,seedData:l,head:d,isRootRender:P}=n;if(!P)return console.log("REFRESH FAILED"),e;let T=(0,a.applyRouterStatePatchToTree)([""],g,r,e.canonicalUrl);if(null===T)return(0,h.handleSegmentMismatch)(e,t,r);if((0,o.isNavigatingToNewRootLayout)(g,T))return(0,s.handleExternalUrl)(e,p,m,e.pushRef.pendingPush);let x=c?(0,i.createHrefFromUrl)(c):void 0;if(c&&(p.canonicalUrl=x),null!==l){let e=l[1],t=l[3];y.rsc=e,y.prefetchRsc=null,y.loading=t,(0,u.fillLazyItemsTillLeafWithHead)(b,y,void 0,r,l,d,void 0),p.prefetchCache=new Map}await (0,f.refreshInactiveParallelSegments)({navigatedAt:b,state:e,updatedTree:T,updatedCache:y,includeNextUrl:v,canonicalUrl:p.canonicalUrl||e.canonicalUrl}),p.cache=y,p.patchedTree=T,g=T}return(0,l.handleMutable)(e,p)},()=>e)}n(50593),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79289:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return v},MissingStaticPage:function(){return y},NormalizeError:function(){return m},PageNotFoundError:function(){return g},SP:function(){return d},ST:function(){return f},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return l},getLocationOrigin:function(){return o},getURL:function(){return s},isAbsoluteUrl:function(){return a},isResSent:function(){return u},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return b}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,i=Array(r),a=0;a<r;a++)i[a]=arguments[a];return n||(n=!0,t=e(...i)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function o(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function s(){let{href:e}=window.location,t=o();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function u(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&u(n))return r;if(!r)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,f=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class m extends Error{}class g extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class v extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},84949:(e,t)=>{function n(e){return e.replace(/\/$/,"")||"/"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"removeTrailingSlash",{enumerable:!0,get:function(){return n}})},85814:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return g},useLinkStatus:function(){return v}});let r=n(84441),i=n(60687),a=r._(n(43210)),o=n(30195),s=n(22142),l=n(59154),u=n(53038),c=n(79289),h=n(96127);n(50148);let d=n(73406),f=n(61794),p=n(63690);function m(e){return"string"==typeof e?e:(0,o.formatUrl)(e)}function g(e){let t,n,r,[o,g]=(0,a.useOptimistic)(d.IDLE_LINK_STATUS),v=(0,a.useRef)(null),{href:b,as:P,children:T,prefetch:x=null,passHref:w,replace:R,shallow:E,scroll:S,onClick:_,onMouseEnter:A,onTouchStart:M,legacyBehavior:j=!1,onNavigate:O,ref:C,unstable_dynamicOnHover:D,...L}=e;t=T,j&&("string"==typeof t||"number"==typeof t)&&(t=(0,i.jsx)("a",{children:t}));let k=a.default.useContext(s.AppRouterContext),V=!1!==x,U=null===x?l.PrefetchKind.AUTO:l.PrefetchKind.FULL,{href:N,as:F}=a.default.useMemo(()=>{let e=m(b);return{href:e,as:P?m(P):e}},[b,P]);j&&(n=a.default.Children.only(t));let I=j?n&&"object"==typeof n&&n.ref:C,B=a.default.useCallback(e=>(null!==k&&(v.current=(0,d.mountLinkInstance)(e,N,k,U,V,g)),()=>{v.current&&((0,d.unmountLinkForCurrentNavigation)(v.current),v.current=null),(0,d.unmountPrefetchableInstance)(e)}),[V,N,k,U,g]),H={ref:(0,u.useMergedRef)(B,I),onClick(e){j||"function"!=typeof _||_(e),j&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),k&&(e.defaultPrevented||function(e,t,n,r,i,o,s){let{nodeName:l}=e.currentTarget;if(!("A"===l.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,f.isLocalURL)(t)){i&&(e.preventDefault(),location.replace(t));return}e.preventDefault(),a.default.startTransition(()=>{if(s){let e=!1;if(s({preventDefault:()=>{e=!0}}),e)return}(0,p.dispatchNavigateAction)(n||t,i?"replace":"push",null==o||o,r.current)})}}(e,N,F,v,R,S,O))},onMouseEnter(e){j||"function"!=typeof A||A(e),j&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),k&&V&&(0,d.onNavigationIntent)(e.currentTarget,!0===D)},onTouchStart:function(e){j||"function"!=typeof M||M(e),j&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),k&&V&&(0,d.onNavigationIntent)(e.currentTarget,!0===D)}};return(0,c.isAbsoluteUrl)(F)?H.href=F:j&&!w&&("a"!==n.type||"href"in n.props)||(H.href=(0,h.addBasePath)(F)),r=j?a.default.cloneElement(n,H):(0,i.jsx)("a",{...L,...H,children:t}),(0,i.jsx)(y.Provider,{value:o,children:r})}n(32708);let y=(0,a.createContext)(d.IDLE_LINK_STATUS),v=()=>(0,a.useContext)(y);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},86044:(e,t,n)=>{n.d(t,{xQ:()=>a});var r=n(43210),i=n(21279);function a(e=!0){let t=(0,r.useContext)(i.t);if(null===t)return[!0,null];let{isPresent:n,onExitComplete:o,register:s}=t,l=(0,r.useId)();(0,r.useEffect)(()=>{e&&s(l)},[e]);let u=(0,r.useCallback)(()=>e&&o&&o(l),[l,o,e]);return!n&&o?[!1,u]:[!0]}},86770:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function e(t,n,r,l){let u,[c,h,d,f,p]=n;if(1===t.length){let e=s(n,r);return(0,o.addRefreshMarkerToActiveParallelSegments)(e,l),e}let[m,g]=t;if(!(0,a.matchSegment)(m,c))return null;if(2===t.length)u=s(h[g],r);else if(null===(u=e((0,i.getNextFlightSegmentPath)(t),h[g],r,l)))return null;let y=[t[0],{...h,[g]:u},d,f];return p&&(y[4]=!0),(0,o.addRefreshMarkerToActiveParallelSegments)(y,l),y}}});let r=n(83913),i=n(74007),a=n(14077),o=n(22308);function s(e,t){let[n,i]=e,[o,l]=t;if(o===r.DEFAULT_SEGMENT_KEY&&n!==r.DEFAULT_SEGMENT_KEY)return e;if((0,a.matchSegment)(n,o)){let t={};for(let e in i)void 0!==l[e]?t[e]=s(i[e],l[e]):t[e]=i[e];for(let e in l)t[e]||(t[e]=l[e]);let r=[n,t];return e[2]&&(r[2]=e[2]),e[3]&&(r[3]=e[3]),e[4]&&(r[4]=e[4]),r}return t}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},89752:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{createEmptyCacheNode:function(){return M},createPrefetchURL:function(){return _},default:function(){return D},isExternalURL:function(){return S}});let r=n(84441),i=n(60687),a=r._(n(43210)),o=n(22142),s=n(59154),l=n(57391),u=n(10449),c=n(19129),h=r._(n(35656)),d=n(35416),f=n(96127),p=n(77022),m=n(67086),g=n(44397),y=n(89330),v=n(25942),b=n(26736),P=n(70642),T=n(12776),x=n(63690),w=n(36875),R=n(97860);n(73406);let E={};function S(e){return e.origin!==window.location.origin}function _(e){let t;if((0,d.isBot)(window.navigator.userAgent))return null;try{t=new URL((0,f.addBasePath)(e),window.location.href)}catch(t){throw Object.defineProperty(Error("Cannot prefetch '"+e+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return S(t)?null:t}function A(e){let{appRouterState:t}=e;return(0,a.useInsertionEffect)(()=>{let{tree:e,pushRef:n,canonicalUrl:r}=t,i={...n.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:e};n.pendingPush&&(0,l.createHrefFromUrl)(new URL(window.location.href))!==r?(n.pendingPush=!1,window.history.pushState(i,"",r)):window.history.replaceState(i,"",r)},[t]),(0,a.useEffect)(()=>{},[t.nextUrl,t.tree]),null}function M(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function j(e){null==e&&(e={});let t=window.history.state,n=null==t?void 0:t.__NA;n&&(e.__NA=n);let r=null==t?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;return r&&(e.__PRIVATE_NEXTJS_INTERNALS_TREE=r),e}function O(e){let{headCacheNode:t}=e,n=null!==t?t.head:null,r=null!==t?t.prefetchHead:null,i=null!==r?r:n;return(0,a.useDeferredValue)(n,i)}function C(e){let t,{actionQueue:n,assetPrefix:r,globalError:l}=e,d=(0,c.useActionQueue)(n),{canonicalUrl:f}=d,{searchParams:T,pathname:S}=(0,a.useMemo)(()=>{let e=new URL(f,"http://n");return{searchParams:e.searchParams,pathname:(0,b.hasBasePath)(e.pathname)?(0,v.removeBasePath)(e.pathname):e.pathname}},[f]);(0,a.useEffect)(()=>{function e(e){var t;e.persisted&&(null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(E.pendingMpaPath=void 0,(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",e),()=>{window.removeEventListener("pageshow",e)}},[]),(0,a.useEffect)(()=>{function e(e){let t="reason"in e?e.reason:e.error;if((0,R.isRedirectError)(t)){e.preventDefault();let n=(0,w.getURLFromRedirectError)(t);(0,w.getRedirectTypeFromError)(t)===R.RedirectType.push?x.publicAppRouterInstance.push(n,{}):x.publicAppRouterInstance.replace(n,{})}}return window.addEventListener("error",e),window.addEventListener("unhandledrejection",e),()=>{window.removeEventListener("error",e),window.removeEventListener("unhandledrejection",e)}},[]);let{pushRef:_}=d;if(_.mpaNavigation){if(E.pendingMpaPath!==f){let e=window.location;_.pendingPush?e.assign(f):e.replace(f),E.pendingMpaPath=f}(0,a.use)(y.unresolvedThenable)}(0,a.useEffect)(()=>{let e=window.history.pushState.bind(window.history),t=window.history.replaceState.bind(window.history),n=e=>{var t;let n=window.location.href,r=null==(t=window.history.state)?void 0:t.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,a.startTransition)(()=>{(0,c.dispatchAppRouterAction)({type:s.ACTION_RESTORE,url:new URL(null!=e?e:n,n),tree:r})})};window.history.pushState=function(t,r,i){return(null==t?void 0:t.__NA)||(null==t?void 0:t._N)||(t=j(t),i&&n(i)),e(t,r,i)},window.history.replaceState=function(e,r,i){return(null==e?void 0:e.__NA)||(null==e?void 0:e._N)||(e=j(e),i&&n(i)),t(e,r,i)};let r=e=>{if(e.state){if(!e.state.__NA)return void window.location.reload();(0,a.startTransition)(()=>{(0,x.dispatchTraverseAction)(window.location.href,e.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",r),()=>{window.history.pushState=e,window.history.replaceState=t,window.removeEventListener("popstate",r)}},[]);let{cache:M,tree:C,nextUrl:D,focusAndScrollRef:L}=d,k=(0,a.useMemo)(()=>(0,g.findHeadInCache)(M,C[1]),[M,C]),U=(0,a.useMemo)(()=>(0,P.getSelectedParams)(C),[C]),N=(0,a.useMemo)(()=>({parentTree:C,parentCacheNode:M,parentSegmentPath:null,url:f}),[C,M,f]),F=(0,a.useMemo)(()=>({tree:C,focusAndScrollRef:L,nextUrl:D}),[C,L,D]);if(null!==k){let[e,n]=k;t=(0,i.jsx)(O,{headCacheNode:e},n)}else t=null;let I=(0,i.jsxs)(m.RedirectBoundary,{children:[t,M.rsc,(0,i.jsx)(p.AppRouterAnnouncer,{tree:C})]});return I=(0,i.jsx)(h.ErrorBoundary,{errorComponent:l[0],errorStyles:l[1],children:I}),(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(A,{appRouterState:d}),(0,i.jsx)(V,{}),(0,i.jsx)(u.PathParamsContext.Provider,{value:U,children:(0,i.jsx)(u.PathnameContext.Provider,{value:S,children:(0,i.jsx)(u.SearchParamsContext.Provider,{value:T,children:(0,i.jsx)(o.GlobalLayoutRouterContext.Provider,{value:F,children:(0,i.jsx)(o.AppRouterContext.Provider,{value:x.publicAppRouterInstance,children:(0,i.jsx)(o.LayoutRouterContext.Provider,{value:N,children:I})})})})})})]})}function D(e){let{actionQueue:t,globalErrorComponentAndStyles:[n,r],assetPrefix:a}=e;return(0,T.useNavFailureHandler)(),(0,i.jsx)(h.ErrorBoundary,{errorComponent:h.default,children:(0,i.jsx)(C,{actionQueue:t,assetPrefix:a,globalError:[n,r]})})}let L=new Set,k=new Set;function V(){let[,e]=a.default.useState(0),t=L.size;return(0,a.useEffect)(()=>{let n=()=>e(e=>e+1);return k.add(n),t!==L.size&&n(),()=>{k.delete(n)}},[t,e]),[...L].map((e,t)=>(0,i.jsx)("link",{rel:"stylesheet",href:""+e,precedence:"next"},t))}globalThis._N_E_STYLE_LOAD=function(e){let t=L.size;return L.add(e),L.size!==t&&k.forEach(e=>e()),Promise.resolve()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},95796:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return n}});let n=/Mediapartners-Google|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addBasePath",{enumerable:!0,get:function(){return a}});let r=n(98834),i=n(54674);function a(e,t){return(0,i.normalizePathTrailingSlash)((0,r.addPathPrefix)(e,""))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},96493:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"handleSegmentMismatch",{enumerable:!0,get:function(){return i}});let r=n(25232);function i(e,t,n){return(0,r.handleExternalUrl)(e,{},e.canonicalUrl,!0)}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97464:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function e(t,n,a){let o=a.length<=2,[s,l]=a,u=(0,i.createRouterCacheKey)(l),c=n.parallelRoutes.get(s),h=t.parallelRoutes.get(s);h&&h!==c||(h=new Map(c),t.parallelRoutes.set(s,h));let d=null==c?void 0:c.get(u),f=h.get(u);if(o){f&&f.lazyData&&f!==d||h.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!f||!d){f||h.set(u,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return f===d&&(f={lazyData:f.lazyData,rsc:f.rsc,prefetchRsc:f.prefetchRsc,head:f.head,prefetchHead:f.prefetchHead,parallelRoutes:new Map(f.parallelRoutes),loading:f.loading},h.set(u,f)),e(f,d,(0,r.getNextFlightSegmentPath)(a))}}});let r=n(74007),i=n(33123);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},97905:(e,t,n)=>{let r;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}n.d(t,{P:()=>am});let a=e=>Array.isArray(e);function o(e,t){if(!Array.isArray(t))return!1;let n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function s(e){return"string"==typeof e||Array.isArray(e)}function l(e){let t=[{},{}];return null==e||e.values.forEach((e,n)=>{t[0][n]=e.get(),t[1][n]=e.getVelocity()}),t}function u(e,t,n,r){if("function"==typeof t){let[i,a]=l(r);t=t(void 0!==n?n:e.custom,i,a)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,a]=l(r);t=t(void 0!==n?n:e.custom,i,a)}return t}function c(e,t,n){let r=e.getProps();return u(r,t,void 0!==n?n:r.custom,e)}let h=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],d=["initial",...h];function f(e){let t;return()=>(void 0===t&&(t=e()),t)}let p=f(()=>void 0!==window.ScrollTimeline);class m{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let n=0;n<this.animations.length;n++)this.animations[n][e]=t}attachTimeline(e,t){let n=this.animations.map(n=>p()&&n.attachTimeline?n.attachTimeline(e):"function"==typeof t?t(n):void 0);return()=>{n.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class g extends m{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function y(e,t){return e?e[t]||e.default||e:void 0}function v(e){let t=0,n=e.next(t);for(;!n.done&&t<2e4;)t+=50,n=e.next(t);return t>=2e4?1/0:t}function b(e){return"function"==typeof e}function P(e,t){e.timeline=t,e.onfinish=null}let T=e=>Array.isArray(e)&&"number"==typeof e[0],x={linearEasing:void 0},w=function(e,t){let n=f(e);return()=>{var e;return null!=(e=x[t])?e:n()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),R=(e,t,n)=>{let r=t-e;return 0===r?1:(n-e)/r},E=(e,t,n=10)=>{let r="",i=Math.max(Math.round(t/n),2);for(let t=0;t<i;t++)r+=e(R(0,i-1,t))+", ";return`linear(${r.substring(0,r.length-2)})`},S=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,_={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:S([0,.65,.55,1]),circOut:S([.55,0,1,.45]),backIn:S([.31,.01,.66,-.59]),backOut:S([.33,1.53,.69,.99])},A={x:!1,y:!1};function M(e,t){let n=function(e,t,n){if(e instanceof Element)return[e];if("string"==typeof e){let t=document.querySelectorAll(e);return t?Array.from(t):[]}return Array.from(e)}(e),r=new AbortController;return[n,{passive:!0,...t,signal:r.signal},()=>r.abort()]}function j(e){return t=>{"touch"===t.pointerType||A.x||A.y||e(t)}}let O=(e,t)=>!!t&&(e===t||O(e,t.parentElement)),C=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,D=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),L=new WeakSet;function k(e){return t=>{"Enter"===t.key&&e(t)}}function V(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let U=(e,t)=>{let n=e.currentTarget;if(!n)return;let r=k(()=>{if(L.has(n))return;V(n,"down");let e=k(()=>{V(n,"up")});n.addEventListener("keyup",e,t),n.addEventListener("blur",()=>V(n,"cancel"),t)});n.addEventListener("keydown",r,t),n.addEventListener("blur",()=>n.removeEventListener("keydown",r),t)};function N(e){return C(e)&&!(A.x||A.y)}let F=e=>1e3*e,I=e=>e/1e3,B=e=>e,H=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],K=new Set(H),z=new Set(["width","height","top","left","right","bottom",...H]),W=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),$=e=>a(e)?e[e.length-1]||0:e,Y={skipAnimations:!1,useManualTiming:!1},G=["read","resolveKeyframes","update","preRender","render","postRender"];function q(e,t){let n=!1,r=!0,i={delta:0,timestamp:0,isProcessing:!1},a=()=>n=!0,o=G.reduce((e,t)=>(e[t]=function(e){let t=new Set,n=new Set,r=!1,i=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function s(t){a.has(t)&&(l.schedule(t),e()),t(o)}let l={schedule:(e,i=!1,o=!1)=>{let s=o&&r?t:n;return i&&a.add(e),s.has(e)||s.add(e),e},cancel:e=>{n.delete(e),a.delete(e)},process:e=>{if(o=e,r){i=!0;return}r=!0,[t,n]=[n,t],t.forEach(s),t.clear(),r=!1,i&&(i=!1,l.process(e))}};return l}(a),e),{}),{read:s,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:d}=o,f=()=>{let a=Y.useManualTiming?i.timestamp:performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(a-i.timestamp,40),1),i.timestamp=a,i.isProcessing=!0,s.process(i),l.process(i),u.process(i),c.process(i),h.process(i),d.process(i),i.isProcessing=!1,n&&t&&(r=!1,e(f))},p=()=>{n=!0,r=!0,i.isProcessing||e(f)};return{schedule:G.reduce((e,t)=>{let r=o[t];return e[t]=(e,t=!1,i=!1)=>(n||p(),r.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<G.length;t++)o[G[t]].cancel(e)},state:i,steps:o}}let{schedule:X,cancel:Q,state:Z,steps:J}=q("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:B,!0);function ee(){r=void 0}let et={now:()=>(void 0===r&&et.set(Z.isProcessing||Y.useManualTiming?Z.timestamp:performance.now()),r),set:e=>{r=e,queueMicrotask(ee)}};function en(e,t){-1===e.indexOf(t)&&e.push(t)}function er(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}class ei{constructor(){this.subscriptions=[]}add(e){return en(this.subscriptions,e),()=>er(this.subscriptions,e)}notify(e,t,n){let r=this.subscriptions.length;if(r)if(1===r)this.subscriptions[0](e,t,n);else for(let i=0;i<r;i++){let r=this.subscriptions[i];r&&r(e,t,n)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let ea=e=>!isNaN(parseFloat(e)),eo={current:void 0};class es{constructor(e,t={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let n=et.now();this.updatedAt!==n&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=et.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=ea(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new ei);let n=this.events[e].add(t);return"change"===e?()=>{n(),X.read(()=>{this.events.change.getSize()||this.stop()})}:n}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,n){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-n}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return eo.current&&eo.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=et.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let n=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),n?1e3/n*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function el(e,t){return new es(e,t)}let eu=e=>!!(e&&e.getVelocity);function ec(e,t){let n=e.getValue("willChange");if(eu(n)&&n.add)return n.add(t)}let eh=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),ed="data-"+eh("framerAppearId"),ef={current:!1},ep=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e;function em(e,t,n,r){if(e===t&&n===r)return B;let i=t=>(function(e,t,n,r,i){let a,o,s=0;do(a=ep(o=t+(n-t)/2,r,i)-e)>0?n=o:t=o;while(Math.abs(a)>1e-7&&++s<12);return o})(t,0,1,e,n);return e=>0===e||1===e?e:ep(i(e),t,r)}let eg=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,ey=e=>t=>1-e(1-t),ev=em(.33,1.53,.69,.99),eb=ey(ev),eP=eg(eb),eT=e=>(e*=2)<1?.5*eb(e):.5*(2-Math.pow(2,-10*(e-1))),ex=e=>1-Math.sin(Math.acos(e)),ew=ey(ex),eR=eg(ex),eE=e=>/^0[^.\s]+$/u.test(e),eS=(e,t,n)=>n>t?t:n<e?e:n,e_={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},eA={...e_,transform:e=>eS(0,1,e)},eM={...e_,default:1},ej=e=>Math.round(1e5*e)/1e5,eO=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eC=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eD=(e,t)=>n=>!!("string"==typeof n&&eC.test(n)&&n.startsWith(e)||t&&null!=n&&Object.prototype.hasOwnProperty.call(n,t)),eL=(e,t,n)=>r=>{if("string"!=typeof r)return r;let[i,a,o,s]=r.match(eO);return{[e]:parseFloat(i),[t]:parseFloat(a),[n]:parseFloat(o),alpha:void 0!==s?parseFloat(s):1}},ek=e=>eS(0,255,e),eV={...e_,transform:e=>Math.round(ek(e))},eU={test:eD("rgb","red"),parse:eL("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+eV.transform(e)+", "+eV.transform(t)+", "+eV.transform(n)+", "+ej(eA.transform(r))+")"},eN={test:eD("#"),parse:function(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}},transform:eU.transform},eF=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eI=eF("deg"),eB=eF("%"),eH=eF("px"),eK=eF("vh"),ez=eF("vw"),eW={...eB,parse:e=>eB.parse(e)/100,transform:e=>eB.transform(100*e)},e$={test:eD("hsl","hue"),parse:eL("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+eB.transform(ej(t))+", "+eB.transform(ej(n))+", "+ej(eA.transform(r))+")"},eY={test:e=>eU.test(e)||eN.test(e)||e$.test(e),parse:e=>eU.test(e)?eU.parse(e):e$.test(e)?e$.parse(e):eN.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eU.transform(e):e$.transform(e)},eG=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eq="number",eX="color",eQ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eZ(e){let t=e.toString(),n=[],r={color:[],number:[],var:[]},i=[],a=0,o=t.replace(eQ,e=>(eY.test(e)?(r.color.push(a),i.push(eX),n.push(eY.parse(e))):e.startsWith("var(")?(r.var.push(a),i.push("var"),n.push(e)):(r.number.push(a),i.push(eq),n.push(parseFloat(e))),++a,"${}")).split("${}");return{values:n,split:o,indexes:r,types:i}}function eJ(e){return eZ(e).values}function e0(e){let{split:t,types:n}=eZ(e),r=t.length;return e=>{let i="";for(let a=0;a<r;a++)if(i+=t[a],void 0!==e[a]){let t=n[a];t===eq?i+=ej(e[a]):t===eX?i+=eY.transform(e[a]):i+=e[a]}return i}}let e1=e=>"number"==typeof e?0:e,e2={test:function(e){var t,n;return isNaN(e)&&"string"==typeof e&&((null==(t=e.match(eO))?void 0:t.length)||0)+((null==(n=e.match(eG))?void 0:n.length)||0)>0},parse:eJ,createTransformer:e0,getAnimatableNone:function(e){let t=eJ(e);return e0(e)(t.map(e1))}},e3=new Set(["brightness","contrast","saturate","opacity"]);function e5(e){let[t,n]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[r]=n.match(eO)||[];if(!r)return e;let i=n.replace(r,""),a=+!!e3.has(t);return r!==n&&(a*=100),t+"("+a+i+")"}let e9=/\b([a-z-]*)\(.*?\)/gu,e4={...e2,getAnimatableNone:e=>{let t=e.match(e9);return t?t.map(e5).join(" "):e}},e6={...e_,transform:Math.round},e7={borderWidth:eH,borderTopWidth:eH,borderRightWidth:eH,borderBottomWidth:eH,borderLeftWidth:eH,borderRadius:eH,radius:eH,borderTopLeftRadius:eH,borderTopRightRadius:eH,borderBottomRightRadius:eH,borderBottomLeftRadius:eH,width:eH,maxWidth:eH,height:eH,maxHeight:eH,top:eH,right:eH,bottom:eH,left:eH,padding:eH,paddingTop:eH,paddingRight:eH,paddingBottom:eH,paddingLeft:eH,margin:eH,marginTop:eH,marginRight:eH,marginBottom:eH,marginLeft:eH,backgroundPositionX:eH,backgroundPositionY:eH,rotate:eI,rotateX:eI,rotateY:eI,rotateZ:eI,scale:eM,scaleX:eM,scaleY:eM,scaleZ:eM,skew:eI,skewX:eI,skewY:eI,distance:eH,translateX:eH,translateY:eH,translateZ:eH,x:eH,y:eH,z:eH,perspective:eH,transformPerspective:eH,opacity:eA,originX:eW,originY:eW,originZ:eH,zIndex:e6,size:eH,fillOpacity:eA,strokeOpacity:eA,numOctaves:e6},e8={...e7,color:eY,backgroundColor:eY,outlineColor:eY,fill:eY,stroke:eY,borderColor:eY,borderTopColor:eY,borderRightColor:eY,borderBottomColor:eY,borderLeftColor:eY,filter:e4,WebkitFilter:e4},te=e=>e8[e];function tt(e,t){let n=te(e);return n!==e4&&(n=e2),n.getAnimatableNone?n.getAnimatableNone(t):void 0}let tn=new Set(["auto","none","0"]),tr=e=>e===e_||e===eH,ti=(e,t)=>parseFloat(e.split(", ")[t]),ta=(e,t)=>(n,{transform:r})=>{if("none"===r||!r)return 0;let i=r.match(/^matrix3d\((.+)\)$/u);if(i)return ti(i[1],t);{let t=r.match(/^matrix\((.+)\)$/u);return t?ti(t[1],e):0}},to=new Set(["x","y","z"]),ts=H.filter(e=>!to.has(e)),tl={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:ta(4,13),y:ta(5,14)};tl.translateX=tl.x,tl.translateY=tl.y;let tu=new Set,tc=!1,th=!1;function td(){if(th){let e=Array.from(tu).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),n=new Map;t.forEach(e=>{let t=function(e){let t=[];return ts.forEach(n=>{let r=e.getValue(n);void 0!==r&&(t.push([n,r.get()]),r.set(+!!n.startsWith("scale")))}),t}(e);t.length&&(n.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=n.get(e);t&&t.forEach(([t,n])=>{var r;null==(r=e.getValue(t))||r.set(n)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}th=!1,tc=!1,tu.forEach(e=>e.complete()),tu.clear()}function tf(){tu.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(th=!0)})}class tp{constructor(e,t,n,r,i,a=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=n,this.motionValue=r,this.element=i,this.isAsync=a}scheduleResolve(){this.isScheduled=!0,this.isAsync?(tu.add(this),tc||(tc=!0,X.read(tf),X.resolveKeyframes(td))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:n,motionValue:r}=this;for(let i=0;i<e.length;i++)if(null===e[i])if(0===i){let i=null==r?void 0:r.get(),a=e[e.length-1];if(void 0!==i)e[0]=i;else if(n&&t){let r=n.readValue(t,a);null!=r&&(e[0]=r)}void 0===e[0]&&(e[0]=a),r&&void 0===i&&r.set(e[0])}else e[i]=e[i-1]}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),tu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,tu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let tm=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),tg=e=>t=>"string"==typeof t&&t.startsWith(e),ty=tg("--"),tv=tg("var(--"),tb=e=>!!tv(e)&&tP.test(e.split("/*")[0].trim()),tP=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,tT=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tx=e=>t=>t.test(e),tw=[e_,eH,eB,eI,ez,eK,{test:e=>"auto"===e,parse:e=>e}],tR=e=>tw.find(tx(e));class tE extends tp{constructor(e,t,n,r,i){super(e,t,n,r,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:n}=this;if(!t||!t.current)return;super.readKeyframes();for(let n=0;n<e.length;n++){let r=e[n];if("string"==typeof r&&tb(r=r.trim())){let i=function e(t,n,r=1){B(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,a]=function(e){let t=tT.exec(e);if(!t)return[,];let[,n,r,i]=t;return[`--${null!=n?n:r}`,i]}(t);if(!i)return;let o=window.getComputedStyle(n).getPropertyValue(i);if(o){let e=o.trim();return tm(e)?parseFloat(e):e}return tb(a)?e(a,n,r+1):a}(r,t.current);void 0!==i&&(e[n]=i),n===e.length-1&&(this.finalKeyframe=r)}}if(this.resolveNoneKeyframes(),!z.has(n)||2!==e.length)return;let[r,i]=e,a=tR(r),o=tR(i);if(a!==o)if(tr(a)&&tr(o))for(let t=0;t<e.length;t++){let n=e[t];"string"==typeof n&&(e[t]=parseFloat(n))}else this.needsMeasurement=!0}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,n=[];for(let t=0;t<e.length;t++){var r;("number"==typeof(r=e[t])?0===r:null===r||"none"===r||"0"===r||eE(r))&&n.push(t)}n.length&&function(e,t,n){let r,i=0;for(;i<e.length&&!r;){let t=e[i];"string"==typeof t&&!tn.has(t)&&eZ(t).values.length&&(r=e[i]),i++}if(r&&n)for(let i of t)e[i]=tt(n,r)}(e,n,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:n}=this;if(!e||!e.current)return;"height"===n&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=tl[n](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let r=t[t.length-1];void 0!==r&&e.getValue(n,r).jump(r,!1)}measureEndState(){var e;let{element:t,name:n,unresolvedKeyframes:r}=this;if(!t||!t.current)return;let i=t.getValue(n);i&&i.jump(this.measuredOrigin,!1);let a=r.length-1,o=r[a];r[a]=tl[n](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null==(e=this.removedTransforms)?void 0:e.length)&&this.removedTransforms.forEach(([e,n])=>{t.getValue(e).set(n)}),this.resolveNoneKeyframes()}}let tS=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(e2.test(e)||"0"===e)&&!e.startsWith("url(")),t_=e=>null!==e;function tA(e,{repeat:t,repeatType:n="loop"},r){let i=e.filter(t_),a=t&&"loop"!==n&&t%2==1?0:i.length-1;return a&&void 0!==r?r:i[a]}class tM{constructor({autoplay:e=!0,delay:t=0,type:n="keyframes",repeat:r=0,repeatDelay:i=0,repeatType:a="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=et.now(),this.options={autoplay:e,delay:t,type:n,repeat:r,repeatDelay:i,repeatType:a,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(tf(),td()),this._resolved}onKeyframesResolved(e,t){this.resolvedAt=et.now(),this.hasAttemptedResolve=!0;let{name:n,type:r,velocity:i,delay:a,onComplete:o,onUpdate:s,isGenerator:l}=this.options;if(!l&&!function(e,t,n,r){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let a=e[e.length-1],o=tS(i,t),s=tS(a,t);return B(o===s,`You are trying to animate ${t} from "${i}" to "${a}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${a} via the \`style\` property.`),!!o&&!!s&&(function(e){let t=e[0];if(1===e.length)return!0;for(let n=0;n<e.length;n++)if(e[n]!==t)return!0}(e)||("spring"===n||b(n))&&r)}(e,n,r,i))if(ef.current||!a){s&&s(tA(e,this.options,t)),o&&o(),this.resolveFinishedPromise();return}else this.options.duration=0;let u=this.initPlayback(e,t);!1!==u&&(this._resolved={keyframes:e,finalKeyframe:t,...u},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}let tj=(e,t,n)=>e+(t-e)*n;function tO(e,t,n){return(n<0&&(n+=1),n>1&&(n-=1),n<1/6)?e+(t-e)*6*n:n<.5?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function tC(e,t){return n=>n>0?t:e}let tD=(e,t,n)=>{let r=e*e,i=n*(t*t-r)+r;return i<0?0:Math.sqrt(i)},tL=[eN,eU,e$],tk=e=>tL.find(t=>t.test(e));function tV(e){let t=tk(e);if(B(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let n=t.parse(e);return t===e$&&(n=function({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,n/=100;let i=0,a=0,o=0;if(t/=100){let r=n<.5?n*(1+t):n+t-n*t,s=2*n-r;i=tO(s,r,e+1/3),a=tO(s,r,e),o=tO(s,r,e-1/3)}else i=a=o=n;return{red:Math.round(255*i),green:Math.round(255*a),blue:Math.round(255*o),alpha:r}}(n)),n}let tU=(e,t)=>{let n=tV(e),r=tV(t);if(!n||!r)return tC(e,t);let i={...n};return e=>(i.red=tD(n.red,r.red,e),i.green=tD(n.green,r.green,e),i.blue=tD(n.blue,r.blue,e),i.alpha=tj(n.alpha,r.alpha,e),eU.transform(i))},tN=(e,t)=>n=>t(e(n)),tF=(...e)=>e.reduce(tN),tI=new Set(["none","hidden"]);function tB(e,t){return n=>tj(e,t,n)}function tH(e){return"number"==typeof e?tB:"string"==typeof e?tb(e)?tC:eY.test(e)?tU:tW:Array.isArray(e)?tK:"object"==typeof e?eY.test(e)?tU:tz:tC}function tK(e,t){let n=[...e],r=n.length,i=e.map((e,n)=>tH(e)(e,t[n]));return e=>{for(let t=0;t<r;t++)n[t]=i[t](e);return n}}function tz(e,t){let n={...e,...t},r={};for(let i in n)void 0!==e[i]&&void 0!==t[i]&&(r[i]=tH(e[i])(e[i],t[i]));return e=>{for(let t in r)n[t]=r[t](e);return n}}let tW=(e,t)=>{let n=e2.createTransformer(t),r=eZ(e),i=eZ(t);return r.indexes.var.length===i.indexes.var.length&&r.indexes.color.length===i.indexes.color.length&&r.indexes.number.length>=i.indexes.number.length?tI.has(e)&&!i.values.length||tI.has(t)&&!r.values.length?function(e,t){return tI.has(e)?n=>n<=0?e:t:n=>n>=1?t:e}(e,t):tF(tK(function(e,t){var n;let r=[],i={color:0,var:0,number:0};for(let a=0;a<t.values.length;a++){let o=t.types[a],s=e.indexes[o][i[o]],l=null!=(n=e.values[s])?n:0;r[a]=l,i[o]++}return r}(r,i),i.values),n):(B(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tC(e,t))};function t$(e,t,n){return"number"==typeof e&&"number"==typeof t&&"number"==typeof n?tj(e,t,n):tH(e)(e,t)}function tY(e,t,n){var r,i;let a=Math.max(t-5,0);return r=n-e(a),(i=t-a)?1e3/i*r:0}let tG={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tq(e,t){return e*Math.sqrt(1-t*t)}let tX=["duration","bounce"],tQ=["stiffness","damping","mass"];function tZ(e,t){return t.some(t=>void 0!==e[t])}function tJ(e=tG.visualDuration,t=tG.bounce){let n,r="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:a}=r,o=r.keyframes[0],s=r.keyframes[r.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:c,mass:h,duration:d,velocity:f,isResolvedFromDuration:p}=function(e){let t={velocity:tG.velocity,stiffness:tG.stiffness,damping:tG.damping,mass:tG.mass,isResolvedFromDuration:!1,...e};if(!tZ(e,tQ)&&tZ(e,tX))if(e.visualDuration){let n=2*Math.PI/(1.2*e.visualDuration),r=n*n,i=2*eS(.05,1,1-(e.bounce||0))*Math.sqrt(r);t={...t,mass:tG.mass,stiffness:r,damping:i}}else{let n=function({duration:e=tG.duration,bounce:t=tG.bounce,velocity:n=tG.velocity,mass:r=tG.mass}){let i,a;B(e<=F(tG.maxDuration),"Spring duration must be 10 seconds or less");let o=1-t;o=eS(tG.minDamping,tG.maxDamping,o),e=eS(tG.minDuration,tG.maxDuration,I(e)),o<1?(i=t=>{let r=t*o,i=r*e;return .001-(r-n)/tq(t,o)*Math.exp(-i)},a=t=>{let r=t*o*e,a=Math.pow(o,2)*Math.pow(t,2)*e,s=Math.exp(-r),l=tq(Math.pow(t,2),o);return(r*n+n-a)*s*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-n)*e+1),a=t=>e*e*(n-t)*Math.exp(-t*e));let s=function(e,t,n){let r=n;for(let n=1;n<12;n++)r-=e(r)/t(r);return r}(i,a,5/e);if(e=F(e),isNaN(s))return{stiffness:tG.stiffness,damping:tG.damping,duration:e};{let t=Math.pow(s,2)*r;return{stiffness:t,damping:2*o*Math.sqrt(r*t),duration:e}}}(e);(t={...t,...n,mass:tG.mass}).isResolvedFromDuration=!0}return t}({...r,velocity:-I(r.velocity||0)}),m=f||0,g=c/(2*Math.sqrt(u*h)),y=s-o,b=I(Math.sqrt(u/h)),P=5>Math.abs(y);if(i||(i=P?tG.restSpeed.granular:tG.restSpeed.default),a||(a=P?tG.restDelta.granular:tG.restDelta.default),g<1){let e=tq(b,g);n=t=>s-Math.exp(-g*b*t)*((m+g*b*y)/e*Math.sin(e*t)+y*Math.cos(e*t))}else if(1===g)n=e=>s-Math.exp(-b*e)*(y+(m+b*y)*e);else{let e=b*Math.sqrt(g*g-1);n=t=>{let n=Math.exp(-g*b*t),r=Math.min(e*t,300);return s-n*((m+g*b*y)*Math.sinh(r)+e*y*Math.cosh(r))/e}}let T={calculatedDuration:p&&d||null,next:e=>{let t=n(e);if(p)l.done=e>=d;else{let r=0;g<1&&(r=0===e?F(m):tY(n,e,t));let o=Math.abs(s-t)<=a;l.done=Math.abs(r)<=i&&o}return l.value=l.done?s:t,l},toString:()=>{let e=Math.min(v(T),2e4),t=E(t=>T.next(e*t).value,e,30);return e+"ms "+t}};return T}function t0({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:a=500,modifyTarget:o,min:s,max:l,restDelta:u=.5,restSpeed:c}){let h,d,f=e[0],p={done:!1,value:f},m=e=>void 0!==s&&e<s||void 0!==l&&e>l,g=e=>void 0===s?l:void 0===l||Math.abs(s-e)<Math.abs(l-e)?s:l,y=n*t,v=f+y,b=void 0===o?v:o(v);b!==v&&(y=b-f);let P=e=>-y*Math.exp(-e/r),T=e=>b+P(e),x=e=>{let t=P(e),n=T(e);p.done=Math.abs(t)<=u,p.value=p.done?b:n},w=e=>{m(p.value)&&(h=e,d=tJ({keyframes:[p.value,g(p.value)],velocity:tY(T,e,p.value),damping:i,stiffness:a,restDelta:u,restSpeed:c}))};return w(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==h||(t=!0,x(e),w(e)),void 0!==h&&e>=h)?d.next(e-h):(t||x(e),p)}}}let t1=em(.42,0,1,1),t2=em(0,0,.58,1),t3=em(.42,0,.58,1),t5=e=>Array.isArray(e)&&"number"!=typeof e[0],t9={linear:B,easeIn:t1,easeInOut:t3,easeOut:t2,circIn:ex,circInOut:eR,circOut:ew,backIn:eb,backInOut:eP,backOut:ev,anticipate:eT},t4=e=>{if(T(e)){B(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,n,r,i]=e;return em(t,n,r,i)}return"string"==typeof e?(B(void 0!==t9[e],`Invalid easing type '${e}'`),t9[e]):e};function t6({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){var i;let a=t5(r)?r.map(t4):t4(r),o={done:!1,value:t[0]},s=function(e,t,{clamp:n=!0,ease:r,mixer:i}={}){let a=e.length;if(B(a===t.length,"Both input and output ranges must be the same length"),1===a)return()=>t[0];if(2===a&&t[0]===t[1])return()=>t[1];let o=e[0]===e[1];e[0]>e[a-1]&&(e=[...e].reverse(),t=[...t].reverse());let s=function(e,t,n){let r=[],i=n||t$,a=e.length-1;for(let n=0;n<a;n++){let a=i(e[n],e[n+1]);t&&(a=tF(Array.isArray(t)?t[n]||B:t,a)),r.push(a)}return r}(t,r,i),l=s.length,u=n=>{if(o&&n<e[0])return t[0];let r=0;if(l>1)for(;r<e.length-2&&!(n<e[r+1]);r++);let i=R(e[r],e[r+1],n);return s[r](i)};return n?t=>u(eS(e[0],e[a-1],t)):u}((i=n&&n.length===t.length?n:function(e){let t=[0];return!function(e,t){let n=e[e.length-1];for(let r=1;r<=t;r++){let i=R(0,t,r);e.push(tj(n,1,i))}}(t,e.length-1),t}(t),i.map(t=>t*e)),t,{ease:Array.isArray(a)?a:t.map(()=>a||t3).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(o.value=s(t),o.done=t>=e,o)}}let t7=e=>{let t=({timestamp:t})=>e(t);return{start:()=>X.update(t,!0),stop:()=>Q(t),now:()=>Z.isProcessing?Z.timestamp:et.now()}},t8={decay:t0,inertia:t0,tween:t6,keyframes:t6,spring:tJ},ne=e=>e/100;class nt extends tM{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()};let{name:t,motionValue:n,element:r,keyframes:i}=this.options,a=(null==r?void 0:r.KeyframeResolver)||tp;this.resolver=new a(i,(e,t)=>this.onKeyframesResolved(e,t),t,n,r),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){let t,n,{type:r="keyframes",repeat:i=0,repeatDelay:a=0,repeatType:o,velocity:s=0}=this.options,l=b(r)?r:t8[r]||t6;l!==t6&&"number"!=typeof e[0]&&(t=tF(ne,t$(e[0],e[1])),e=[0,100]);let u=l({...this.options,keyframes:e});"mirror"===o&&(n=l({...this.options,keyframes:[...e].reverse(),velocity:-s})),null===u.calculatedDuration&&(u.calculatedDuration=v(u));let{calculatedDuration:c}=u,h=c+a;return{generator:u,mirroredGenerator:n,mapPercentToKeyframes:t,calculatedDuration:c,resolvedDuration:h,totalDuration:h*(i+1)-a}}onPostResolved(){let{autoplay:e=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){let{resolved:n}=this;if(!n){let{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}let{finalKeyframe:r,generator:i,mirroredGenerator:a,mapPercentToKeyframes:o,keyframes:s,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=n;if(null===this.startTime)return i.next(0);let{delay:h,repeat:d,repeatType:f,repeatDelay:p,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;let g=this.currentTime-h*(this.speed>=0?1:-1),y=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,b=i;if(d){let e=Math.min(this.currentTime,u)/c,t=Math.floor(e),n=e%1;!n&&e>=1&&(n=1),1===n&&t--,(t=Math.min(t,d+1))%2&&("reverse"===f?(n=1-n,p&&(n-=p/c)):"mirror"===f&&(b=a)),v=eS(0,1,n)*c}let P=y?{done:!1,value:s[0]}:b.next(v);o&&(P.value=o(P.value));let{done:T}=P;y||null===l||(T=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let x=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return x&&void 0!==r&&(P.value=tA(s,this.options,r)),m&&m(P.value),x&&this.finish(),P}get duration(){let{resolved:e}=this;return e?I(e.calculatedDuration):0}get time(){return I(this.currentTime)}set time(e){e=F(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=I(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:e=t7,onPlay:t,startTime:n}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let r=this.driver.now();null!==this.holdTime?this.startTime=r-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=r):this.startTime=null!=n?n:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!=(e=this.currentTime)?e:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}let nn=new Set(["opacity","clipPath","filter","transform"]),nr=f(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ni={anticipate:eT,backInOut:eP,circInOut:eR};class na extends tM{constructor(e){super(e);let{name:t,motionValue:n,element:r,keyframes:i}=this.options;this.resolver=new tE(i,(e,t)=>this.onKeyframesResolved(e,t),t,n,r),this.resolver.scheduleResolve()}initPlayback(e,t){var n;let{duration:r=300,times:i,ease:a,type:o,motionValue:s,name:l,startTime:u}=this.options;if(!s.owner||!s.owner.current)return!1;if("string"==typeof a&&w()&&a in ni&&(a=ni[a]),b((n=this.options).type)||"spring"===n.type||!function e(t){return!!("function"==typeof t&&w()||!t||"string"==typeof t&&(t in _||w())||T(t)||Array.isArray(t)&&t.every(e))}(n.ease)){let{onComplete:t,onUpdate:n,motionValue:s,element:l,...u}=this.options,c=function(e,t){let n=new nt({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0}),r={done:!1,value:e[0]},i=[],a=0;for(;!r.done&&a<2e4;)i.push((r=n.sample(a)).value),a+=10;return{times:void 0,keyframes:i,duration:a-10,ease:"linear"}}(e,u);1===(e=c.keyframes).length&&(e[1]=e[0]),r=c.duration,i=c.times,a=c.ease,o="keyframes"}let c=function(e,t,n,{delay:r=0,duration:i=300,repeat:a=0,repeatType:o="loop",ease:s="easeInOut",times:l}={}){let u={[t]:n};l&&(u.offset=l);let c=function e(t,n){if(t)return"function"==typeof t&&w()?E(t,n):T(t)?S(t):Array.isArray(t)?t.map(t=>e(t,n)||_.easeOut):_[t]}(s,i);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:a+1,direction:"reverse"===o?"alternate":"normal"})}(s.owner.current,l,e,{...this.options,duration:r,times:i,ease:a});return c.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(P(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{let{onComplete:n}=this.options;s.set(tA(e,this.options,t)),n&&n(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:r,times:i,type:o,ease:a,keyframes:e}}get duration(){let{resolved:e}=this;if(!e)return 0;let{duration:t}=e;return I(t)}get time(){let{resolved:e}=this;if(!e)return 0;let{animation:t}=e;return I(t.currentTime||0)}set time(e){let{resolved:t}=this;if(!t)return;let{animation:n}=t;n.currentTime=F(e)}get speed(){let{resolved:e}=this;if(!e)return 1;let{animation:t}=e;return t.playbackRate}set speed(e){let{resolved:t}=this;if(!t)return;let{animation:n}=t;n.playbackRate=e}get state(){let{resolved:e}=this;if(!e)return"idle";let{animation:t}=e;return t.playState}get startTime(){let{resolved:e}=this;if(!e)return null;let{animation:t}=e;return t.startTime}attachTimeline(e){if(this._resolved){let{resolved:t}=this;if(!t)return B;let{animation:n}=t;P(n,e)}else this.pendingTimeline=e;return B}play(){if(this.isStopped)return;let{resolved:e}=this;if(!e)return;let{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){let{resolved:e}=this;if(!e)return;let{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:e}=this;if(!e)return;let{animation:t,keyframes:n,duration:r,type:i,ease:a,times:o}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){let{motionValue:e,onUpdate:t,onComplete:s,element:l,...u}=this.options,c=new nt({...u,keyframes:n,duration:r,type:i,ease:a,times:o,isGenerator:!0}),h=F(this.time);e.setWithVelocity(c.sample(h-10).value,c.sample(h).value,10)}let{onStop:s}=this.options;s&&s(),this.cancel()}complete(){let{resolved:e}=this;e&&e.animation.finish()}cancel(){let{resolved:e}=this;e&&e.animation.cancel()}static supports(e){let{motionValue:t,name:n,repeatDelay:r,repeatType:i,damping:a,type:o}=e;if(!t||!t.owner||!(t.owner.current instanceof HTMLElement))return!1;let{onUpdate:s,transformTemplate:l}=t.owner.getProps();return nr()&&n&&nn.has(n)&&!s&&!l&&!r&&"mirror"!==i&&0!==a&&"inertia"!==o}}let no={type:"spring",stiffness:500,damping:25,restSpeed:10},ns=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),nl={type:"keyframes",duration:.8},nu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},nc=(e,{keyframes:t})=>t.length>2?nl:K.has(e)?e.startsWith("scale")?ns(t[1]):no:nu,nh=(e,t,n,r={},i,a)=>o=>{let s=y(r,e)||{},l=s.delay||r.delay||0,{elapsed:u=0}=r;u-=F(l);let c={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:t.getVelocity(),...s,delay:-u,onUpdate:e=>{t.set(e),s.onUpdate&&s.onUpdate(e)},onComplete:()=>{o(),s.onComplete&&s.onComplete()},name:e,motionValue:t,element:a?void 0:i};!function({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:a,repeatType:o,repeatDelay:s,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(s)&&(c={...c,...nc(e,c)}),c.duration&&(c.duration=F(c.duration)),c.repeatDelay&&(c.repeatDelay=F(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let h=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(h=!0)),(ef.current||Y.skipAnimations)&&(h=!0,c.duration=0,c.delay=0),h&&!a&&void 0!==t.get()){let e=tA(c.keyframes,s);if(void 0!==e)return X.update(()=>{c.onUpdate(e),c.onComplete()}),new g([])}return!a&&na.supports(c)?new na(c):new nt(c)};function nd(e,t,{delay:n=0,transitionOverride:r,type:i}={}){var a;let{transition:o=e.getDefaultTransition(),transitionEnd:s,...l}=t;r&&(o=r);let u=[],h=i&&e.animationState&&e.animationState.getState()[i];for(let t in l){let r=e.getValue(t,null!=(a=e.latestValues[t])?a:null),i=l[t];if(void 0===i||h&&function({protectedKeys:e,needsAnimating:t},n){let r=e.hasOwnProperty(n)&&!0!==t[n];return t[n]=!1,r}(h,t))continue;let s={delay:n,...y(o||{},t)},c=!1;if(window.MotionHandoffAnimation){let n=e.props[ed];if(n){let e=window.MotionHandoffAnimation(n,t,X);null!==e&&(s.startTime=e,c=!0)}}ec(e,t),r.start(nh(t,r,i,e.shouldReduceMotion&&z.has(t)?{type:!1}:s,e,c));let d=r.animation;d&&u.push(d)}return s&&Promise.all(u).then(()=>{X.update(()=>{s&&function(e,t){let{transitionEnd:n={},transition:r={},...i}=c(e,t)||{};for(let t in i={...i,...n}){let n=$(i[t]);e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,el(n))}}(e,s)})}),u}function nf(e,t,n={}){var r;let i=c(e,t,"exit"===n.type?null==(r=e.presenceContext)?void 0:r.custom:void 0),{transition:a=e.getDefaultTransition()||{}}=i||{};n.transitionOverride&&(a=n.transitionOverride);let o=i?()=>Promise.all(nd(e,i,n)):()=>Promise.resolve(),s=e.variantChildren&&e.variantChildren.size?(r=0)=>{let{delayChildren:i=0,staggerChildren:o,staggerDirection:s}=a;return function(e,t,n=0,r=0,i=1,a){let o=[],s=(e.variantChildren.size-1)*r,l=1===i?(e=0)=>e*r:(e=0)=>s-e*r;return Array.from(e.variantChildren).sort(np).forEach((e,r)=>{e.notify("AnimationStart",t),o.push(nf(e,t,{...a,delay:n+l(r)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,i+r,o,s,n)}:()=>Promise.resolve(),{when:l}=a;if(!l)return Promise.all([o(),s(n.delay)]);{let[e,t]="beforeChildren"===l?[o,s]:[s,o];return e().then(()=>t())}}function np(e,t){return e.sortNodePosition(t)}let nm=d.length,ng=[...h].reverse(),ny=h.length;function nv(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function nb(){return{animate:nv(!0),whileInView:nv(),whileHover:nv(),whileTap:nv(),whileDrag:nv(),whileFocus:nv(),exit:nv()}}class nP{constructor(e){this.isMounted=!1,this.node=e}update(){}}class nT extends nP{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:n})=>(function(e,t,n={}){let r;if(e.notify("AnimationStart",t),Array.isArray(t))r=Promise.all(t.map(t=>nf(e,t,n)));else if("string"==typeof t)r=nf(e,t,n);else{let i="function"==typeof t?c(e,t,n.custom):t;r=Promise.all(nd(e,i,n))}return r.then(()=>{e.notify("AnimationComplete",t)})})(e,t,n))),n=nb(),r=!0,l=t=>(n,r)=>{var i;let a=c(e,r,"exit"===t?null==(i=e.presenceContext)?void 0:i.custom:void 0);if(a){let{transition:e,transitionEnd:t,...r}=a;n={...n,...r,...t}}return n};function u(u){let{props:c}=e,h=function e(t){if(!t)return;if(!t.isControllingVariants){let n=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(n.initial=t.props.initial),n}let n={};for(let e=0;e<nm;e++){let r=d[e],i=t.props[r];(s(i)||!1===i)&&(n[r]=i)}return n}(e.parent)||{},f=[],p=new Set,m={},g=1/0;for(let t=0;t<ny;t++){var y,v;let d=ng[t],b=n[d],P=void 0!==c[d]?c[d]:h[d],T=s(P),x=d===u?b.isActive:null;!1===x&&(g=t);let w=P===h[d]&&P!==c[d]&&T;if(w&&r&&e.manuallyAnimateOnMount&&(w=!1),b.protectedKeys={...m},!b.isActive&&null===x||!P&&!b.prevProp||i(P)||"boolean"==typeof P)continue;let R=(y=b.prevProp,"string"==typeof(v=P)?v!==y:!!Array.isArray(v)&&!o(v,y)),E=R||d===u&&b.isActive&&!w&&T||t>g&&T,S=!1,_=Array.isArray(P)?P:[P],A=_.reduce(l(d),{});!1===x&&(A={});let{prevResolvedValues:M={}}=b,j={...M,...A},O=t=>{E=!0,p.has(t)&&(S=!0,p.delete(t)),b.needsAnimating[t]=!0;let n=e.getValue(t);n&&(n.liveStyle=!1)};for(let e in j){let t=A[e],n=M[e];if(m.hasOwnProperty(e))continue;let r=!1;(a(t)&&a(n)?o(t,n):t===n)?void 0!==t&&p.has(e)?O(e):b.protectedKeys[e]=!0:null!=t?O(e):p.add(e)}b.prevProp=P,b.prevResolvedValues=A,b.isActive&&(m={...m,...A}),r&&e.blockInitialAnimation&&(E=!1);let C=!(w&&R)||S;E&&C&&f.push(..._.map(e=>({animation:e,options:{type:d}})))}if(p.size){let t={};p.forEach(n=>{let r=e.getBaseTarget(n),i=e.getValue(n);i&&(i.liveStyle=!0),t[n]=null!=r?r:null}),f.push({animation:t})}let b=!!f.length;return r&&(!1===c.initial||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(b=!1),r=!1,b?t(f):Promise.resolve()}return{animateChanges:u,setActive:function(t,r){var i;if(n[t].isActive===r)return Promise.resolve();null==(i=e.variantChildren)||i.forEach(e=>{var n;return null==(n=e.animationState)?void 0:n.setActive(t,r)}),n[t].isActive=r;let a=u(t);for(let e in n)n[e].protectedKeys={};return a},setAnimateFunction:function(n){t=n(e)},getState:()=>n,reset:()=>{n=nb(),r=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null==(e=this.unmountControls)||e.call(this)}}let nx=0;class nw extends nP{constructor(){super(...arguments),this.id=nx++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let r=this.node.animationState.setActive("exit",!e);t&&!e&&r.then(()=>t(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}}function nR(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}function nE(e){return{point:{x:e.pageX,y:e.pageY}}}let nS=e=>t=>C(t)&&e(t,nE(t));function n_(e,t,n,r){return nR(e,t,nS(n),r)}let nA=(e,t)=>Math.abs(e-t);class nM{constructor(e,t,{transformPagePoint:n,contextWindow:r,dragSnapToOrigin:i=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=nC(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,n=function(e,t){return Math.sqrt(nA(e.x,t.x)**2+nA(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!n)return;let{point:r}=e,{timestamp:i}=Z;this.history.push({...r,timestamp:i});let{onStart:a,onMove:o}=this.handlers;t||(a&&a(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{this.lastMoveEvent=e,this.lastMoveEventInfo=nj(t,this.transformPagePoint),X.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{this.end();let{onEnd:n,onSessionEnd:r,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=nC("pointercancel"===e.type?this.lastMoveEventInfo:nj(t,this.transformPagePoint),this.history);this.startEvent&&n&&n(e,a),r&&r(e,a)},!C(e))return;this.dragSnapToOrigin=i,this.handlers=t,this.transformPagePoint=n,this.contextWindow=r||window;let a=nj(nE(e),this.transformPagePoint),{point:o}=a,{timestamp:s}=Z;this.history=[{...o,timestamp:s}];let{onSessionStart:l}=t;l&&l(e,nC(a,this.history)),this.removeListeners=tF(n_(this.contextWindow,"pointermove",this.handlePointerMove),n_(this.contextWindow,"pointerup",this.handlePointerUp),n_(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),Q(this.updatePoint)}}function nj(e,t){return t?{point:t(e.point)}:e}function nO(e,t){return{x:e.x-t.x,y:e.y-t.y}}function nC({point:e},t){return{point:e,delta:nO(e,nD(t)),offset:nO(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null,i=nD(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>F(.1)));)n--;if(!r)return{x:0,y:0};let a=I(i.timestamp-r.timestamp);if(0===a)return{x:0,y:0};let o={x:(i.x-r.x)/a,y:(i.y-r.y)/a};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(t,.1)}}function nD(e){return e[e.length-1]}function nL(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}function nk(e){return e.max-e.min}function nV(e,t,n,r=.5){e.origin=r,e.originPoint=tj(t.min,t.max,e.origin),e.scale=nk(n)/nk(t),e.translate=tj(n.min,n.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function nU(e,t,n,r){nV(e.x,t.x,n.x,r?r.originX:void 0),nV(e.y,t.y,n.y,r?r.originY:void 0)}function nN(e,t,n){e.min=n.min+t.min,e.max=e.min+nk(t)}function nF(e,t,n){e.min=t.min-n.min,e.max=e.min+nk(t)}function nI(e,t,n){nF(e.x,t.x,n.x),nF(e.y,t.y,n.y)}function nB(e,t,n){return{min:void 0!==t?e.min+t:void 0,max:void 0!==n?e.max+n-(e.max-e.min):void 0}}function nH(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function nK(e,t,n){return{min:nz(e,t),max:nz(e,n)}}function nz(e,t){return"number"==typeof e?e:e[t]||0}let nW=()=>({translate:0,scale:1,origin:0,originPoint:0}),n$=()=>({x:nW(),y:nW()}),nY=()=>({min:0,max:0}),nG=()=>({x:nY(),y:nY()});function nq(e){return[e("x"),e("y")]}function nX({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function nQ(e){return void 0===e||1===e}function nZ({scale:e,scaleX:t,scaleY:n}){return!nQ(e)||!nQ(t)||!nQ(n)}function nJ(e){return nZ(e)||n0(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function n0(e){var t,n;return(t=e.x)&&"0%"!==t||(n=e.y)&&"0%"!==n}function n1(e,t,n,r,i){return void 0!==i&&(e=r+i*(e-r)),r+n*(e-r)+t}function n2(e,t=0,n=1,r,i){e.min=n1(e.min,t,n,r,i),e.max=n1(e.max,t,n,r,i)}function n3(e,{x:t,y:n}){n2(e.x,t.translate,t.scale,t.originPoint),n2(e.y,n.translate,n.scale,n.originPoint)}function n5(e,t){e.min=e.min+t,e.max=e.max+t}function n9(e,t,n,r,i=.5){let a=tj(e.min,e.max,i);n2(e,t,n,a,r)}function n4(e,t){n9(e.x,t.x,t.scaleX,t.scale,t.originX),n9(e.y,t.y,t.scaleY,t.scale,t.originY)}function n6(e,t){return nX(function(e,t){if(!t)return e;let n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}(e.getBoundingClientRect(),t))}let n7=({current:e})=>e?e.ownerDocument.defaultView:null,n8=new WeakMap;class re{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=nG(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let{dragSnapToOrigin:r}=this.getProps();this.panSession=new nM(e,{onSessionStart:e=>{let{dragSnapToOrigin:n}=this.getProps();n?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(nE(e).point)},onStart:(e,t)=>{let{drag:n,dragPropagation:r,onDragStart:i}=this.getProps();if(n&&!r&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(e){if("x"===e||"y"===e)if(A[e])return null;else return A[e]=!0,()=>{A[e]=!1};return A.x||A.y?null:(A.x=A.y=!0,()=>{A.x=A.y=!1})}(n),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),nq(e=>{let t=this.getAxisMotionValue(e).get()||0;if(eB.test(t)){let{projection:n}=this.visualElement;if(n&&n.layout){let r=n.layout.layoutBox[e];r&&(t=nk(r)*(parseFloat(t)/100))}}this.originPoint[e]=t}),i&&X.postRender(()=>i(e,t)),ec(this.visualElement,"transform");let{animationState:a}=this.visualElement;a&&a.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:n,dragDirectionLock:r,onDirectionLock:i,onDrag:a}=this.getProps();if(!n&&!this.openDragLock)return;let{offset:o}=t;if(r&&null===this.currentDirection){this.currentDirection=function(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}(o),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),a&&a(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>nq(e=>{var t;return"paused"===this.getAnimationState(e)&&(null==(t=this.getAxisMotionValue(e).animation)?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:r,contextWindow:n7(this.visualElement)})}stop(e,t){let n=this.isDragging;if(this.cancel(),!n)return;let{velocity:r}=t;this.startAnimation(r);let{onDragEnd:i}=this.getProps();i&&X.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:n}=this.getProps();!n&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,n){let{drag:r}=this.getProps();if(!n||!rt(e,r,this.currentDirection))return;let i=this.getAxisMotionValue(e),a=this.originPoint[e]+n[e];this.constraints&&this.constraints[e]&&(a=function(e,{min:t,max:n},r){return void 0!==t&&e<t?e=r?tj(t,e,r.min):Math.max(e,t):void 0!==n&&e>n&&(e=r?tj(n,e,r.max):Math.min(e,n)),e}(a,this.constraints[e],this.elastic[e])),i.set(a)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:n}=this.getProps(),r=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null==(e=this.visualElement.projection)?void 0:e.layout,i=this.constraints;t&&nL(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&r?this.constraints=function(e,{top:t,left:n,bottom:r,right:i}){return{x:nB(e.x,n,i),y:nB(e.y,t,r)}}(r.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:nK(e,"left","right"),y:nK(e,"top","bottom")}}(n),i!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&nq(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let n={};return void 0!==t.min&&(n.min=t.min-e.min),void 0!==t.max&&(n.max=t.max-e.min),n}(r.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!nL(t))return!1;let r=t.current;B(null!==r,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let a=function(e,t,n){let r=n6(e,n),{scroll:i}=t;return i&&(n5(r.x,i.offset.x),n5(r.y,i.offset.y)),r}(r,i.root,this.visualElement.getTransformPagePoint()),o=(e=i.layout.layoutBox,{x:nH(e.x,a.x),y:nH(e.y,a.y)});if(n){let e=n(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=nX(e))}return o}startAnimation(e){let{drag:t,dragMomentum:n,dragElastic:r,dragTransition:i,dragSnapToOrigin:a,onDragTransitionEnd:o}=this.getProps(),s=this.constraints||{};return Promise.all(nq(o=>{if(!rt(o,t,this.currentDirection))return;let l=s&&s[o]||{};a&&(l={min:0,max:0});let u={type:"inertia",velocity:n?e[o]:0,bounceStiffness:r?200:1e6,bounceDamping:r?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(e,t){let n=this.getAxisMotionValue(e);return ec(this.visualElement,e),n.start(nh(e,n,0,t,this.visualElement,!1))}stopAnimation(){nq(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){nq(e=>{var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.pause()})}getAnimationState(e){var t;return null==(t=this.getAxisMotionValue(e).animation)?void 0:t.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,n=this.visualElement.getProps();return n[t]||this.visualElement.getValue(e,(n.initial?n.initial[e]:void 0)||0)}snapToCursor(e){nq(t=>{let{drag:n}=this.getProps();if(!rt(t,n,this.currentDirection))return;let{projection:r}=this.visualElement,i=this.getAxisMotionValue(t);if(r&&r.layout){let{min:n,max:a}=r.layout.layoutBox[t];i.set(e[t]-tj(n,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:n}=this.visualElement;if(!nL(t)||!n||!this.constraints)return;this.stopAnimation();let r={x:0,y:0};nq(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let n=t.get();r[e]=function(e,t){let n=.5,r=nk(e),i=nk(t);return i>r?n=R(t.min,t.max-r,e.min):r>i&&(n=R(e.min,e.max-i,t.min)),eS(0,1,n)}({min:n,max:n},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",n.root&&n.root.updateScroll(),n.updateLayout(),this.resolveConstraints(),nq(t=>{if(!rt(t,e,null))return;let n=this.getAxisMotionValue(t),{min:i,max:a}=this.constraints[t];n.set(tj(i,a,r[t]))})}addListeners(){if(!this.visualElement.current)return;n8.set(this.visualElement,this);let e=n_(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:n=!0}=this.getProps();t&&n&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();nL(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,r=n.addEventListener("measure",t);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),X.read(t);let i=nR(window,"resize",()=>this.scalePositionWithinConstraints()),a=n.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(nq(t=>{let n=this.getAxisMotionValue(t);n&&(this.originPoint[t]+=e[t].translate,n.set(n.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),r(),a&&a()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:n=!1,dragPropagation:r=!1,dragConstraints:i=!1,dragElastic:a=.35,dragMomentum:o=!0}=e;return{...e,drag:t,dragDirectionLock:n,dragPropagation:r,dragConstraints:i,dragElastic:a,dragMomentum:o}}}function rt(e,t,n){return(!0===t||t===e)&&(null===n||n===e)}class rn extends nP{constructor(e){super(e),this.removeGroupControls=B,this.removeListeners=B,this.controls=new re(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||B}unmount(){this.removeGroupControls(),this.removeListeners()}}let rr=e=>(t,n)=>{e&&X.postRender(()=>e(t,n))};class ri extends nP{constructor(){super(...arguments),this.removePointerDownListener=B}onPointerDown(e){this.session=new nM(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:n7(this.node)})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:n,onPanEnd:r}=this.node.getProps();return{onSessionStart:rr(e),onStart:rr(t),onMove:n,onEnd:(e,t)=>{delete this.session,r&&X.postRender(()=>r(e,t))}}}mount(){this.removePointerDownListener=n_(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var ra,ro,rs=n(60687),rl=n(43210),ru=n(86044),rc=n(12157);let rh=(0,rl.createContext)({}),rd={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function rf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let rp={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e)if(!eH.test(e))return e;else e=parseFloat(e);let n=rf(e,t.target.x),r=rf(e,t.target.y);return`${n}% ${r}%`}},rm={},{schedule:rg,cancel:ry}=q(queueMicrotask,!1);class rv extends rl.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n,layoutId:r}=this.props,{projection:i}=e;Object.assign(rm,rP),i&&(t.group&&t.group.add(i),n&&n.register&&r&&n.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),rd.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:n,drag:r,isPresent:i}=this.props,a=n.projection;return a&&(a.isPresent=i,r||e.layoutDependency!==t||void 0===t?a.willUpdate():this.safeToRemove(),e.isPresent!==i&&(i?a.promote():a.relegate()||X.postRender(()=>{let e=a.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),rg.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:n}=this.props,{projection:r}=e;r&&(r.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(r),n&&n.deregister&&n.deregister(r))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function rb(e){let[t,n]=(0,ru.xQ)(),r=(0,rl.useContext)(rc.L);return(0,rs.jsx)(rv,{...e,layoutGroup:r,switchLayoutGroup:(0,rl.useContext)(rh),isPresent:t,safeToRemove:n})}let rP={borderRadius:{...rp,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:rp,borderTopRightRadius:rp,borderBottomLeftRadius:rp,borderBottomRightRadius:rp,boxShadow:{correct:(e,{treeScale:t,projectionDelta:n})=>{let r=e2.parse(e);if(r.length>5)return e;let i=e2.createTransformer(e),a=+("number"!=typeof r[0]),o=n.x.scale*t.x,s=n.y.scale*t.y;r[0+a]/=o,r[1+a]/=s;let l=tj(o,s,.5);return"number"==typeof r[2+a]&&(r[2+a]/=l),"number"==typeof r[3+a]&&(r[3+a]/=l),i(r)}}},rT=(e,t)=>e.depth-t.depth;class rx{constructor(){this.children=[],this.isDirty=!1}add(e){en(this.children,e),this.isDirty=!0}remove(e){er(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(rT),this.isDirty=!1,this.children.forEach(e)}}function rw(e){let t=eu(e)?e.get():e;return W(t)?t.toValue():t}let rR=["TopLeft","TopRight","BottomLeft","BottomRight"],rE=rR.length,rS=e=>"string"==typeof e?parseFloat(e):e,r_=e=>"number"==typeof e||eH.test(e);function rA(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let rM=rO(0,.5,ew),rj=rO(.5,.95,B);function rO(e,t,n){return r=>r<e?0:r>t?1:n(R(e,t,r))}function rC(e,t){e.min=t.min,e.max=t.max}function rD(e,t){rC(e.x,t.x),rC(e.y,t.y)}function rL(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function rk(e,t,n,r,i){return e-=t,e=r+1/n*(e-r),void 0!==i&&(e=r+1/i*(e-r)),e}function rV(e,t,[n,r,i],a,o){!function(e,t=0,n=1,r=.5,i,a=e,o=e){if(eB.test(t)&&(t=parseFloat(t),t=tj(o.min,o.max,t/100)-o.min),"number"!=typeof t)return;let s=tj(a.min,a.max,r);e===a&&(s-=t),e.min=rk(e.min,t,n,s,i),e.max=rk(e.max,t,n,s,i)}(e,t[n],t[r],t[i],t.scale,a,o)}let rU=["x","scaleX","originX"],rN=["y","scaleY","originY"];function rF(e,t,n,r){rV(e.x,t,rU,n?n.x:void 0,r?r.x:void 0),rV(e.y,t,rN,n?n.y:void 0,r?r.y:void 0)}function rI(e){return 0===e.translate&&1===e.scale}function rB(e){return rI(e.x)&&rI(e.y)}function rH(e,t){return e.min===t.min&&e.max===t.max}function rK(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function rz(e,t){return rK(e.x,t.x)&&rK(e.y,t.y)}function rW(e){return nk(e.x)/nk(e.y)}function r$(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class rY{constructor(){this.members=[]}add(e){en(this.members,e),e.scheduleRender()}remove(e){if(er(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t,n=this.members.findIndex(t=>e===t);if(0===n)return!1;for(let e=n;e>=0;e--){let n=this.members[e];if(!1!==n.isPresent){t=n;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let n=this.lead;if(e!==n&&(this.prevLead=n,this.lead=e,e.show(),n)){n.instance&&n.scheduleRender(),e.scheduleRender(),e.resumeFrom=n,t&&(e.resumeFrom.preserveOpacity=!0),n.snapshot&&(e.snapshot=n.snapshot,e.snapshot.latestValues=n.animationValues||n.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:r}=e.options;!1===r&&n.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:n}=e;t.onExitComplete&&t.onExitComplete(),n&&n.options.onExitComplete&&n.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let rG={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},rq="undefined"!=typeof window&&void 0!==window.MotionDebug,rX=["","X","Y","Z"],rQ={visibility:"hidden"},rZ=0;function rJ(e,t,n,r){let{latestValues:i}=t;i[e]&&(n[e]=i[e],t.setStaticValue(e,0),r&&(r[e]=0))}function r0({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(e={},n=null==t?void 0:t()){this.id=rZ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,rq&&(rG.totalNodes=rG.resolvedTargetDeltas=rG.recalculatedProjection=0),this.nodes.forEach(r3),this.nodes.forEach(ie),this.nodes.forEach(it),this.nodes.forEach(r5),rq&&window.MotionDebug.record(rG)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=n?n.root||n:this,this.path=n?[...n.path,n]:[],this.parent=n,this.depth=n?n.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new rx)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new ei),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let n=this.eventHandlers.get(e);n&&n.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,n=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:r,layout:i,visualElement:a}=this.options;if(a&&!a.current&&a.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),n&&(i||r)&&(this.isLayoutDirty=!0),e){let n,r=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,n&&n(),n=function(e,t){let n=et.now(),r=({timestamp:i})=>{let a=i-n;a>=250&&(Q(r),e(a-t))};return X.read(r,!0),()=>Q(r)}(r,250),rd.hasAnimatedSinceResize&&(rd.hasAnimatedSinceResize=!1,this.nodes.forEach(r8))})}r&&this.root.registerSharedNode(r,this),!1!==this.options.animate&&a&&(r||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeTargetChanged:n,layout:r})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||a.getDefaultTransition()||il,{onLayoutAnimationStart:o,onLayoutAnimationComplete:s}=a.getProps(),l=!this.targetLayout||!rz(this.targetLayout,r)||n,u=!t&&n;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...y(i,"layout"),onPlay:o,onComplete:s};(a.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||r8(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=r})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ir),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:n}=t.options;if(!n)return;let r=n.props[ed];if(window.MotionHasOptimisedAnimation(r,"transform")){let{layout:e,layoutId:n}=t.options;window.MotionCancelOptimisedAnimation(r,"transform",X,!(e||n))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:n}=this.options;if(void 0===t&&!n)return;let r=this.getTransformTemplate();this.prevTransformTemplateValue=r?r(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(r4);return}this.isUpdating||this.nodes.forEach(r6),this.isUpdating=!1,this.nodes.forEach(r7),this.nodes.forEach(r1),this.nodes.forEach(r2),this.clearAllSnapshots();let e=et.now();Z.delta=eS(0,1e3/60,e-Z.timestamp),Z.timestamp=e,Z.isProcessing=!0,J.update.process(Z),J.preRender.process(Z),J.render.process(Z),Z.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,rg.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(r9),this.sharedNodes.forEach(ii)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,X.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){X.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=nG(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){let t=r(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!rB(this.projectionDelta),n=this.getTransformTemplate(),r=n?n(this.latestValues,""):void 0,a=r!==this.prevTransformTemplateValue;e&&(t||nJ(this.latestValues)||a)&&(i(this.instance,r),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let n=this.measurePageBox(),r=this.removeElementScroll(n);return e&&(r=this.removeTransform(r)),ih((t=r).x),ih(t.y),{animationId:this.root.animationId,measuredBox:n,layoutBox:r,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return nG();let n=t.measureViewportBox();if(!((null==(e=this.scroll)?void 0:e.wasRoot)||this.path.some(ip))){let{scroll:e}=this.root;e&&(n5(n.x,e.offset.x),n5(n.y,e.offset.y))}return n}removeElementScroll(e){var t;let n=nG();if(rD(n,e),null==(t=this.scroll)?void 0:t.wasRoot)return n;for(let t=0;t<this.path.length;t++){let r=this.path[t],{scroll:i,options:a}=r;r!==this.root&&i&&a.layoutScroll&&(i.wasRoot&&rD(n,e),n5(n.x,i.offset.x),n5(n.y,i.offset.y))}return n}applyTransform(e,t=!1){let n=nG();rD(n,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];!t&&r.options.layoutScroll&&r.scroll&&r!==r.root&&n4(n,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),nJ(r.latestValues)&&n4(n,r.latestValues)}return nJ(this.latestValues)&&n4(n,this.latestValues),n}removeTransform(e){let t=nG();rD(t,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];if(!n.instance||!nJ(n.latestValues))continue;nZ(n.latestValues)&&n.updateSnapshot();let r=nG();rD(r,n.measurePageBox()),rF(t,n.latestValues,n.snapshot?n.snapshot.layoutBox:void 0,r)}return nJ(this.latestValues)&&rF(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Z.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,n,r,i;let a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==a;if(!(e||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null==(t=this.parent)?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:l}=this.options;if(this.layout&&(s||l)){if(this.resolvedRelativeTargetAt=Z.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nG(),this.relativeTargetOrigin=nG(),nI(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),rD(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=nG(),this.targetWithTransforms=nG()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),n=this.target,r=this.relativeTarget,i=this.relativeParent.target,nN(n.x,r.x,i.x),nN(n.y,r.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):rD(this.target,this.layout.layoutBox),n3(this.target,this.targetDelta)):rD(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=nG(),this.relativeTargetOrigin=nG(),nI(this.relativeTargetOrigin,this.target,e.target),rD(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}rq&&rG.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||nZ(this.parent.latestValues)||n0(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),n=!!this.resumingFrom||this!==t,r=!0;if((this.isProjectionDirty||(null==(e=this.parent)?void 0:e.isProjectionDirty))&&(r=!1),n&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(r=!1),this.resolvedRelativeTargetAt===Z.timestamp&&(r=!1),r)return;let{layout:i,layoutId:a}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||a))return;rD(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,s=this.treeScale.y;!function(e,t,n,r=!1){let i,a,o=n.length;if(o){t.x=t.y=1;for(let s=0;s<o;s++){a=(i=n[s]).projectionDelta;let{visualElement:o}=i.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(r&&i.options.layoutScroll&&i.scroll&&i!==i.root&&n4(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),a&&(t.x*=a.x.scale,t.y*=a.y.scale,n3(e,a)),r&&nJ(i.latestValues)&&n4(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}}(this.layoutCorrected,this.treeScale,this.path,n),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=nG());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(rL(this.prevProjectionDelta.x,this.projectionDelta.x),rL(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),nU(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===s&&r$(this.projectionDelta.x,this.prevProjectionDelta.x)&&r$(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),rq&&rG.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null==(t=this.options.visualElement)||t.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=n$(),this.projectionDelta=n$(),this.projectionDeltaWithTransform=n$()}setAnimationOrigin(e,t=!1){let n,r=this.snapshot,i=r?r.latestValues:{},a={...this.latestValues},o=n$();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let s=nG(),l=(r?r.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,h=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(is));this.animationProgress=0,this.mixTargetDelta=t=>{let r=t/1e3;if(ia(o.x,e.x,r),ia(o.y,e.y,r),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,f,p,m,g;nI(s,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,p=this.relativeTargetOrigin,m=s,g=r,io(f.x,p.x,m.x,g),io(f.y,p.y,m.y,g),n&&(u=this.relativeTarget,d=n,rH(u.x,d.x)&&rH(u.y,d.y))&&(this.isProjectionDirty=!1),n||(n=nG()),rD(n,this.relativeTarget)}l&&(this.animationValues=a,function(e,t,n,r,i,a){i?(e.opacity=tj(0,void 0!==n.opacity?n.opacity:1,rM(r)),e.opacityExit=tj(void 0!==t.opacity?t.opacity:1,0,rj(r))):a&&(e.opacity=tj(void 0!==t.opacity?t.opacity:1,void 0!==n.opacity?n.opacity:1,r));for(let i=0;i<rE;i++){let a=`border${rR[i]}Radius`,o=rA(t,a),s=rA(n,a);(void 0!==o||void 0!==s)&&(o||(o=0),s||(s=0),0===o||0===s||r_(o)===r_(s)?(e[a]=Math.max(tj(rS(o),rS(s),r),0),(eB.test(s)||eB.test(o))&&(e[a]+="%")):e[a]=s)}(t.rotate||n.rotate)&&(e.rotate=tj(t.rotate||0,n.rotate||0,r))}(a,i,this.latestValues,r,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=r},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=X.update(()=>{rd.hasAnimatedSinceResize=!0,this.currentAnimation=function(e,t,n){let r=eu(0)?0:el(e);return r.start(nh("",r,1e3,n)),r.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onComplete:()=>{e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:n,layout:r,latestValues:i}=e;if(t&&n&&r){if(this!==e&&this.layout&&r&&id(this.options.animationType,this.layout.layoutBox,r.layoutBox)){n=this.target||nG();let t=nk(this.layout.layoutBox.x);n.x.min=e.target.x.min,n.x.max=n.x.min+t;let r=nk(this.layout.layoutBox.y);n.y.min=e.target.y.min,n.y.max=n.y.min+r}rD(t,n),n4(t,i),nU(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new rY),this.sharedNodes.get(e).add(t);let n=t.options.initialPromotionConfig;t.promote({transition:n?n.transition:void 0,preserveFollowOpacity:n&&n.shouldPreserveFollowOpacity?n.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null==(e=this.getStack())?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null==(e=this.getStack())?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:n}={}){let r=this.getStack();r&&r.promote(this,n),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:n}=e;if((n.z||n.rotate||n.rotateX||n.rotateY||n.rotateZ||n.skewX||n.skewY)&&(t=!0),!t)return;let r={};n.z&&rJ("z",e,r,this.animationValues);for(let t=0;t<rX.length;t++)rJ(`rotate${rX[t]}`,e,r,this.animationValues),rJ(`skew${rX[t]}`,e,r,this.animationValues);for(let t in e.render(),r)e.setStaticValue(t,r[t]),this.animationValues&&(this.animationValues[t]=r[t]);e.scheduleRender()}getProjectionStyles(e){var t,n;if(!this.instance||this.isSVG)return;if(!this.isVisible)return rQ;let r={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,r.opacity="",r.pointerEvents=rw(null==e?void 0:e.pointerEvents)||"",r.transform=i?i(this.latestValues,""):"none",r;let a=this.getLead();if(!this.projectionDelta||!this.layout||!a.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=rw(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!nJ(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let o=a.animationValues||a.latestValues;this.applyTransformsToTarget(),r.transform=function(e,t,n){let r="",i=e.x.translate/t.x,a=e.y.translate/t.y,o=(null==n?void 0:n.z)||0;if((i||a||o)&&(r=`translate3d(${i}px, ${a}px, ${o}px) `),(1!==t.x||1!==t.y)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:a,skewX:o,skewY:s}=n;e&&(r=`perspective(${e}px) ${r}`),t&&(r+=`rotate(${t}deg) `),i&&(r+=`rotateX(${i}deg) `),a&&(r+=`rotateY(${a}deg) `),o&&(r+=`skewX(${o}deg) `),s&&(r+=`skewY(${s}deg) `)}let s=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==s||1!==l)&&(r+=`scale(${s}, ${l})`),r||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),i&&(r.transform=i(o,r.transform));let{x:s,y:l}=this.projectionDelta;for(let e in r.transformOrigin=`${100*s.origin}% ${100*l.origin}% 0`,a.animationValues?r.opacity=a===this?null!=(n=null!=(t=o.opacity)?t:this.latestValues.opacity)?n:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:r.opacity=a===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,rm){if(void 0===o[e])continue;let{correct:t,applyTo:n}=rm[e],i="none"===r.transform?o[e]:t(o[e],a);if(n){let e=n.length;for(let t=0;t<e;t++)r[n[t]]=i}else r[e]=i}return this.options.layoutId&&(r.pointerEvents=a===this?rw(null==e?void 0:e.pointerEvents)||"":"none"),r}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null==(t=e.currentAnimation)?void 0:t.stop()}),this.root.nodes.forEach(r4),this.root.sharedNodes.clear()}}}function r1(e){e.updateLayout()}function r2(e){var t;let n=(null==(t=e.resumeFrom)?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:r}=e.layout,{animationType:i}=e.options,a=n.source!==e.layout.source;"size"===i?nq(e=>{let r=a?n.measuredBox[e]:n.layoutBox[e],i=nk(r);r.min=t[e].min,r.max=r.min+i}):id(i,n.layoutBox,t)&&nq(r=>{let i=a?n.measuredBox[r]:n.layoutBox[r],o=nk(t[r]);i.max=i.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[r].max=e.relativeTarget[r].min+o)});let o=n$();nU(o,t,n.layoutBox);let s=n$();a?nU(s,e.applyTransform(r,!0),n.measuredBox):nU(s,t,n.layoutBox);let l=!rB(o),u=!1;if(!e.resumeFrom){let r=e.getClosestProjectingParent();if(r&&!r.resumeFrom){let{snapshot:i,layout:a}=r;if(i&&a){let o=nG();nI(o,n.layoutBox,i.layoutBox);let s=nG();nI(s,t,a.layoutBox),rz(o,s)||(u=!0),r.options.layoutRoot&&(e.relativeTarget=s,e.relativeTargetOrigin=o,e.relativeParent=r)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:n,delta:s,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function r3(e){rq&&rG.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function r5(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function r9(e){e.clearSnapshot()}function r4(e){e.clearMeasurements()}function r6(e){e.isLayoutDirty=!1}function r7(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function r8(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ie(e){e.resolveTargetDelta()}function it(e){e.calcProjection()}function ir(e){e.resetSkewAndRotation()}function ii(e){e.removeLeadSnapshot()}function ia(e,t,n){e.translate=tj(t.translate,0,n),e.scale=tj(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function io(e,t,n,r){e.min=tj(t.min,n.min,r),e.max=tj(t.max,n.max,r)}function is(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let il={duration:.45,ease:[.4,0,.1,1]},iu=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),ic=iu("applewebkit/")&&!iu("chrome/")?Math.round:B;function ih(e){e.min=ic(e.min),e.max=ic(e.max)}function id(e,t,n){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(rW(t)-rW(n)))}function ip(e){var t;return e!==e.root&&(null==(t=e.scroll)?void 0:t.wasRoot)}let im=r0({attachResizeListener:(e,t)=>nR(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ig={current:void 0},iy=r0({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!ig.current){let e=new im({});e.mount(window),e.setOptions({layoutScroll:!0}),ig.current=e}return ig.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function iv(e,t,n){let{props:r}=e;e.animationState&&r.whileHover&&e.animationState.setActive("whileHover","Start"===n);let i=r["onHover"+n];i&&X.postRender(()=>i(t,nE(t)))}class ib extends nP{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,a]=M(e,n),o=j(e=>{let{target:n}=e,r=t(e);if("function"!=typeof r||!n)return;let a=j(e=>{r(e),n.removeEventListener("pointerleave",a)});n.addEventListener("pointerleave",a,i)});return r.forEach(e=>{e.addEventListener("pointerenter",o,i)}),a}(e,e=>(iv(this.node,e,"Start"),e=>iv(this.node,e,"End"))))}unmount(){}}class iP extends nP{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tF(nR(this.node.current,"focus",()=>this.onFocus()),nR(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function iT(e,t,n){let{props:r}=e;e.animationState&&r.whileTap&&e.animationState.setActive("whileTap","Start"===n);let i=r["onTap"+("End"===n?"":n)];i&&X.postRender(()=>i(t,nE(t)))}class ix extends nP{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,n={}){let[r,i,a]=M(e,n),o=e=>{let r=e.currentTarget;if(!N(e)||L.has(r))return;L.add(r);let a=t(e),o=(e,t)=>{window.removeEventListener("pointerup",s),window.removeEventListener("pointercancel",l),N(e)&&L.has(r)&&(L.delete(r),"function"==typeof a&&a(e,{success:t}))},s=e=>{o(e,n.useGlobalTarget||O(r,e.target))},l=e=>{o(e,!1)};window.addEventListener("pointerup",s,i),window.addEventListener("pointercancel",l,i)};return r.forEach(e=>{D.has(e.tagName)||-1!==e.tabIndex||null!==e.getAttribute("tabindex")||(e.tabIndex=0),(n.useGlobalTarget?window:e).addEventListener("pointerdown",o,i),e.addEventListener("focus",e=>U(e,i),i)}),a}(e,e=>(iT(this.node,e,"Start"),(e,{success:t})=>iT(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iw=new WeakMap,iR=new WeakMap,iE=e=>{let t=iw.get(e.target);t&&t(e)},iS=e=>{e.forEach(iE)},i_={some:0,all:1};class iA extends nP{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:n,amount:r="some",once:i}=e,a={root:t?t.current:void 0,rootMargin:n,threshold:"number"==typeof r?r:i_[r]};return function(e,t,n){let r=function({root:e,...t}){let n=e||document;iR.has(n)||iR.set(n,{});let r=iR.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(iS,{root:e,...t})),r[i]}(t);return iw.set(e,n),r.observe(e),()=>{iw.delete(e),r.unobserve(e)}}(this.node.current,a,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:n,onViewportLeave:r}=this.node.getProps(),a=t?n:r;a&&a(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}(e,t))&&this.startObserver()}unmount(){}}let iM=(0,rl.createContext)({strict:!1});var ij=n(32582);let iO=(0,rl.createContext)({});function iC(e){return i(e.animate)||d.some(t=>s(e[t]))}function iD(e){return!!(iC(e)||e.variants)}function iL(e){return Array.isArray(e)?e.join(" "):e}var ik=n(7044);let iV={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iU={};for(let e in iV)iU[e]={isEnabled:t=>iV[e].some(e=>!!t[e])};let iN=Symbol.for("motionComponentSymbol");var iF=n(21279),iI=n(15124);let iB=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function iH(e){if("string"!=typeof e||e.includes("-"));else if(iB.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}var iK=n(72789);let iz=e=>(t,n)=>{let r=(0,rl.useContext)(iO),a=(0,rl.useContext)(iF.t),o=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:n},r,a,o){let s={latestValues:function(e,t,n,r){let a={},o=r(e,{});for(let e in o)a[e]=rw(o[e]);let{initial:s,animate:l}=e,c=iC(e),h=iD(e);t&&h&&!c&&!1!==e.inherit&&(void 0===s&&(s=t.initial),void 0===l&&(l=t.animate));let d=!!n&&!1===n.initial,f=(d=d||!1===s)?l:s;if(f&&"boolean"!=typeof f&&!i(f)){let t=Array.isArray(f)?f:[f];for(let n=0;n<t.length;n++){let r=u(e,t[n]);if(r){let{transitionEnd:e,transition:t,...n}=r;for(let e in n){let t=n[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(a[e]=t)}for(let t in e)a[t]=e[t]}}}return a}(r,a,o,e),renderState:t()};return n&&(s.onMount=e=>n({props:r,current:e,...s}),s.onUpdate=e=>n(e)),s})(e,t,r,a);return n?o():(0,iK.M)(o)},iW=(e,t)=>t&&"number"==typeof e?t.transform(e):e,i$={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iY=H.length;function iG(e,t,n){let{style:r,vars:i,transformOrigin:a}=e,o=!1,s=!1;for(let e in t){let n=t[e];if(K.has(e)){o=!0;continue}if(ty(e)){i[e]=n;continue}{let t=iW(n,e7[e]);e.startsWith("origin")?(s=!0,a[e]=t):r[e]=t}}if(!t.transform&&(o||n?r.transform=function(e,t,n){let r="",i=!0;for(let a=0;a<iY;a++){let o=H[a],s=e[o];if(void 0===s)continue;let l=!0;if(!(l="number"==typeof s?s===+!!o.startsWith("scale"):0===parseFloat(s))||n){let e=iW(s,e7[o]);if(!l){i=!1;let t=i$[o]||o;r+=`${t}(${e}) `}n&&(t[o]=e)}}return r=r.trim(),n?r=n(t,i?"":r):i&&(r="none"),r}(t,e.transform,n):r.transform&&(r.transform="none")),s){let{originX:e="50%",originY:t="50%",originZ:n=0}=a;r.transformOrigin=`${e} ${t} ${n}`}}let iq={offset:"stroke-dashoffset",array:"stroke-dasharray"},iX={offset:"strokeDashoffset",array:"strokeDasharray"};function iQ(e,t,n){return"string"==typeof e?e:eH.transform(t+n*e)}function iZ(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:a,pathLength:o,pathSpacing:s=1,pathOffset:l=0,...u},c,h){if(iG(e,u,h),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:f,dimensions:p}=e;d.transform&&(p&&(f.transform=d.transform),delete d.transform),p&&(void 0!==i||void 0!==a||f.transform)&&(f.transformOrigin=function(e,t,n){let r=iQ(t,e.x,e.width),i=iQ(n,e.y,e.height);return`${r} ${i}`}(p,void 0!==i?i:.5,void 0!==a?a:.5)),void 0!==t&&(d.x=t),void 0!==n&&(d.y=n),void 0!==r&&(d.scale=r),void 0!==o&&function(e,t,n=1,r=0,i=!0){e.pathLength=1;let a=i?iq:iX;e[a.offset]=eH.transform(-r);let o=eH.transform(t),s=eH.transform(n);e[a.array]=`${o} ${s}`}(d,o,s,l,!1)}let iJ=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),i0=()=>({...iJ(),attrs:{}}),i1=e=>"string"==typeof e&&"svg"===e.toLowerCase();function i2(e,{style:t,vars:n},r,i){for(let a in Object.assign(e.style,t,i&&i.getProjectionStyles(r)),n)e.style.setProperty(a,n[a])}let i3=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function i5(e,t,n,r){for(let n in i2(e,t,void 0,r),t.attrs)e.setAttribute(i3.has(n)?n:eh(n),t.attrs[n])}function i9(e,{layout:t,layoutId:n}){return K.has(e)||e.startsWith("origin")||(t||void 0!==n)&&(!!rm[e]||"opacity"===e)}function i4(e,t,n){var r;let{style:i}=e,a={};for(let o in i)(eu(i[o])||t.style&&eu(t.style[o])||i9(o,e)||(null==(r=null==n?void 0:n.getValue(o))?void 0:r.liveStyle)!==void 0)&&(a[o]=i[o]);return a}function i6(e,t,n){let r=i4(e,t,n);for(let n in e)(eu(e[n])||eu(t[n]))&&(r[-1!==H.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=e[n]);return r}let i7=["x","y","width","height","cx","cy","r"],i8={useVisualState:iz({scrapeMotionValuesFromProps:i6,createRenderState:i0,onUpdate:({props:e,prevProps:t,current:n,renderState:r,latestValues:i})=>{if(!n)return;let a=!!e.drag;if(!a){for(let e in i)if(K.has(e)){a=!0;break}}if(!a)return;let o=!t;if(t)for(let n=0;n<i7.length;n++){let r=i7[n];e[r]!==t[r]&&(o=!0)}o&&X.read(()=>{!function(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(e){t.dimensions={x:0,y:0,width:0,height:0}}}(n,r),X.render(()=>{iZ(r,i,i1(n.tagName),e.transformTemplate),i5(n,r)})})}})},ae={useVisualState:iz({scrapeMotionValuesFromProps:i4,createRenderState:iJ})};function at(e,t,n){for(let r in t)eu(t[r])||i9(r,n)||(e[r]=t[r])}let an=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function ar(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||an.has(e)}let ai=e=>!ar(e);try{!function(e){e&&(ai=t=>t.startsWith("on")?!ar(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}let aa={current:null},ao={current:!1},as=[...tw,eY,e2],al=e=>as.find(tx(e)),au=new WeakMap,ac=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ah{scrapeMotionValuesFromProps(e,t,n){return{}}constructor({parent:e,props:t,presenceContext:n,reducedMotionConfig:r,blockInitialAnimation:i,visualState:a},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=tp,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=et.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,X.render(this.render,!1,!0))};let{latestValues:s,renderState:l,onUpdate:u}=a;this.onUpdate=u,this.latestValues=s,this.baseTarget={...s},this.initialValues=t.initial?{...s}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=n,this.depth=e?e.depth+1:0,this.reducedMotionConfig=r,this.options=o,this.blockInitialAnimation=!!i,this.isControllingVariants=iC(t),this.isVariantNode=iD(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...h}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in h){let t=h[e];void 0!==s[e]&&eu(t)&&t.set(s[e],!1)}}mount(e){this.current=e,au.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),ao.current||function(){if(ao.current=!0,ik.B)if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>aa.current=e.matches;e.addListener(t),t()}else aa.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||aa.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in au.delete(this.current),this.projection&&this.projection.unmount(),Q(this.notifyUpdate),Q(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let n;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let r=K.has(e),i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&X.preRender(this.notifyUpdate),r&&this.projection&&(this.projection.isTransformDirty=!0)}),a=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(n=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),a(),n&&n(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iU){let t=iU[e];if(!t)continue;let{isEnabled:n,Feature:r}=t;if(!this.features[e]&&r&&n(this.props)&&(this.features[e]=new r(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):nG()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<ac.length;t++){let n=ac[t];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let r=e["on"+n];r&&(this.propEventSubscriptions[n]=this.on(n,r))}this.prevMotionValues=function(e,t,n){for(let r in t){let i=t[r],a=n[r];if(eu(i))e.addValue(r,i);else if(eu(a))e.addValue(r,el(i,{owner:e}));else if(a!==i)if(e.hasValue(r)){let t=e.getValue(r);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(r);e.addValue(r,el(void 0!==t?t:i,{owner:e}))}}for(let r in n)void 0===t[r]&&e.removeValue(r);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let n=this.values.get(e);t!==n&&(n&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let n=this.values.get(e);return void 0===n&&void 0!==t&&(n=el(null===t?void 0:t,{owner:this}),this.addValue(e,n)),n}readValue(e,t){var n;let r=void 0===this.latestValues[e]&&this.current?null!=(n=this.getBaseTargetFromProps(this.props,e))?n:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=r&&("string"==typeof r&&(tm(r)||eE(r))?r=parseFloat(r):!al(r)&&e2.test(t)&&(r=tt(e,t)),this.setBaseTarget(e,eu(r)?r.get():r)),eu(r)?r.get():r}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let n,{initial:r}=this.props;if("string"==typeof r||"object"==typeof r){let i=u(this.props,r,null==(t=this.presenceContext)?void 0:t.custom);i&&(n=i[e])}if(r&&void 0!==n)return n;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||eu(i)?void 0!==this.initialValues[e]&&void 0===n?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new ei),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class ad extends ah{constructor(){super(...arguments),this.KeyframeResolver=tE}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:n}){delete t[e],delete n[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;eu(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}class af extends ad{constructor(){super(...arguments),this.type="html",this.renderInstance=i2}readValueFromInstance(e,t){if(K.has(t)){let e=te(t);return e&&e.default||0}{let n=window.getComputedStyle(e),r=(ty(t)?n.getPropertyValue(t):n[t])||0;return"string"==typeof r?r.trim():r}}measureInstanceViewportBox(e,{transformPagePoint:t}){return n6(e,t)}build(e,t,n){iG(e,t,n.transformTemplate)}scrapeMotionValuesFromProps(e,t,n){return i4(e,t,n)}}class ap extends ad{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=nG}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(K.has(t)){let e=te(t);return e&&e.default||0}return t=i3.has(t)?t:eh(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,n){return i6(e,t,n)}build(e,t,n){iZ(e,t,this.isSVGTag,n.transformTemplate)}renderInstance(e,t,n,r){i5(e,t,n,r)}mount(e){this.isSVGTag=i1(e.tagName),super.mount(e)}}let am=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(n,r)=>"create"===r?e:(t.has(r)||t.set(r,e(r)),t.get(r))})}((ra={animation:{Feature:nT},exit:{Feature:nw},inView:{Feature:iA},tap:{Feature:ix},focus:{Feature:iP},hover:{Feature:ib},pan:{Feature:ri},drag:{Feature:rn,ProjectionNode:iy,MeasureLayout:rb},layout:{ProjectionNode:iy,MeasureLayout:rb}},ro=(e,t)=>iH(e)?new ap(t):new af(t,{allowProjection:e!==rl.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){var a,o;function l(e,a){var o,l,u;let c,h={...(0,rl.useContext)(ij.Q),...e,layoutId:function({layoutId:e}){let t=(0,rl.useContext)(rc.L).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:d}=h,f=function(e){let{initial:t,animate:n}=function(e,t){if(iC(e)){let{initial:t,animate:n}=e;return{initial:!1===t||s(t)?t:void 0,animate:s(n)?n:void 0}}return!1!==e.inherit?t:{}}(e,(0,rl.useContext)(iO));return(0,rl.useMemo)(()=>({initial:t,animate:n}),[iL(t),iL(n)])}(e),p=r(e,d);if(!d&&ik.B){l=0,u=0,(0,rl.useContext)(iM).strict;let e=function(e){let{drag:t,layout:n}=iU;if(!t&&!n)return{};let r={...t,...n};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==n?void 0:n.isEnabled(e))?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(h);c=e.MeasureLayout,f.visualElement=function(e,t,n,r,i){var a,o;let{visualElement:s}=(0,rl.useContext)(iO),l=(0,rl.useContext)(iM),u=(0,rl.useContext)(iF.t),c=(0,rl.useContext)(ij.Q).reducedMotion,h=(0,rl.useRef)(null);r=r||l.renderer,!h.current&&r&&(h.current=r(e,{visualState:t,parent:s,props:n,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:c}));let d=h.current,f=(0,rl.useContext)(rh);d&&!d.projection&&i&&("html"===d.type||"svg"===d.type)&&function(e,t,n,r){let{layoutId:i,layout:a,drag:o,dragConstraints:s,layoutScroll:l,layoutRoot:u}=t;e.projection=new n(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:a,alwaysMeasureLayout:!!o||s&&nL(s),visualElement:e,animationType:"string"==typeof a?a:"both",initialPromotionConfig:r,layoutScroll:l,layoutRoot:u})}(h.current,n,i,f);let p=(0,rl.useRef)(!1);(0,rl.useInsertionEffect)(()=>{d&&p.current&&d.update(n,u)});let m=n[ed],g=(0,rl.useRef)(!!m&&!(null==(a=window.MotionHandoffIsComplete)?void 0:a.call(window,m))&&(null==(o=window.MotionHasOptimisedAnimation)?void 0:o.call(window,m)));return(0,iI.E)(()=>{d&&(p.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),rg.render(d.render),g.current&&d.animationState&&d.animationState.animateChanges())}),(0,rl.useEffect)(()=>{d&&(!g.current&&d.animationState&&d.animationState.animateChanges(),g.current&&(queueMicrotask(()=>{var e;null==(e=window.MotionHandoffMarkAsComplete)||e.call(window,m)}),g.current=!1))}),d}(i,p,h,t,e.ProjectionNode)}return(0,rs.jsxs)(iO.Provider,{value:f,children:[c&&f.visualElement?(0,rs.jsx)(c,{visualElement:f.visualElement,...h}):null,n(i,e,(o=f.visualElement,(0,rl.useCallback)(e=>{e&&p.onMount&&p.onMount(e),o&&(e?o.mount(e):o.unmount()),a&&("function"==typeof a?a(e):nL(a)&&(a.current=e))},[o])),p,d,f.visualElement)]})}e&&function(e){for(let t in e)iU[t]={...iU[t],...e[t]}}(e),l.displayName=`motion.${"string"==typeof i?i:`create(${null!=(o=null!=(a=i.displayName)?a:i.name)?o:""})`}`;let u=(0,rl.forwardRef)(l);return u[iN]=i,u}({...iH(e)?i8:ae,preloadedFeatures:ra,useRender:function(e=!1){return(t,n,r,{latestValues:i},a)=>{let o=(iH(t)?function(e,t,n,r){let i=(0,rl.useMemo)(()=>{let n=i0();return iZ(n,t,i1(r),e.transformTemplate),{...n.attrs,style:{...n.style}}},[t]);if(e.style){let t={};at(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let n={},r=function(e,t){let n=e.style||{},r={};return at(r,n,e),Object.assign(r,function({transformTemplate:e},t){return(0,rl.useMemo)(()=>{let n=iJ();return iG(n,t,e),Object.assign({},n.vars,n.style)},[t])}(e,t)),r}(e,t);return e.drag&&!1!==e.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(n.tabIndex=0),n.style=r,n})(n,i,a,t),s=function(e,t,n){let r={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(ai(i)||!0===n&&ar(i)||!t&&!ar(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}(n,"string"==typeof t,e),l=t!==rl.Fragment?{...s,...o,ref:r}:{},{children:u}=n,c=(0,rl.useMemo)(()=>eu(u)?u.get():u,[u]);return(0,rl.createElement)(t,{...l,children:c})}}(t),createVisualElement:ro,Component:e})}))},97936:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"hmrRefreshReducer",{enumerable:!0,get:function(){return r}}),n(59008),n(57391),n(86770),n(2030),n(25232),n(59435),n(56928),n(89752),n(96493),n(68214);let r=function(e,t){return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},98834:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addPathPrefix",{enumerable:!0,get:function(){return i}});let r=n(19169);function i(e,t){if(!e.startsWith("/")||!t)return e;let{pathname:n,query:i,hash:a}=(0,r.parsePath)(e);return""+t+n+i+a}}};