exports.id=1799,exports.ids=[1799],exports.modules={7036:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},11437:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(75324).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},13964:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(75324).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},15935:(e,t,r)=>{Promise.resolve().then(r.bind(r,84199))},16764:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},56085:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(75324).A)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},61135:()=>{},64398:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(75324).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(75324).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},76183:(e,t,r)=>{Promise.resolve().then(r.bind(r,79025))},79025:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var n=r(60687),s=r(82136);function i({children:e}){return(0,n.jsx)(s.SessionProvider,{children:e})}},84199:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx","default")},88920:(e,t,r)=>{"use strict";r.d(t,{N:()=>b});var n=r(60687),s=r(43210),i=r(12157),o=r(72789),l=r(21279),d=r(32582);class a extends s.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:e,isPresent:t}){let r=(0,s.useId)(),i=(0,s.useRef)(null),o=(0,s.useRef)({width:0,height:0,top:0,left:0}),{nonce:l}=(0,s.useContext)(d.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:n,top:s,left:d}=o.current;if(t||!i.current||!e||!n)return;i.current.dataset.motionPopId=r;let a=document.createElement("style");return l&&(a.nonce=l),document.head.appendChild(a),a.sheet&&a.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${n}px !important;
            top: ${s}px !important;
            left: ${d}px !important;
          }
        `),()=>{document.head.removeChild(a)}},[t]),(0,n.jsx)(a,{isPresent:t,childRef:i,sizeRef:o,children:s.cloneElement(e,{ref:i})})}let c=({children:e,initial:t,isPresent:r,onExitComplete:i,custom:d,presenceAffectsLayout:a,mode:c})=>{let p=(0,o.M)(u),m=(0,s.useId)(),f=(0,s.useCallback)(e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;i&&i()},[p,i]),v=(0,s.useMemo)(()=>({id:m,initial:t,isPresent:r,custom:d,onExitComplete:f,register:e=>(p.set(e,!1),()=>p.delete(e))}),a?[Math.random(),f]:[r,f]);return(0,s.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[r]),s.useEffect(()=>{r||p.size||!i||i()},[r]),"popLayout"===c&&(e=(0,n.jsx)(h,{isPresent:r,children:e})),(0,n.jsx)(l.t.Provider,{value:v,children:e})};function u(){return new Map}var p=r(86044);let m=e=>e.key||"";function f(e){let t=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&t.push(e)}),t}var v=r(15124);let b=({children:e,custom:t,initial:r=!0,onExitComplete:l,presenceAffectsLayout:d=!0,mode:a="sync",propagate:h=!1})=>{let[u,b]=(0,p.xQ)(h),y=(0,s.useMemo)(()=>f(e),[e]),x=h&&!u?[]:y.map(m),P=(0,s.useRef)(!0),g=(0,s.useRef)(y),k=(0,o.M)(()=>new Map),[A,C]=(0,s.useState)(y),[M,j]=(0,s.useState)(y);(0,v.E)(()=>{P.current=!1,g.current=y;for(let e=0;e<M.length;e++){let t=m(M[e]);x.includes(t)?k.delete(t):!0!==k.get(t)&&k.set(t,!1)}},[M,x.length,x.join("-")]);let w=[];if(y!==A){let e=[...y];for(let t=0;t<M.length;t++){let r=M[t],n=m(r);x.includes(n)||(e.splice(t,0,r),w.push(r))}"wait"===a&&w.length&&(e=w),j(f(e)),C(y);return}let{forceRender:E}=(0,s.useContext)(i.L);return(0,n.jsx)(n.Fragment,{children:M.map(e=>{let s=m(e),i=(!h||!!u)&&(y===M||x.includes(s));return(0,n.jsx)(c,{isPresent:i,initial:(!P.current||!!r)&&void 0,custom:i?void 0:t,presenceAffectsLayout:d,mode:a,onExitComplete:i?void 0:()=>{if(!k.has(s))return;k.set(s,!0);let e=!0;k.forEach(t=>{t||(e=!1)}),e&&(null==E||E(),j(g.current),h&&(null==b||b()),l&&l())},children:e},s)})})}},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var n=r(37413),s=r(7339),i=r.n(s);r(61135);var o=r(84199);let l={title:"Invincible - AI Content Generation Platform",description:"The ultimate content writing SaaS platform powered by advanced AI technology"};function d({children:e}){return(0,n.jsx)("html",{lang:"en",className:"dark",children:(0,n.jsx)("body",{className:`${i().className} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,suppressHydrationWarning:!0,children:(0,n.jsx)(o.default,{children:(0,n.jsx)("div",{className:"min-h-screen",children:e})})})})}},99891:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(75324).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};