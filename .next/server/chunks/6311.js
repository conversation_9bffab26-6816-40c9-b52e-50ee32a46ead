exports.id=6311,exports.ids=[6311],exports.modules={78335:()=>{},96487:()=>{},99475:(e,t,r)=>{"use strict";r.a(e,async(e,o)=>{try{r.d(t,{JU:()=>h,pB:()=>u,uG:()=>i});var s=r(94612),a=r(75263),n=e([a]);a=(n.then?(await n)():n)[0];class l{constructor(){let e=["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","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","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","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","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","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","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","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","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"],t=process.env.TAVILY_API_KEY,r=t?[t,...e]:e;this.apiKeys=r,this.currentKeyIndex=0,this.keyQuotaStatus=new Map,this.lastRotationTime=new Date,console.log(`🔑 TavilyApiKeyRotator initialized with ${this.apiKeys.length} API keys`),this.apiKeys.forEach(e=>{this.keyQuotaStatus.set(e,{hitLimit:!1,errorCount:0})})}getCurrentApiKey(){let e=this.apiKeys[this.currentKeyIndex],t=this.keyQuotaStatus.get(e);return t?.hitLimit&&t.resetTime&&new Date<t.resetTime?(console.log(`🔄 Current key has hit limit, auto-rotating...`),this.rotateToNextValidKey(),this.getCurrentApiKey()):(t?.hitLimit&&t.resetTime&&new Date>=t.resetTime&&(this.keyQuotaStatus.set(e,{hitLimit:!1,errorCount:0}),console.log(`🔄 API key quota reset for key ending in ...${e.slice(-4)}`)),e)}rotateToNextValidKey(){let e=this.currentKeyIndex,t=0,r=2*this.apiKeys.length;do{this.currentKeyIndex=(this.currentKeyIndex+1)%this.apiKeys.length,t++;let e=this.apiKeys[this.currentKeyIndex],o=this.keyQuotaStatus.get(e);if(!o?.hitLimit||o.resetTime&&new Date>=o.resetTime){console.log(`🔄 INSTANT ROTATION: Switched to API key ${this.currentKeyIndex+1}/${this.apiKeys.length} (errors: ${o?.errorCount||0})`),this.lastRotationTime=new Date;return}if(t>=r){console.warn(`⚠️ Rotation attempts exceeded, using current key anyway`);break}}while(this.currentKeyIndex!==e);console.warn("⚠️ All API keys have issues. Using least problematic key."),this.findLeastProblematicKey()}findLeastProblematicKey(){let e=0,t=1/0;this.apiKeys.forEach((r,o)=>{let s=this.keyQuotaStatus.get(r),a=s?.errorCount||0;s?.hitLimit&&s.resetTime&&new Date<s.resetTime&&(a+=1e3),a<t&&(t=a,e=o)}),this.currentKeyIndex=e,console.log(`🔄 Selected least problematic key: ${e+1}/${this.apiKeys.length} (score: ${t})`)}markKeyAsQuotaExceeded(e){let t=new Date;t.setHours(t.getHours()+24);let r=this.keyQuotaStatus.get(e)||{hitLimit:!1,errorCount:0};this.keyQuotaStatus.set(e,{hitLimit:!0,resetTime:t,errorCount:r.errorCount+1}),console.warn(`⚠️ API key ending in ...${e.slice(-4)} marked as exhausted (errors: ${r.errorCount+1}). Will reset at ${t.toISOString()}`),this.rotateToNextValidKey()}markKeyError(e,t="generic"){let r=this.keyQuotaStatus.get(e)||{hitLimit:!1,errorCount:0};this.keyQuotaStatus.set(e,{...r,errorCount:r.errorCount+1}),console.warn(`⚠️ API key ending in ...${e.slice(-4)} error count: ${r.errorCount+1} (${t})`),r.errorCount>=3&&(console.log(`🔄 Key has high error count, rotating to different key`),this.rotateToNextValidKey())}forceRotate(){console.log(`🔄 FORCE ROTATION requested`),this.rotateToNextValidKey();let e=this.getCurrentApiKey();return console.log(`🔄 Force rotation complete: now using key ending in ...${e.slice(-4)}`),e}instantRotate(e){console.log(`⚡ INSTANT ROTATION: ${e}`);let t=this.getCurrentApiKey();this.rotateToNextValidKey();let r=this.getCurrentApiKey();return console.log(`⚡ Instant rotation: ...${t.slice(-4)} → ...${r.slice(-4)}`),r}getStatus(){let e=Array.from(this.keyQuotaStatus.values()).filter(e=>!e.hitLimit||e.resetTime&&new Date>=e.resetTime).length,t=this.apiKeys.map((e,t)=>{let r=this.keyQuotaStatus.get(e),o=!r?.hitLimit||r.resetTime&&new Date>=r.resetTime;return{keyId:`Key ${t+1} (...${e.slice(-4)})`,status:o?"active":"limited",errors:r?.errorCount||0}});return{totalKeys:this.apiKeys.length,currentKeyIndex:this.currentKeyIndex,availableKeys:e,lastRotation:this.lastRotationTime,keyHealthReport:t}}resetKeyErrors(e){let t=this.keyQuotaStatus.get(e);t&&(this.keyQuotaStatus.set(e,{...t,errorCount:0}),console.log(`🔄 Reset error count for key ending in ...${e.slice(-4)}`))}resetAllKeys(){console.log(`🔄 EMERGENCY RESET: Resetting all API keys`),this.apiKeys.forEach(e=>{this.keyQuotaStatus.set(e,{hitLimit:!1,errorCount:0})}),this.currentKeyIndex=0,console.log(`🔄 All keys reset, starting from key 1`)}}class c{static getInstance(){return c.instance||(c.instance=new c),c.instance}async extractContent(e,t={}){let{timeout:r=1e4,maxLength:o=5e3,includeMetadata:n=!0}=t;try{let t;console.log(`🔍 Extracting content from: ${e}`);let i=await s.A.get(e,{timeout:r,headers:{"User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",Accept:"text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8","Accept-Language":"en-US,en;q=0.5","Accept-Encoding":"gzip, deflate",DNT:"1",Connection:"keep-alive","Upgrade-Insecure-Requests":"1"}}),l=a.load(i.data);l("script, style, nav, header, footer, aside, .advertisement, .ads, .social-share, .cookie-banner, .popup, .modal").remove();let c=l("title").text().trim()||l("h1").first().text().trim()||l('meta[property="og:title"]').attr("content")||"Extracted Content",h="";for(let e of["article",'[role="main"]',"main",".content",".post-content",".entry-content",".article-content",".article-body",".post-body",".story-body","#content",".main-content",".page-content"]){let t=l(e);if(t.length>0){let e=t.text().trim();e.length>h.length&&e.length>200&&(h=e)}}(!h||h.length<200)&&l("div, section, p").each((e,t)=>{let r=l(t).text().trim();r.length>h.length&&r.length>100&&(h=r)}),h||(h=l("body").text().trim()),(h=h.replace(/\s+/g," ").replace(/\n\s*\n/g,"\n").trim()).length>o&&(h=h.substring(0,o)+"..."),n&&(t={description:l('meta[name="description"]').attr("content")||l('meta[property="og:description"]').attr("content"),author:l('meta[name="author"]').attr("content")||l('[rel="author"]').text().trim(),publishDate:l('meta[property="article:published_time"]').attr("content")||l("time").attr("datetime"),keywords:l('meta[name="keywords"]').attr("content")?.split(",").map(e=>e.trim())});let u=this.extractStatistics(h),y=this.extractKeyInsights(h),d=h.split(/\s+/).length;return console.log(`✅ Extracted ${h.length} characters from ${e} (${d} words)`),{success:!0,url:e,title:c,content:h,metadata:t,statistics:u,keyInsights:y,wordCount:d}}catch(r){let t=r instanceof Error?r.message:"Unknown error";return console.error(`❌ Failed to extract content from ${e}:`,t),{success:!1,url:e,title:"",content:"",wordCount:0,error:t}}}async extractMultiple(e,t={}){let{maxConcurrent:r=3,...o}=t;console.log(`🔍 Extracting content from ${e.length} URLs (max concurrent: ${r})`);let s=[];for(let t=0;t<e.length;t+=r){let a=e.slice(t,t+r);console.log(`📦 Processing batch ${Math.floor(t/r)+1}/${Math.ceil(e.length/r)}`);let n=a.map(e=>this.extractContent(e,o)),i=await Promise.all(n);s.push(...i),t+r<e.length&&await new Promise(e=>setTimeout(e,1e3))}let a=s.filter(e=>e.success).length;return console.log(`✅ Content extraction complete: ${a}/${e.length} successful`),s}extractStatistics(e){let t=[],r=e.match(/\d+(\.\d+)?%/g);r&&t.push(...r.slice(0,5));let o=e.match(/\$?\d{1,3}(,\d{3})*(\.\d+)?(million|billion|thousand|k|m|b|%)?/gi);o&&t.push(...o.slice(0,5));let s=e.match(/(19|20)\d{2}/g);return s&&t.push(...s.slice(0,3)),[...new Set(t)].slice(0,10)}extractKeyInsights(e){let t=[],r=e.split(/[.!?]/).filter(e=>e.trim().length>30&&e.trim().length<200),o=["according to","research shows","studies indicate","experts say","data reveals"];return r.forEach(e=>{o.some(t=>e.toLowerCase().includes(t))&&t.push(e.trim())}),t.slice(0,8)}}class h{constructor(){this.apiKey=process.env.GOOGLE_SEARCH_API_KEY||"",this.searchEngineId=process.env.GOOGLE_SEARCH_ENGINE_ID||"",this.apiKey&&this.searchEngineId||console.warn("⚠️ Google Search API credentials not found")}async search(e,t=10){try{if(!this.apiKey||!this.searchEngineId)throw Error("Google Search API credentials not configured");console.log(`🔍 Google search: ${e}`);let r=new URLSearchParams({key:this.apiKey,cx:this.searchEngineId,q:e,num:Math.min(t,10).toString()}),o=(await s.A.get(`https://www.googleapis.com/customsearch/v1?${r}`)).data;return{items:(o.items||[]).map(e=>({title:e.title,link:e.link,snippet:e.snippet||"",displayLink:e.displayLink})),searchInformation:{totalResults:o.searchInformation?.totalResults||"0",searchTime:o.searchInformation?.searchTime||0}}}catch(e){throw console.error("❌ Google search failed:",e),e}}async extractContent(e){let t=c.getInstance(),r=await t.extractContent(e);return r.success?r.content:""}}class u{constructor(){this.baseUrl="https://api.tavily.com",this.keyRotator=new l,this.extractor=c.getInstance()}async search(e,t=10){let r,o=this.keyRotator.getStatus(),a=Math.max(2*o.totalKeys,10);console.log(`🔍 Starting Tavily search with up to ${a} attempts across ${o.totalKeys} API keys`);for(let n=1;n<=a;n++)try{console.log(`🔍 Tavily search attempt ${n}/${a} for: ${e}`);let r=this.keyRotator.getCurrentApiKey(),o=this.keyRotator.getStatus();console.log(`🔑 Using API key ending in: ...${r.slice(-4)} (${o.availableKeys}/${o.totalKeys} keys available)`);let i=(await s.A.post(`${this.baseUrl}/search`,{api_key:r,query:e,search_depth:"advanced",include_answer:!0,include_images:!1,include_raw_content:!1,max_results:Math.min(t,20),include_domains:[],exclude_domains:[]},{timeout:3e4,headers:{"Content-Type":"application/json"}})).data,l=i.results.map(e=>({title:e.title,link:e.url,snippet:e.content.substring(0,300)+(e.content.length>300?"...":""),displayLink:new URL(e.url).hostname}));return console.log(`📊 Tavily found ${l.length} results for "${e}"`),0===l.length?console.log(`⚠️ No results found for query: "${e}"`):l.forEach((e,t)=>{console.log(`${t+1}. ${e.link}`)}),{items:l,searchInformation:{totalResults:l.length.toString(),searchTime:i.response_time||0}}}catch(l){r=l;let e=this.keyRotator.getCurrentApiKey();console.error(`❌ Tavily search attempt ${n} failed:`,l.response?.data||l.message);let t=!1,s="";if(l.response){let r=l.response.status,o=l.response.data,a=o?.detail?.error||o?.error||o?.message||"";429===r||432===r||a.toLowerCase().includes("quota")||a.toLowerCase().includes("limit")||a.toLowerCase().includes("rate")||a.toLowerCase().includes("usage")?(t=!0,s="Quota/Rate limit exceeded",this.keyRotator.markKeyAsQuotaExceeded(e)):401===r||403===r||a.toLowerCase().includes("unauthorized")||a.toLowerCase().includes("forbidden")||a.toLowerCase().includes("invalid")||a.toLowerCase().includes("expired")?(t=!0,s="Authentication error",this.keyRotator.markKeyAsQuotaExceeded(e)):400===r||422===r||a.toLowerCase().includes("bad request")||a.toLowerCase().includes("validation")?(t=!0,s="Request validation error (possibly key-specific)"):a.toLowerCase().includes("too many")||a.toLowerCase().includes("throttle")?(t=!0,s="Request throttling detected",this.keyRotator.markKeyAsQuotaExceeded(e)):r>=500&&1===n&&(t=!0,s="Server error (trying different key)")}else if("ECONNREFUSED"===l.code||"ENOTFOUND"===l.code||"TIMEOUT"===l.code||l.message.toLowerCase().includes("network")||l.message.toLowerCase().includes("timeout")){if("ENOTFOUND"===l.code&&n>=5)throw console.error(`❌ Persistent DNS/network error after ${n} attempts. This is likely not a key issue.`),console.error(`🔑 Current key status: ${this.keyRotator.getStatus().availableKeys}/${this.keyRotator.getStatus().totalKeys} keys available`),Error(`Network connectivity issue (ENOTFOUND): ${l.message}. This appears to be a DNS/network problem, not an API key issue.`);n<=3&&(t=!0,s=`Network/Connection error (attempt ${n}, trying different endpoint)`)}if(t){console.log(`🔄 INSTANT KEY ROTATION: ${s}`),console.log(`🔄 Rotating from key ending in: ...${e.slice(-4)}`);let t=this.keyRotator.forceRotate();if(console.log(`🔄 Rotated to key ending in: ...${t.slice(-4)}`),n<a){console.log(`⚡ Retrying immediately with new key (attempt ${n+1}/${a})`);continue}}let i=this.keyRotator.getStatus().availableKeys;if(0===i&&n>=o.totalKeys)throw console.error(`❌ No healthy API keys remaining after ${n} attempts`),console.error(`🔑 All ${o.totalKeys} keys have been exhausted or hit limits`),Error(`All ${o.totalKeys} API keys have been exhausted or hit rate limits: ${r.response?.data?.error||r.message}`);if(n<a){let e=t?500:1e3*Math.pow(2,n);console.log(`⏳ Waiting ${e}ms before retry... (${i} healthy keys remaining)`),await new Promise(t=>setTimeout(t,e))}}console.error(`❌ All Tavily search attempts failed after ${a} attempts`);let n=this.keyRotator.getStatus();throw console.error(`🔑 Final key rotation status: ${n.availableKeys}/${n.totalKeys} keys available`),n.keyHealthReport&&n.keyHealthReport.length>0&&(console.error(`🏥 Key health report:`),n.keyHealthReport.forEach(e=>{console.error(`   ${e.keyId}: ${e.status} (${e.errors} errors)`)})),Error(`Failed to perform Tavily search after ${a} attempts across ${n.totalKeys} API keys: ${r.response?.data?.error||r.message}`)}async extractContent(e){let t=await this.extractor.extractContent(e);return t.success?t.content:""}async searchAndExtract(e,t=5){try{console.log(`🔍 Tavily search and extract for: ${e}`);let r=await this.search(e,t);if(0===r.items.length)return{searchResults:[],extractedContent:[]};let o=r.items.map(e=>e.link),s=(await this.extractor.extractMultiple(o,{maxConcurrent:3,timeout:8e3,maxLength:3e3})).filter(e=>e.success&&e.content.length>200);return console.log(`✅ Search and extract complete: ${s.length}/${r.items.length} pages extracted`),{searchResults:r.items,extractedContent:s}}catch(t){return console.error(`❌ Search and extract failed for: ${e}`,t),{searchResults:[],extractedContent:[]}}}async parallelSearch(e,t=5){console.log(`🔍 Executing ${e.length} Tavily searches in parallel...`);let r=e.map(async(e,r)=>{try{await new Promise(e=>setTimeout(e,300*r));let o=await this.search(e,t);return{query:e,results:o.items}}catch(t){return console.warn(`Parallel Tavily search failed for query: ${e}`,t),{query:e,results:[]}}});return Promise.all(r)}async parallelSearchAndExtract(e,t=3){console.log(`🔍 Executing ${e.length} Tavily search and extract operations in parallel...`);let r=e.map(async(e,r)=>{try{await new Promise(e=>setTimeout(e,400*r));let o=await this.searchAndExtract(e,t);return{query:e,extractedContent:o.extractedContent}}catch(t){return console.warn(`Parallel Tavily search and extract failed for query: ${e}`,t),{query:e,extractedContent:[]}}});return Promise.all(r)}getKeyRotatorStatus(){return this.keyRotator.getStatus()}forceKeyRotation(){return this.keyRotator.forceRotate()}getCurrentKeyInfo(){let e=this.keyRotator.getStatus(),t=this.keyRotator.getCurrentApiKey();return{key:`...${t.slice(-4)}`,status:e.availableKeys>0?"healthy":"all_exhausted",availableKeys:e.availableKeys}}}async function i(e,t=10){try{console.log(`🔍 Enhanced Tavily search with instant rotation: "${e}" (${t} results)`);let r=new u,o=r.getCurrentKeyInfo();console.log(`🔑 Starting with API key: ${o.key} (${o.availableKeys} keys available)`);let s=(await r.search(e,t)).items.map(e=>({title:e.title,url:e.link,snippet:e.snippet,content:e.snippet,description:e.snippet,displayLink:e.displayLink,source:"tavily_enhanced"}));console.log(`✅ Enhanced Tavily search successful: ${s.length} results for "${e}"`);let a=r.getCurrentKeyInfo();return a.key!==o.key&&console.log(`🔄 Key rotation occurred during search: ${o.key} → ${a.key}`),s}catch(r){console.error(`❌ Enhanced Tavily search failed for "${e}":`,r.message);try{let e=new u().getKeyRotatorStatus();console.error(`🔑 Final key status: ${e.availableKeys}/${e.totalKeys} available`),e.keyHealthReport&&e.keyHealthReport.length>0&&(console.error(`🏥 Key health report:`),e.keyHealthReport.forEach(e=>{console.error(`   ${e.keyId}: ${e.status} (${e.errors} errors)`)}))}catch(e){console.error(`⚠️ Could not get key status:`,e)}return console.log(`🔄 Providing fallback mock data for "${e}"`),function(e,t){console.log(`🔄 Generating ${t} fallback search results for "${e}"`);let r=[],o=encodeURIComponent(e);r.push({title:`Complete Guide to ${e} - 2025 Edition`,url:`https://example.com/guide-${o}`,snippet:`Comprehensive guide covering all aspects of ${e}. Learn everything you need to know with practical examples and expert insights.`,content:`Comprehensive guide covering all aspects of ${e}. Learn everything you need to know with practical examples and expert insights.`,description:`Comprehensive guide covering all aspects of ${e}. Learn everything you need to know with practical examples and expert insights.`,displayLink:"example.com",source:"fallback_mock"}),t>1&&r.push({title:`Best Practices and Tips for ${e}`,url:`https://example.org/best-practices-${o}`,snippet:`Expert-recommended best practices and proven strategies for ${e}. Avoid common mistakes and optimize your approach.`,content:`Expert-recommended best practices and proven strategies for ${e}. Avoid common mistakes and optimize your approach.`,description:`Expert-recommended best practices and proven strategies for ${e}. Avoid common mistakes and optimize your approach.`,displayLink:"example.org",source:"fallback_mock"}),t>2&&r.push({title:`${e} vs Alternatives - Detailed Comparison`,url:`https://example.net/comparison-${o}`,snippet:`In-depth comparison of ${e} with popular alternatives. Features, pricing, pros and cons analysis to help you choose.`,content:`In-depth comparison of ${e} with popular alternatives. Features, pricing, pros and cons analysis to help you choose.`,description:`In-depth comparison of ${e} with popular alternatives. Features, pricing, pros and cons analysis to help you choose.`,displayLink:"example.net",source:"fallback_mock"});for(let s=r.length;s<t;s++)r.push({title:`${e} - Resource ${s+1}`,url:`https://example${s+1}.com/resource-${o}`,snippet:`Additional information and resources about ${e}. This covers important aspects and provides valuable insights.`,content:`Additional information and resources about ${e}. This covers important aspects and provides valuable insights.`,description:`Additional information and resources about ${e}. This covers important aspects and provides valuable insights.`,displayLink:`example${s+1}.com`,source:"fallback_mock"});return console.log(`✅ Generated ${r.length} fallback search results`),r}(e,Math.min(t,3))}}o()}catch(e){o(e)}})}};