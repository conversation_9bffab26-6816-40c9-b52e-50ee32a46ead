"use strict";exports.id=3595,exports.ids=[3595],exports.modules={3595:(e,t,a)=>{a.d(t,{KnowledgeBaseOptimizer:()=>o});var s=a(80505);class o{static{this.globalCache=new Map}static getGlobalKnowledgeBase(e){let t=e.toLowerCase().replace(/[^a-z0-9]/g,"_");return this.globalCache.has(t)||this.globalCache.set(t,new s.I(t)),this.globalCache.get(t)}static findCachedAnalyses(e,t=5){console.log(`🔍 Searching global cache for analyses on "${e}"...`);let a=[],s=e.toLowerCase().split(" ");return this.globalCache.forEach((e,t)=>{e.getEntriesByType("competitive").forEach(e=>{let t=this.calculateRelevance(s,e);if(t>=.5){let s=e.url?.split("v=")[1]||"";a.push({videoId:s,title:e.title||"",analysis:e.content,timestamp:e.metadata?.timestamp||Date.now(),relevanceScore:t})}})}),a.sort((e,t)=>{let a=(e.relevanceScore||0)+1/(Date.now()-e.timestamp+1);return(t.relevanceScore||0)+1/(Date.now()-t.timestamp+1)-a}),console.log(`📊 Found ${a.length} relevant cached analyses`),a.slice(0,t)}static findCachedCaptions(e){console.log(`🔍 Checking global cache for captions of ${e.length} videos...`);let t=new Map;return this.globalCache.forEach(a=>{a.getEntriesByType("extracted_content").forEach(a=>{let s=a.url?.split("v=")[1];s&&e.includes(s)&&Date.now()-(a.metadata?.timestamp||0)<6048e5&&t.set(s,a.content)})}),console.log(`💾 Found ${t.size} cached captions`),t}static buildTopicProfile(e){console.log(`📈 Building topic profile for "${e}"...`);let t=e.toLowerCase().split(" "),a=new Map,s=new Map,o=[],c=[];if(this.globalCache.forEach(e=>{[...e.getEntriesByType("research"),...e.getEntriesByType("competitive"),...e.getEntriesByType("extracted_content")].forEach(e=>{this.calculateRelevance(t,e)>.3&&(e.metadata?.keywords?.forEach(e=>{a.set(e,(a.get(e)||0)+1)}),e.metadata?.gaps?.forEach(e=>{s.set(e,(s.get(e)||0)+1)}),e.metadata?.keyInsights&&o.push(...e.metadata.keyInsights),e.metadata?.statistics?.forEach(e=>{(e.includes("views")||e.includes("engagement"))&&c.push(e)}))})}),0===a.size)return console.log("\uD83D\uDCCA No historical data found for topic profile"),null;let l=Array.from(a.entries()).sort((e,t)=>t[1]-e[1]).map(([e])=>e).slice(0,10),n=Array.from(s.entries()).sort((e,t)=>t[1]-e[1]).map(([e])=>e).slice(0,10),i={topic:e,commonThemes:l,successfulPatterns:[...new Set(o)].slice(0,10),contentGaps:n,bestPractices:[...new Set(c)].slice(0,5),lastUpdated:Date.now()};return console.log(`📊 Topic profile created with ${i.commonThemes.length} themes, ${i.contentGaps.length} gaps`),i}static findSuccessfulPatterns(e,t){console.log(`🎯 Finding successful patterns for ${e} style, ${t} duration...`);let a=[];return this.globalCache.forEach(s=>{s.searchContent("Final Script").forEach(s=>{if(s.metadata?.keywords?.includes(e)||s.metadata?.keywords?.includes(t)){let e=s.content.match(/\[([\d:]+)\]\s*([^[]+)/g);if(e){let t=e.slice(0,3).map(e=>e.replace(/\[[\d:]+\]\s*/,""));a.push(...t)}}})}),console.log(`✨ Found ${a.length} successful patterns`),[...new Set(a)].slice(0,5)}static calculateRelevance(e,t){let a=0,s=(t.title||"").toLowerCase().split(" "),o=t.content.substring(0,500).toLowerCase();e.forEach(e=>{s.includes(e)&&(a+=.3),o.includes(e)&&(a+=.1),t.metadata?.keywords?.some(t=>t.toLowerCase().includes(e))&&(a+=.2)});let c=(Date.now()-(t.metadata?.timestamp||0))/864e5;return c<1?a+=.2:c<3?a+=.1:c<7&&(a+=.05),Math.min(a,1)}static getOptimizationRecommendations(e){console.log(`💡 Generating optimization recommendations for "${e}"...`);let t=[],a=this.findCachedAnalyses(e,3);a.length>0&&t.push(`🔄 Found ${a.length} cached competitor analyses - reuse to save API calls`);let s=this.buildTopicProfile(e);s&&(s.contentGaps.length>0&&t.push(`📊 Common content gaps: ${s.contentGaps.slice(0,3).join(", ")}`),s.bestPractices.length>0&&t.push(`✨ Best practice: ${s.bestPractices[0]}`),s.commonThemes.length>0&&t.push(`🎯 Focus on themes: ${s.commonThemes.slice(0,3).join(", ")}`));let o=this.findSuccessfulPatterns("educational","5-10 minutes");return o.length>0&&t.push(`💡 Successful hook: "${o[0].substring(0,50)}..."`),console.log(`💡 Generated ${t.length} recommendations`),t}static trackMetrics(e,t){this.getGlobalKnowledgeBase("_optimization_metrics").addEntry({type:"research",title:`Optimization Metrics: ${e}`,content:JSON.stringify(t,null,2),metadata:{source:"workflow_optimization",timestamp:Date.now(),statistics:[`${t.cachedAnalysesFound} cached analyses found`,`${t.cachedCaptionsFound} cached captions found`,`${t.apiCallsSaved} API calls saved`,`${t.timeSaved}s time saved`],keywords:["optimization","metrics","performance"]}}),console.log(`📊 Tracked optimization metrics: ${t.apiCallsSaved} API calls saved`)}static cleanupCache(e=2592e6){console.log("\uD83E\uDDF9 Cleaning up old cache entries...");let t=0;this.globalCache.forEach((a,s)=>{[...a.getEntriesByType("research"),...a.getEntriesByType("competitive"),...a.getEntriesByType("extracted_content"),...a.getEntriesByType("writing_style")].forEach(s=>{Date.now()-(s.metadata?.timestamp||0)>e&&(a.removeEntry(s.id),t++)}),0===a.getSize()&&this.globalCache.delete(s)}),console.log(`🧹 Removed ${t} old entries`)}static getGlobalCacheSize(){return this.globalCache.size}static clearGlobalCache(){console.log("\uD83E\uDDF9 Clearing entire global knowledge base cache...");let e=this.globalCache.size;this.globalCache.clear(),console.log(`🧹 Cleared ${e} global cache entries`)}constructor(){this.sessionCache=new Map,this.CACHE_DURATION=6048e5,this.RELEVANCE_THRESHOLD=.7}}}};