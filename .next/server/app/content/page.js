(()=>{var e={};e.id=7800,e.ids=[7800],e.modules={2943:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5361:(e,t,r)=>{Promise.resolve().then(r.bind(r,21514))},7036:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},10022:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},15935:(e,t,r)=>{Promise.resolve().then(r.bind(r,84199))},16764:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21514:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content/page.tsx","default")},25366:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},25860:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>p,tree:()=>c});var i=r(65239),s=r(48088),o=r(88170),n=r.n(o),l=r(30893),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);r.d(t,a);let c={children:["",{children:["content",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,21514)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/content/page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/content/page",pathname:"/content",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},41550:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},48730:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70615:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},76183:(e,t,r)=>{Promise.resolve().then(r.bind(r,79025))},78122:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79025:(e,t,r)=>{"use strict";r.d(t,{default:()=>o});var i=r(60687),s=r(82136);function o({children:e}){return(0,i.jsx)(s.SessionProvider,{children:e})}},84199:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});let i=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx","default")},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},88920:(e,t,r)=>{"use strict";r.d(t,{N:()=>v});var i=r(60687),s=r(43210),o=r(12157),n=r(72789),l=r(21279),a=r(32582);class c extends s.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function d({children:e,isPresent:t}){let r=(0,s.useId)(),o=(0,s.useRef)(null),n=(0,s.useRef)({width:0,height:0,top:0,left:0}),{nonce:l}=(0,s.useContext)(a.Q);return(0,s.useInsertionEffect)(()=>{let{width:e,height:i,top:s,left:a}=n.current;if(t||!o.current||!e||!i)return;o.current.dataset.motionPopId=r;let c=document.createElement("style");return l&&(c.nonce=l),document.head.appendChild(c),c.sheet&&c.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${e}px !important;
            height: ${i}px !important;
            top: ${s}px !important;
            left: ${a}px !important;
          }
        `),()=>{document.head.removeChild(c)}},[t]),(0,i.jsx)(c,{isPresent:t,childRef:o,sizeRef:n,children:s.cloneElement(e,{ref:o})})}let h=({children:e,initial:t,isPresent:r,onExitComplete:o,custom:a,presenceAffectsLayout:c,mode:h})=>{let x=(0,n.M)(p),m=(0,s.useId)(),u=(0,s.useCallback)(e=>{for(let t of(x.set(e,!0),x.values()))if(!t)return;o&&o()},[x,o]),b=(0,s.useMemo)(()=>({id:m,initial:t,isPresent:r,custom:a,onExitComplete:u,register:e=>(x.set(e,!1),()=>x.delete(e))}),c?[Math.random(),u]:[r,u]);return(0,s.useMemo)(()=>{x.forEach((e,t)=>x.set(t,!1))},[r]),s.useEffect(()=>{r||x.size||!o||o()},[r]),"popLayout"===h&&(e=(0,i.jsx)(d,{isPresent:r,children:e})),(0,i.jsx)(l.t.Provider,{value:b,children:e})};function p(){return new Map}var x=r(86044);let m=e=>e.key||"";function u(e){let t=[];return s.Children.forEach(e,e=>{(0,s.isValidElement)(e)&&t.push(e)}),t}var b=r(15124);let v=({children:e,custom:t,initial:r=!0,onExitComplete:l,presenceAffectsLayout:a=!0,mode:c="sync",propagate:d=!1})=>{let[p,v]=(0,x.xQ)(d),g=(0,s.useMemo)(()=>u(e),[e]),y=d&&!p?[]:g.map(m),f=(0,s.useRef)(!0),w=(0,s.useRef)(g),j=(0,n.M)(()=>new Map),[k,N]=(0,s.useState)(g),[C,P]=(0,s.useState)(g);(0,b.E)(()=>{f.current=!1,w.current=g;for(let e=0;e<C.length;e++){let t=m(C[e]);y.includes(t)?j.delete(t):!0!==j.get(t)&&j.set(t,!1)}},[C,y.length,y.join("-")]);let A=[];if(g!==k){let e=[...g];for(let t=0;t<C.length;t++){let r=C[t],i=m(r);y.includes(i)||(e.splice(t,0,r),A.push(r))}"wait"===c&&A.length&&(e=A),P(u(e)),N(g);return}let{forceRender:M}=(0,s.useContext)(o.L);return(0,i.jsx)(i.Fragment,{children:C.map(e=>{let s=m(e),o=(!d||!!p)&&(g===C||y.includes(s));return(0,i.jsx)(h,{isPresent:o,initial:(!f.current||!!r)&&void 0,custom:o?void 0:t,presenceAffectsLayout:a,mode:c,onExitComplete:o?void 0:()=>{if(!j.has(s))return;j.set(s,!0);let e=!0;j.forEach(t=>{t||(e=!1)}),e&&(null==M||M(),P(w.current),d&&(null==v||v()),l&&l())},children:e},s)})})}},92363:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a,metadata:()=>l});var i=r(37413),s=r(7339),o=r.n(s);r(61135);var n=r(84199);let l={title:"Invincible - AI Content Generation Platform",description:"The ultimate content writing SaaS platform powered by advanced AI technology"};function a({children:e}){return(0,i.jsx)("html",{lang:"en",className:"dark",children:(0,i.jsx)("body",{className:`${o().className} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,suppressHydrationWarning:!0,children:(0,i.jsx)(n.default,{children:(0,i.jsx)("div",{className:"min-h-screen",children:e})})})})}},95633:(e,t,r)=>{Promise.resolve().then(r.bind(r,99182))},99182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var i=r(60687),s=r(43210),o=r(97905),n=r(88920),l=r(10022),a=r(41550),c=r(2943),d=r(92363),h=r(70615),p=r(31158),x=r(88233),m=r(48730),u=r(75324);let b=(0,u.A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);var v=r(11860),g=r(99270),y=r(25366);let f=(0,u.A)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);var w=r(78122),j=r(85814),k=r.n(j),N=r(82136);let C={blog:{icon:l.A,label:"Blog Post",color:"from-pink-500 to-rose-500",bgColor:"bg-pink-500/10",borderColor:"border-pink-500/20"},email:{icon:a.A,label:"Email",color:"from-emerald-500 to-teal-500",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/20"},youtube_script:{icon:c.A,label:"YouTube Script",color:"from-red-500 to-orange-500",bgColor:"bg-red-500/10",borderColor:"border-red-500/20"},invincible_research:{icon:d.A,label:"Invincible V.1",color:"from-violet-700 to-indigo-700",bgColor:"bg-violet-700/10",borderColor:"border-violet-700/20"}};function P(){let{data:e}=(0,N.useSession)(),[t,r]=(0,s.useState)([]),[a,c]=(0,s.useState)(!0),[d,u]=(0,s.useState)("all"),[j,P]=(0,s.useState)(""),[A,M]=(0,s.useState)("newest"),[S,R]=(0,s.useState)("grid"),[$,U]=(0,s.useState)({total:0,limit:12,offset:0,hasMore:!1}),[E,I]=(0,s.useState)(!1),[L,T]=(0,s.useState)(!1),[_,D]=(0,s.useState)(!1),[q,H]=(0,s.useState)(""),[F,z]=(0,s.useState)(!1),V=async(e=0,t=!1)=>{try{0===e&&I(!0);let i=new URLSearchParams({limit:$.limit.toString(),offset:e.toString()});"all"!==d&&(i.append("type",d),console.log("Fetching content with type filter:",d));let s=`/api/content?${i}`;console.log("Fetching from URL:",s);let o=await fetch(s);if(o.ok){let e=await o.json();console.log("API response:",e),t?r(t=>[...t,...e.content||[]]):r(e.content||[]),U(e.pagination||{total:0,limit:12,offset:0,hasMore:!1})}else console.error("API response error:",o.status,o.statusText)}catch(e){console.error("Error fetching content:",e)}finally{c(!1),I(!1)}},G=e=>{u(e),r([]),U(e=>({...e,offset:0}))},O=async e=>{try{(await fetch(`/api/content?id=${e}`,{method:"DELETE"})).ok&&(r(t=>t.filter(t=>t.id!==e)),U(e=>({...e,total:e.total-1})))}catch(e){console.error("Error deleting content:",e)}},B=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("Error copying content:",e)}},W=e=>{let t=document.createElement("a"),r=new Blob([e.content],{type:"text/plain"});t.href=URL.createObjectURL(r),t.download=`${e.title.replace(/[^a-z0-9]/gi,"_")}.txt`,document.body.appendChild(t),t.click(),document.body.removeChild(t)},Z=async e=>{try{if("youtube_script"===e.type)return`/youtube-script-view?script=${encodeURIComponent(e.content)}&title=${encodeURIComponent(e.title)}`;if(e.id&&(await fetch(`/api/articles/${e.id}`)).ok)return`/article-view/${e.id}`;let t=await fetch("/api/articles/store",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:e.title,content:e.content,type:e.type,metadata:e.metadata})}),r=await t.json();if(t.ok&&r.success&&r.url)return r.url;return console.warn("Failed to store article, using fallback URL"),`/article-view?article=${encodeURIComponent(e.content)}&title=${encodeURIComponent(e.title)}&type=${encodeURIComponent(e.type)}`}catch(t){return console.error("Error generating article URL:",t),`/article-view?article=${encodeURIComponent(e.content)}&title=${encodeURIComponent(e.title)}&type=${encodeURIComponent(e.type)}`}},Q=async(e,t)=>{t.preventDefault();try{let t=await Z(e);window.location.href=t}catch(t){console.error("Error handling view click:",t),"youtube_script"===e.type?window.location.href=`/youtube-script-view?script=${encodeURIComponent(e.content)}&title=${encodeURIComponent(e.title)}`:window.location.href=`/article-view?article=${encodeURIComponent(e.content)}&title=${encodeURIComponent(e.title)}&type=${encodeURIComponent(e.type)}`}},X=t.filter(e=>""===j||e.title.toLowerCase().includes(j.toLowerCase())||e.content.toLowerCase().includes(j.toLowerCase())).sort((e,t)=>{switch(A){case"newest":return new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime();case"oldest":return new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime();case"title":return e.title.localeCompare(t.title);case"wordCount":return(t.wordCount||0)-(e.wordCount||0);default:return 0}}),Y=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),J=e=>C[e]||C.blog,K=({item:e,index:t})=>{let r=J(e.type),s=r.icon;return(0,i.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*t},className:`group relative backdrop-blur-xl border rounded-xl p-6 transition-all duration-300 ${"invincible_research"===e.type?"bg-gradient-to-br from-violet-900/20 to-indigo-900/20 border-violet-500/30 hover:border-violet-400/50 shadow-2xl":`bg-white/5 ${r.borderColor} hover:bg-white/10 hover:border-white/30`}`,children:["invincible_research"===e.type&&(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-xl"}),(0,i.jsxs)("div",{className:"relative space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between",children:[(0,i.jsx)("div",{className:`p-3 rounded-xl border ${"invincible_research"===e.type?"bg-violet-700/30 border-violet-600/50 backdrop-blur-sm":`${r.bgColor} ${r.borderColor}`}`,children:(0,i.jsx)(s,{className:"w-6 h-6 text-white"})}),(0,i.jsxs)("div",{className:"flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,i.jsx)(o.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t=>{t.stopPropagation(),B(e.content)},className:"p-2 text-gray-400 hover:text-white rounded-lg hover:bg-white/10 transition-colors",title:"Copy content",children:(0,i.jsx)(h.A,{className:"w-4 h-4"})}),(0,i.jsx)(o.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t=>{t.stopPropagation(),W(e)},className:"p-2 text-gray-400 hover:text-white rounded-lg hover:bg-white/10 transition-colors",title:"Download",children:(0,i.jsx)(p.A,{className:"w-4 h-4"})}),(0,i.jsx)(o.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t=>{t.stopPropagation(),O(e.id)},className:"p-2 text-gray-400 hover:text-red-400 rounded-lg hover:bg-red-500/10 transition-colors",title:"Delete",children:(0,i.jsx)(x.A,{className:"w-4 h-4"})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-white font-semibold text-lg mb-2 line-clamp-2",children:e.title}),(0,i.jsx)("p",{className:"text-gray-400 text-sm line-clamp-3 leading-relaxed",children:e.preview})]}),(0,i.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-white/10",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3 text-xs text-gray-500",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(m.A,{className:"w-3 h-3"}),(0,i.jsx)("span",{children:Y(e.createdAt)})]}),e.wordCount&&(0,i.jsxs)("span",{children:[e.wordCount.toLocaleString()," words"]})]}),(0,i.jsx)(o.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:t=>Q(e,t),className:"px-3 py-1.5 bg-violet-600 hover:bg-violet-700 text-white text-sm rounded-lg transition-colors",children:"View"})]})]})]})};return e?(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-violet-900",children:(0,i.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,i.jsx)(n.N,{children:_&&(0,i.jsx)(o.P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},className:"mb-6 bg-gradient-to-r from-violet-600/20 to-indigo-600/20 backdrop-blur-xl border border-violet-500/30 rounded-xl p-4",children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,i.jsx)("div",{className:"p-2 bg-violet-600/30 rounded-lg",children:(0,i.jsx)(b,{className:"w-5 h-5 text-violet-300"})}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("p",{className:"text-white font-medium",children:["Filtering by ",q]}),(0,i.jsx)("p",{className:"text-violet-200 text-sm",children:"Showing content from your dashboard selection"})]})]}),(0,i.jsx)("button",{onClick:()=>D(!1),className:"p-1 text-violet-300 hover:text-white transition-colors",children:(0,i.jsx)(v.A,{className:"w-4 h-4"})})]})})}),(0,i.jsxs)("div",{className:"mb-8",children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"My Content Library"}),(0,i.jsx)("p",{className:"text-gray-400",children:"Access and manage all your generated content"})]}),(0,i.jsxs)("div",{className:"mb-8 space-y-4",children:[(0,i.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,i.jsxs)("div",{className:"relative flex-1",children:[(0,i.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,i.jsx)("input",{type:"text",placeholder:"Search content...",value:j,onChange:e=>P(e.target.value),className:"w-full pl-10 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-violet-500/50 transition-colors"})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsxs)("button",{onClick:()=>T(!L),className:"px-4 py-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-gray-400 hover:text-white transition-colors flex items-center space-x-2",children:[(0,i.jsx)(b,{className:"w-4 h-4"}),(0,i.jsx)("span",{children:"Filters"})]}),(0,i.jsx)("button",{onClick:()=>R("grid"===S?"list":"grid"),className:"p-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-gray-400 hover:text-white transition-colors",children:"grid"===S?(0,i.jsx)(y.A,{className:"w-4 h-4"}):(0,i.jsx)(f,{className:"w-4 h-4"})}),(0,i.jsx)("button",{onClick:()=>V(),disabled:E,className:"p-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-gray-400 hover:text-white transition-colors disabled:opacity-50",children:(0,i.jsx)(w.A,{className:`w-4 h-4 ${E?"animate-spin":""}`})})]})]}),(0,i.jsx)(n.N,{children:L&&(0,i.jsx)(o.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-6",children:(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Content Type"}),(0,i.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,i.jsxs)("button",{onClick:()=>G("all"),className:`px-3 py-2 text-sm rounded-lg transition-colors ${"all"===d?"bg-violet-600 text-white":"bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white"}`,children:["All (",$.total,")"]}),Object.entries(C).map(([e,t])=>(0,i.jsxs)("button",{"data-filter":e,onClick:()=>G(e),className:`px-3 py-2 text-sm rounded-lg transition-colors flex items-center space-x-1 ${d===e?"bg-violet-600 text-white":"bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white"}`,children:[(0,i.jsx)(t.icon,{className:"w-3 h-3"}),(0,i.jsx)("span",{children:t.label})]},e))]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Sort By"}),(0,i.jsxs)("select",{value:A,onChange:e=>M(e.target.value),className:"w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:border-violet-500/50",children:[(0,i.jsx)("option",{value:"newest",children:"Newest First"}),(0,i.jsx)("option",{value:"oldest",children:"Oldest First"}),(0,i.jsx)("option",{value:"title",children:"Title A-Z"}),(0,i.jsx)("option",{value:"wordCount",children:"Word Count (High to Low)"})]})]})]})})})]}),(0,i.jsxs)("div",{className:"mb-6 grid grid-cols-1 sm:grid-cols-4 gap-4",children:[(0,i.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-white",children:$.total}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"Total Content"})]}),(0,i.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-violet-400",children:t.filter(e=>"invincible_research"===e.type).length}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"Invincible V.1"})]}),(0,i.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-pink-400",children:t.filter(e=>"blog"===e.type).length}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"Blog Posts"})]}),(0,i.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4",children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-red-400",children:t.filter(e=>"youtube_script"===e.type).length}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:"YouTube Scripts"})]})]}),a?(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,i.jsx)("div",{className:"animate-pulse",children:(0,i.jsx)("div",{className:"h-64 bg-white/5 rounded-xl"})},e))}):0===X.length?(0,i.jsxs)(o.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center py-20",children:[(0,i.jsx)(l.A,{className:"w-16 h-16 text-gray-600 mx-auto mb-6"}),(0,i.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No content found"}),(0,i.jsx)("p",{className:"text-gray-400 mb-8",children:j?"Try adjusting your search terms":"Start creating content to build your library"}),(0,i.jsx)(k(),{href:"/invincible",className:"px-6 py-3 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors",children:"Create Content"})]}):(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)("div",{className:`grid gap-6 ${"grid"===S?"grid-cols-1 md:grid-cols-2 lg:grid-cols-3":"grid-cols-1"}`,children:X.map((e,t)=>(0,i.jsx)(K,{item:e,index:t},e.id))}),$.hasMore&&(0,i.jsx)("div",{className:"text-center mt-12",children:(0,i.jsx)(o.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{$.hasMore&&V($.offset+$.limit,!0)},className:"px-8 py-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-white transition-colors",children:"Load More Content"})})]})]})}):(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-violet-900 flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"Access Denied"}),(0,i.jsx)("p",{className:"text-gray-400 mb-6",children:"Please sign in to view your content."}),(0,i.jsx)(k(),{href:"/login",className:"px-6 py-3 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors",children:"Sign In"})]})})}},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});let i=(0,r(75324).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[7719,5380,4101],()=>r(25860));module.exports=i})();