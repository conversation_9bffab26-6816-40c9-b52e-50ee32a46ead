(()=>{var e={};e.id=1298,e.ids=[1298],e.modules={2943:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]])},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11437:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25898:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/youtube/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/youtube/page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45583:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},56085:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},56218:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["generate",{children:["youtube",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,25898)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/youtube/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/youtube/page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/generate/youtube/page",pathname:"/generate/youtube",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},57896:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(60687),a=s(43210),i=s(82136),l=s(16189),n=s(97905),o=s(11437),d=s(2943),c=s(78200),u=s(28559),x=s(56085),h=s(45583),m=s(99270);let p=(0,s(75324).A)("CirclePlay",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polygon",{points:"10 8 16 12 10 16 10 8",key:"1cimsy"}]]);var b=s(13861),f=s(70615),g=s(31158),j=s(85814),v=s.n(j),y=s(42347);function w(){let{data:e,status:t}=(0,i.useSession)();(0,l.useRouter)();let[s,j]=(0,a.useState)({title:"",brief:"",duration:"5-10 minutes",style:"educational",targetAudience:"general audience",useAdvancedResearch:!0,videoUrls:""}),[w,N]=(0,a.useState)(!1),[A,k]=(0,a.useState)(""),[S,P]=(0,a.useState)(null),[D,C]=(0,a.useState)(0),[T,R]=(0,a.useState)(""),[q,M]=(0,a.useState)(null),[_,U]=(0,a.useState)(0),[F,I]=(0,a.useState)(0),[L,V]=(0,a.useState)(null);if("loading"===t)return(0,r.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-red-400 border-t-transparent rounded-full mx-auto"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Loading..."})]})});if("unauthenticated"===t)return null;let z=async e=>{e.preventDefault(),N(!0),C(0),R("Starting script generation..."),M(null),V(null),I(0);try{let e=await fetch("/api/generate/youtube",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}),t=await e.json();if(t.success){k(t.content),P(t.progressId),M(t.metadata),V(t.research),U(t.wordCount||t.content.split(/\s+/).filter(e=>e.length>0).length);let e=[{message:"Searching web and scraping research content...",progress:15},{message:"Finding YouTube videos and extracting captions...",progress:35},{message:"Generating script with comprehensive analysis...",progress:60},{message:"Finalizing research-backed script...",progress:90},{message:"Script generation complete!",progress:100}];for(let t=0;t<e.length;t++)setTimeout(()=>{C(e[t].progress),R(e[t].message)},1500*t)}else alert("Error: "+t.error)}catch(e){alert("Failed to generate YouTube script")}finally{setTimeout(()=>N(!1),8e3)}},G=[{id:0,title:"Web Research",icon:(0,r.jsx)(o.A,{className:"w-6 h-6"}),description:"Search & scrape content from 10 sources",color:"from-blue-500 to-cyan-500"},{id:1,title:"YouTube Analysis",icon:(0,r.jsx)(d.A,{className:"w-6 h-6"}),description:"Find top 5 videos & extract captions",color:"from-red-500 to-orange-500"},{id:2,title:"Script Generation",icon:(0,r.jsx)(c.A,{className:"w-6 h-6"}),description:"AI follows your instructions exactly",color:"from-purple-500 to-pink-500"}];return(0,r.jsxs)("div",{className:"min-h-screen bg-[#0f0f0f]",children:[(0,r.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-red-950/30 via-[#0f0f0f] to-red-900/20"}),(0,r.jsx)(n.P.div,{animate:{scale:[1,1.2,1],opacity:[.3,.6,.3]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},className:"absolute top-1/3 left-1/4 w-[600px] h-[400px] bg-gradient-to-r from-red-500/20 to-transparent rounded-full blur-[120px]"})]}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)("div",{className:"border-b border-[#272727] bg-[#0f0f0f]/95 backdrop-blur-xl",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)(v(),{href:"/dashboard",children:(0,r.jsx)(n.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"p-2 rounded-full hover:bg-[#272727] transition-all duration-200",children:(0,r.jsx)(u.A,{className:"w-6 h-6 text-white"})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(n.P.div,{whileHover:{scale:1.1,rotate:5},className:"relative",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-[#ff0000] rounded-lg flex items-center justify-center shadow-lg",children:(0,r.jsx)(d.A,{className:"w-6 h-6 text-white"})}),(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-[#0f0f0f] animate-pulse"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-2xl font-bold text-white flex items-center gap-2",children:["Smart Script Generator",(0,r.jsx)("span",{className:"text-xs bg-[#ff0000] text-white px-2 py-1 rounded-full font-medium",children:"V2.0"})]}),(0,r.jsx)("p",{className:"text-[#aaa] text-sm",children:"Research-Powered YouTube Scripts"})]})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)(n.P.div,{whileHover:{scale:1.05},className:"hidden md:flex items-center space-x-2 bg-[#272727] rounded-full px-4 py-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-white text-sm font-medium",children:"Gemini 2.5 Flash"})]})})]})})}),(0,r.jsx)("div",{className:"max-w-6xl mx-auto px-6 py-8",children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"bg-[#181818] border border-[#303030] rounded-2xl p-6",children:[(0,r.jsxs)("h3",{className:"text-lg font-bold text-white mb-4 flex items-center",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-yellow-400 mr-2"}),"Streamlined Workflow"]}),(0,r.jsx)("div",{className:"grid grid-cols-3 gap-4",children:G.map((e,t)=>(0,r.jsxs)(n.P.div,{className:`relative p-4 rounded-xl border-2 transition-all duration-300 ${F>=e.id&&w?"border-white bg-gradient-to-br "+e.color+" shadow-lg":"border-[#404040] bg-[#272727]"}`,whileHover:{scale:1.02},children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsx)("div",{className:`p-2 rounded-lg ${F>=e.id&&w?"bg-white/20":"bg-[#404040]"}`,children:e.icon}),F>=e.id&&w&&(0,r.jsx)(n.P.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full"})]}),(0,r.jsx)("h4",{className:"font-semibold text-white text-sm",children:e.title}),(0,r.jsx)("p",{className:"text-xs text-[#aaa] mt-1",children:e.description})]},e.id))})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-[#181818] border border-[#303030] rounded-2xl p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-bold text-white",children:"Create Your Script"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 bg-[#272727] rounded-full px-3 py-1",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-white text-xs font-medium",children:"Ready"})]})]}),(0,r.jsxs)("form",{onSubmit:z,className:"space-y-5",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-[#aaa] mb-2",children:"\uD83C\uDFAC Video Title *"}),(0,r.jsx)("input",{type:"text",value:s.title,onChange:e=>j({...s,title:e.target.value}),placeholder:"How to create viral YouTube videos in 2024...",required:!0,className:"w-full px-4 py-3 bg-[#272727] border border-[#404040] rounded-xl text-white placeholder-[#666] focus:border-[#ff0000] transition-all duration-300"})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-[#aaa] mb-2 flex items-center",children:["\uD83D\uDCDD Script Instructions *",(0,r.jsx)("span",{className:"ml-2 text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded-full",children:"AI Directive"})]}),(0,r.jsx)("textarea",{value:s.brief,onChange:e=>j({...s,brief:e.target.value}),placeholder:"Tell me exactly how you want this script created:  • What tone and style should I use? • What specific points must be covered? • How should I structure the content? • What's your target audience? • Any specific requirements or constraints?  Example: 'Create an energetic, beginner-friendly tutorial that explains X in simple terms, includes 3 actionable tips, and has a strong call-to-action at the end.'",required:!0,rows:6,className:"w-full px-4 py-3 bg-[#272727] border border-[#404040] rounded-xl text-white placeholder-[#666] focus:border-[#ff0000] transition-all duration-300 resize-none"}),(0,r.jsxs)("div",{className:"mt-2 flex items-center justify-between text-xs text-[#aaa]",children:[(0,r.jsx)("span",{children:"\uD83D\uDCA1 The AI will follow these instructions precisely when creating your script"}),(0,r.jsxs)("span",{children:[s.brief.length,"/1000"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-[#aaa] mb-2 flex items-center",children:["\uD83D\uDCFA YouTube Video Links (Optional)",(0,r.jsx)("span",{className:"ml-2 text-xs bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full",children:"Style Learning"})]}),(0,r.jsx)("textarea",{value:s.videoUrls,onChange:e=>j({...s,videoUrls:e.target.value}),placeholder:"Paste YouTube video URLs here (one per line): https://www.youtube.com/watch?v=example1 https://www.youtube.com/watch?v=example2 https://youtu.be/example3",rows:4,className:"w-full px-4 py-3 bg-[#272727] border border-[#404040] rounded-xl text-white placeholder-[#666] focus:border-[#ff0000] transition-all duration-300 resize-none font-mono text-sm"}),(0,r.jsxs)("div",{className:"mt-2 flex items-center justify-between text-xs text-[#aaa]",children:[(0,r.jsx)("span",{children:"\uD83D\uDCA1 AI will analyze these videos to learn writing style and structure"}),(0,r.jsxs)("span",{children:[s.videoUrls.split("\n").filter(e=>e.trim().includes("youtube")).length," videos"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-[#aaa] mb-2",children:"⏱️ Duration"}),(0,r.jsxs)("select",{value:s.duration,onChange:e=>j({...s,duration:e.target.value}),className:"w-full px-3 py-2 bg-[#272727] border border-[#404040] rounded-lg text-white text-sm focus:border-[#ff0000] transition-all",children:[(0,r.jsx)("option",{value:"3-5 minutes",children:"3-5 min"}),(0,r.jsx)("option",{value:"5-10 minutes",children:"5-10 min"}),(0,r.jsx)("option",{value:"10-15 minutes",children:"10-15 min"}),(0,r.jsx)("option",{value:"15-30 minutes",children:"15-30 min"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-[#aaa] mb-2",children:"\uD83C\uDFA8 Style"}),(0,r.jsxs)("select",{value:s.style,onChange:e=>j({...s,style:e.target.value}),className:"w-full px-3 py-2 bg-[#272727] border border-[#404040] rounded-lg text-white text-sm focus:border-[#ff0000] transition-all",children:[(0,r.jsx)("option",{value:"educational",children:"Educational"}),(0,r.jsx)("option",{value:"entertainment",children:"Entertainment"}),(0,r.jsx)("option",{value:"tutorial",children:"Tutorial"}),(0,r.jsx)("option",{value:"review",children:"Review"}),(0,r.jsx)("option",{value:"vlog",children:"Vlog"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-[#aaa] mb-2",children:"\uD83D\uDC65 Audience"}),(0,r.jsx)("input",{type:"text",value:s.targetAudience,onChange:e=>j({...s,targetAudience:e.target.value}),placeholder:"General audience",className:"w-full px-3 py-2 bg-[#272727] border border-[#404040] rounded-lg text-white placeholder-[#666] text-sm focus:border-[#ff0000] transition-all"})]})]}),(0,r.jsx)(n.P.button,{type:"submit",disabled:w||!s.title||!s.brief,whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full py-4 bg-gradient-to-r from-[#ff0000] to-[#cc0000] text-white font-bold rounded-xl hover:from-[#ff1a1a] hover:to-[#e60000] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 flex items-center justify-center space-x-3",children:w?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.P.div,{animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},className:"w-5 h-5 border-2 border-white/30 border-t-white rounded-full"}),(0,r.jsx)("span",{children:"Generating Script..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Generate YouTube Script"})]})}),w&&(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-[#aaa]",children:T}),(0,r.jsxs)("span",{className:"text-[#ff0000] font-medium",children:[D,"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-[#404040] rounded-full h-2",children:(0,r.jsx)(n.P.div,{className:"bg-gradient-to-r from-[#ff0000] to-[#ff6600] h-2 rounded-full transition-all duration-500",style:{width:`${D}%`}})})]})]})]})]}),L&&(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-[#181818] border border-[#303030] rounded-2xl p-6",children:[(0,r.jsxs)("h3",{className:"text-2xl font-bold text-white mb-6 flex items-center",children:[(0,r.jsx)(m.A,{className:"w-6 h-6 text-green-400 mr-3"}),"Research Sources Used"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[L.webSources&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"text-lg font-semibold text-white mb-4",children:["\uD83C\uDF10 Web Research (",L.webSources.length," sources)"]}),(0,r.jsx)("div",{className:"space-y-3",children:L.webSources.slice(0,5).map((e,t)=>(0,r.jsxs)("div",{className:"p-4 bg-[#272727] rounded-xl border border-[#404040] hover:border-[#555] transition-colors",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,r.jsxs)("span",{className:"text-white font-medium text-sm",children:[e.title.substring(0,50),"..."]}),(0,r.jsx)("span",{className:"text-xs text-green-400 bg-green-400/20 px-2 py-1 rounded-full",children:e.source})]}),(0,r.jsx)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"text-xs text-blue-400 hover:text-blue-300 transition-colors",children:"View Source →"})]},t))})]}),L.youtubeVideos&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"text-lg font-semibold text-white mb-4",children:["\uD83D\uDCFA YouTube Analysis (",L.youtubeVideos.length," videos)"]}),(0,r.jsx)("div",{className:"space-y-3",children:L.youtubeVideos.map((e,t)=>(0,r.jsxs)("div",{className:"p-4 bg-[#272727] rounded-xl border border-[#404040] hover:border-[#555] transition-colors",children:[(0,r.jsxs)("div",{className:"mb-3",children:[(0,r.jsx)("h5",{className:"text-white font-medium text-sm mb-1",children:e.title}),(0,r.jsx)("p",{className:"text-xs text-[#aaa]",children:e.channel}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 mt-2",children:[(0,r.jsxs)("span",{className:"text-xs text-red-400 bg-red-400/20 px-2 py-1 rounded",children:[e.views," views"]}),(0,r.jsxs)("span",{className:"text-xs text-green-400 bg-green-400/20 px-2 py-1 rounded",children:[e.captionLength," chars"]})]})]}),(0,r.jsxs)("a",{href:e.url,target:"_blank",rel:"noopener noreferrer",className:"inline-flex items-center space-x-2 text-xs text-[#ff0000] hover:text-[#ff3333] transition-colors",children:[(0,r.jsx)(p,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Watch Video"})]})]},t))})]})]})]}),A&&(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"w-full",children:[(0,r.jsxs)("div",{className:"mb-6 flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"\uD83C\uDFAC Your Viral Script is Ready!"}),(0,r.jsx)("p",{className:"text-[#aaa]",children:"Research-powered, AI-generated YouTube script with stunning visual display"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"text-right text-sm text-[#aaa]",children:[(0,r.jsxs)("div",{children:[_.toLocaleString()," words"]}),(0,r.jsxs)("div",{children:[Math.ceil(_/150)," min read time"]})]}),(0,r.jsxs)(v(),{href:`/youtube-script-view?title=${encodeURIComponent(s.title)}&script=${encodeURIComponent(A)}`,className:"px-6 py-3 bg-[#ff0000] hover:bg-[#cc0000] text-white font-medium rounded-xl transition-all flex items-center space-x-2 shadow-lg",children:[(0,r.jsx)(b.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Full Screen View"})]})]})]}),(0,r.jsx)("div",{className:"w-full rounded-2xl overflow-hidden border border-[#303030]/50 shadow-2xl",children:(0,r.jsx)(y.A,{title:s.title,content:A,wordCount:_})}),(0,r.jsxs)("div",{className:"mt-6 flex items-center justify-center space-x-4",children:[(0,r.jsxs)("button",{onClick:()=>{navigator.clipboard.writeText(A)},className:"flex items-center space-x-2 px-6 py-3 bg-[#272727] hover:bg-[#404040] text-white rounded-xl transition-all shadow-lg",children:[(0,r.jsx)(f.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Copy Script"})]}),(0,r.jsxs)("button",{onClick:()=>{let e=new Blob([A],{type:"text/plain"}),t=URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download=`youtube-script-${s.title.replace(/\s+/g,"-").toLowerCase()}.txt`,r.click()},className:"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-[#ff0000] to-[#cc0000] hover:from-[#ff1a1a] hover:to-[#e60000] text-white rounded-xl transition-all shadow-lg",children:[(0,r.jsx)(g.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Download Script"})]})]})]})]})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63375:(e,t,s)=>{Promise.resolve().then(s.bind(s,25898))},71623:(e,t,s)=>{Promise.resolve().then(s.bind(s,57896))},78200:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},99270:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,5380,4101,2403],()=>s(56218));module.exports=r})();