(()=>{var e={};e.id=6571,e.ids=[6571],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7036:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},7110:(e,t,r)=>{Promise.resolve().then(r.bind(r,74279))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},15935:(e,t,r)=>{Promise.resolve().then(r.bind(r,84199))},16189:(e,t,r)=>{"use strict";var s=r(65773);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},16764:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},36648:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["generate",{children:["email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,74279)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/email/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/email/page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/generate/email/page",pathname:"/generate/email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},41550:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},45583:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},56085:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},61135:()=>{},61473:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>y});var s=r(60687),a=r(43210),i=r(82136),l=r(16189),n=r(97905),o=r(28559),d=r(41550),c=r(56085),h=r(11860),m=r(96474),u=r(45583),p=r(70615),x=r(31158),b=r(85814),v=r.n(b);function y(){let{data:e,status:t}=(0,i.useSession)();(0,l.useRouter)();let[r,b]=(0,a.useState)({purpose:"",audience:"",tone:"professional",keyPoints:[""]}),[y,f]=(0,a.useState)(!1),[g,j]=(0,a.useState)("");if("loading"===t)return(0,s.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-emerald-400 border-t-transparent rounded-full mx-auto"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Loading..."})]})});if("unauthenticated"===t)return null;let w=async e=>{e.preventDefault(),f(!0);try{let e=await fetch("/api/generate/email",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...r,keyPoints:r.keyPoints.filter(e=>""!==e.trim())})}),t=await e.json();t.success?j(t.content):alert("Error: "+t.error)}catch(e){alert("Failed to generate email")}finally{f(!1)}},P=e=>{let t=r.keyPoints.filter((t,r)=>r!==e);b({...r,keyPoints:t.length>0?t:[""]})},k=(e,t)=>{let s=[...r.keyPoints];s[e]=t,b({...r,keyPoints:s})};return(0,s.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,s.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-900/20 via-black to-teal-900/20"}),(0,s.jsx)(n.P.div,{animate:{x:[0,100,0],y:[0,-100,0]},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-emerald-500/10 rounded-full blur-[100px]"}),(0,s.jsx)(n.P.div,{animate:{x:[0,-100,0],y:[0,100,0]},transition:{duration:15,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-teal-500/10 rounded-full blur-[120px]"})]}),(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsx)("div",{className:"border-b border-white/10 bg-black/60 backdrop-blur-xl",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(v(),{href:"/dashboard",children:(0,s.jsx)(n.P.button,{whileHover:{scale:1.05},className:"p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all",children:(0,s.jsx)(o.A,{className:"w-5 h-5 text-white"})})}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-emerald-600 to-teal-600",children:(0,s.jsx)(d.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Email Composer"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Professional Email Generation"})]})]})]})})})}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsx)(n.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"space-y-6",children:(0,s.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-white mb-6 flex items-center",children:[(0,s.jsx)(c.A,{className:"w-5 h-5 mr-2 text-emerald-400"}),"Email Configuration"]}),(0,s.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Purpose *"}),(0,s.jsx)("input",{type:"text",value:r.purpose,onChange:e=>b({...r,purpose:e.target.value}),placeholder:"e.g., Product launch announcement, Follow-up meeting...",required:!0,className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Target Audience *"}),(0,s.jsx)("input",{type:"text",value:r.audience,onChange:e=>b({...r,audience:e.target.value}),placeholder:"e.g., Customers, Team members, Potential clients...",required:!0,className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Tone"}),(0,s.jsxs)("select",{value:r.tone,onChange:e=>b({...r,tone:e.target.value}),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-emerald-500/50 transition-all",children:[(0,s.jsx)("option",{value:"professional",children:"Professional"}),(0,s.jsx)("option",{value:"friendly",children:"Friendly"}),(0,s.jsx)("option",{value:"formal",children:"Formal"}),(0,s.jsx)("option",{value:"casual",children:"Casual"}),(0,s.jsx)("option",{value:"persuasive",children:"Persuasive"}),(0,s.jsx)("option",{value:"urgent",children:"Urgent"}),(0,s.jsx)("option",{value:"grateful",children:"Grateful"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Key Points to Include"}),(0,s.jsxs)("div",{className:"space-y-3",children:[r.keyPoints.map((e,t)=>(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("input",{type:"text",value:e,onChange:e=>k(t,e.target.value),placeholder:`Key point ${t+1}...`,className:"flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-emerald-500/50 transition-all"}),r.keyPoints.length>1&&(0,s.jsx)("button",{type:"button",onClick:()=>P(t),className:"p-3 text-red-400 hover:text-red-300 hover:bg-red-500/10 rounded-xl transition-all",children:(0,s.jsx)(h.A,{className:"w-5 h-5"})})]},t)),(0,s.jsxs)("button",{type:"button",onClick:()=>{b({...r,keyPoints:[...r.keyPoints,""]})},className:"w-full py-3 border-2 border-dashed border-white/20 rounded-xl text-gray-400 hover:text-white hover:border-emerald-500/50 transition-all flex items-center justify-center space-x-2",children:[(0,s.jsx)(m.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Add Key Point"})]})]})]}),(0,s.jsx)(n.P.button,{type:"submit",disabled:y||!r.purpose||!r.audience,whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white font-semibold rounded-xl hover:from-emerald-700 hover:to-teal-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2",children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),(0,s.jsx)("span",{children:"Generating Email..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(u.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Generate Email"})]})})]})]})}),(0,s.jsx)(n.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:(0,s.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Generated Email"}),g&&(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(g)},className:"p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors",children:(0,s.jsx)(p.A,{className:"w-4 h-4 text-white"})}),(0,s.jsx)("button",{onClick:()=>{let e=new Blob([g],{type:"text/plain"}),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download=`email-${r.purpose.replace(/\s+/g,"-").toLowerCase()}.txt`,s.click()},className:"p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors",children:(0,s.jsx)(x.A,{className:"w-4 h-4 text-white"})})]})]}),g?(0,s.jsx)("div",{className:"bg-black/40 rounded-xl p-4 border border-white/10 max-h-[600px] overflow-y-auto",children:(0,s.jsx)("pre",{className:"text-gray-300 whitespace-pre-wrap font-mono text-sm leading-relaxed",children:g})}):(0,s.jsxs)("div",{className:"text-center py-12 text-gray-400",children:[(0,s.jsx)(d.A,{className:"w-16 h-16 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"Your generated email will appear here..."})]})]})})]})})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70615:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},74279:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/email/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/email/page.tsx","default")},76183:(e,t,r)=>{Promise.resolve().then(r.bind(r,79025))},76846:(e,t,r)=>{Promise.resolve().then(r.bind(r,61473))},79025:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(60687),a=r(82136);function i({children:e}){return(0,s.jsx)(a.SessionProvider,{children:e})}},84199:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx","default")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>n});var s=r(37413),a=r(7339),i=r.n(a);r(61135);var l=r(84199);let n={title:"Invincible - AI Content Generation Platform",description:"The ultimate content writing SaaS platform powered by advanced AI technology"};function o({children:e}){return(0,s.jsx)("html",{lang:"en",className:"dark",children:(0,s.jsx)("body",{className:`${i().className} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,suppressHydrationWarning:!0,children:(0,s.jsx)(l.default,{children:(0,s.jsx)("div",{className:"min-h-screen",children:e})})})})}},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,5380,4101],()=>r(36648));module.exports=s})();