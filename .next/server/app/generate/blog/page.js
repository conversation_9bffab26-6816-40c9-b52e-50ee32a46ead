(()=>{var e={};e.id=6949,e.ids=[6949],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{T:()=>n,cn:()=>a});var s=r(49384),i=r(82348);function a(...e){return(0,i.QP)((0,s.$)(e))}function n(e,t){if(!e)return t||"";try{return decodeURIComponent(e)}catch(r){return console.warn("URI decode failed, using fallback:",r),t||e}}},7036:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},10022:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15935:(e,t,r)=>{Promise.resolve().then(r.bind(r,84199))},16764:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25970:(e,t,r)=>{Promise.resolve().then(r.bind(r,44909))},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},44909:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var s=r(60687),i=r(43210),a=r(97905),n=r(28559),o=r(10022),l=r(56085),d=r(99270),c=r(45583),h=r(70615),p=r(31158),x=r(85814),u=r.n(x),m=r(82136),b=r(16189),g=r(4780);function v(){let{data:e,status:t}=(0,m.useSession)();(0,b.useRouter)();let[r,x]=(0,i.useState)({topic:"",title:"",wordCount:1e3,tone:"professional",includeResearch:!0,targetKeyword:"",competitors:"",targetAudience:""}),[v,w]=(0,i.useState)(!1),[f,y]=(0,i.useState)(""),[j,k]=(0,i.useState)(!1);if("loading"===t)return(0,s.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-pink-400 border-t-transparent rounded-full mx-auto"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Loading..."})]})});if("unauthenticated"===t)return null;let N=async e=>{e.preventDefault(),w(!0);try{let e=await fetch("/api/generate/blog",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),t=await e.json();t.success?y(t.content):alert("Error: "+t.error)}catch(e){alert("Failed to generate blog post")}finally{w(!1)}};return(0,s.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,s.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-pink-900/20 via-black to-rose-900/20"}),(0,s.jsx)(a.P.div,{animate:{x:[0,100,0],y:[0,-100,0]},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-pink-500/10 rounded-full blur-[100px]"}),(0,s.jsx)(a.P.div,{animate:{x:[0,-100,0],y:[0,100,0]},transition:{duration:15,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-rose-500/10 rounded-full blur-[120px]"})]}),(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsx)("div",{className:"border-b border-white/10 bg-black/60 backdrop-blur-xl",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsx)(u(),{href:"/dashboard",children:(0,s.jsx)(a.P.button,{whileHover:{scale:1.05},className:"p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all",children:(0,s.jsx)(n.A,{className:"w-5 h-5 text-white"})})}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-pink-600 to-rose-600",children:(0,s.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Blog Writer"}),(0,s.jsx)("p",{className:"text-gray-400",children:"SEO-Optimized Content Generation"})]})]})]})})})}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,s.jsx)(a.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"space-y-6",children:(0,s.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,s.jsxs)("h2",{className:"text-xl font-semibold text-white mb-6 flex items-center",children:[(0,s.jsx)(l.A,{className:"w-5 h-5 mr-2 text-pink-400"}),"Content Configuration"]}),(0,s.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Blog Topic *"}),(0,s.jsx)("input",{type:"text",value:r.topic,onChange:e=>x({...r,topic:e.target.value}),placeholder:"Enter your blog topic...",required:!0,className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Title (Optional)"}),(0,s.jsx)("input",{type:"text",value:r.title,onChange:e=>x({...r,title:e.target.value}),placeholder:"Leave blank for AI-generated title",className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Word Count"}),(0,s.jsxs)("select",{value:r.wordCount,onChange:e=>x({...r,wordCount:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-pink-500/50 transition-all",children:[(0,s.jsx)("option",{value:500,children:"500 words"}),(0,s.jsx)("option",{value:1e3,children:"1,000 words"}),(0,s.jsx)("option",{value:1500,children:"1,500 words"}),(0,s.jsx)("option",{value:2e3,children:"2,000 words"}),(0,s.jsx)("option",{value:3e3,children:"3,000 words"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Tone"}),(0,s.jsxs)("select",{value:r.tone,onChange:e=>x({...r,tone:e.target.value}),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-pink-500/50 transition-all",children:[(0,s.jsx)("option",{value:"professional",children:"Professional"}),(0,s.jsx)("option",{value:"casual",children:"Casual"}),(0,s.jsx)("option",{value:"conversational",children:"Conversational"}),(0,s.jsx)("option",{value:"authoritative",children:"Authoritative"}),(0,s.jsx)("option",{value:"friendly",children:"Friendly"}),(0,s.jsx)("option",{value:"academic",children:"Academic"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(d.A,{className:"w-5 h-5 text-pink-400"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"font-medium text-white",children:"Include Research"}),(0,s.jsx)("p",{className:"text-sm text-gray-400",children:"Enhance content with web research"})]})]}),(0,s.jsx)("button",{type:"button",onClick:()=>x({...r,includeResearch:!r.includeResearch}),className:(0,g.cn)("relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none",r.includeResearch?"bg-pink-600":"bg-gray-600"),children:(0,s.jsx)("span",{className:(0,g.cn)("pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out",r.includeResearch?"translate-x-5":"translate-x-0")})})]}),(0,s.jsxs)("button",{type:"button",onClick:()=>k(!j),className:"w-full text-left text-pink-400 hover:text-pink-300 font-medium transition-colors",children:[j?"Hide":"Show"," Advanced Options"]}),j&&(0,s.jsxs)(a.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Target Keyword"}),(0,s.jsx)("input",{type:"text",value:r.targetKeyword,onChange:e=>x({...r,targetKeyword:e.target.value}),placeholder:"Primary SEO keyword",className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Competitors"}),(0,s.jsx)("input",{type:"text",value:r.competitors,onChange:e=>x({...r,competitors:e.target.value}),placeholder:"Competitor URLs or names",className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Target Audience"}),(0,s.jsx)("input",{type:"text",value:r.targetAudience,onChange:e=>x({...r,targetAudience:e.target.value}),placeholder:"Who is this content for?",className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"})]})]}),(0,s.jsx)(a.P.button,{type:"submit",disabled:v||!r.topic,whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full py-4 bg-gradient-to-r from-pink-600 to-rose-600 text-white font-semibold rounded-xl hover:from-pink-700 hover:to-rose-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2",children:v?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),(0,s.jsx)("span",{children:"Generating Blog Post..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"w-5 h-5"}),(0,s.jsx)("span",{children:"Generate Blog Post"})]})})]})]})}),(0,s.jsx)(a.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:(0,s.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Generated Content"}),f&&(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(f)},className:"p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors",children:(0,s.jsx)(h.A,{className:"w-4 h-4 text-white"})}),(0,s.jsx)("button",{onClick:()=>{let e=new Blob([f],{type:"text/markdown"}),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download=`${r.topic.replace(/\s+/g,"-").toLowerCase()}.md`,s.click()},className:"p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors",children:(0,s.jsx)(p.A,{className:"w-4 h-4 text-white"})})]})]}),f?(0,s.jsx)("div",{className:"bg-black/40 rounded-xl p-4 border border-white/10 max-h-[600px] overflow-y-auto",children:(0,s.jsx)("pre",{className:"text-gray-300 whitespace-pre-wrap font-mono text-sm leading-relaxed",children:f})}):(0,s.jsxs)("div",{className:"text-center py-12 text-gray-400",children:[(0,s.jsx)(o.A,{className:"w-16 h-16 mx-auto mb-4 opacity-50"}),(0,s.jsx)("p",{children:"Your generated blog post will appear here..."})]})]})})]})})]})]})}},45583:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},45787:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/blog/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/blog/page.tsx","default")},56085:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70615:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},72682:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let d={children:["",{children:["generate",{children:["blog",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,45787)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/blog/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/generate/blog/page.tsx"],h={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/generate/blog/page",pathname:"/generate/blog",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},76183:(e,t,r)=>{Promise.resolve().then(r.bind(r,79025))},79025:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(60687),i=r(82136);function a({children:e}){return(0,s.jsx)(i.SessionProvider,{children:e})}},84199:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx","default")},86218:(e,t,r)=>{Promise.resolve().then(r.bind(r,45787))},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l,metadata:()=>o});var s=r(37413),i=r(7339),a=r.n(i);r(61135);var n=r(84199);let o={title:"Invincible - AI Content Generation Platform",description:"The ultimate content writing SaaS platform powered by advanced AI technology"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",className:"dark",children:(0,s.jsx)("body",{className:`${a().className} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,suppressHydrationWarning:!0,children:(0,s.jsx)(n.default,{children:(0,s.jsx)("div",{className:"min-h-screen",children:e})})})})}},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(75324).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,5380,4101,7985],()=>r(72682));module.exports=s})();