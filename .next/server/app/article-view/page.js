(()=>{var e={};e.id=8851,e.ids=[8851],e.modules={1708:e=>{"use strict";e.exports=require("node:process")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,s)=>{"use strict";s.d(t,{T:()=>l,cn:()=>a});var r=s(49384),i=s(82348);function a(...e){return(0,i.QP)((0,r.$)(e))}function l(e,t){if(!e)return t||"";try{return decodeURIComponent(e)}catch(s){return console.warn("URI decode failed, using fallback:",s),t||e}}},7036:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},15935:(e,t,s)=>{Promise.resolve().then(s.bind(s,84199))},16764:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46300:(e,t,s)=>{Promise.resolve().then(s.bind(s,46905))},46905:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>z});var r=s(60687),i=s(43210),a=s(82136),l=s(16189),n=s(97905),o=s(11736),d=s(85629),c=s(10022),x=s(28559),m=s(92363),h=s(40228),b=s(48730),p=s(49014),u=s(67760),v=s(13964),f=s(70615),g=s(31158),j=s(11860),N=s(56085),w=s(99891),y=s(86561),k=s(78200),A=s(68184),P=s(99270),S=s(33872),C=s(11437),U=s(13861),_=s(25334),q=s(25541),D=s(85814),G=s.n(D),I=s(4780);function z(){var e,t;let{data:s,status:D}=(0,a.useSession)();(0,l.useRouter)();let[z,L]=(0,i.useState)(""),[E,R]=(0,i.useState)("AI Generated Article"),[$,O]=(0,i.useState)(null),[T,B]=(0,i.useState)(!1),[M,Q]=(0,i.useState)(!0),[H,V]=(0,i.useState)(0),[W,X]=(0,i.useState)(!1),[F,K]=(0,i.useState)(!1),[Y,J]=(0,i.useState)(!1);if("loading"===D)return(0,r.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Loading article..."})]})});if("unauthenticated"===D)return null;if(M)return(0,r.jsxs)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:[(0,r.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10"}),(0,r.jsx)(n.P.div,{animate:{x:[0,100,0],y:[0,-100,0]},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]"})]}),(0,r.jsxs)("div",{className:"relative z-10 text-center",children:[(0,r.jsx)(n.P.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-16 h-16 border-4 border-violet-600 border-t-transparent rounded-full mx-auto mb-4"}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-white mb-2",children:"Loading Article"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Preparing your superior content..."})]})]});if(!z)return(0,r.jsxs)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:[(0,r.jsx)("div",{className:"fixed inset-0 z-0",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10"})}),(0,r.jsxs)("div",{className:"relative z-10 text-center",children:[(0,r.jsx)("div",{className:"p-3 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-2xl mb-6 w-fit mx-auto",children:(0,r.jsx)(c.A,{className:"w-8 h-8 text-white"})}),(0,r.jsx)("h2",{className:"text-xl font-semibold text-white mb-2",children:"No Article Found"}),(0,r.jsx)("p",{className:"text-gray-400 mb-6",children:"No generated article found in your session."}),(0,r.jsxs)(G(),{href:"/dashboard",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-violet-600 to-indigo-600 text-white rounded-xl hover:scale-105 transition-transform",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 mr-2"}),"Go to Dashboard"]})]})]});let Z=z.split(/\s+/).filter(e=>e.length>0).length,ee=Math.ceil(Z/200);return(0,r.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,r.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-violet-900/10 via-black to-indigo-900/10"}),(0,r.jsx)(n.P.div,{animate:{x:[0,100,0],y:[0,-100,0]},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-500/10 rounded-full blur-[100px]"}),(0,r.jsx)(n.P.div,{animate:{x:[0,-100,0],y:[0,100,0]},transition:{duration:15,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-500/10 rounded-full blur-[120px]"})]}),(0,r.jsx)(n.P.div,{className:"fixed top-0 left-0 h-1 bg-gradient-to-r from-violet-600 to-indigo-600 z-50",style:{width:`${H}%`}}),(0,r.jsx)(n.P.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"relative z-10 border-b border-white/10 backdrop-blur-xl bg-black/40",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsxs)(G(),{href:"/dashboard",className:"flex items-center space-x-3 text-gray-400 hover:text-white transition-colors group",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 group-hover:-translate-x-1 transition-transform"}),(0,r.jsx)("span",{className:"font-medium",children:"Back to Dashboard"})]}),(0,r.jsx)("div",{className:"h-6 w-px bg-white/20"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-xl blur-lg opacity-70"}),(0,r.jsx)("div",{className:"relative bg-black rounded-xl p-2.5 border border-white/20",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-white"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-xl font-bold text-white",children:"Superior Article"}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"AI Generated Content"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-6 text-sm text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:new Date().toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(b.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[ee," min read"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[Z.toLocaleString()," words"]})]})]}),(0,r.jsx)("div",{className:"h-6 w-px bg-white/20"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("button",{onClick:()=>X(!W),className:(0,I.cn)("p-2.5 rounded-lg transition-colors",W?"bg-violet-600/20 text-violet-400":"text-gray-400 hover:text-white hover:bg-white/10"),children:(0,r.jsx)(p.A,{className:`w-5 h-5 ${W?"fill-current":""}`})}),(0,r.jsx)("button",{onClick:()=>K(!F),className:(0,I.cn)("p-2.5 rounded-lg transition-colors",F?"bg-red-600/20 text-red-400":"text-gray-400 hover:text-white hover:bg-white/10"),children:(0,r.jsx)(u.A,{className:`w-5 h-5 ${F?"fill-current":""}`})}),(0,r.jsxs)("button",{onClick:()=>{navigator.clipboard.writeText(z),B(!0),setTimeout(()=>B(!1),2e3)},className:"flex items-center space-x-2 px-4 py-2.5 text-sm font-medium text-white bg-white/10 backdrop-blur-sm border border-white/20 rounded-lg hover:bg-white/20 transition-all",children:[T?(0,r.jsx)(v.A,{className:"w-4 h-4"}):(0,r.jsx)(f.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:T?"Copied":"Copy"})]}),(0,r.jsxs)(n.P.button,{onClick:()=>{let e=new Blob([z],{type:"text/markdown"}),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download=`${E.replace(/\s+/g,"-").toLowerCase()}.md`,s.click(),URL.revokeObjectURL(t)},whileHover:{scale:1.05},whileTap:{scale:.95},className:"flex items-center space-x-2 px-4 py-2.5 text-sm font-medium text-white bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg hover:shadow-lg transition-all",children:[(0,r.jsx)(g.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Download"})]})]})]})]})})}),Y&&(0,r.jsx)(n.P.div,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"relative z-10 max-w-6xl mx-auto px-6 pt-6",children:(0,r.jsx)("div",{className:"bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-blue-500/30 rounded-xl p-4 mb-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-blue-600/20 rounded-lg",children:(0,r.jsx)(c.A,{className:"w-5 h-5 text-blue-400"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h4",{className:"text-white font-medium",children:"Large Article Generated"}),(0,r.jsx)("p",{className:"text-sm text-gray-300",children:"This article exceeded normal storage limits due to its comprehensive length. Your full content has been successfully loaded and is ready for viewing."})]}),(0,r.jsx)("button",{onClick:()=>J(!1),className:"text-gray-400 hover:text-white transition-colors",children:(0,r.jsx)(j.A,{className:"w-5 h-5"})})]})})}),(0,r.jsxs)("main",{className:"relative z-10 max-w-6xl mx-auto px-6 py-12",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"text-center mb-16",children:[(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/20 mb-8",children:[(0,r.jsx)(N.A,{className:"w-4 h-4 text-violet-400"}),(0,r.jsx)("span",{className:"text-sm text-gray-200",children:"Invincible V.1 Generated"}),(0,r.jsx)(m.A,{className:"w-4 h-4 text-yellow-400"})]}),(0,r.jsxs)("div",{className:"flex flex-wrap justify-center gap-3 mb-8",children:[(0,r.jsxs)("span",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-violet-600/20 text-violet-300 border border-violet-500/30",children:[(0,r.jsx)(N.A,{className:"w-4 h-4 mr-2"}),"AI Generated"]}),(0,r.jsxs)("span",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-emerald-600/20 text-emerald-300 border border-emerald-500/30",children:[(0,r.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"SEO Optimized"]}),(0,r.jsxs)("span",{className:"inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-amber-600/20 text-amber-300 border border-amber-500/30",children:[(0,r.jsx)(y.A,{className:"w-4 h-4 mr-2"}),"Superior Quality"]})]}),(0,r.jsx)("h1",{className:"text-4xl md:text-6xl font-bold text-white leading-tight mb-6",children:E}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-8 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-full blur-md opacity-70"}),(0,r.jsx)("div",{className:"relative w-12 h-12 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-full flex items-center justify-center",children:(0,r.jsx)(k.A,{className:"w-6 h-6 text-white"})})]}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-white",children:"Invincible V.1"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"AI Content Agent"})]})]}),(0,r.jsx)("div",{className:"h-8 w-px bg-white/20"}),(0,r.jsxs)("div",{className:"flex items-center space-x-6 text-sm text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(b.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[ee," min read"]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(c.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:[Z.toLocaleString()," words"]})]})]})]})]}),$&&(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8 mb-12",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-r from-violet-600 to-indigo-600 rounded-lg",children:(0,r.jsx)(A.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-white",children:"Content Quality Analysis"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-sm text-gray-400",children:"Overall Grade:"}),(0,r.jsx)("span",{className:`text-3xl font-bold bg-gradient-to-r ${(e=$.overallScore)>=85?"from-emerald-500 to-green-500":e>=70?"from-blue-500 to-cyan-500":e>=50?"from-amber-500 to-orange-500":"from-red-500 to-rose-500"} bg-clip-text text-transparent`,children:(t=$.overallScore)>=90?"A+":t>=85?"A":t>=80?"A-":t>=75?"B+":t>=70?"B":t>=65?"B-":t>=60?"C+":t>=55?"C":"D"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8",children:[{label:"SEO Score",score:$.seoScore,icon:P.A,color:"violet"},{label:"AEO Score",score:$.aeoScore,icon:S.A,color:"blue"},{label:"GEO Score",score:$.geoScore,icon:C.A,color:"emerald"},{label:"Readability",score:$.readabilityScore,icon:U.A,color:"amber"},{label:"Uniqueness",score:$.uniquenessScore,icon:N.A,color:"pink"},{label:"External Links",score:$.externalLinkingScore,icon:_.A,color:"cyan"}].map((e,t)=>(0,r.jsxs)(n.P.div,{initial:{scale:0,opacity:0},animate:{scale:1,opacity:1},transition:{delay:.05*t},className:"text-center p-4 rounded-xl bg-white/5 border border-white/10 hover:bg-white/10 transition-all",children:[(0,r.jsxs)("div",{className:"relative mb-4",children:[(0,r.jsxs)("svg",{className:"w-24 h-24 mx-auto transform -rotate-90",children:[(0,r.jsx)("circle",{cx:"48",cy:"48",r:"40",stroke:"currentColor",strokeWidth:"6",fill:"none",className:"text-white/20"}),(0,r.jsx)("circle",{cx:"48",cy:"48",r:"40",stroke:`url(#gradient-${t})`,strokeWidth:"6",fill:"none",strokeDasharray:`${e.score/100*251.33} 251.33`,strokeLinecap:"round",className:"transition-all duration-1000"}),(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:`gradient-${t}`,children:[(0,r.jsx)("stop",{offset:"0%",stopColor:`var(--${e.color}-500)`}),(0,r.jsx)("stop",{offset:"100%",stopColor:`var(--${e.color}-400)`})]})})]}),(0,r.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-2xl font-bold text-white",children:Math.round(e.score)}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"/ 100"})]})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:(0,I.cn)("inline-flex p-2 rounded-lg","violet"===e.color&&"bg-violet-600/20","blue"===e.color&&"bg-blue-600/20","emerald"===e.color&&"bg-emerald-600/20","amber"===e.color&&"bg-amber-600/20","pink"===e.color&&"bg-pink-600/20","cyan"===e.color&&"bg-cyan-600/20"),children:(0,r.jsx)(e.icon,{className:(0,I.cn)("w-4 h-4","violet"===e.color&&"text-violet-400","blue"===e.color&&"text-blue-400","emerald"===e.color&&"text-emerald-400","amber"===e.color&&"text-amber-400","pink"===e.color&&"text-pink-400","cyan"===e.color&&"text-cyan-400")})}),(0,r.jsx)("p",{className:"text-sm font-medium text-white",children:e.label})]})]},e.label))})]}),(0,r.jsx)(n.P.article,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.3},className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl overflow-hidden",children:(0,r.jsx)("div",{className:"prose prose-lg prose-invert max-w-none p-8 md:p-12",children:(0,r.jsx)(o.oz,{remarkPlugins:[d.A],components:{h1:({children:e})=>(0,r.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-white mb-6 mt-8 first:mt-0",children:e}),h2:({children:e})=>(0,r.jsx)("h2",{className:"text-2xl md:text-3xl font-bold text-white mb-4 mt-10 pb-3 border-b border-white/20",children:e}),h3:({children:e})=>(0,r.jsx)("h3",{className:"text-xl md:text-2xl font-semibold text-gray-200 mb-3 mt-8",children:e}),p:({children:e})=>(0,r.jsx)("p",{className:"text-gray-300 leading-relaxed mb-6 text-base md:text-lg",children:e}),ul:({children:e})=>(0,r.jsx)("ul",{className:"space-y-3 mb-6 ml-0 list-none",children:e}),ol:({children:e})=>(0,r.jsx)("ol",{className:"space-y-3 mb-6 ml-0 list-none",children:e}),li:({children:e})=>(0,r.jsx)("li",{className:"text-gray-300 leading-relaxed pl-8 relative before:content-[''] before:absolute before:left-0 before:top-[0.6rem] before:w-1.5 before:h-1.5 before:bg-violet-500 before:rounded-full",children:e}),strong:({children:e})=>(0,r.jsx)("strong",{className:"font-semibold text-white",children:e}),em:({children:e})=>(0,r.jsx)("em",{className:"italic text-gray-300",children:e}),blockquote:({children:e})=>(0,r.jsx)("blockquote",{className:"border-l-4 border-violet-500 pl-6 my-8 text-gray-300 italic bg-white/5 py-4 rounded-r-lg",children:e}),code:({children:e})=>(0,r.jsx)("code",{className:"bg-slate-100 text-slate-800 px-2 py-1 rounded text-sm font-mono",children:e}),pre:({children:e})=>(0,r.jsx)("pre",{className:"bg-slate-900 text-slate-100 p-4 rounded-lg overflow-x-auto my-6",children:e}),table:({children:e})=>(0,r.jsx)("div",{className:"overflow-x-auto my-8 rounded-2xl shadow-2xl border border-white/20 bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm",children:(0,r.jsx)("table",{className:"min-w-full border-collapse",children:e})}),thead:({children:e})=>(0,r.jsx)("thead",{className:"bg-gradient-to-r from-blue-600/20 to-purple-600/20 border-b border-white/20",children:e}),tbody:({children:e})=>(0,r.jsx)("tbody",{className:"divide-y divide-white/10",children:e}),tr:({children:e})=>(0,r.jsx)("tr",{className:"hover:bg-white/5 transition-all duration-200",children:e}),th:({children:e})=>(0,r.jsxs)("th",{className:"px-6 py-4 text-left font-semibold text-white border-r border-white/10 last:border-r-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 relative",children:[(0,r.jsx)("div",{className:"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500"}),e]}),td:({children:e})=>(0,r.jsx)("td",{className:"px-6 py-4 text-gray-200 border-r border-white/10 last:border-r-0 font-medium",children:e}),a:({href:e,children:t})=>(0,r.jsxs)("a",{href:e,target:"_blank",rel:"noopener noreferrer",className:"text-indigo-600 hover:text-indigo-700 font-medium underline decoration-1 underline-offset-2 inline-flex items-center gap-1 transition-colors",children:[t,(0,r.jsx)(_.A,{className:"w-3.5 h-3.5"})]})},children:z})})}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},className:"mt-16 text-center",children:[(0,r.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-8 mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-3 mb-4",children:[(0,r.jsx)("div",{className:"p-2 bg-gradient-to-r from-emerald-600 to-teal-600 rounded-lg",children:(0,r.jsx)(q.A,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("span",{className:"text-lg font-semibold text-white",children:"Superior Content Quality"})]}),(0,r.jsx)("p",{className:"text-gray-300 mb-6",children:"This article was generated using Invincible V.1's advanced RAG system, competitive analysis, and human writing patterns for maximum impact."}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"text-center p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsx)(w.A,{className:"w-8 h-8 text-emerald-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm font-medium text-white",children:"SEO Optimized"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Built for search visibility"})]}),(0,r.jsxs)("div",{className:"text-center p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsx)(k.A,{className:"w-8 h-8 text-violet-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm font-medium text-white",children:"AI-Powered"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Advanced reasoning model"})]}),(0,r.jsxs)("div",{className:"text-center p-4 rounded-xl bg-white/5 border border-white/10",children:[(0,r.jsx)(m.A,{className:"w-8 h-8 text-yellow-400 mx-auto mb-2"}),(0,r.jsx)("p",{className:"text-sm font-medium text-white",children:"Superior Quality"}),(0,r.jsx)("p",{className:"text-xs text-gray-400",children:"Beats all competition"})]})]})]}),(0,r.jsx)(G(),{href:"/dashboard",children:(0,r.jsxs)(n.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"inline-flex items-center space-x-3 px-8 py-4 bg-gradient-to-r from-violet-600 to-indigo-600 text-white font-semibold rounded-xl hover:shadow-2xl transition-all",children:[(0,r.jsx)(x.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Back to Dashboard"}),(0,r.jsx)(N.A,{className:"w-5 h-5"})]})})]})]})]})}},55935:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/page.tsx","default")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73136:e=>{"use strict";e.exports=require("node:url")},76183:(e,t,s)=>{Promise.resolve().then(s.bind(s,79025))},76760:e=>{"use strict";e.exports=require("node:path")},78652:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),i=s(48088),a=s(88170),l=s.n(a),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["article-view",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,55935)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/article-view/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/article-view/page",pathname:"/article-view",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79025:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(60687),i=s(82136);function a({children:e}){return(0,r.jsx)(i.SessionProvider,{children:e})}},84199:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx","default")},88156:(e,t,s)=>{Promise.resolve().then(s.bind(s,55935))},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>n});var r=s(37413),i=s(7339),a=s.n(i);s(61135);var l=s(84199);let n={title:"Invincible - AI Content Generation Platform",description:"The ultimate content writing SaaS platform powered by advanced AI technology"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",className:"dark",children:(0,r.jsx)("body",{className:`${a().className} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,suppressHydrationWarning:!0,children:(0,r.jsx)(l.default,{children:(0,r.jsx)("div",{className:"min-h-screen",children:e})})})})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,5380,4101,7985,4244],()=>s(78652));module.exports=r})();