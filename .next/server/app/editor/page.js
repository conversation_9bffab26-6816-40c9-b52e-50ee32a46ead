(()=>{var e={};e.id=6766,e.ids=[6766],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(75324).A)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(75324).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},13861:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(75324).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},13964:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(75324).A)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},16189:(e,t,n)=>{"use strict";var r=n(65773);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}}),n.o(r,"useSearchParams")&&n.d(t,{useSearchParams:function(){return r.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25334:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(75324).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},25366:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(75324).A)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30619:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>M});var r=n(60687),i=n(43210),o=n(82136),s=n(16189),l=n(97905),a=n(88920),c=n(37897),d=n(74606),h=n(56085),p=n(78200),u=n(12597),f=n(13861),m=n(75324);let g=(0,m.A)("Minimize",[["path",{d:"M8 3v3a2 2 0 0 1-2 2H3",key:"hohbtr"}],["path",{d:"M21 8h-3a2 2 0 0 1-2-2V3",key:"5jw1f3"}],["path",{d:"M3 16h3a2 2 0 0 1 2 2v3",key:"198tvr"}],["path",{d:"M16 21v-3a2 2 0 0 1 2-2h3",key:"ph8mxp"}]]),y=(0,m.A)("Maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);var b=n(10022),w=n(8819),v=n(31158),x=n(84027),k=n(85814),S=n.n(k),C=n(75474);function M(){let{data:e,status:t}=(0,o.useSession)();(0,s.useRouter)();let[n,m]=(0,i.useState)(""),[k,M]=(0,i.useState)("Untitled Document"),[N,A]=(0,i.useState)(!1),[O,T]=(0,i.useState)(!1),[E,D]=(0,i.useState)(!1),[R,I]=(0,i.useState)([]),[P,L]=(0,i.useState)(!1),[j,z]=(0,i.useState)(0),[$,B]=(0,i.useState)(0);if("loading"===t)return(0,r.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Loading editor..."})]})});if("unauthenticated"===t)return null;let H=async()=>{D(!0);let e={id:Date.now().toString(),title:k,content:n,createdAt:new Date,updatedAt:new Date};await new Promise(e=>setTimeout(e,1e3)),I(t=>[e,...t]),D(!1)},V=e=>{M(e.title),m(e.content),L(!1)},F=e=>{window.open(`/generate/${e}`,"_blank")};return(0,r.jsxs)("div",{className:`min-h-screen transition-all duration-300 ${O?"fixed inset-0 z-50":""}`,children:[!O&&(0,r.jsx)("div",{className:"border-b border-white/10 bg-black/20 backdrop-blur-sm",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(S(),{href:"/",children:(0,r.jsxs)(l.P.button,{whileHover:{scale:1.05},className:"flex items-center space-x-2 text-white hover:text-blue-400 transition-colors",children:[(0,r.jsx)(c.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"font-semibold",children:"Invincible"})]})}),(0,r.jsx)("div",{className:"text-gray-400",children:"|"}),(0,r.jsx)("input",{type:"text",value:k,onChange:e=>M(e.target.value),className:"bg-transparent text-white font-medium text-lg focus:outline-none focus:text-blue-400 transition-colors",placeholder:"Document title..."}),E&&(0,r.jsxs)("div",{className:"flex items-center text-green-400 text-sm",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"}),"Saving..."]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(l.P.button,{whileHover:{scale:1.05},onClick:()=>F("blog"),className:"flex items-center space-x-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors",title:"Open Blog Generator",children:(0,r.jsx)(d.A,{className:"w-4 h-4"})}),(0,r.jsx)(l.P.button,{whileHover:{scale:1.05},onClick:()=>F("email"),className:"flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",title:"Open Email Generator",children:(0,r.jsx)(h.A,{className:"w-4 h-4"})}),(0,r.jsx)(l.P.button,{whileHover:{scale:1.05},onClick:()=>F("youtube"),className:"flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors",title:"Open Video Script Generator",children:(0,r.jsx)(p.A,{className:"w-4 h-4"})}),(0,r.jsx)("div",{className:"w-px h-6 bg-white/20"}),(0,r.jsx)(l.P.button,{whileHover:{scale:1.05},onClick:()=>A(!N),className:`p-2 rounded-lg transition-colors ${N?"bg-blue-600 text-white":"text-gray-300 hover:text-white hover:bg-white/10"}`,title:N?"Exit Preview":"Preview",children:N?(0,r.jsx)(u.A,{className:"w-4 h-4"}):(0,r.jsx)(f.A,{className:"w-4 h-4"})}),(0,r.jsx)(l.P.button,{whileHover:{scale:1.05},onClick:()=>T(!O),className:"p-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors",title:O?"Exit Fullscreen":"Fullscreen",children:O?(0,r.jsx)(g,{className:"w-4 h-4"}):(0,r.jsx)(y,{className:"w-4 h-4"})}),(0,r.jsx)("div",{className:"w-px h-6 bg-white/20"}),(0,r.jsxs)(l.P.button,{whileHover:{scale:1.05},onClick:()=>L(!P),className:"flex items-center space-x-2 px-3 py-2 glass-card text-white hover:bg-white/20 rounded-lg transition-colors",children:[(0,r.jsx)(b.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Open"})]}),(0,r.jsxs)(l.P.button,{whileHover:{scale:1.05},onClick:H,disabled:E,className:"flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50",children:[(0,r.jsx)(w.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Save"})]}),(0,r.jsx)(l.P.button,{whileHover:{scale:1.05},onClick:()=>{let e=new Blob([n],{type:"text/html"}),t=URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download=`${k}.html`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(t)},className:"p-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors",title:"Export",children:(0,r.jsx)(v.A,{className:"w-4 h-4"})}),(0,r.jsx)(S(),{href:"/dashboard",children:(0,r.jsx)(l.P.button,{whileHover:{scale:1.05},className:"p-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors",title:"Dashboard",children:(0,r.jsx)(x.A,{className:"w-4 h-4"})})})]})]})})}),(0,r.jsxs)("div",{className:"flex h-full",children:[(0,r.jsx)(a.N,{children:P&&!O&&(0,r.jsxs)(l.P.div,{initial:{x:-300,opacity:0},animate:{x:0,opacity:1},exit:{x:-300,opacity:0},className:"w-80 bg-black/30 backdrop-blur-sm border-r border-white/10 p-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Documents"}),0===R.length?(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"No saved documents yet"}):(0,r.jsx)("div",{className:"space-y-2",children:R.map(e=>(0,r.jsxs)(l.P.div,{whileHover:{scale:1.02},onClick:()=>V(e),className:"glass-card p-3 cursor-pointer hover:bg-white/20 transition-colors",children:[(0,r.jsx)("h4",{className:"text-white font-medium truncate",children:e.title}),(0,r.jsx)("p",{className:"text-gray-400 text-xs",children:e.createdAt.toLocaleDateString()})]},e.id))})]})}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,r.jsx)("div",{className:`flex-1 p-8 ${O?"h-screen":""}`,children:N?(0,r.jsxs)("div",{className:"glass-card p-8 max-w-4xl mx-auto",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:k}),(0,r.jsx)("div",{className:"prose prose-invert max-w-none",dangerouslySetInnerHTML:{__html:n}})]}):(0,r.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,r.jsx)(C.A,{content:n,onChange:m,placeholder:"Start writing your masterpiece...",className:"min-h-[600px]"})})}),!O&&(0,r.jsx)("div",{className:"border-t border-white/10 bg-black/20 backdrop-blur-sm px-8 py-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsxs)("span",{children:[j," words"]}),(0,r.jsxs)("span",{children:[$," characters"]}),(0,r.jsxs)("span",{children:["Last saved: ",E?"Saving...":"Just now"]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-4",children:(0,r.jsx)("span",{children:"Ready"})})]})})]})]})]})}},31158:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(75324).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},37773:(e,t,n)=>{Promise.resolve().then(n.bind(n,30619))},37897:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(75324).A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},39981:(e,t,n)=>{Promise.resolve().then(n.bind(n,60714))},60714:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});let r=(0,n(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/editor/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/editor/page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75474:(e,t,n)=>{"use strict";n.d(t,{A:()=>cQ});var r,i,o,s,l,a=n(60687),c=n(43210),d=n(51215);function h(e){this.content=e}h.prototype={constructor:h,find:function(e){for(var t=0;t<this.content.length;t+=2)if(this.content[t]===e)return t;return -1},get:function(e){var t=this.find(e);return -1==t?void 0:this.content[t+1]},update:function(e,t,n){var r=n&&n!=e?this.remove(n):this,i=r.find(e),o=r.content.slice();return -1==i?o.push(n||e,t):(o[i+1]=t,n&&(o[i]=n)),new h(o)},remove:function(e){var t=this.find(e);if(-1==t)return this;var n=this.content.slice();return n.splice(t,2),new h(n)},addToStart:function(e,t){return new h([e,t].concat(this.remove(e).content))},addToEnd:function(e,t){var n=this.remove(e).content.slice();return n.push(e,t),new h(n)},addBefore:function(e,t,n){var r=this.remove(t),i=r.content.slice(),o=r.find(e);return i.splice(-1==o?i.length:o,0,t,n),new h(i)},forEach:function(e){for(var t=0;t<this.content.length;t+=2)e(this.content[t],this.content[t+1])},prepend:function(e){return(e=h.from(e)).size?new h(e.content.concat(this.subtract(e).content)):this},append:function(e){return(e=h.from(e)).size?new h(this.subtract(e).content.concat(e.content)):this},subtract:function(e){var t=this;e=h.from(e);for(var n=0;n<e.content.length;n+=2)t=t.remove(e.content[n]);return t},toObject:function(){var e={};return this.forEach(function(t,n){e[t]=n}),e},get size(){return this.content.length>>1}},h.from=function(e){if(e instanceof h)return e;var t=[];if(e)for(var n in e)t.push(n,e[n]);return new h(t)};class p{constructor(e,t){if(this.content=e,this.size=t||0,null==t)for(let t=0;t<e.length;t++)this.size+=e[t].nodeSize}nodesBetween(e,t,n,r=0,i){for(let o=0,s=0;s<t;o++){let l=this.content[o],a=s+l.nodeSize;if(a>e&&!1!==n(l,r+s,i||null,o)&&l.content.size){let i=s+1;l.nodesBetween(Math.max(0,e-i),Math.min(l.content.size,t-i),n,r+i)}s=a}}descendants(e){this.nodesBetween(0,this.size,e)}textBetween(e,t,n,r){let i="",o=!0;return this.nodesBetween(e,t,(s,l)=>{let a=s.isText?s.text.slice(Math.max(e,l)-l,t-l):s.isLeaf?r?"function"==typeof r?r(s):r:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&a||s.isTextblock)&&n&&(o?o=!1:i+=n),i+=a},0),i}append(e){if(!e.size)return this;if(!this.size)return e;let t=this.lastChild,n=e.firstChild,r=this.content.slice(),i=0;for(t.isText&&t.sameMarkup(n)&&(r[r.length-1]=t.withText(t.text+n.text),i=1);i<e.content.length;i++)r.push(e.content[i]);return new p(r,this.size+e.size)}cut(e,t=this.size){if(0==e&&t==this.size)return this;let n=[],r=0;if(t>e)for(let i=0,o=0;o<t;i++){let s=this.content[i],l=o+s.nodeSize;l>e&&((o<e||l>t)&&(s=s.isText?s.cut(Math.max(0,e-o),Math.min(s.text.length,t-o)):s.cut(Math.max(0,e-o-1),Math.min(s.content.size,t-o-1))),n.push(s),r+=s.nodeSize),o=l}return new p(n,r)}cutByIndex(e,t){return e==t?p.empty:0==e&&t==this.content.length?this:new p(this.content.slice(e,t))}replaceChild(e,t){let n=this.content[e];if(n==t)return this;let r=this.content.slice(),i=this.size+t.nodeSize-n.nodeSize;return r[e]=t,new p(r,i)}addToStart(e){return new p([e].concat(this.content),this.size+e.nodeSize)}addToEnd(e){return new p(this.content.concat(e),this.size+e.nodeSize)}eq(e){if(this.content.length!=e.content.length)return!1;for(let t=0;t<this.content.length;t++)if(!this.content[t].eq(e.content[t]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(e){let t=this.content[e];if(!t)throw RangeError("Index "+e+" out of range for "+this);return t}maybeChild(e){return this.content[e]||null}forEach(e){for(let t=0,n=0;t<this.content.length;t++){let r=this.content[t];e(r,n,t),n+=r.nodeSize}}findDiffStart(e,t=0){return function e(t,n,r){for(let i=0;;i++){if(i==t.childCount||i==n.childCount)return t.childCount==n.childCount?null:r;let o=t.child(i),s=n.child(i);if(o==s){r+=o.nodeSize;continue}if(!o.sameMarkup(s))return r;if(o.isText&&o.text!=s.text){for(let e=0;o.text[e]==s.text[e];e++)r++;return r}if(o.content.size||s.content.size){let t=e(o.content,s.content,r+1);if(null!=t)return t}r+=o.nodeSize}}(this,e,t)}findDiffEnd(e,t=this.size,n=e.size){return function e(t,n,r,i){for(let o=t.childCount,s=n.childCount;;){if(0==o||0==s)return o==s?null:{a:r,b:i};let l=t.child(--o),a=n.child(--s),c=l.nodeSize;if(l==a){r-=c,i-=c;continue}if(!l.sameMarkup(a))return{a:r,b:i};if(l.isText&&l.text!=a.text){let e=0,t=Math.min(l.text.length,a.text.length);for(;e<t&&l.text[l.text.length-e-1]==a.text[a.text.length-e-1];)e++,r--,i--;return{a:r,b:i}}if(l.content.size||a.content.size){let t=e(l.content,a.content,r-1,i-1);if(t)return t}r-=c,i-=c}}(this,e,t,n)}findIndex(e,t=-1){if(0==e)return f(0,e);if(e==this.size)return f(this.content.length,e);if(e>this.size||e<0)throw RangeError(`Position ${e} outside of fragment (${this})`);for(let n=0,r=0;;n++){let i=r+this.child(n).nodeSize;if(i>=e){if(i==e||t>0)return f(n+1,i);return f(n,r)}r=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(e=>e.toJSON()):null}static fromJSON(e,t){if(!t)return p.empty;if(!Array.isArray(t))throw RangeError("Invalid input for Fragment.fromJSON");return new p(t.map(e.nodeFromJSON))}static fromArray(e){if(!e.length)return p.empty;let t,n=0;for(let r=0;r<e.length;r++){let i=e[r];n+=i.nodeSize,r&&i.isText&&e[r-1].sameMarkup(i)?(t||(t=e.slice(0,r)),t[t.length-1]=i.withText(t[t.length-1].text+i.text)):t&&t.push(i)}return new p(t||e,n)}static from(e){if(!e)return p.empty;if(e instanceof p)return e;if(Array.isArray(e))return this.fromArray(e);if(e.attrs)return new p([e],e.nodeSize);throw RangeError("Can not convert "+e+" to a Fragment"+(e.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}p.empty=new p([],0);let u={index:0,offset:0};function f(e,t){return u.index=e,u.offset=t,u}function m(e,t){if(e===t)return!0;if(!(e&&"object"==typeof e)||!(t&&"object"==typeof t))return!1;let n=Array.isArray(e);if(Array.isArray(t)!=n)return!1;if(n){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!m(e[n],t[n]))return!1}else{for(let n in e)if(!(n in t)||!m(e[n],t[n]))return!1;for(let n in t)if(!(n in e))return!1}return!0}class g{constructor(e,t){this.type=e,this.attrs=t}addToSet(e){let t,n=!1;for(let r=0;r<e.length;r++){let i=e[r];if(this.eq(i))return e;if(this.type.excludes(i.type))t||(t=e.slice(0,r));else{if(i.type.excludes(this.type))return e;!n&&i.type.rank>this.type.rank&&(t||(t=e.slice(0,r)),t.push(this),n=!0),t&&t.push(i)}}return t||(t=e.slice()),n||t.push(this),t}removeFromSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return e.slice(0,t).concat(e.slice(t+1));return e}isInSet(e){for(let t=0;t<e.length;t++)if(this.eq(e[t]))return!0;return!1}eq(e){return this==e||this.type==e.type&&m(this.attrs,e.attrs)}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return e}static fromJSON(e,t){if(!t)throw RangeError("Invalid input for Mark.fromJSON");let n=e.marks[t.type];if(!n)throw RangeError(`There is no mark type ${t.type} in this schema`);let r=n.create(t.attrs);return n.checkAttrs(r.attrs),r}static sameSet(e,t){if(e==t)return!0;if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].eq(t[n]))return!1;return!0}static setFrom(e){if(!e||Array.isArray(e)&&0==e.length)return g.none;if(e instanceof g)return[e];let t=e.slice();return t.sort((e,t)=>e.type.rank-t.type.rank),t}}g.none=[];class y extends Error{}class b{constructor(e,t,n){this.content=e,this.openStart=t,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(e,t){let n=function e(t,n,r,i){let{index:o,offset:s}=t.findIndex(n),l=t.maybeChild(o);if(s==n||l.isText)return i&&!i.canReplace(o,o,r)?null:t.cut(0,n).append(r).append(t.cut(n));let a=e(l.content,n-s-1,r);return a&&t.replaceChild(o,l.copy(a))}(this.content,e+this.openStart,t);return n&&new b(n,this.openStart,this.openEnd)}removeBetween(e,t){return new b(function e(t,n,r){let{index:i,offset:o}=t.findIndex(n),s=t.maybeChild(i),{index:l,offset:a}=t.findIndex(r);if(o==n||s.isText){if(a!=r&&!t.child(l).isText)throw RangeError("Removing non-flat range");return t.cut(0,n).append(t.cut(r))}if(i!=l)throw RangeError("Removing non-flat range");return t.replaceChild(i,s.copy(e(s.content,n-o-1,r-o-1)))}(this.content,e+this.openStart,t+this.openStart),this.openStart,this.openEnd)}eq(e){return this.content.eq(e.content)&&this.openStart==e.openStart&&this.openEnd==e.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let e={content:this.content.toJSON()};return this.openStart>0&&(e.openStart=this.openStart),this.openEnd>0&&(e.openEnd=this.openEnd),e}static fromJSON(e,t){if(!t)return b.empty;let n=t.openStart||0,r=t.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw RangeError("Invalid input for Slice.fromJSON");return new b(p.fromJSON(e,t.content),n,r)}static maxOpen(e,t=!0){let n=0,r=0;for(let r=e.firstChild;r&&!r.isLeaf&&(t||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=e.lastChild;n&&!n.isLeaf&&(t||!n.type.spec.isolating);n=n.lastChild)r++;return new b(e,n,r)}}function w(e,t){if(!t.type.compatibleContent(e.type))throw new y("Cannot join "+t.type.name+" onto "+e.type.name)}function v(e,t,n){let r=e.node(n);return w(r,t.node(n)),r}function x(e,t){let n=t.length-1;n>=0&&e.isText&&e.sameMarkup(t[n])?t[n]=e.withText(t[n].text+e.text):t.push(e)}function k(e,t,n,r){let i=(t||e).node(n),o=0,s=t?t.index(n):i.childCount;e&&(o=e.index(n),e.depth>n?o++:e.textOffset&&(x(e.nodeAfter,r),o++));for(let e=o;e<s;e++)x(i.child(e),r);t&&t.depth==n&&t.textOffset&&x(t.nodeBefore,r)}function S(e,t){return e.type.checkContent(t),e.copy(t)}function C(e,t,n){let r=[];return k(null,e,n,r),e.depth>n&&x(S(v(e,t,n+1),C(e,t,n+1)),r),k(t,null,n,r),new p(r)}b.empty=new b(p.empty,0,0);class M{constructor(e,t,n){this.pos=e,this.path=t,this.parentOffset=n,this.depth=t.length/3-1}resolveDepth(e){return null==e?this.depth:e<0?this.depth+e:e}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(e){return this.path[3*this.resolveDepth(e)]}index(e){return this.path[3*this.resolveDepth(e)+1]}indexAfter(e){return e=this.resolveDepth(e),this.index(e)+(e!=this.depth||this.textOffset?1:0)}start(e){return 0==(e=this.resolveDepth(e))?0:this.path[3*e-1]+1}end(e){return e=this.resolveDepth(e),this.start(e)+this.node(e).content.size}before(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position before the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]}after(e){if(!(e=this.resolveDepth(e)))throw RangeError("There is no position after the top-level node");return e==this.depth+1?this.pos:this.path[3*e-1]+this.path[3*e].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let e=this.parent,t=this.index(this.depth);if(t==e.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=e.child(t);return n?e.child(t).cut(n):r}get nodeBefore(){let e=this.index(this.depth),t=this.pos-this.path[this.path.length-1];return t?this.parent.child(e).cut(0,t):0==e?null:this.parent.child(e-1)}posAtIndex(e,t){t=this.resolveDepth(t);let n=this.path[3*t],r=0==t?0:this.path[3*t-1]+1;for(let t=0;t<e;t++)r+=n.child(t).nodeSize;return r}marks(){let e=this.parent,t=this.index();if(0==e.content.size)return g.none;if(this.textOffset)return e.child(t).marks;let n=e.maybeChild(t-1),r=e.maybeChild(t);if(!n){let e=n;n=r,r=e}let i=n.marks;for(var o=0;o<i.length;o++)!1!==i[o].type.spec.inclusive||r&&i[o].isInSet(r.marks)||(i=i[o--].removeFromSet(i));return i}marksAcross(e){let t=this.parent.maybeChild(this.index());if(!t||!t.isInline)return null;let n=t.marks,r=e.parent.maybeChild(e.index());for(var i=0;i<n.length;i++)!1!==n[i].type.spec.inclusive||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(e){for(let t=this.depth;t>0;t--)if(this.start(t)<=e&&this.end(t)>=e)return t;return 0}blockRange(e=this,t){if(e.pos<this.pos)return e.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==e.pos?1:0);n>=0;n--)if(e.pos<=this.end(n)&&(!t||t(this.node(n))))return new T(this,e,n);return null}sameParent(e){return this.pos-this.parentOffset==e.pos-e.parentOffset}max(e){return e.pos>this.pos?e:this}min(e){return e.pos<this.pos?e:this}toString(){let e="";for(let t=1;t<=this.depth;t++)e+=(e?"/":"")+this.node(t).type.name+"_"+this.index(t-1);return e+":"+this.parentOffset}static resolve(e,t){if(!(t>=0&&t<=e.content.size))throw RangeError("Position "+t+" out of range");let n=[],r=0,i=t;for(let t=e;;){let{index:e,offset:o}=t.content.findIndex(i),s=i-o;if(n.push(t,e,r+o),!s||(t=t.child(e)).isText)break;i=s-1,r+=o+1}return new M(t,n,i)}static resolveCached(e,t){let n=O.get(e);if(n)for(let e=0;e<n.elts.length;e++){let r=n.elts[e];if(r.pos==t)return r}else O.set(e,n=new N);let r=n.elts[n.i]=M.resolve(e,t);return n.i=(n.i+1)%A,r}}class N{constructor(){this.elts=[],this.i=0}}let A=12,O=new WeakMap;class T{constructor(e,t,n){this.$from=e,this.$to=t,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let E=Object.create(null);class D{constructor(e,t,n,r=g.none){this.type=e,this.attrs=t,this.marks=r,this.content=n||p.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(e){return this.content.child(e)}maybeChild(e){return this.content.maybeChild(e)}forEach(e){this.content.forEach(e)}nodesBetween(e,t,n,r=0){this.content.nodesBetween(e,t,n,r,this)}descendants(e){this.nodesBetween(0,this.content.size,e)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(e,t,n,r){return this.content.textBetween(e,t,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(e){return this==e||this.sameMarkup(e)&&this.content.eq(e.content)}sameMarkup(e){return this.hasMarkup(e.type,e.attrs,e.marks)}hasMarkup(e,t,n){return this.type==e&&m(this.attrs,t||e.defaultAttrs||E)&&g.sameSet(this.marks,n||g.none)}copy(e=null){return e==this.content?this:new D(this.type,this.attrs,e,this.marks)}mark(e){return e==this.marks?this:new D(this.type,this.attrs,this.content,e)}cut(e,t=this.content.size){return 0==e&&t==this.content.size?this:this.copy(this.content.cut(e,t))}slice(e,t=this.content.size,n=!1){if(e==t)return b.empty;let r=this.resolve(e),i=this.resolve(t),o=n?0:r.sharedDepth(t),s=r.start(o);return new b(r.node(o).content.cut(r.pos-s,i.pos-s),r.depth-o,i.depth-o)}replace(e,t,n){var r=this.resolve(e),i=this.resolve(t);if(n.openStart>r.depth)throw new y("Inserted content deeper than insertion position");if(r.depth-n.openStart!=i.depth-n.openEnd)throw new y("Inconsistent open depths");return function e(t,n,r,i){let o=t.index(i),s=t.node(i);if(o==n.index(i)&&i<t.depth-r.openStart){let l=e(t,n,r,i+1);return s.copy(s.content.replaceChild(o,l))}if(!r.content.size)return S(s,C(t,n,i));if(r.openStart||r.openEnd||t.depth!=i||n.depth!=i){let{start:e,end:o}=function(e,t){let n=t.depth-e.openStart,r=t.node(n).copy(e.content);for(let e=n-1;e>=0;e--)r=t.node(e).copy(p.from(r));return{start:r.resolveNoCache(e.openStart+n),end:r.resolveNoCache(r.content.size-e.openEnd-n)}}(r,t);return S(s,function e(t,n,r,i,o){let s=t.depth>o&&v(t,n,o+1),l=i.depth>o&&v(r,i,o+1),a=[];return k(null,t,o,a),s&&l&&n.index(o)==r.index(o)?(w(s,l),x(S(s,e(t,n,r,i,o+1)),a)):(s&&x(S(s,C(t,n,o+1)),a),k(n,r,o,a),l&&x(S(l,C(r,i,o+1)),a)),k(i,null,o,a),new p(a)}(t,e,o,n,i))}{let e=t.parent,i=e.content;return S(e,i.cut(0,t.parentOffset).append(r.content).append(i.cut(n.parentOffset)))}}(r,i,n,0)}nodeAt(e){for(let t=this;;){let{index:n,offset:r}=t.content.findIndex(e);if(!(t=t.maybeChild(n)))return null;if(r==e||t.isText)return t;e-=r+1}}childAfter(e){let{index:t,offset:n}=this.content.findIndex(e);return{node:this.content.maybeChild(t),index:t,offset:n}}childBefore(e){if(0==e)return{node:null,index:0,offset:0};let{index:t,offset:n}=this.content.findIndex(e);if(n<e)return{node:this.content.child(t),index:t,offset:n};let r=this.content.child(t-1);return{node:r,index:t-1,offset:n-r.nodeSize}}resolve(e){return M.resolveCached(this,e)}resolveNoCache(e){return M.resolve(this,e)}rangeHasMark(e,t,n){let r=!1;return t>e&&this.nodesBetween(e,t,e=>(n.isInSet(e.marks)&&(r=!0),!r)),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let e=this.type.name;return this.content.size&&(e+="("+this.content.toStringInner()+")"),I(this.marks,e)}contentMatchAt(e){let t=this.type.contentMatch.matchFragment(this.content,0,e);if(!t)throw Error("Called contentMatchAt on a node with invalid content");return t}canReplace(e,t,n=p.empty,r=0,i=n.childCount){let o=this.contentMatchAt(e).matchFragment(n,r,i),s=o&&o.matchFragment(this.content,t);if(!s||!s.validEnd)return!1;for(let e=r;e<i;e++)if(!this.type.allowsMarks(n.child(e).marks))return!1;return!0}canReplaceWith(e,t,n,r){if(r&&!this.type.allowsMarks(r))return!1;let i=this.contentMatchAt(e).matchType(n),o=i&&i.matchFragment(this.content,t);return!!o&&o.validEnd}canAppend(e){return e.content.size?this.canReplace(this.childCount,this.childCount,e.content):this.type.compatibleContent(e.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let e=g.none;for(let t=0;t<this.marks.length;t++){let n=this.marks[t];n.type.checkAttrs(n.attrs),e=n.addToSet(e)}if(!g.sameSet(e,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(e=>e.type.name)}`);this.content.forEach(e=>e.check())}toJSON(){let e={type:this.type.name};for(let t in this.attrs){e.attrs=this.attrs;break}return this.content.size&&(e.content=this.content.toJSON()),this.marks.length&&(e.marks=this.marks.map(e=>e.toJSON())),e}static fromJSON(e,t){let n;if(!t)throw RangeError("Invalid input for Node.fromJSON");if(t.marks){if(!Array.isArray(t.marks))throw RangeError("Invalid mark data for Node.fromJSON");n=t.marks.map(e.markFromJSON)}if("text"==t.type){if("string"!=typeof t.text)throw RangeError("Invalid text node in JSON");return e.text(t.text,n)}let r=p.fromJSON(e,t.content),i=e.nodeType(t.type).create(t.attrs,r,n);return i.type.checkAttrs(i.attrs),i}}D.prototype.text=void 0;class R extends D{constructor(e,t,n,r){if(super(e,t,null,r),!n)throw RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):I(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(e,t){return this.text.slice(e,t)}get nodeSize(){return this.text.length}mark(e){return e==this.marks?this:new R(this.type,this.attrs,this.text,e)}withText(e){return e==this.text?this:new R(this.type,this.attrs,e,this.marks)}cut(e=0,t=this.text.length){return 0==e&&t==this.text.length?this:this.withText(this.text.slice(e,t))}eq(e){return this.sameMarkup(e)&&this.text==e.text}toJSON(){let e=super.toJSON();return e.text=this.text,e}}function I(e,t){for(let n=e.length-1;n>=0;n--)t=e[n].type.name+"("+t+")";return t}class P{constructor(e){this.validEnd=e,this.next=[],this.wrapCache=[]}static parse(e,t){var n;let r,i=new L(e,t);if(null==i.next)return P.empty;let o=function e(t){let n=[];do n.push(function(t){let n=[];do n.push(function(t){let n=function(t){if(t.eat("(")){let n=e(t);return t.eat(")")||t.err("Missing closing paren"),n}if(/\W/.test(t.next))t.err("Unexpected token '"+t.next+"'");else{let e=(function(e,t){let n=e.nodeTypes,r=n[t];if(r)return[r];let i=[];for(let e in n){let r=n[e];r.isInGroup(t)&&i.push(r)}return 0==i.length&&e.err("No node type or group '"+t+"' found"),i})(t,t.next).map(e=>(null==t.inline?t.inline=e.isInline:t.inline!=e.isInline&&t.err("Mixing inline and block content"),{type:"name",value:e}));return t.pos++,1==e.length?e[0]:{type:"choice",exprs:e}}}(t);for(;;)if(t.eat("+"))n={type:"plus",expr:n};else if(t.eat("*"))n={type:"star",expr:n};else if(t.eat("?"))n={type:"opt",expr:n};else if(t.eat("{"))n=function(e,t){let n=j(e),r=n;return e.eat(",")&&(r="}"!=e.next?j(e):-1),e.eat("}")||e.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:t}}(t,n);else break;return n}(t));while(t.next&&")"!=t.next&&"|"!=t.next);return 1==n.length?n[0]:{type:"seq",exprs:n}}(t));while(t.eat("|"));return 1==n.length?n[0]:{type:"choice",exprs:n}}(i);i.next&&i.err("Unexpected trailing text");let s=(n=function(e){let t=[[]];return i(function e(t,o){if("choice"==t.type)return t.exprs.reduce((t,n)=>t.concat(e(n,o)),[]);if("seq"==t.type)for(let r=0;;r++){let s=e(t.exprs[r],o);if(r==t.exprs.length-1)return s;i(s,o=n())}else if("star"==t.type){let s=n();return r(o,s),i(e(t.expr,s),s),[r(s)]}else if("plus"==t.type){let s=n();return i(e(t.expr,o),s),i(e(t.expr,s),s),[r(s)]}else if("opt"==t.type)return[r(o)].concat(e(t.expr,o));else if("range"==t.type){let s=o;for(let r=0;r<t.min;r++){let r=n();i(e(t.expr,s),r),s=r}if(-1==t.max)i(e(t.expr,s),s);else for(let o=t.min;o<t.max;o++){let o=n();r(s,o),i(e(t.expr,s),o),s=o}return[r(s)]}else if("name"==t.type)return[r(o,void 0,t.value)];else throw Error("Unknown expr type")}(e,0),n()),t;function n(){return t.push([])-1}function r(e,n,r){let i={term:r,to:n};return t[e].push(i),i}function i(e,t){e.forEach(e=>e.to=t)}}(o),r=Object.create(null),function e(t){let i=[];t.forEach(e=>{n[e].forEach(({term:e,to:t})=>{let r;if(e){for(let t=0;t<i.length;t++)i[t][0]==e&&(r=i[t][1]);$(n,t).forEach(t=>{r||i.push([e,r=[]]),-1==r.indexOf(t)&&r.push(t)})}})});let o=r[t.join(",")]=new P(t.indexOf(n.length-1)>-1);for(let t=0;t<i.length;t++){let n=i[t][1].sort(z);o.next.push({type:i[t][0],next:r[n.join(",")]||e(n)})}return o}($(n,0)));return function(e,t){for(let n=0,r=[e];n<r.length;n++){let e=r[n],i=!e.validEnd,o=[];for(let t=0;t<e.next.length;t++){let{type:n,next:s}=e.next[t];o.push(n.name),i&&!(n.isText||n.hasRequiredAttrs())&&(i=!1),-1==r.indexOf(s)&&r.push(s)}i&&t.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(s,i),s}matchType(e){for(let t=0;t<this.next.length;t++)if(this.next[t].type==e)return this.next[t].next;return null}matchFragment(e,t=0,n=e.childCount){let r=this;for(let i=t;r&&i<n;i++)r=r.matchType(e.child(i).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let e=0;e<this.next.length;e++){let{type:t}=this.next[e];if(!(t.isText||t.hasRequiredAttrs()))return t}return null}compatible(e){for(let t=0;t<this.next.length;t++)for(let n=0;n<e.next.length;n++)if(this.next[t].type==e.next[n].type)return!0;return!1}fillBefore(e,t=!1,n=0){let r=[this];return function i(o,s){let l=o.matchFragment(e,n);if(l&&(!t||l.validEnd))return p.from(s.map(e=>e.createAndFill()));for(let e=0;e<o.next.length;e++){let{type:t,next:n}=o.next[e];if(!(t.isText||t.hasRequiredAttrs())&&-1==r.indexOf(n)){r.push(n);let e=i(n,s.concat(t));if(e)return e}}return null}(this,[])}findWrapping(e){for(let t=0;t<this.wrapCache.length;t+=2)if(this.wrapCache[t]==e)return this.wrapCache[t+1];let t=this.computeWrapping(e);return this.wrapCache.push(e,t),t}computeWrapping(e){let t=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),i=r.match;if(i.matchType(e)){let e=[];for(let t=r;t.type;t=t.via)e.push(t.type);return e.reverse()}for(let e=0;e<i.next.length;e++){let{type:o,next:s}=i.next[e];o.isLeaf||o.hasRequiredAttrs()||o.name in t||r.type&&!s.validEnd||(n.push({match:o.contentMatch,type:o,via:r}),t[o.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(e){if(e>=this.next.length)throw RangeError(`There's no ${e}th edge in this content match`);return this.next[e]}toString(){let e=[];return!function t(n){e.push(n);for(let r=0;r<n.next.length;r++)-1==e.indexOf(n.next[r].next)&&t(n.next[r].next)}(this),e.map((t,n)=>{let r=n+(t.validEnd?"*":" ")+" ";for(let n=0;n<t.next.length;n++)r+=(n?", ":"")+t.next[n].type.name+"->"+e.indexOf(t.next[n].next);return r}).join("\n")}}P.empty=new P(!0);class L{constructor(e,t){this.string=e,this.nodeTypes=t,this.inline=null,this.pos=0,this.tokens=e.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(e){return this.next==e&&(this.pos++||!0)}err(e){throw SyntaxError(e+" (in content expression '"+this.string+"')")}}function j(e){/\D/.test(e.next)&&e.err("Expected number, got '"+e.next+"'");let t=Number(e.next);return e.pos++,t}function z(e,t){return t-e}function $(e,t){let n=[];return function t(r){let i=e[r];if(1==i.length&&!i[0].term)return t(i[0].to);n.push(r);for(let e=0;e<i.length;e++){let{term:r,to:o}=i[e];r||-1!=n.indexOf(o)||t(o)}}(t),n.sort(z)}function B(e){let t=Object.create(null);for(let n in e){let r=e[n];if(!r.hasDefault)return null;t[n]=r.default}return t}function H(e,t){let n=Object.create(null);for(let r in e){let i=t&&t[r];if(void 0===i){let t=e[r];if(t.hasDefault)i=t.default;else throw RangeError("No value supplied for attribute "+r)}n[r]=i}return n}function V(e,t,n,r){for(let r in t)if(!(r in e))throw RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let n in e){let r=e[n];r.validate&&r.validate(t[n])}}function F(e,t){let n=Object.create(null);if(t)for(let r in t)n[r]=new _(e,r,t[r]);return n}class W{constructor(e,t,n){this.name=e,this.schema=t,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=F(e,n.attrs),this.defaultAttrs=B(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==e),this.isText="text"==e}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==P.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(e){return this.groups.indexOf(e)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let e in this.attrs)if(this.attrs[e].isRequired)return!0;return!1}compatibleContent(e){return this==e||this.contentMatch.compatible(e.contentMatch)}computeAttrs(e){return!e&&this.defaultAttrs?this.defaultAttrs:H(this.attrs,e)}create(e=null,t,n){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new D(this,this.computeAttrs(e),p.from(t),g.setFrom(n))}createChecked(e=null,t,n){return t=p.from(t),this.checkContent(t),new D(this,this.computeAttrs(e),t,g.setFrom(n))}createAndFill(e=null,t,n){if(e=this.computeAttrs(e),(t=p.from(t)).size){let e=this.contentMatch.fillBefore(t);if(!e)return null;t=e.append(t)}let r=this.contentMatch.matchFragment(t),i=r&&r.fillBefore(p.empty,!0);return i?new D(this,e,t.append(i),g.setFrom(n)):null}validContent(e){let t=this.contentMatch.matchFragment(e);if(!t||!t.validEnd)return!1;for(let t=0;t<e.childCount;t++)if(!this.allowsMarks(e.child(t).marks))return!1;return!0}checkContent(e){if(!this.validContent(e))throw RangeError(`Invalid content for node ${this.name}: ${e.toString().slice(0,50)}`)}checkAttrs(e){V(this.attrs,e,"node",this.name)}allowsMarkType(e){return null==this.markSet||this.markSet.indexOf(e)>-1}allowsMarks(e){if(null==this.markSet)return!0;for(let t=0;t<e.length;t++)if(!this.allowsMarkType(e[t].type))return!1;return!0}allowedMarks(e){let t;if(null==this.markSet)return e;for(let n=0;n<e.length;n++)this.allowsMarkType(e[n].type)?t&&t.push(e[n]):t||(t=e.slice(0,n));return t?t.length?t:g.none:e}static compile(e,t){let n=Object.create(null);e.forEach((e,r)=>n[e]=new W(e,t,r));let r=t.spec.topNode||"doc";if(!n[r])throw RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw RangeError("Every schema needs a 'text' type");for(let e in n.text.attrs)throw RangeError("The text node type should not have attributes");return n}}class _{constructor(e,t,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(e,t,n){let r=n.split("|");return n=>{let i=null===n?"null":typeof n;if(0>r.indexOf(i))throw RangeError(`Expected value of type ${r} for attribute ${t} on type ${e}, got ${i}`)}}(e,t,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class q{constructor(e,t,n,r){this.name=e,this.rank=t,this.schema=n,this.spec=r,this.attrs=F(e,r.attrs),this.excluded=null;let i=B(this.attrs);this.instance=i?new g(this,i):null}create(e=null){return!e&&this.instance?this.instance:new g(this,H(this.attrs,e))}static compile(e,t){let n=Object.create(null),r=0;return e.forEach((e,i)=>n[e]=new q(e,r++,t,i)),n}removeFromSet(e){for(var t=0;t<e.length;t++)e[t].type==this&&(e=e.slice(0,t).concat(e.slice(t+1)),t--);return e}isInSet(e){for(let t=0;t<e.length;t++)if(e[t].type==this)return e[t]}checkAttrs(e){V(this.attrs,e,"mark",this.name)}excludes(e){return this.excluded.indexOf(e)>-1}}class K{constructor(e){this.linebreakReplacement=null,this.cached=Object.create(null);let t=this.spec={};for(let n in e)t[n]=e[n];t.nodes=h.from(e.nodes),t.marks=h.from(e.marks||{}),this.nodes=W.compile(this.spec.nodes,this),this.marks=q.compile(this.spec.marks,this);let n=Object.create(null);for(let e in this.nodes){if(e in this.marks)throw RangeError(e+" can not be both a node and a mark");let t=this.nodes[e],r=t.spec.content||"",i=t.spec.marks;if(t.contentMatch=n[r]||(n[r]=P.parse(r,this.nodes)),t.inlineContent=t.contentMatch.inlineContent,t.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!t.isInline||!t.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=t}t.markSet="_"==i?null:i?J(this,i.split(" ")):""!=i&&t.inlineContent?null:[]}for(let e in this.marks){let t=this.marks[e],n=t.spec.excludes;t.excluded=null==n?[t]:""==n?[]:J(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(e,t=null,n,r){if("string"==typeof e)e=this.nodeType(e);else if(e instanceof W){if(e.schema!=this)throw RangeError("Node type from different schema used ("+e.name+")")}else throw RangeError("Invalid node type: "+e);return e.createChecked(t,n,r)}text(e,t){let n=this.nodes.text;return new R(n,n.defaultAttrs,e,g.setFrom(t))}mark(e,t){return"string"==typeof e&&(e=this.marks[e]),e.create(t)}nodeFromJSON(e){return D.fromJSON(this,e)}markFromJSON(e){return g.fromJSON(this,e)}nodeType(e){let t=this.nodes[e];if(!t)throw RangeError("Unknown node type: "+e);return t}}function J(e,t){let n=[];for(let r=0;r<t.length;r++){let i=t[r],o=e.marks[i],s=o;if(o)n.push(o);else for(let t in e.marks){let r=e.marks[t];("_"==i||r.spec.group&&r.spec.group.split(" ").indexOf(i)>-1)&&n.push(s=r)}if(!s)throw SyntaxError("Unknown mark type: '"+t[r]+"'")}return n}class U{constructor(e,t){this.schema=e,this.rules=t,this.tags=[],this.styles=[];let n=this.matchedStyles=[];t.forEach(e=>{if(null!=e.tag)this.tags.push(e);else if(null!=e.style){let t=/[^=]*/.exec(e.style)[0];0>n.indexOf(t)&&n.push(t),this.styles.push(e)}}),this.normalizeLists=!this.tags.some(t=>{if(!/^(ul|ol)\b/.test(t.tag)||!t.node)return!1;let n=e.nodes[t.node];return n.contentMatch.matchType(n)})}parse(e,t={}){let n=new ee(this,t,!1);return n.addAll(e,g.none,t.from,t.to),n.finish()}parseSlice(e,t={}){let n=new ee(this,t,!0);return n.addAll(e,g.none,t.from,t.to),b.maxOpen(n.finish())}matchTag(e,t,n){for(let o=n?this.tags.indexOf(n)+1:0;o<this.tags.length;o++){var r,i;let n=this.tags[o];if(r=e,i=n.tag,(r.matches||r.msMatchesSelector||r.webkitMatchesSelector||r.mozMatchesSelector).call(r,i)&&(void 0===n.namespace||e.namespaceURI==n.namespace)&&(!n.context||t.matchesContext(n.context))){if(n.getAttrs){let t=n.getAttrs(e);if(!1===t)continue;n.attrs=t||void 0}return n}}}matchStyle(e,t,n,r){for(let i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){let r=this.styles[i],o=r.style;if(0==o.indexOf(e)&&(!r.context||n.matchesContext(r.context))&&(!(o.length>e.length)||61==o.charCodeAt(e.length)&&o.slice(e.length+1)==t)){if(r.getAttrs){let e=r.getAttrs(t);if(!1===e)continue;r.attrs=e||void 0}return r}}}static schemaRules(e){let t=[];function n(e){let n=null==e.priority?50:e.priority,r=0;for(;r<t.length;r++){let e=t[r];if((null==e.priority?50:e.priority)<n)break}t.splice(r,0,e)}for(let t in e.marks){let r=e.marks[t].spec.parseDOM;r&&r.forEach(e=>{n(e=et(e)),e.mark||e.ignore||e.clearMark||(e.mark=t)})}for(let t in e.nodes){let r=e.nodes[t].spec.parseDOM;r&&r.forEach(e=>{n(e=et(e)),e.node||e.ignore||e.mark||(e.node=t)})}return t}static fromSchema(e){return e.cached.domParser||(e.cached.domParser=new U(e,U.schemaRules(e)))}}let G={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Y={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},X={ol:!0,ul:!0};function Q(e,t,n){return null!=t?!!t|2*("full"===t):e&&"pre"==e.whitespace?3:-5&n}class Z{constructor(e,t,n,r,i,o){this.type=e,this.attrs=t,this.marks=n,this.solid=r,this.options=o,this.content=[],this.activeMarks=g.none,this.match=i||(4&o?null:e.contentMatch)}findWrapping(e){if(!this.match){if(!this.type)return[];let t=this.type.contentMatch.fillBefore(p.from(e));if(t)this.match=this.type.contentMatch.matchFragment(t);else{let t=this.type.contentMatch,n;return(n=t.findWrapping(e.type))?(this.match=t,n):null}}return this.match.findWrapping(e.type)}finish(e){if(!(1&this.options)){let e=this.content[this.content.length-1],t;e&&e.isText&&(t=/[ \t\r\n\u000c]+$/.exec(e.text))&&(e.text.length==t[0].length?this.content.pop():this.content[this.content.length-1]=e.withText(e.text.slice(0,e.text.length-t[0].length)))}let t=p.from(this.content);return!e&&this.match&&(t=t.append(this.match.fillBefore(p.empty,!0))),this.type?this.type.create(this.attrs,t,this.marks):t}inlineContext(e){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:e.parentNode&&!G.hasOwnProperty(e.parentNode.nodeName.toLowerCase())}}class ee{constructor(e,t,n){this.parser=e,this.options=t,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r=t.topNode,i,o=Q(null,t.preserveWhitespace,0)|4*!!n;i=r?new Z(r.type,r.attrs,g.none,!0,t.topMatch||r.type.contentMatch,o):n?new Z(null,null,g.none,!0,null,o):new Z(e.schema.topNodeType,null,g.none,!0,null,o),this.nodes=[i],this.find=t.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(e,t){3==e.nodeType?this.addTextNode(e,t):1==e.nodeType&&this.addElement(e,t)}addTextNode(e,t){let n=e.nodeValue,r=this.top,i=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===i||r.inlineContext(e)||/[^ \t\r\n\u000c]/.test(n)){if(i)n="full"!==i?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let t=r.content[r.content.length-1],i=e.previousSibling;(!t||i&&"BR"==i.nodeName||t.isText&&/[ \t\r\n\u000c]$/.test(t.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),t,!/\S/.test(n)),this.findInText(e)}else this.findInside(e)}addElement(e,t,n){let r=this.localPreserveWS,i=this.top;("PRE"==e.tagName||/pre/.test(e.style&&e.style.whiteSpace))&&(this.localPreserveWS=!0);let o=e.nodeName.toLowerCase(),s;X.hasOwnProperty(o)&&this.parser.normalizeLists&&function(e){for(let t=e.firstChild,n=null;t;t=t.nextSibling){let e=1==t.nodeType?t.nodeName.toLowerCase():null;e&&X.hasOwnProperty(e)&&n?(n.appendChild(t),t=n):"li"==e?n=t:e&&(n=null)}}(e);let l=this.options.ruleFromNode&&this.options.ruleFromNode(e)||(s=this.parser.matchTag(e,this,n));e:if(l?l.ignore:Y.hasOwnProperty(o))this.findInside(e),this.ignoreFallback(e,t);else if(!l||l.skip||l.closeParent){l&&l.closeParent?this.open=Math.max(0,this.open-1):l&&l.skip.nodeType&&(e=l.skip);let n,r=this.needsBlock;if(G.hasOwnProperty(o))i.content.length&&i.content[0].isInline&&this.open&&(this.open--,i=this.top),n=!0,i.type||(this.needsBlock=!0);else if(!e.firstChild){this.leafFallback(e,t);break e}let s=l&&l.skip?t:this.readStyles(e,t);s&&this.addAll(e,s),n&&this.sync(i),this.needsBlock=r}else{let n=this.readStyles(e,t);n&&this.addElementByRule(e,l,n,!1===l.consuming?s:void 0)}this.localPreserveWS=r}leafFallback(e,t){"BR"==e.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(e.ownerDocument.createTextNode("\n"),t)}ignoreFallback(e,t){"BR"!=e.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),t,!0)}readStyles(e,t){let n=e.style;if(n&&n.length)for(let e=0;e<this.parser.matchedStyles.length;e++){let r=this.parser.matchedStyles[e],i=n.getPropertyValue(r);if(i)for(let e;;){let n=this.parser.matchStyle(r,i,this,e);if(!n)break;if(n.ignore)return null;if(t=n.clearMark?t.filter(e=>!n.clearMark(e)):t.concat(this.parser.schema.marks[n.mark].create(n.attrs)),!1===n.consuming)e=n;else break}}return t}addElementByRule(e,t,n,r){let i,o;if(t.node)if((o=this.parser.schema.nodes[t.node]).isLeaf)this.insertNode(o.create(t.attrs),n,"BR"==e.nodeName)||this.leafFallback(e,n);else{let e=this.enter(o,t.attrs||null,n,t.preserveWhitespace);e&&(i=!0,n=e)}else{let e=this.parser.schema.marks[t.mark];n=n.concat(e.create(t.attrs))}let s=this.top;if(o&&o.isLeaf)this.findInside(e);else if(r)this.addElement(e,n,r);else if(t.getContent)this.findInside(e),t.getContent(e,this.parser.schema).forEach(e=>this.insertNode(e,n,!1));else{let r=e;"string"==typeof t.contentElement?r=e.querySelector(t.contentElement):"function"==typeof t.contentElement?r=t.contentElement(e):t.contentElement&&(r=t.contentElement),this.findAround(e,r,!0),this.addAll(r,n),this.findAround(e,r,!1)}i&&this.sync(s)&&this.open--}addAll(e,t,n,r){let i=n||0;for(let o=n?e.childNodes[n]:e.firstChild,s=null==r?null:e.childNodes[r];o!=s;o=o.nextSibling,++i)this.findAtPoint(e,i),this.addDOM(o,t);this.findAtPoint(e,i)}findPlace(e,t,n){let r,i;for(let t=this.open,o=0;t>=0;t--){let s=this.nodes[t],l=s.findWrapping(e);if(l&&(!r||r.length>l.length+o)&&(r=l,i=s,!l.length))break;if(s.solid){if(n)break;o+=2}}if(!r)return null;this.sync(i);for(let e=0;e<r.length;e++)t=this.enterInner(r[e],null,t,!1);return t}insertNode(e,t,n){if(e.isInline&&this.needsBlock&&!this.top.type){let e=this.textblockFromContext();e&&(t=this.enterInner(e,null,t))}let r=this.findPlace(e,t,n);if(r){this.closeExtra();let t=this.top;t.match&&(t.match=t.match.matchType(e.type));let n=g.none;for(let i of r.concat(e.marks))(t.type?t.type.allowsMarkType(i.type):en(i.type,e.type))&&(n=i.addToSet(n));return t.content.push(e.mark(n)),!0}return!1}enter(e,t,n,r){let i=this.findPlace(e.create(t),n,!1);return i&&(i=this.enterInner(e,t,n,!0,r)),i}enterInner(e,t,n,r=!1,i){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(e);let s=Q(e,i,o.options);4&o.options&&0==o.content.length&&(s|=4);let l=g.none;return n=n.filter(t=>(o.type?!o.type.allowsMarkType(t.type):!en(t.type,e))||(l=t.addToSet(l),!1)),this.nodes.push(new Z(e,t,l,r,null,s)),this.open++,n}closeExtra(e=!1){let t=this.nodes.length-1;if(t>this.open){for(;t>this.open;t--)this.nodes[t-1].content.push(this.nodes[t].finish(e));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(e){for(let t=this.open;t>=0;t--)if(this.nodes[t]==e)return this.open=t,!0;else this.localPreserveWS&&(this.nodes[t].options|=1);return!1}get currentPos(){this.closeExtra();let e=0;for(let t=this.open;t>=0;t--){let n=this.nodes[t].content;for(let t=n.length-1;t>=0;t--)e+=n[t].nodeSize;t&&e++}return e}findAtPoint(e,t){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==e&&this.find[n].offset==t&&(this.find[n].pos=this.currentPos)}findInside(e){if(this.find)for(let t=0;t<this.find.length;t++)null==this.find[t].pos&&1==e.nodeType&&e.contains(this.find[t].node)&&(this.find[t].pos=this.currentPos)}findAround(e,t,n){if(e!=t&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==e.nodeType&&e.contains(this.find[r].node)&&t.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(e){if(this.find)for(let t=0;t<this.find.length;t++)this.find[t].node==e&&(this.find[t].pos=this.currentPos-(e.nodeValue.length-this.find[t].offset))}matchesContext(e){if(e.indexOf("|")>-1)return e.split(/\s*\|\s*/).some(this.matchesContext,this);let t=e.split("/"),n=this.options.context,r=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),i=-(n?n.depth+1:0)+ +!r,o=(e,s)=>{for(;e>=0;e--){let l=t[e];if(""==l){if(e==t.length-1||0==e)continue;for(;s>=i;s--)if(o(e-1,s))return!0;return!1}{let e=s>0||0==s&&r?this.nodes[s].type:n&&s>=i?n.node(s-i).type:null;if(!e||e.name!=l&&!e.isInGroup(l))return!1;s--}}return!0};return o(t.length-1,this.open)}textblockFromContext(){let e=this.options.context;if(e)for(let t=e.depth;t>=0;t--){let n=e.node(t).contentMatchAt(e.indexAfter(t)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let e in this.parser.schema.nodes){let t=this.parser.schema.nodes[e];if(t.isTextblock&&t.defaultAttrs)return t}}}function et(e){let t={};for(let n in e)t[n]=e[n];return t}function en(e,t){let n=t.schema.nodes;for(let r in n){let i=n[r];if(!i.allowsMarkType(e))continue;let o=[],s=e=>{o.push(e);for(let n=0;n<e.edgeCount;n++){let{type:r,next:i}=e.edge(n);if(r==t||0>o.indexOf(i)&&s(i))return!0}};if(s(i.contentMatch))return!0}}class er{constructor(e,t){this.nodes=e,this.marks=t}serializeFragment(e,t={},n){n||(n=eo(t).createDocumentFragment());let r=n,i=[];return e.forEach(e=>{if(i.length||e.marks.length){let n=0,o=0;for(;n<i.length&&o<e.marks.length;){let t=e.marks[o];if(!this.marks[t.type.name]){o++;continue}if(!t.eq(i[n][0])||!1===t.type.spec.spanning)break;n++,o++}for(;n<i.length;)r=i.pop()[1];for(;o<e.marks.length;){let n=e.marks[o++],s=this.serializeMark(n,e.isInline,t);s&&(i.push([n,r]),r.appendChild(s.dom),r=s.contentDOM||s.dom)}}r.appendChild(this.serializeNodeInner(e,t))}),n}serializeNodeInner(e,t){let{dom:n,contentDOM:r}=el(eo(t),this.nodes[e.type.name](e),null,e.attrs);if(r){if(e.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(e.content,t,r)}return n}serializeNode(e,t={}){let n=this.serializeNodeInner(e,t);for(let r=e.marks.length-1;r>=0;r--){let i=this.serializeMark(e.marks[r],e.isInline,t);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(e,t,n={}){let r=this.marks[e.type.name];return r&&el(eo(n),r(e,t),null,e.attrs)}static renderSpec(e,t,n=null,r){return el(e,t,n,r)}static fromSchema(e){return e.cached.domSerializer||(e.cached.domSerializer=new er(this.nodesFromSchema(e),this.marksFromSchema(e)))}static nodesFromSchema(e){let t=ei(e.nodes);return t.text||(t.text=e=>e.text),t}static marksFromSchema(e){return ei(e.marks)}}function ei(e){let t={};for(let n in e){let r=e[n].spec.toDOM;r&&(t[n]=r)}return t}function eo(e){return e.document||window.document}let es=new WeakMap;function el(e,t,n,r){let i,o,s;if("string"==typeof t)return{dom:e.createTextNode(t)};if(null!=t.nodeType)return{dom:t};if(t.dom&&null!=t.dom.nodeType)return t;let l=t[0],a;if("string"!=typeof l)throw RangeError("Invalid array passed to renderSpec");if(r&&(void 0===(o=es.get(r))&&es.set(r,(s=null,!function e(t){if(t&&"object"==typeof t)if(Array.isArray(t))if("string"==typeof t[0])s||(s=[]),s.push(t);else for(let n=0;n<t.length;n++)e(t[n]);else for(let n in t)e(t[n])}(r),o=s)),a=o)&&a.indexOf(t)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let c=l.indexOf(" ");c>0&&(n=l.slice(0,c),l=l.slice(c+1));let d=n?e.createElementNS(n,l):e.createElement(l),h=t[1],p=1;if(h&&"object"==typeof h&&null==h.nodeType&&!Array.isArray(h)){for(let e in p=2,h)if(null!=h[e]){let t=e.indexOf(" ");t>0?d.setAttributeNS(e.slice(0,t),e.slice(t+1),h[e]):d.setAttribute(e,h[e])}}for(let o=p;o<t.length;o++){let s=t[o];if(0===s){if(o<t.length-1||o>p)throw RangeError("Content hole must be the only child of its parent node");return{dom:d,contentDOM:d}}{let{dom:t,contentDOM:o}=el(e,s,n,r);if(d.appendChild(t),o){if(i)throw RangeError("Multiple content holes");i=o}}}return{dom:d,contentDOM:i}}class ea{constructor(e,t,n){this.pos=e,this.delInfo=t,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class ec{constructor(e,t=!1){if(this.ranges=e,this.inverted=t,!e.length&&ec.empty)return ec.empty}recover(e){let t=0,n=65535&e;if(!this.inverted)for(let e=0;e<n;e++)t+=this.ranges[3*e+2]-this.ranges[3*e+1];return this.ranges[3*n]+t+(e-(65535&e))/65536}mapResult(e,t=1){return this._map(e,t,!1)}map(e,t=1){return this._map(e,t,!0)}_map(e,t,n){let r=0,i=this.inverted?2:1,o=this.inverted?1:2;for(let s=0;s<this.ranges.length;s+=3){let l=this.ranges[s]-(this.inverted?r:0);if(l>e)break;let a=this.ranges[s+i],c=this.ranges[s+o],d=l+a;if(e<=d){let i=a?e==l?-1:e==d?1:t:t,o=l+r+(i<0?0:c);if(n)return o;let h=e==(t<0?l:d)?null:s/3+(e-l)*65536,p=e==l?2:e==d?1:4;return(t<0?e!=l:e!=d)&&(p|=8),new ea(o,p,h)}r+=c-a}return n?e+r:new ea(e+r,0,null)}touches(e,t){let n=0,r=65535&t,i=this.inverted?2:1,o=this.inverted?1:2;for(let t=0;t<this.ranges.length;t+=3){let s=this.ranges[t]-(this.inverted?n:0);if(s>e)break;let l=this.ranges[t+i];if(e<=s+l&&t==3*r)return!0;n+=this.ranges[t+o]-l}return!1}forEach(e){let t=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,i=0;r<this.ranges.length;r+=3){let o=this.ranges[r],s=o-(this.inverted?i:0),l=o+(this.inverted?0:i),a=this.ranges[r+t],c=this.ranges[r+n];e(s,s+a,l,l+c),i+=c-a}}invert(){return new ec(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(e){return 0==e?ec.empty:new ec(e<0?[0,-e,0]:[0,0,e])}}ec.empty=new ec([]);class ed{constructor(e,t,n=0,r=e?e.length:0){this.mirror=t,this.from=n,this.to=r,this._maps=e||[],this.ownData=!(e||t)}get maps(){return this._maps}slice(e=0,t=this.maps.length){return new ed(this._maps,this.mirror,e,t)}appendMap(e,t){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(e),null!=t&&this.setMirror(this._maps.length-1,t)}appendMapping(e){for(let t=0,n=this._maps.length;t<e._maps.length;t++){let r=e.getMirror(t);this.appendMap(e._maps[t],null!=r&&r<t?n+r:void 0)}}getMirror(e){if(this.mirror){for(let t=0;t<this.mirror.length;t++)if(this.mirror[t]==e)return this.mirror[t+(t%2?-1:1)]}}setMirror(e,t){this.mirror||(this.mirror=[]),this.mirror.push(e,t)}appendMappingInverted(e){for(let t=e.maps.length-1,n=this._maps.length+e._maps.length;t>=0;t--){let r=e.getMirror(t);this.appendMap(e._maps[t].invert(),null!=r&&r>t?n-r-1:void 0)}}invert(){let e=new ed;return e.appendMappingInverted(this),e}map(e,t=1){if(this.mirror)return this._map(e,t,!0);for(let n=this.from;n<this.to;n++)e=this._maps[n].map(e,t);return e}mapResult(e,t=1){return this._map(e,t,!1)}_map(e,t,n){let r=0;for(let n=this.from;n<this.to;n++){let i=this._maps[n].mapResult(e,t);if(null!=i.recover){let t=this.getMirror(n);if(null!=t&&t>n&&t<this.to){n=t,e=this._maps[t].recover(i.recover);continue}}r|=i.delInfo,e=i.pos}return n?e:new ea(e,r,null)}}let eh=Object.create(null);class ep{getMap(){return ec.empty}merge(e){return null}static fromJSON(e,t){if(!t||!t.stepType)throw RangeError("Invalid input for Step.fromJSON");let n=eh[t.stepType];if(!n)throw RangeError(`No step type ${t.stepType} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in eh)throw RangeError("Duplicate use of step JSON ID "+e);return eh[e]=t,t.prototype.jsonID=e,t}}class eu{constructor(e,t){this.doc=e,this.failed=t}static ok(e){return new eu(e,null)}static fail(e){return new eu(null,e)}static fromReplace(e,t,n,r){try{return eu.ok(e.replace(t,n,r))}catch(e){if(e instanceof y)return eu.fail(e.message);throw e}}}function ef(e,t,n){let r=[];for(let i=0;i<e.childCount;i++){let o=e.child(i);o.content.size&&(o=o.copy(ef(o.content,t,o))),o.isInline&&(o=t(o,n,i)),r.push(o)}return p.fromArray(r)}class em extends ep{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=e.resolve(this.from),r=n.node(n.sharedDepth(this.to)),i=new b(ef(t.content,(e,t)=>e.isAtom&&t.type.allowsMarkType(this.mark.type)?e.mark(this.mark.addToSet(e.marks)):e,r),t.openStart,t.openEnd);return eu.fromReplace(e,this.from,this.to,i)}invert(){return new eg(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new em(t.pos,n.pos,this.mark)}merge(e){return e instanceof em&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new em(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new em(t.from,t.to,e.markFromJSON(t.mark))}}ep.jsonID("addMark",em);class eg extends ep{constructor(e,t,n){super(),this.from=e,this.to=t,this.mark=n}apply(e){let t=e.slice(this.from,this.to),n=new b(ef(t.content,e=>e.mark(this.mark.removeFromSet(e.marks)),e),t.openStart,t.openEnd);return eu.fromReplace(e,this.from,this.to,n)}invert(){return new em(this.from,this.to,this.mark)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deleted&&n.deleted||t.pos>=n.pos?null:new eg(t.pos,n.pos,this.mark)}merge(e){return e instanceof eg&&e.mark.eq(this.mark)&&this.from<=e.to&&this.to>=e.from?new eg(Math.min(this.from,e.from),Math.max(this.to,e.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new eg(t.from,t.to,e.markFromJSON(t.mark))}}ep.jsonID("removeMark",eg);class ey extends ep{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return eu.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.addToSet(t.marks));return eu.fromReplace(e,this.pos,this.pos+1,new b(p.from(n),0,+!t.isLeaf))}invert(e){let t=e.nodeAt(this.pos);if(t){let e=this.mark.addToSet(t.marks);if(e.length==t.marks.length){for(let n=0;n<t.marks.length;n++)if(!t.marks[n].isInSet(e))return new ey(this.pos,t.marks[n]);return new ey(this.pos,this.mark)}}return new eb(this.pos,this.mark)}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new ey(t.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new ey(t.pos,e.markFromJSON(t.mark))}}ep.jsonID("addNodeMark",ey);class eb extends ep{constructor(e,t){super(),this.pos=e,this.mark=t}apply(e){let t=e.nodeAt(this.pos);if(!t)return eu.fail("No node at mark step's position");let n=t.type.create(t.attrs,null,this.mark.removeFromSet(t.marks));return eu.fromReplace(e,this.pos,this.pos+1,new b(p.from(n),0,+!t.isLeaf))}invert(e){let t=e.nodeAt(this.pos);return t&&this.mark.isInSet(t.marks)?new ey(this.pos,this.mark):this}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new eb(t.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new eb(t.pos,e.markFromJSON(t.mark))}}ep.jsonID("removeNodeMark",eb);class ew extends ep{constructor(e,t,n,r=!1){super(),this.from=e,this.to=t,this.slice=n,this.structure=r}apply(e){return this.structure&&ex(e,this.from,this.to)?eu.fail("Structure replace would overwrite content"):eu.fromReplace(e,this.from,this.to,this.slice)}getMap(){return new ec([this.from,this.to-this.from,this.slice.size])}invert(e){return new ew(this.from,this.from+this.slice.size,e.slice(this.from,this.to))}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1);return t.deletedAcross&&n.deletedAcross?null:new ew(t.pos,Math.max(t.pos,n.pos),this.slice,this.structure)}merge(e){if(!(e instanceof ew)||e.structure||this.structure)return null;if(this.from+this.slice.size!=e.from||this.slice.openEnd||e.slice.openStart)if(e.to!=this.from||this.slice.openStart||e.slice.openEnd)return null;else{let t=this.slice.size+e.slice.size==0?b.empty:new b(e.slice.content.append(this.slice.content),e.slice.openStart,this.slice.openEnd);return new ew(e.from,this.to,t,this.structure)}{let t=this.slice.size+e.slice.size==0?b.empty:new b(this.slice.content.append(e.slice.content),this.slice.openStart,e.slice.openEnd);return new ew(this.from,this.to+(e.to-e.from),t,this.structure)}}toJSON(){let e={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new ew(t.from,t.to,b.fromJSON(e,t.slice),!!t.structure)}}ep.jsonID("replace",ew);class ev extends ep{constructor(e,t,n,r,i,o,s=!1){super(),this.from=e,this.to=t,this.gapFrom=n,this.gapTo=r,this.slice=i,this.insert=o,this.structure=s}apply(e){if(this.structure&&(ex(e,this.from,this.gapFrom)||ex(e,this.gapTo,this.to)))return eu.fail("Structure gap-replace would overwrite content");let t=e.slice(this.gapFrom,this.gapTo);if(t.openStart||t.openEnd)return eu.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,t.content);return n?eu.fromReplace(e,this.from,this.to,n):eu.fail("Content does not fit in gap")}getMap(){return new ec([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(e){let t=this.gapTo-this.gapFrom;return new ev(this.from,this.from+this.slice.size+t,this.from+this.insert,this.from+this.insert+t,e.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(e){let t=e.mapResult(this.from,1),n=e.mapResult(this.to,-1),r=this.from==this.gapFrom?t.pos:e.map(this.gapFrom,-1),i=this.to==this.gapTo?n.pos:e.map(this.gapTo,1);return t.deletedAcross&&n.deletedAcross||r<t.pos||i>n.pos?null:new ev(t.pos,n.pos,r,i,this.slice,this.insert,this.structure)}toJSON(){let e={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(e.slice=this.slice.toJSON()),this.structure&&(e.structure=!0),e}static fromJSON(e,t){if("number"!=typeof t.from||"number"!=typeof t.to||"number"!=typeof t.gapFrom||"number"!=typeof t.gapTo||"number"!=typeof t.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new ev(t.from,t.to,t.gapFrom,t.gapTo,b.fromJSON(e,t.slice),t.insert,!!t.structure)}}function ex(e,t,n){let r=e.resolve(t),i=n-t,o=r.depth;for(;i>0&&o>0&&r.indexAfter(o)==r.node(o).childCount;)o--,i--;if(i>0){let e=r.node(o).maybeChild(r.indexAfter(o));for(;i>0;){if(!e||e.isLeaf)return!0;e=e.firstChild,i--}}return!1}function ek(e,t,n,r=n.contentMatch,i=!0){let o=e.doc.nodeAt(t),s=[],l=t+1;for(let t=0;t<o.childCount;t++){let a=o.child(t),c=l+a.nodeSize,d=r.matchType(a.type);if(d){r=d;for(let t=0;t<a.marks.length;t++)n.allowsMarkType(a.marks[t].type)||e.step(new eg(l,c,a.marks[t]));if(i&&a.isText&&"pre"!=n.whitespace){let e,t=/\r?\n|\r/g,r;for(;e=t.exec(a.text);)r||(r=new b(p.from(n.schema.text(" ",n.allowedMarks(a.marks))),0,0)),s.push(new ew(l+e.index,l+e.index+e[0].length,r))}}else s.push(new ew(l,c,b.empty));l=c}if(!r.validEnd){let t=r.fillBefore(p.empty,!0);e.replace(l,l,new b(t,0,0))}for(let t=s.length-1;t>=0;t--)e.step(s[t])}function eS(e){let t=e.parent.content.cutByIndex(e.startIndex,e.endIndex);for(let n=e.depth;;--n){let r=e.$from.node(n),i=e.$from.index(n),o=e.$to.indexAfter(n);if(n<e.depth&&r.canReplace(i,o,t))return n;if(0==n||r.type.spec.isolating||!((0==i||r.canReplace(i,r.childCount))&&(o==r.childCount||r.canReplace(0,o))))break}return null}function eC(e,t,n=null,r=e){let i=function(e,t){let{parent:n,startIndex:r,endIndex:i}=e,o=n.contentMatchAt(r).findWrapping(t);if(!o)return null;let s=o.length?o[0]:t;return n.canReplaceWith(r,i,s)?o:null}(e,t),o=i&&function(e,t){let{parent:n,startIndex:r,endIndex:i}=e,o=n.child(r),s=t.contentMatch.findWrapping(o.type);if(!s)return null;let l=(s.length?s[s.length-1]:t).contentMatch;for(let e=r;l&&e<i;e++)l=l.matchType(n.child(e).type);return l&&l.validEnd?s:null}(r,t);return o?i.map(eM).concat({type:t,attrs:n}).concat(o.map(eM)):null}function eM(e){return{type:e,attrs:null}}function eN(e,t,n,r){t.forEach((i,o)=>{if(i.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(i.text);){let i=e.mapping.slice(r).map(n+1+o+s.index);e.replaceWith(i,i+1,t.type.schema.linebreakReplacement.create())}}})}function eA(e,t,n,r){t.forEach((i,o)=>{if(i.type==i.type.schema.linebreakReplacement){let i=e.mapping.slice(r).map(n+1+o);e.replaceWith(i,i+1,t.type.schema.text("\n"))}})}function eO(e,t,n=1,r){let i=e.resolve(t),o=i.depth-n,s=r&&r[r.length-1]||i.parent;if(o<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!s.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let e=i.depth-1,t=n-2;e>o;e--,t--){let n=i.node(e),o=i.index(e);if(n.type.spec.isolating)return!1;let s=n.content.cutByIndex(o,n.childCount),l=r&&r[t+1];l&&(s=s.replaceChild(0,l.type.create(l.attrs)));let a=r&&r[t]||n;if(!n.canReplace(o+1,n.childCount)||!a.type.validContent(s))return!1}let l=i.indexAfter(o),a=r&&r[0];return i.node(o).canReplaceWith(l,l,a?a.type:i.node(o+1).type)}function eT(e,t){let n=e.resolve(t),r=n.index();return eE(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function eE(e,t){return!!(e&&t&&!e.isLeaf&&function(e,t){t.content.size||e.type.compatibleContent(t.type);let n=e.contentMatchAt(e.childCount),{linebreakReplacement:r}=e.type.schema;for(let i=0;i<t.childCount;i++){let o=t.child(i),s=o.type==r?e.type.schema.nodes.text:o.type;if(!(n=n.matchType(s))||!e.type.allowsMarks(o.marks))return!1}return n.validEnd}(e,t))}function eD(e,t,n=-1){let r=e.resolve(t);for(let e=r.depth;;e--){let i,o,s=r.index(e);if(e==r.depth?(i=r.nodeBefore,o=r.nodeAfter):n>0?(i=r.node(e+1),s++,o=r.node(e).maybeChild(s)):(i=r.node(e).maybeChild(s-1),o=r.node(e+1)),i&&!i.isTextblock&&eE(i,o)&&r.node(e).canReplace(s,s+1))return t;if(0==e)break;t=n<0?r.before(e):r.after(e)}}function eR(e,t,n){let r=e.resolve(t);if(!n.content.size)return t;let i=n.content;for(let e=0;e<n.openStart;e++)i=i.firstChild.content;for(let e=1;e<=(0==n.openStart&&n.size?2:1);e++)for(let t=r.depth;t>=0;t--){let n=t==r.depth?0:r.pos<=(r.start(t+1)+r.end(t+1))/2?-1:1,o=r.index(t)+ +(n>0),s=r.node(t),l=!1;if(1==e)l=s.canReplace(o,o,i);else{let e=s.contentMatchAt(o).findWrapping(i.firstChild.type);l=e&&s.canReplaceWith(o,o,e[0])}if(l)return 0==n?r.pos:n<0?r.before(t+1):r.after(t+1)}return null}function eI(e,t,n=t,r=b.empty){if(t==n&&!r.size)return null;let i=e.resolve(t),o=e.resolve(n);return eP(i,o,r)?new ew(t,n,r):new eL(i,o,r).fit()}function eP(e,t,n){return!n.openStart&&!n.openEnd&&e.start()==t.start()&&e.parent.canReplace(e.index(),t.index(),n.content)}ep.jsonID("replaceAround",ev);class eL{constructor(e,t,n){this.$from=e,this.$to=t,this.unplaced=n,this.frontier=[],this.placed=p.empty;for(let t=0;t<=e.depth;t++){let n=e.node(t);this.frontier.push({type:n.type,match:n.contentMatchAt(e.indexAfter(t))})}for(let t=e.depth;t>0;t--)this.placed=p.from(e.node(t).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let e=this.findFittable();e?this.placeNodes(e):this.openMore()||this.dropNode()}let e=this.mustMoveInline(),t=this.placed.size-this.depth-this.$from.depth,n=this.$from,r=this.close(e<0?this.$to:n.doc.resolve(e));if(!r)return null;let i=this.placed,o=n.depth,s=r.depth;for(;o&&s&&1==i.childCount;)i=i.firstChild.content,o--,s--;let l=new b(i,o,s);return e>-1?new ev(n.pos,e,this.$to.pos,this.$to.end(),l,t):l.size||n.pos!=this.$to.pos?new ew(n.pos,r.pos,l):null}findFittable(){let e=this.unplaced.openStart;for(let t=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<e;n++){let i=t.firstChild;if(t.childCount>1&&(r=0),i.type.spec.isolating&&r<=n){e=n;break}t=i.content}for(let t=1;t<=2;t++)for(let n=1==t?e:this.unplaced.openStart;n>=0;n--){let e,r=null,i=(n?(r=e$(this.unplaced.content,n-1).firstChild).content:this.unplaced.content).firstChild;for(let e=this.depth;e>=0;e--){let{type:o,match:s}=this.frontier[e],l,a=null;if(1==t&&(i?s.matchType(i.type)||(a=s.fillBefore(p.from(i),!1)):r&&o.compatibleContent(r.type)))return{sliceDepth:n,frontierDepth:e,parent:r,inject:a};if(2==t&&i&&(l=s.findWrapping(i.type)))return{sliceDepth:n,frontierDepth:e,parent:r,wrap:l};if(r&&s.matchType(r.type))break}}}openMore(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=e$(e,t);return!!r.childCount&&!r.firstChild.isLeaf&&(this.unplaced=new b(e,t+1,Math.max(n,r.size+t>=e.size-n?t+1:0)),!0)}dropNode(){let{content:e,openStart:t,openEnd:n}=this.unplaced,r=e$(e,t);if(r.childCount<=1&&t>0){let i=e.size-t<=t+r.size;this.unplaced=new b(ej(e,t-1,1),t-1,i?t-1:n)}else this.unplaced=new b(ej(e,t,1),t,n)}placeNodes({sliceDepth:e,frontierDepth:t,parent:n,inject:r,wrap:i}){for(;this.depth>t;)this.closeFrontierNode();if(i)for(let e=0;e<i.length;e++)this.openFrontierNode(i[e]);let o=this.unplaced,s=n?n.content:o.content,l=o.openStart-e,a=0,c=[],{match:d,type:h}=this.frontier[t];if(r){for(let e=0;e<r.childCount;e++)c.push(r.child(e));d=d.matchFragment(r)}let u=s.size+e-(o.content.size-o.openEnd);for(;a<s.childCount;){let e=s.child(a),t=d.matchType(e.type);if(!t)break;(++a>1||0==l||e.content.size)&&(d=t,c.push(function e(t,n,r){if(n<=0)return t;let i=t.content;return n>1&&(i=i.replaceChild(0,e(i.firstChild,n-1,1==i.childCount?r-1:0))),n>0&&(i=t.type.contentMatch.fillBefore(i).append(i),r<=0&&(i=i.append(t.type.contentMatch.matchFragment(i).fillBefore(p.empty,!0)))),t.copy(i)}(e.mark(h.allowedMarks(e.marks)),1==a?l:0,a==s.childCount?u:-1)))}let f=a==s.childCount;f||(u=-1),this.placed=ez(this.placed,t,p.from(c)),this.frontier[t].match=d,f&&u<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let e=0,t=s;e<u;e++){let e=t.lastChild;this.frontier.push({type:e.type,match:e.contentMatchAt(e.childCount)}),t=e.content}this.unplaced=f?0==e?b.empty:new b(ej(o.content,e-1,1),e-1,u<0?o.openEnd:e-1):new b(ej(o.content,e,a),o.openStart,o.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let e=this.frontier[this.depth],t;if(!e.type.isTextblock||!eB(this.$to,this.$to.depth,e.type,e.match,!1)||this.$to.depth==this.depth&&(t=this.findCloseLevel(this.$to))&&t.depth==this.depth)return -1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(e){t:for(let t=Math.min(this.depth,e.depth);t>=0;t--){let{match:n,type:r}=this.frontier[t],i=t<e.depth&&e.end(t+1)==e.pos+(e.depth-(t+1)),o=eB(e,t,r,n,i);if(o){for(let n=t-1;n>=0;n--){let{match:t,type:r}=this.frontier[n],i=eB(e,n,r,t,!0);if(!i||i.childCount)continue t}return{depth:t,fit:o,move:i?e.doc.resolve(e.after(t+1)):e}}}}close(e){let t=this.findCloseLevel(e);if(!t)return null;for(;this.depth>t.depth;)this.closeFrontierNode();t.fit.childCount&&(this.placed=ez(this.placed,t.depth,t.fit)),e=t.move;for(let n=t.depth+1;n<=e.depth;n++){let t=e.node(n),r=t.type.contentMatch.fillBefore(t.content,!0,e.index(n));this.openFrontierNode(t.type,t.attrs,r)}return e}openFrontierNode(e,t=null,n){let r=this.frontier[this.depth];r.match=r.match.matchType(e),this.placed=ez(this.placed,this.depth,p.from(e.create(t,n))),this.frontier.push({type:e,match:e.contentMatch})}closeFrontierNode(){let e=this.frontier.pop().match.fillBefore(p.empty,!0);e.childCount&&(this.placed=ez(this.placed,this.frontier.length,e))}}function ej(e,t,n){return 0==t?e.cutByIndex(n,e.childCount):e.replaceChild(0,e.firstChild.copy(ej(e.firstChild.content,t-1,n)))}function ez(e,t,n){return 0==t?e.append(n):e.replaceChild(e.childCount-1,e.lastChild.copy(ez(e.lastChild.content,t-1,n)))}function e$(e,t){for(let n=0;n<t;n++)e=e.firstChild.content;return e}function eB(e,t,n,r,i){let o=e.node(t),s=i?e.indexAfter(t):e.index(t);if(s==o.childCount&&!n.compatibleContent(o.type))return null;let l=r.fillBefore(o.content,!0,s);return l&&!function(e,t,n){for(let r=n;r<t.childCount;r++)if(!e.allowsMarks(t.child(r).marks))return!0;return!1}(n,o.content,s)?l:null}function eH(e,t){let n=[],r=Math.min(e.depth,t.depth);for(let i=r;i>=0;i--){let r=e.start(i);if(r<e.pos-(e.depth-i)||t.end(i)>t.pos+(t.depth-i)||e.node(i).type.spec.isolating||t.node(i).type.spec.isolating)break;(r==t.start(i)||i==e.depth&&i==t.depth&&e.parent.inlineContent&&t.parent.inlineContent&&i&&t.start(i-1)==r-1)&&n.push(i)}return n}class eV extends ep{constructor(e,t,n){super(),this.pos=e,this.attr=t,this.value=n}apply(e){let t=e.nodeAt(this.pos);if(!t)return eu.fail("No node at attribute step's position");let n=Object.create(null);for(let e in t.attrs)n[e]=t.attrs[e];n[this.attr]=this.value;let r=t.type.create(n,null,t.marks);return eu.fromReplace(e,this.pos,this.pos+1,new b(p.from(r),0,+!t.isLeaf))}getMap(){return ec.empty}invert(e){return new eV(this.pos,this.attr,e.nodeAt(this.pos).attrs[this.attr])}map(e){let t=e.mapResult(this.pos,1);return t.deletedAfter?null:new eV(t.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(e,t){if("number"!=typeof t.pos||"string"!=typeof t.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new eV(t.pos,t.attr,t.value)}}ep.jsonID("attr",eV);class eF extends ep{constructor(e,t){super(),this.attr=e,this.value=t}apply(e){let t=Object.create(null);for(let n in e.attrs)t[n]=e.attrs[n];t[this.attr]=this.value;let n=e.type.create(t,e.content,e.marks);return eu.ok(n)}getMap(){return ec.empty}invert(e){return new eF(this.attr,e.attrs[this.attr])}map(e){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(e,t){if("string"!=typeof t.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new eF(t.attr,t.value)}}ep.jsonID("docAttr",eF);let eW=class extends Error{};(eW=function e(t){let n=Error.call(this,t);return n.__proto__=e.prototype,n}).prototype=Object.create(Error.prototype),eW.prototype.constructor=eW,eW.prototype.name="TransformError";class e_{constructor(e){this.doc=e,this.steps=[],this.docs=[],this.mapping=new ed}get before(){return this.docs.length?this.docs[0]:this.doc}step(e){let t=this.maybeStep(e);if(t.failed)throw new eW(t.failed);return this}maybeStep(e){let t=e.apply(this.doc);return t.failed||this.addStep(e,t.doc),t}get docChanged(){return this.steps.length>0}addStep(e,t){this.docs.push(this.doc),this.steps.push(e),this.mapping.appendMap(e.getMap()),this.doc=t}replace(e,t=e,n=b.empty){let r=eI(this.doc,e,t,n);return r&&this.step(r),this}replaceWith(e,t,n){return this.replace(e,t,new b(p.from(n),0,0))}delete(e,t){return this.replace(e,t,b.empty)}insert(e,t){return this.replaceWith(e,e,t)}replaceRange(e,t,n){return!function(e,t,n,r){if(!r.size)return e.deleteRange(t,n);let i=e.doc.resolve(t),o=e.doc.resolve(n);if(eP(i,o,r))return e.step(new ew(t,n,r));let s=eH(i,e.doc.resolve(n));0==s[s.length-1]&&s.pop();let l=-(i.depth+1);s.unshift(l);for(let e=i.depth,t=i.pos-1;e>0;e--,t--){let n=i.node(e).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;s.indexOf(e)>-1?l=e:i.before(e)==t&&s.splice(1,0,-e)}let a=s.indexOf(l),c=[],d=r.openStart;for(let e=r.content,t=0;;t++){let n=e.firstChild;if(c.push(n),t==r.openStart)break;e=n.content}for(let e=d-1;e>=0;e--){var h;let t=c[e],n=(h=t.type).spec.defining||h.spec.definingForContent;if(n&&!t.sameMarkup(i.node(Math.abs(l)-1)))d=e;else if(n||!t.type.isTextblock)break}for(let t=r.openStart;t>=0;t--){let l=(t+d+1)%(r.openStart+1),h=c[l];if(h)for(let t=0;t<s.length;t++){let c=s[(t+a)%s.length],d=!0;c<0&&(d=!1,c=-c);let u=i.node(c-1),f=i.index(c-1);if(u.canReplaceWith(f,f,h.type,h.marks))return e.replace(i.before(c),d?o.after(c):n,new b(function e(t,n,r,i,o){if(n<r){let o=t.firstChild;t=t.replaceChild(0,o.copy(e(o.content,n+1,r,i,o)))}if(n>i){let e=o.contentMatchAt(0),n=e.fillBefore(t).append(t);t=n.append(e.matchFragment(n).fillBefore(p.empty,!0))}return t}(r.content,0,r.openStart,l),l,r.openEnd))}}let u=e.steps.length;for(let l=s.length-1;l>=0&&(e.replace(t,n,r),!(e.steps.length>u));l--){let e=s[l];e<0||(t=i.before(e),n=o.after(e))}}(this,e,t,n),this}replaceRangeWith(e,t,n){var r=e,i=t;if(!n.isInline&&r==i&&this.doc.resolve(r).parent.content.size){let e=function(e,t,n){let r=e.resolve(t);if(r.parent.canReplaceWith(r.index(),r.index(),n))return t;if(0==r.parentOffset)for(let e=r.depth-1;e>=0;e--){let t=r.index(e);if(r.node(e).canReplaceWith(t,t,n))return r.before(e+1);if(t>0)return null}if(r.parentOffset==r.parent.content.size)for(let e=r.depth-1;e>=0;e--){let t=r.indexAfter(e);if(r.node(e).canReplaceWith(t,t,n))return r.after(e+1);if(t<r.node(e).childCount)break}return null}(this.doc,r,n.type);null!=e&&(r=i=e)}return this.replaceRange(r,i,new b(p.from(n),0,0)),this}deleteRange(e,t){return!function(e,t,n){let r=e.doc.resolve(t),i=e.doc.resolve(n),o=eH(r,i);for(let t=0;t<o.length;t++){let n=o[t],s=t==o.length-1;if(s&&0==n||r.node(n).type.contentMatch.validEnd)return e.delete(r.start(n),i.end(n));if(n>0&&(s||r.node(n-1).canReplace(r.index(n-1),i.indexAfter(n-1))))return e.delete(r.before(n),i.after(n))}for(let o=1;o<=r.depth&&o<=i.depth;o++)if(t-r.start(o)==r.depth-o&&n>r.end(o)&&i.end(o)-n!=i.depth-o&&r.start(o-1)==i.start(o-1)&&r.node(o-1).canReplace(r.index(o-1),i.index(o-1)))return e.delete(r.before(o),n);e.delete(t,n)}(this,e,t),this}lift(e,t){return!function(e,t,n){let{$from:r,$to:i,depth:o}=t,s=r.before(o+1),l=i.after(o+1),a=s,c=l,d=p.empty,h=0;for(let e=o,t=!1;e>n;e--)t||r.index(e)>0?(t=!0,d=p.from(r.node(e).copy(d)),h++):a--;let u=p.empty,f=0;for(let e=o,t=!1;e>n;e--)t||i.after(e+1)<i.end(e)?(t=!0,u=p.from(i.node(e).copy(u)),f++):c++;e.step(new ev(a,c,s,l,new b(d.append(u),h,f),d.size-h,!0))}(this,e,t),this}join(e,t=1){return!function(e,t,n){let r=null,{linebreakReplacement:i}=e.doc.type.schema,o=e.doc.resolve(t-n),s=o.node().type;if(i&&s.inlineContent){let e="pre"==s.whitespace,t=!!s.contentMatch.matchType(i);e&&!t?r=!1:!e&&t&&(r=!0)}let l=e.steps.length;if(!1===r){let r=e.doc.resolve(t+n);eA(e,r.node(),r.before(),l)}s.inlineContent&&ek(e,t+n-1,s,o.node().contentMatchAt(o.index()),null==r);let a=e.mapping.slice(l),c=a.map(t-n);if(e.step(new ew(c,a.map(t+n,-1),b.empty,!0)),!0===r){let t=e.doc.resolve(c);eN(e,t.node(),t.before(),e.steps.length)}}(this,e,t),this}wrap(e,t){return!function(e,t,n){let r=p.empty;for(let e=n.length-1;e>=0;e--){if(r.size){let t=n[e].type.contentMatch.matchFragment(r);if(!t||!t.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}r=p.from(n[e].type.create(n[e].attrs,r))}let i=t.start,o=t.end;e.step(new ev(i,o,i,o,new b(r,0,0),n.length,!0))}(this,e,t),this}setBlockType(e,t=e,n,r=null){var i=this;if(!n.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let o=i.steps.length;return i.doc.nodesBetween(e,t,(e,t)=>{var s,l,a;let c,d,h="function"==typeof r?r(e):r;if(e.isTextblock&&!e.hasMarkup(n,h)&&(s=i.doc,l=i.mapping.slice(o).map(t),a=n,d=(c=s.resolve(l)).index(),c.parent.canReplaceWith(d,d+1,a))){let r=null;if(n.schema.linebreakReplacement){let e="pre"==n.whitespace,t=!!n.contentMatch.matchType(n.schema.linebreakReplacement);e&&!t?r=!1:!e&&t&&(r=!0)}!1===r&&eA(i,e,t,o),ek(i,i.mapping.slice(o).map(t,1),n,void 0,null===r);let s=i.mapping.slice(o),l=s.map(t,1),a=s.map(t+e.nodeSize,1);return i.step(new ev(l,a,l+1,a-1,new b(p.from(n.create(h,null,e.marks)),0,0),1,!0)),!0===r&&eN(i,e,t,o),!1}}),this}setNodeMarkup(e,t,n=null,r){return!function(e,t,n,r,i){let o=e.doc.nodeAt(t);if(!o)throw RangeError("No node at given position");n||(n=o.type);let s=n.create(r,null,i||o.marks);if(o.isLeaf)return e.replaceWith(t,t+o.nodeSize,s);if(!n.validContent(o.content))throw RangeError("Invalid content for node type "+n.name);e.step(new ev(t,t+o.nodeSize,t+1,t+o.nodeSize-1,new b(p.from(s),0,0),1,!0))}(this,e,t,n,r),this}setNodeAttribute(e,t,n){return this.step(new eV(e,t,n)),this}setDocAttribute(e,t){return this.step(new eF(e,t)),this}addNodeMark(e,t){return this.step(new ey(e,t)),this}removeNodeMark(e,t){let n=this.doc.nodeAt(e);if(!n)throw RangeError("No node at position "+e);if(t instanceof g)t.isInSet(n.marks)&&this.step(new eb(e,t));else{let r=n.marks,i,o=[];for(;i=t.isInSet(r);)o.push(new eb(e,i)),r=i.removeFromSet(r);for(let e=o.length-1;e>=0;e--)this.step(o[e])}return this}split(e,t=1,n){return!function(e,t,n=1,r){let i=e.doc.resolve(t),o=p.empty,s=p.empty;for(let e=i.depth,t=i.depth-n,l=n-1;e>t;e--,l--){o=p.from(i.node(e).copy(o));let t=r&&r[l];s=p.from(t?t.type.create(t.attrs,s):i.node(e).copy(s))}e.step(new ew(t,t,new b(o.append(s),n,n),!0))}(this,e,t,n),this}addMark(e,t,n){var r;let i,o,s,l;return r=this,s=[],l=[],r.doc.nodesBetween(e,t,(r,a,c)=>{if(!r.isInline)return;let d=r.marks;if(!n.isInSet(d)&&c.type.allowsMarkType(n.type)){let c=Math.max(a,e),h=Math.min(a+r.nodeSize,t),p=n.addToSet(d);for(let e=0;e<d.length;e++)d[e].isInSet(p)||(i&&i.to==c&&i.mark.eq(d[e])?i.to=h:s.push(i=new eg(c,h,d[e])));o&&o.to==c?o.to=h:l.push(o=new em(c,h,n))}}),s.forEach(e=>r.step(e)),l.forEach(e=>r.step(e)),this}removeMark(e,t,n){var r;let i,o;return r=this,i=[],o=0,r.doc.nodesBetween(e,t,(r,s)=>{if(!r.isInline)return;o++;let l=null;if(n instanceof q){let e=r.marks,t;for(;t=n.isInSet(e);)(l||(l=[])).push(t),e=t.removeFromSet(e)}else n?n.isInSet(r.marks)&&(l=[n]):l=r.marks;if(l&&l.length){let n=Math.min(s+r.nodeSize,t);for(let t=0;t<l.length;t++){let r=l[t],a;for(let e=0;e<i.length;e++){let t=i[e];t.step==o-1&&r.eq(i[e].style)&&(a=t)}a?(a.to=n,a.step=o):i.push({style:r,from:Math.max(s,e),to:n,step:o})}}}),i.forEach(e=>r.step(new eg(e.from,e.to,e.style))),this}clearIncompatible(e,t,n){return ek(this,e,t,n),this}}let eq=Object.create(null);class eK{constructor(e,t,n){this.$anchor=e,this.$head=t,this.ranges=n||[new eJ(e.min(t),e.max(t))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let e=this.ranges;for(let t=0;t<e.length;t++)if(e[t].$from.pos!=e[t].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(e,t=b.empty){let n=t.content.lastChild,r=null;for(let e=0;e<t.openEnd;e++)r=n,n=n.lastChild;let i=e.steps.length,o=this.ranges;for(let s=0;s<o.length;s++){let{$from:l,$to:a}=o[s],c=e.mapping.slice(i);e.replaceRange(c.map(l.pos),c.map(a.pos),s?b.empty:t),0==s&&e3(e,i,(n?n.isInline:r&&r.isTextblock)?-1:1)}}replaceWith(e,t){let n=e.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:o,$to:s}=r[i],l=e.mapping.slice(n),a=l.map(o.pos),c=l.map(s.pos);i?e.deleteRange(a,c):(e.replaceRangeWith(a,c,t),e3(e,n,t.isInline?-1:1))}}static findFrom(e,t,n=!1){let r=e.parent.inlineContent?new eY(e):e2(e.node(0),e.parent,e.pos,e.index(),t,n);if(r)return r;for(let r=e.depth-1;r>=0;r--){let i=t<0?e2(e.node(0),e.node(r),e.before(r+1),e.index(r),t,n):e2(e.node(0),e.node(r),e.after(r+1),e.index(r)+1,t,n);if(i)return i}return null}static near(e,t=1){return this.findFrom(e,t)||this.findFrom(e,-t)||new e0(e.node(0))}static atStart(e){return e2(e,e,0,0,1)||new e0(e)}static atEnd(e){return e2(e,e,e.content.size,e.childCount,-1)||new e0(e)}static fromJSON(e,t){if(!t||!t.type)throw RangeError("Invalid input for Selection.fromJSON");let n=eq[t.type];if(!n)throw RangeError(`No selection type ${t.type} defined`);return n.fromJSON(e,t)}static jsonID(e,t){if(e in eq)throw RangeError("Duplicate use of selection JSON ID "+e);return eq[e]=t,t.prototype.jsonID=e,t}getBookmark(){return eY.between(this.$anchor,this.$head).getBookmark()}}eK.prototype.visible=!0;class eJ{constructor(e,t){this.$from=e,this.$to=t}}let eU=!1;function eG(e){eU||e.parent.inlineContent||(eU=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+e.parent.type.name+")"))}class eY extends eK{constructor(e,t=e){eG(e),eG(t),super(e,t)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(e,t){let n=e.resolve(t.map(this.head));if(!n.parent.inlineContent)return eK.near(n);let r=e.resolve(t.map(this.anchor));return new eY(r.parent.inlineContent?r:n,n)}replace(e,t=b.empty){if(super.replace(e,t),t==b.empty){let t=this.$from.marksAcross(this.$to);t&&e.ensureMarks(t)}}eq(e){return e instanceof eY&&e.anchor==this.anchor&&e.head==this.head}getBookmark(){return new eX(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(e,t){if("number"!=typeof t.anchor||"number"!=typeof t.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new eY(e.resolve(t.anchor),e.resolve(t.head))}static create(e,t,n=t){let r=e.resolve(t);return new this(r,n==t?r:e.resolve(n))}static between(e,t,n){let r=e.pos-t.pos;if((!n||r)&&(n=r>=0?1:-1),!t.parent.inlineContent){let e=eK.findFrom(t,n,!0)||eK.findFrom(t,-n,!0);if(!e)return eK.near(t,n);t=e.$head}return e.parent.inlineContent||(0==r?e=t:(e=(eK.findFrom(e,-n,!0)||eK.findFrom(e,n,!0)).$anchor).pos<t.pos!=r<0&&(e=t)),new eY(e,t)}}eK.jsonID("text",eY);class eX{constructor(e,t){this.anchor=e,this.head=t}map(e){return new eX(e.map(this.anchor),e.map(this.head))}resolve(e){return eY.between(e.resolve(this.anchor),e.resolve(this.head))}}class eQ extends eK{constructor(e){let t=e.nodeAfter;super(e,e.node(0).resolve(e.pos+t.nodeSize)),this.node=t}map(e,t){let{deleted:n,pos:r}=t.mapResult(this.anchor),i=e.resolve(r);return n?eK.near(i):new eQ(i)}content(){return new b(p.from(this.node),0,0)}eq(e){return e instanceof eQ&&e.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new eZ(this.anchor)}static fromJSON(e,t){if("number"!=typeof t.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new eQ(e.resolve(t.anchor))}static create(e,t){return new eQ(e.resolve(t))}static isSelectable(e){return!e.isText&&!1!==e.type.spec.selectable}}eQ.prototype.visible=!1,eK.jsonID("node",eQ);class eZ{constructor(e){this.anchor=e}map(e){let{deleted:t,pos:n}=e.mapResult(this.anchor);return t?new eX(n,n):new eZ(n)}resolve(e){let t=e.resolve(this.anchor),n=t.nodeAfter;return n&&eQ.isSelectable(n)?new eQ(t):eK.near(t)}}class e0 extends eK{constructor(e){super(e.resolve(0),e.resolve(e.content.size))}replace(e,t=b.empty){if(t==b.empty){e.delete(0,e.doc.content.size);let t=eK.atStart(e.doc);t.eq(e.selection)||e.setSelection(t)}else super.replace(e,t)}toJSON(){return{type:"all"}}static fromJSON(e){return new e0(e)}map(e){return new e0(e)}eq(e){return e instanceof e0}getBookmark(){return e1}}eK.jsonID("all",e0);let e1={map(){return this},resolve:e=>new e0(e)};function e2(e,t,n,r,i,o=!1){if(t.inlineContent)return eY.create(e,n);for(let s=r-(i>0?0:1);i>0?s<t.childCount:s>=0;s+=i){let r=t.child(s);if(r.isAtom){if(!o&&eQ.isSelectable(r))return eQ.create(e,n-(i<0?r.nodeSize:0))}else{let t=e2(e,r,n+i,i<0?r.childCount:0,i,o);if(t)return t}n+=r.nodeSize*i}return null}function e3(e,t,n){let r,i=e.steps.length-1;if(i<t)return;let o=e.steps[i];(o instanceof ew||o instanceof ev)&&(e.mapping.maps[i].forEach((e,t,n,i)=>{null==r&&(r=i)}),e.setSelection(eK.near(e.doc.resolve(r),n)))}class e4 extends e_{constructor(e){super(e.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=e.selection,this.storedMarks=e.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(e){if(e.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=e,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(e){return this.storedMarks=e,this.updated|=2,this}ensureMarks(e){return g.sameSet(this.storedMarks||this.selection.$from.marks(),e)||this.setStoredMarks(e),this}addStoredMark(e){return this.ensureMarks(e.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(e){return this.ensureMarks(e.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(e,t){super.addStep(e,t),this.updated=-3&this.updated,this.storedMarks=null}setTime(e){return this.time=e,this}replaceSelection(e){return this.selection.replace(this,e),this}replaceSelectionWith(e,t=!0){let n=this.selection;return t&&(e=e.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||g.none))),n.replaceWith(this,e),this}deleteSelection(){return this.selection.replace(this),this}insertText(e,t,n){let r=this.doc.type.schema;if(null==t)return e?this.replaceSelectionWith(r.text(e),!0):this.deleteSelection();{if(null==n&&(n=t),n=null==n?t:n,!e)return this.deleteRange(t,n);let i=this.storedMarks;if(!i){let e=this.doc.resolve(t);i=n==t?e.marks():e.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(t,n,r.text(e,i)),this.selection.empty||this.setSelection(eK.near(this.selection.$to)),this}}setMeta(e,t){return this.meta["string"==typeof e?e:e.key]=t,this}getMeta(e){return this.meta["string"==typeof e?e:e.key]}get isGeneric(){for(let e in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function e5(e,t){return t&&e?e.bind(t):e}class e6{constructor(e,t,n){this.name=e,this.init=e5(t.init,n),this.apply=e5(t.apply,n)}}let e7=[new e6("doc",{init:e=>e.doc||e.schema.topNodeType.createAndFill(),apply:e=>e.doc}),new e6("selection",{init:(e,t)=>e.selection||eK.atStart(t.doc),apply:e=>e.selection}),new e6("storedMarks",{init:e=>e.storedMarks||null,apply:(e,t,n,r)=>r.selection.$cursor?e.storedMarks:null}),new e6("scrollToSelection",{init:()=>0,apply:(e,t)=>e.scrolledIntoView?t+1:t})];class e8{constructor(e,t){this.schema=e,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=e7.slice(),t&&t.forEach(e=>{if(this.pluginsByKey[e.key])throw RangeError("Adding different instances of a keyed plugin ("+e.key+")");this.plugins.push(e),this.pluginsByKey[e.key]=e,e.spec.state&&this.fields.push(new e6(e.key,e.spec.state,e))})}}class e9{constructor(e){this.config=e}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(e){return this.applyTransaction(e).state}filterTransaction(e,t=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=t){let t=this.config.plugins[n];if(t.spec.filterTransaction&&!t.spec.filterTransaction.call(t,e,this))return!1}return!0}applyTransaction(e){if(!this.filterTransaction(e))return{state:this,transactions:[]};let t=[e],n=this.applyInner(e),r=null;for(;;){let i=!1;for(let o=0;o<this.config.plugins.length;o++){let s=this.config.plugins[o];if(s.spec.appendTransaction){let l=r?r[o].n:0,a=r?r[o].state:this,c=l<t.length&&s.spec.appendTransaction.call(s,l?t.slice(l):t,a,n);if(c&&n.filterTransaction(c,o)){if(c.setMeta("appendedTransaction",e),!r){r=[];for(let e=0;e<this.config.plugins.length;e++)r.push(e<o?{state:n,n:t.length}:{state:this,n:0})}t.push(c),n=n.applyInner(c),i=!0}r&&(r[o]={state:n,n:t.length})}}if(!i)return{state:n,transactions:t}}}applyInner(e){if(!e.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let t=new e9(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let i=n[r];t[i.name]=i.apply(e,this[i.name],this,t)}return t}get tr(){return new e4(this)}static create(e){let t=new e8(e.doc?e.doc.type.schema:e.schema,e.plugins),n=new e9(t);for(let r=0;r<t.fields.length;r++)n[t.fields[r].name]=t.fields[r].init(e,n);return n}reconfigure(e){let t=new e8(this.schema,e.plugins),n=t.fields,r=new e9(t);for(let t=0;t<n.length;t++){let i=n[t].name;r[i]=this.hasOwnProperty(i)?this[i]:n[t].init(e,r)}return r}toJSON(e){let t={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(t.storedMarks=this.storedMarks.map(e=>e.toJSON())),e&&"object"==typeof e)for(let n in e){if("doc"==n||"selection"==n)throw RangeError("The JSON fields `doc` and `selection` are reserved");let r=e[n],i=r.spec.state;i&&i.toJSON&&(t[n]=i.toJSON.call(r,this[r.key]))}return t}static fromJSON(e,t,n){if(!t)throw RangeError("Invalid input for EditorState.fromJSON");if(!e.schema)throw RangeError("Required config field 'schema' missing");let r=new e8(e.schema,e.plugins),i=new e9(r);return r.fields.forEach(r=>{if("doc"==r.name)i.doc=D.fromJSON(e.schema,t.doc);else if("selection"==r.name)i.selection=eK.fromJSON(i.doc,t.selection);else if("storedMarks"==r.name)t.storedMarks&&(i.storedMarks=t.storedMarks.map(e.schema.markFromJSON));else{if(n)for(let o in n){let s=n[o],l=s.spec.state;if(s.key==r.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(t,o)){i[r.name]=l.fromJSON.call(s,e,t[o],i);return}}i[r.name]=r.init(e,i)}}),i}}class te{constructor(e){this.spec=e,this.props={},e.props&&function e(t,n,r){for(let i in t){let o=t[i];o instanceof Function?o=o.bind(n):"handleDOMEvents"==i&&(o=e(o,n,{})),r[i]=o}return r}(e.props,this,this.props),this.key=e.key?e.key.key:tn("plugin")}getState(e){return e[this.key]}}let tt=Object.create(null);function tn(e){return e in tt?e+"$"+ ++tt[e]:(tt[e]=0,e+"$")}class tr{constructor(e="key"){this.key=tn(e)}get(e){return e.config.pluginsByKey[this.key]}getState(e){return e[this.key]}}let ti=function(e){for(var t=0;;t++)if(!(e=e.previousSibling))return t},to=function(e){let t=e.assignedSlot||e.parentNode;return t&&11==t.nodeType?t.host:t},ts=null,tl=function(e,t,n){let r=ts||(ts=document.createRange());return r.setEnd(e,null==n?e.nodeValue.length:n),r.setStart(e,t||0),r},ta=function(){ts=null},tc=function(e,t,n,r){return n&&(th(e,t,n,r,-1)||th(e,t,n,r,1))},td=/^(img|br|input|textarea|hr)$/i;function th(e,t,n,r,i){for(var o;;){if(e==n&&t==r)return!0;if(t==(i<0?0:tp(e))){let n=e.parentNode;if(!n||1!=n.nodeType||tu(e)||td.test(e.nodeName)||"false"==e.contentEditable)return!1;t=ti(e)+(i<0?0:1),e=n}else{if(1!=e.nodeType)return!1;let n=e.childNodes[t+(i<0?-1:0)];if(1==n.nodeType&&"false"==n.contentEditable)if(null==(o=n.pmViewDesc)||!o.ignoreForSelection)return!1;else t+=i;else e=n,t=i<0?tp(e):0}}}function tp(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function tu(e){let t;for(let n=e;n&&!(t=n.pmViewDesc);n=n.parentNode);return t&&t.node&&t.node.isBlock&&(t.dom==e||t.contentDOM==e)}let tf=function(e){return e.focusNode&&tc(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset)};function tm(e,t){let n=document.createEvent("Event");return n.initEvent("keydown",!0,!0),n.keyCode=e,n.key=n.code=t,n}let tg="undefined"!=typeof navigator?navigator:null,ty="undefined"!=typeof document?document:null,tb=tg&&tg.userAgent||"",tw=/Edge\/(\d+)/.exec(tb),tv=/MSIE \d/.exec(tb),tx=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(tb),tk=!!(tv||tx||tw),tS=tv?document.documentMode:tx?+tx[1]:tw?+tw[1]:0,tC=!tk&&/gecko\/(\d+)/i.test(tb);tC&&(/Firefox\/(\d+)/.exec(tb)||[0,0])[1];let tM=!tk&&/Chrome\/(\d+)/.exec(tb),tN=!!tM,tA=tM?+tM[1]:0,tO=!tk&&!!tg&&/Apple Computer/.test(tg.vendor),tT=tO&&(/Mobile\/\w+/.test(tb)||!!tg&&tg.maxTouchPoints>2),tE=tT||!!tg&&/Mac/.test(tg.platform),tD=!!tg&&/Win/.test(tg.platform),tR=/Android \d/.test(tb),tI=!!ty&&"webkitFontSmoothing"in ty.documentElement.style,tP=tI?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function tL(e,t){return"number"==typeof e?e:e[t]}function tj(e,t,n){let r=e.someProp("scrollThreshold")||0,i=e.someProp("scrollMargin")||5,o=e.dom.ownerDocument;for(let s=n||e.dom;s;){if(1!=s.nodeType){s=to(s);continue}let e=s,n=e==o.body,l=n?function(e){let t=e.defaultView&&e.defaultView.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:e.documentElement.clientWidth,top:0,bottom:e.documentElement.clientHeight}}(o):function(e){let t=e.getBoundingClientRect(),n=t.width/e.offsetWidth||1,r=t.height/e.offsetHeight||1;return{left:t.left,right:t.left+e.clientWidth*n,top:t.top,bottom:t.top+e.clientHeight*r}}(e),a=0,c=0;if(t.top<l.top+tL(r,"top")?c=-(l.top-t.top+tL(i,"top")):t.bottom>l.bottom-tL(r,"bottom")&&(c=t.bottom-t.top>l.bottom-l.top?t.top+tL(i,"top")-l.top:t.bottom-l.bottom+tL(i,"bottom")),t.left<l.left+tL(r,"left")?a=-(l.left-t.left+tL(i,"left")):t.right>l.right-tL(r,"right")&&(a=t.right-l.right+tL(i,"right")),a||c)if(n)o.defaultView.scrollBy(a,c);else{let n=e.scrollLeft,r=e.scrollTop;c&&(e.scrollTop+=c),a&&(e.scrollLeft+=a);let i=e.scrollLeft-n,o=e.scrollTop-r;t={left:t.left-i,top:t.top-o,right:t.right-i,bottom:t.bottom-o}}let d=n?"fixed":getComputedStyle(s).position;if(/^(fixed|sticky)$/.test(d))break;s="absolute"==d?s.offsetParent:to(s)}}function tz(e){let t=[],n=e.ownerDocument;for(let r=e;r&&(t.push({dom:r,top:r.scrollTop,left:r.scrollLeft}),e!=n);r=to(r));return t}function t$(e,t){for(let n=0;n<e.length;n++){let{dom:r,top:i,left:o}=e[n];r.scrollTop!=i+t&&(r.scrollTop=i+t),r.scrollLeft!=o&&(r.scrollLeft=o)}}let tB=null;function tH(e,t){return e.left>=t.left-1&&e.left<=t.right+1&&e.top>=t.top-1&&e.top<=t.bottom+1}function tV(e){return e.top<e.bottom||e.left<e.right}function tF(e,t){let n=e.getClientRects();if(n.length){let e=n[t<0?0:n.length-1];if(tV(e))return e}return Array.prototype.find.call(n,tV)||e.getBoundingClientRect()}let tW=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function t_(e,t,n){let{node:r,offset:i,atom:o}=e.docView.domFromPos(t,n<0?-1:1),s=tI||tC;if(3==r.nodeType)if(s&&(tW.test(r.nodeValue)||(n<0?!i:i==r.nodeValue.length))){let e=tF(tl(r,i,i),n);if(tC&&i&&/\s/.test(r.nodeValue[i-1])&&i<r.nodeValue.length){let t=tF(tl(r,i-1,i-1),-1);if(t.top==e.top){let n=tF(tl(r,i,i+1),-1);if(n.top!=e.top)return tq(n,n.left<t.left)}}return e}else{let e=i,t=i,o=n<0?1:-1;return n<0&&!i?(t++,o=-1):n>=0&&i==r.nodeValue.length?(e--,o=1):n<0?e--:t++,tq(tF(tl(r,e,t),o),o<0)}if(!e.state.doc.resolve(t-(o||0)).parent.inlineContent){if(null==o&&i&&(n<0||i==tp(r))){let e=r.childNodes[i-1];if(1==e.nodeType)return tK(e.getBoundingClientRect(),!1)}if(null==o&&i<tp(r)){let e=r.childNodes[i];if(1==e.nodeType)return tK(e.getBoundingClientRect(),!0)}return tK(r.getBoundingClientRect(),n>=0)}if(null==o&&i&&(n<0||i==tp(r))){let e=r.childNodes[i-1],t=3==e.nodeType?tl(e,tp(e)-!s):1!=e.nodeType||"BR"==e.nodeName&&e.nextSibling?null:e;if(t)return tq(tF(t,1),!1)}if(null==o&&i<tp(r)){let e=r.childNodes[i];for(;e.pmViewDesc&&e.pmViewDesc.ignoreForCoords;)e=e.nextSibling;let t=e?3==e.nodeType?tl(e,0,+!s):1==e.nodeType?e:null:null;if(t)return tq(tF(t,-1),!0)}return tq(tF(3==r.nodeType?tl(r):r,-n),n>=0)}function tq(e,t){if(0==e.width)return e;let n=t?e.left:e.right;return{top:e.top,bottom:e.bottom,left:n,right:n}}function tK(e,t){if(0==e.height)return e;let n=t?e.top:e.bottom;return{top:n,bottom:n,left:e.left,right:e.right}}function tJ(e,t,n){let r=e.state,i=e.root.activeElement;r!=t&&e.updateState(t),i!=e.dom&&e.focus();try{return n()}finally{r!=t&&e.updateState(r),i!=e.dom&&i&&i.focus()}}let tU=/[\u0590-\u08ac]/,tG=null,tY=null,tX=!1;class tQ{constructor(e,t,n,r){this.parent=e,this.children=t,this.dom=n,this.contentDOM=r,this.dirty=0,n.pmViewDesc=this}matchesWidget(e){return!1}matchesMark(e){return!1}matchesNode(e,t,n){return!1}matchesHack(e){return!1}parseRule(){return null}stopEvent(e){return!1}get size(){let e=0;for(let t=0;t<this.children.length;t++)e+=this.children[t].size;return e}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let e=0;e<this.children.length;e++)this.children[e].destroy()}posBeforeChild(e){for(let t=0,n=this.posAtStart;;t++){let r=this.children[t];if(r==e)return n;n+=r.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(e,t,n){let r;if(this.contentDOM&&this.contentDOM.contains(1==e.nodeType?e:e.parentNode))if(n<0){let n,r;if(e==this.contentDOM)n=e.childNodes[t-1];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.previousSibling}for(;n&&!((r=n.pmViewDesc)&&r.parent==this);)n=n.previousSibling;return n?this.posBeforeChild(r)+r.size:this.posAtStart}else{let n,r;if(e==this.contentDOM)n=e.childNodes[t];else{for(;e.parentNode!=this.contentDOM;)e=e.parentNode;n=e.nextSibling}for(;n&&!((r=n.pmViewDesc)&&r.parent==this);)n=n.nextSibling;return n?this.posBeforeChild(r):this.posAtEnd}if(e==this.dom&&this.contentDOM)r=t>ti(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))r=2&e.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==t)for(let t=e;;t=t.parentNode){if(t==this.dom){r=!1;break}if(t.previousSibling)break}if(null==r&&t==e.childNodes.length)for(let t=e;;t=t.parentNode){if(t==this.dom){r=!0;break}if(t.nextSibling)break}}return(null==r?n>0:r)?this.posAtEnd:this.posAtStart}nearestDesc(e,t=!1){for(let n=!0,r=e;r;r=r.parentNode){let i=this.getDesc(r),o;if(i&&(!t||i.node))if(!n||!(o=i.nodeDOM)||(1==o.nodeType?o.contains(1==e.nodeType?e:e.parentNode):o==e))return i;else n=!1}}getDesc(e){let t=e.pmViewDesc;for(let e=t;e;e=e.parent)if(e==this)return t}posFromDOM(e,t,n){for(let r=e;r;r=r.parentNode){let i=this.getDesc(r);if(i)return i.localPosFromDOM(e,t,n)}return -1}descAt(e){for(let t=0,n=0;t<this.children.length;t++){let r=this.children[t],i=n+r.size;if(n==e&&i!=n){for(;!r.border&&r.children.length;)for(let e=0;e<r.children.length;e++){let t=r.children[e];if(t.size){r=t;break}}return r}if(e<i)return r.descAt(e-n-r.border);n=i}}domFromPos(e,t){if(!this.contentDOM)return{node:this.dom,offset:0,atom:e+1};let n=0,r=0;for(let t=0;n<this.children.length;n++){let i=this.children[n],o=t+i.size;if(o>e||i instanceof t5){r=e-t;break}t=o}if(r)return this.children[n].domFromPos(r-this.children[n].border,t);for(let e;n&&!(e=this.children[n-1]).size&&e instanceof tZ&&e.side>=0;n--);if(t<=0){let e,r=!0;for(;(e=n?this.children[n-1]:null)&&e.dom.parentNode!=this.contentDOM;n--,r=!1);return e&&t&&r&&!e.border&&!e.domAtom?e.domFromPos(e.size,t):{node:this.contentDOM,offset:e?ti(e.dom)+1:0}}{let e,r=!0;for(;(e=n<this.children.length?this.children[n]:null)&&e.dom.parentNode!=this.contentDOM;n++,r=!1);return e&&r&&!e.border&&!e.domAtom?e.domFromPos(0,t):{node:this.contentDOM,offset:e?ti(e.dom):this.contentDOM.childNodes.length}}}parseRange(e,t,n=0){if(0==this.children.length)return{node:this.contentDOM,from:e,to:t,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let r=-1,i=-1;for(let o=n,s=0;;s++){let n=this.children[s],l=o+n.size;if(-1==r&&e<=l){let i=o+n.border;if(e>=i&&t<=l-n.border&&n.node&&n.contentDOM&&this.contentDOM.contains(n.contentDOM))return n.parseRange(e,t,i);e=o;for(let t=s;t>0;t--){let n=this.children[t-1];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(1)){r=ti(n.dom)+1;break}e-=n.size}-1==r&&(r=0)}if(r>-1&&(l>t||s==this.children.length-1)){t=l;for(let e=s+1;e<this.children.length;e++){let n=this.children[e];if(n.size&&n.dom.parentNode==this.contentDOM&&!n.emptyChildAt(-1)){i=ti(n.dom);break}t+=n.size}-1==i&&(i=this.contentDOM.childNodes.length);break}o=l}return{node:this.contentDOM,from:e,to:t,fromOffset:r,toOffset:i}}emptyChildAt(e){if(this.border||!this.contentDOM||!this.children.length)return!1;let t=this.children[e<0?0:this.children.length-1];return 0==t.size||t.emptyChildAt(e)}domAfterPos(e){let{node:t,offset:n}=this.domFromPos(e,0);if(1!=t.nodeType||n==t.childNodes.length)throw RangeError("No node after pos "+e);return t.childNodes[n]}setSelection(e,t,n,r=!1){let i=Math.min(e,t),o=Math.max(e,t);for(let s=0,l=0;s<this.children.length;s++){let a=this.children[s],c=l+a.size;if(i>l&&o<c)return a.setSelection(e-l-a.border,t-l-a.border,n,r);l=c}let s=this.domFromPos(e,e?-1:1),l=t==e?s:this.domFromPos(t,t?-1:1),a=n.root.getSelection(),c=n.domSelectionRange(),d=!1;if((tC||tO)&&e==t){let{node:e,offset:t}=s;if(3==e.nodeType){if((d=!!(t&&"\n"==e.nodeValue[t-1]))&&t==e.nodeValue.length)for(let t=e,n;t;t=t.parentNode){if(n=t.nextSibling){"BR"==n.nodeName&&(s=l={node:n.parentNode,offset:ti(n)+1});break}let e=t.pmViewDesc;if(e&&e.node&&e.node.isBlock)break}}else{let n=e.childNodes[t-1];d=n&&("BR"==n.nodeName||"false"==n.contentEditable)}}if(tC&&c.focusNode&&c.focusNode!=l.node&&1==c.focusNode.nodeType){let e=c.focusNode.childNodes[c.focusOffset];e&&"false"==e.contentEditable&&(r=!0)}if(!(r||d&&tO)&&tc(s.node,s.offset,c.anchorNode,c.anchorOffset)&&tc(l.node,l.offset,c.focusNode,c.focusOffset))return;let h=!1;if((a.extend||e==t)&&!d){a.collapse(s.node,s.offset);try{e!=t&&a.extend(l.node,l.offset),h=!0}catch(e){}}if(!h){if(e>t){let e=s;s=l,l=e}let n=document.createRange();n.setEnd(l.node,l.offset),n.setStart(s.node,s.offset),a.removeAllRanges(),a.addRange(n)}}ignoreMutation(e){return!this.contentDOM&&"selection"!=e.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(e,t){for(let n=0,r=0;r<this.children.length;r++){let i=this.children[r],o=n+i.size;if(n==o?e<=o&&t>=n:e<o&&t>n){let r=n+i.border,s=o-i.border;if(e>=r&&t<=s){this.dirty=e==n||t==o?2:1,e==r&&t==s&&(i.contentLost||i.dom.parentNode!=this.contentDOM)?i.dirty=3:i.markDirty(e-r,t-r);return}i.dirty=i.dom!=i.contentDOM||i.dom.parentNode!=this.contentDOM||i.children.length?3:2}n=o}this.dirty=2}markParentsDirty(){let e=1;for(let t=this.parent;t;t=t.parent,e++){let n=1==e?2:1;t.dirty<n&&(t.dirty=n)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(e){return!1}}class tZ extends tQ{constructor(e,t,n,r){let i,o=t.type.toDOM;if("function"==typeof o&&(o=o(n,()=>i?i.parent?i.parent.posBeforeChild(i):void 0:r)),!t.type.spec.raw){if(1!=o.nodeType){let e=document.createElement("span");e.appendChild(o),o=e}o.contentEditable="false",o.classList.add("ProseMirror-widget")}super(e,[],o,null),this.widget=t,this.widget=t,i=this}matchesWidget(e){return 0==this.dirty&&e.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(e){let t=this.widget.spec.stopEvent;return!!t&&t(e)}ignoreMutation(e){return"selection"!=e.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}}class t0 extends tQ{constructor(e,t,n,r){super(e,[],t,null),this.textDOM=n,this.text=r}get size(){return this.text.length}localPosFromDOM(e,t){return e!=this.textDOM?this.posAtStart+(t?this.size:0):this.posAtStart+t}domFromPos(e){return{node:this.textDOM,offset:e}}ignoreMutation(e){return"characterData"===e.type&&e.target.nodeValue==e.oldValue}}class t1 extends tQ{constructor(e,t,n,r,i){super(e,[],n,r),this.mark=t,this.spec=i}static create(e,t,n,r){let i=r.nodeViews[t.type.name],o=i&&i(t,r,n);return o&&o.dom||(o=er.renderSpec(document,t.type.spec.toDOM(t,n),null,t.attrs)),new t1(e,t,o.dom,o.contentDOM||o.dom,o)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(e){return 3!=this.dirty&&this.mark.eq(e)}markDirty(e,t){if(super.markDirty(e,t),0!=this.dirty){let e=this.parent;for(;!e.node;)e=e.parent;e.dirty<this.dirty&&(e.dirty=this.dirty),this.dirty=0}}slice(e,t,n){let r=t1.create(this.parent,this.mark,!0,n),i=this.children,o=this.size;t<o&&(i=ns(i,t,o,n)),e>0&&(i=ns(i,0,e,n));for(let e=0;e<i.length;e++)i[e].parent=r;return r.children=i,r}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class t2 extends tQ{constructor(e,t,n,r,i,o,s,l,a){super(e,[],i,o),this.node=t,this.outerDeco=n,this.innerDeco=r,this.nodeDOM=s}static create(e,t,n,r,i,o){let s=i.nodeViews[t.type.name],l,a=s&&s(t,i,()=>l?l.parent?l.parent.posBeforeChild(l):void 0:o,n,r),c=a&&a.dom,d=a&&a.contentDOM;if(t.isText)if(c){if(3!=c.nodeType)throw RangeError("Text must be rendered as a DOM text node")}else c=document.createTextNode(t.text);else if(!c){let e=er.renderSpec(document,t.type.spec.toDOM(t),null,t.attrs);({dom:c,contentDOM:d}=e)}d||t.isText||"BR"==c.nodeName||(c.hasAttribute("contenteditable")||(c.contentEditable="false"),t.type.spec.draggable&&(c.draggable=!0));let h=c;return(c=nt(c,n,t),a)?l=new t6(e,t,n,r,c,d||null,h,a,i,o+1):t.isText?new t4(e,t,n,r,c,h,i):new t2(e,t,n,r,c,d||null,h,i,o+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let e={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(e.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let t=this.children.length-1;t>=0;t--){let n=this.children[t];if(this.dom.contains(n.dom.parentNode)){e.contentElement=n.dom.parentNode;break}}e.contentElement||(e.getContent=()=>p.empty)}else e.contentElement=this.contentDOM;else e.getContent=()=>this.node.content;return e}matchesNode(e,t,n){return 0==this.dirty&&e.eq(this.node)&&nn(t,this.outerDeco)&&n.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return+!this.node.isLeaf}updateChildren(e,t){let n=this.node.inlineContent,r=t,i=e.composing?this.localCompositionInfo(e,t):null,o=i&&i.pos>-1?i:null,s=i&&i.pos<0,l=new ni(this,o&&o.node,e);(function(e,t,n,r){let i=t.locals(e),o=0;if(0==i.length){for(let n=0;n<e.childCount;n++){let s=e.child(n);r(s,i,t.forChild(o,s),n),o+=s.nodeSize}return}let s=0,l=[],a=null;for(let c=0;;){let d,h,p,u;for(;s<i.length&&i[s].to==o;){let e=i[s++];e.widget&&(d?(h||(h=[d])).push(e):d=e)}if(d)if(h){h.sort(no);for(let e=0;e<h.length;e++)n(h[e],c,!!a)}else n(d,c,!!a);if(a)u=-1,p=a,a=null;else if(c<e.childCount)u=c,p=e.child(c++);else break;for(let e=0;e<l.length;e++)l[e].to<=o&&l.splice(e--,1);for(;s<i.length&&i[s].from<=o&&i[s].to>o;)l.push(i[s++]);let f=o+p.nodeSize;if(p.isText){let e=f;s<i.length&&i[s].from<e&&(e=i[s].from);for(let t=0;t<l.length;t++)l[t].to<e&&(e=l[t].to);e<f&&(a=p.cut(e-o),p=p.cut(0,e-o),f=e,u=-1)}else for(;s<i.length&&i[s].to<f;)s++;let m=p.isInline&&!p.isLeaf?l.filter(e=>!e.inline):l.slice();r(p,m,t.forChild(o,p),u),o=f}})(this.node,this.innerDeco,(t,i,o)=>{t.spec.marks?l.syncToMarks(t.spec.marks,n,e):t.type.side>=0&&!o&&l.syncToMarks(i==this.node.childCount?g.none:this.node.child(i).marks,n,e),l.placeWidget(t,e,r)},(t,o,a,c)=>{let d;l.syncToMarks(t.marks,n,e),l.findNodeMatch(t,o,a,c)||s&&e.state.selection.from>r&&e.state.selection.to<r+t.nodeSize&&(d=l.findIndexWithChild(i.node))>-1&&l.updateNodeAt(t,o,a,d,e)||l.updateNextNode(t,o,a,e,c,r)||l.addNode(t,o,a,e,r),r+=t.nodeSize}),l.syncToMarks([],n,e),this.node.isTextblock&&l.addTextblockHacks(),l.destroyRest(),(l.changed||2==this.dirty)&&(o&&this.protectLocalComposition(e,o),function e(t,n,r){let i=t.firstChild,o=!1;for(let s=0;s<n.length;s++){let l=n[s],a=l.dom;if(a.parentNode==t){for(;a!=i;)i=nr(i),o=!0;i=i.nextSibling}else o=!0,t.insertBefore(a,i);if(l instanceof t1){let n=i?i.previousSibling:t.lastChild;e(l.contentDOM,l.children,r),i=n?n.nextSibling:t.firstChild}}for(;i;)i=nr(i),o=!0;o&&r.trackWrites==t&&(r.trackWrites=null)}(this.contentDOM,this.children,e),tT&&function(e){if("UL"==e.nodeName||"OL"==e.nodeName){let t=e.style.cssText;e.style.cssText=t+"; list-style: square !important",window.getComputedStyle(e).listStyle,e.style.cssText=t}}(this.dom))}localCompositionInfo(e,t){let{from:n,to:r}=e.state.selection;if(!(e.state.selection instanceof eY)||n<t||r>t+this.node.content.size)return null;let i=e.input.compositionNode;if(!i||!this.dom.contains(i.parentNode))return null;if(!this.node.inlineContent)return{node:i,pos:-1,text:""};{let e=i.nodeValue,o=function(e,t,n,r){for(let i=0,o=0;i<e.childCount&&o<=r;){let s=e.child(i++),l=o;if(o+=s.nodeSize,!s.isText)continue;let a=s.text;for(;i<e.childCount;){let t=e.child(i++);if(o+=t.nodeSize,!t.isText)break;a+=t.text}if(o>=n){if(o>=r&&a.slice(r-t.length-l,r-l)==t)return r-t.length;let e=l<r?a.lastIndexOf(t,r-l-1):-1;if(e>=0&&e+t.length+l>=n)return l+e;if(n==r&&a.length>=r+t.length-l&&a.slice(r-l,r-l+t.length)==t)return r}}return -1}(this.node.content,e,n-t,r-t);return o<0?null:{node:i,pos:o,text:e}}}protectLocalComposition(e,{node:t,pos:n,text:r}){if(this.getDesc(t))return;let i=t;for(;i.parentNode!=this.contentDOM;i=i.parentNode){for(;i.previousSibling;)i.parentNode.removeChild(i.previousSibling);for(;i.nextSibling;)i.parentNode.removeChild(i.nextSibling);i.pmViewDesc&&(i.pmViewDesc=void 0)}let o=new t0(this,i,t,r);e.input.compositionNodes.push(o),this.children=ns(this.children,n,n+r.length,e,o)}update(e,t,n,r){return 3!=this.dirty&&!!e.sameMarkup(this.node)&&(this.updateInner(e,t,n,r),!0)}updateInner(e,t,n,r){this.updateOuterDeco(t),this.node=e,this.innerDeco=n,this.contentDOM&&this.updateChildren(r,this.posAtStart),this.dirty=0}updateOuterDeco(e){if(nn(e,this.outerDeco))return;let t=1!=this.nodeDOM.nodeType,n=this.dom;this.dom=ne(this.dom,this.nodeDOM,t9(this.outerDeco,this.node,t),t9(e,this.node,t)),this.dom!=n&&(n.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=e}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function t3(e,t,n,r,i){nt(r,t,e);let o=new t2(void 0,e,t,n,r,r,r,i,0);return o.contentDOM&&o.updateChildren(i,0),o}class t4 extends t2{constructor(e,t,n,r,i,o,s){super(e,t,n,r,i,null,o,s,0)}parseRule(){let e=this.nodeDOM.parentNode;for(;e&&e!=this.dom&&!e.pmIsDeco;)e=e.parentNode;return{skip:e||!0}}update(e,t,n,r){return 3!=this.dirty&&(0==this.dirty||!!this.inParent())&&!!e.sameMarkup(this.node)&&(this.updateOuterDeco(t),(0!=this.dirty||e.text!=this.node.text)&&e.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=e.text,r.trackWrites==this.nodeDOM&&(r.trackWrites=null)),this.node=e,this.dirty=0,!0)}inParent(){let e=this.parent.contentDOM;for(let t=this.nodeDOM;t;t=t.parentNode)if(t==e)return!0;return!1}domFromPos(e){return{node:this.nodeDOM,offset:e}}localPosFromDOM(e,t,n){return e==this.nodeDOM?this.posAtStart+Math.min(t,this.node.text.length):super.localPosFromDOM(e,t,n)}ignoreMutation(e){return"characterData"!=e.type&&"selection"!=e.type}slice(e,t,n){let r=this.node.cut(e,t),i=document.createTextNode(r.text);return new t4(this.parent,r,this.outerDeco,this.innerDeco,i,i,n)}markDirty(e,t){super.markDirty(e,t),this.dom!=this.nodeDOM&&(0==e||t==this.nodeDOM.nodeValue.length)&&(this.dirty=3)}get domAtom(){return!1}isText(e){return this.node.text==e}}class t5 extends tQ{parseRule(){return{ignore:!0}}matchesHack(e){return 0==this.dirty&&this.dom.nodeName==e}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class t6 extends t2{constructor(e,t,n,r,i,o,s,l,a,c){super(e,t,n,r,i,o,s,a,c),this.spec=l}update(e,t,n,r){if(3==this.dirty)return!1;if(this.spec.update&&(this.node.type==e.type||this.spec.multiType)){let i=this.spec.update(e,t,n);return i&&this.updateInner(e,t,n,r),i}return(!!this.contentDOM||!!e.isLeaf)&&super.update(e,t,n,r)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(e,t,n,r){this.spec.setSelection?this.spec.setSelection(e,t,n.root):super.setSelection(e,t,n,r)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(e){return!!this.spec.stopEvent&&this.spec.stopEvent(e)}ignoreMutation(e){return this.spec.ignoreMutation?this.spec.ignoreMutation(e):super.ignoreMutation(e)}}let t7=function(e){e&&(this.nodeName=e)};t7.prototype=Object.create(null);let t8=[new t7];function t9(e,t,n){if(0==e.length)return t8;let r=n?t8[0]:new t7,i=[r];for(let o=0;o<e.length;o++){let s=e[o].type.attrs;if(s)for(let e in s.nodeName&&i.push(r=new t7(s.nodeName)),s){let o=s[e];null!=o&&(n&&1==i.length&&i.push(r=new t7(t.isInline?"span":"div")),"class"==e?r.class=(r.class?r.class+" ":"")+o:"style"==e?r.style=(r.style?r.style+";":"")+o:"nodeName"!=e&&(r[e]=o))}}return i}function ne(e,t,n,r){if(n==t8&&r==t8)return t;let i=t;for(let t=0;t<r.length;t++){let o=r[t],s=n[t];if(t){let t;s&&s.nodeName==o.nodeName&&i!=e&&(t=i.parentNode)&&t.nodeName.toLowerCase()==o.nodeName||((t=document.createElement(o.nodeName)).pmIsDeco=!0,t.appendChild(i),s=t8[0]),i=t}!function(e,t,n){for(let r in t)"class"==r||"style"==r||"nodeName"==r||r in n||e.removeAttribute(r);for(let r in n)"class"!=r&&"style"!=r&&"nodeName"!=r&&n[r]!=t[r]&&e.setAttribute(r,n[r]);if(t.class!=n.class){let r=t.class?t.class.split(" ").filter(Boolean):[],i=n.class?n.class.split(" ").filter(Boolean):[];for(let t=0;t<r.length;t++)-1==i.indexOf(r[t])&&e.classList.remove(r[t]);for(let t=0;t<i.length;t++)-1==r.indexOf(i[t])&&e.classList.add(i[t]);0==e.classList.length&&e.removeAttribute("class")}if(t.style!=n.style){if(t.style){let n=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,r;for(;r=n.exec(t.style);)e.style.removeProperty(r[1])}n.style&&(e.style.cssText+=n.style)}}(i,s||t8[0],o)}return i}function nt(e,t,n){return ne(e,e,t8,t9(t,n,1!=e.nodeType))}function nn(e,t){if(e.length!=t.length)return!1;for(let n=0;n<e.length;n++)if(!e[n].type.eq(t[n].type))return!1;return!0}function nr(e){let t=e.nextSibling;return e.parentNode.removeChild(e),t}class ni{constructor(e,t,n){this.lock=t,this.view=n,this.index=0,this.stack=[],this.changed=!1,this.top=e,this.preMatch=function(e,t){let n=t,r=n.children.length,i=e.childCount,o=new Map,s=[];n:for(;i>0;){let l;for(;;)if(r){let e=n.children[r-1];if(e instanceof t1)n=e,r=e.children.length;else{l=e,r--;break}}else if(n==t)break n;else r=n.parent.children.indexOf(n),n=n.parent;let a=l.node;if(a){if(a!=e.child(i-1))break;--i,o.set(l,i),s.push(l)}}return{index:i,matched:o,matches:s.reverse()}}(e.node.content,e)}destroyBetween(e,t){if(e!=t){for(let n=e;n<t;n++)this.top.children[n].destroy();this.top.children.splice(e,t-e),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(e,t,n){let r=0,i=this.stack.length>>1,o=Math.min(i,e.length);for(;r<o&&(r==i-1?this.top:this.stack[r+1<<1]).matchesMark(e[r])&&!1!==e[r].type.spec.spanning;)r++;for(;r<i;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),i--;for(;i<e.length;){this.stack.push(this.top,this.index+1);let r=-1;for(let t=this.index;t<Math.min(this.index+3,this.top.children.length);t++){let n=this.top.children[t];if(n.matchesMark(e[i])&&!this.isLocked(n.dom)){r=t;break}}if(r>-1)r>this.index&&(this.changed=!0,this.destroyBetween(this.index,r)),this.top=this.top.children[this.index];else{let r=t1.create(this.top,e[i],t,n);this.top.children.splice(this.index,0,r),this.top=r,this.changed=!0}this.index=0,i++}}findNodeMatch(e,t,n,r){let i=-1,o;if(r>=this.preMatch.index&&(o=this.preMatch.matches[r-this.preMatch.index]).parent==this.top&&o.matchesNode(e,t,n))i=this.top.children.indexOf(o,this.index);else for(let r=this.index,o=Math.min(this.top.children.length,r+5);r<o;r++){let o=this.top.children[r];if(o.matchesNode(e,t,n)&&!this.preMatch.matched.has(o)){i=r;break}}return!(i<0)&&(this.destroyBetween(this.index,i),this.index++,!0)}updateNodeAt(e,t,n,r,i){let o=this.top.children[r];return 3==o.dirty&&o.dom==o.contentDOM&&(o.dirty=2),!!o.update(e,t,n,i)&&(this.destroyBetween(this.index,r),this.index++,!0)}findIndexWithChild(e){for(;;){let t=e.parentNode;if(!t)return -1;if(t==this.top.contentDOM){let t=e.pmViewDesc;if(t){for(let e=this.index;e<this.top.children.length;e++)if(this.top.children[e]==t)return e}return -1}e=t}}updateNextNode(e,t,n,r,i,o){for(let s=this.index;s<this.top.children.length;s++){let l=this.top.children[s];if(l instanceof t2){let a=this.preMatch.matched.get(l);if(null!=a&&a!=i)return!1;let c=l.dom,d,h=this.isLocked(c)&&!(e.isText&&l.node&&l.node.isText&&l.nodeDOM.nodeValue==e.text&&3!=l.dirty&&nn(t,l.outerDeco));if(!h&&l.update(e,t,n,r))return this.destroyBetween(this.index,s),l.dom!=c&&(this.changed=!0),this.index++,!0;if(!h&&(d=this.recreateWrapper(l,e,t,n,r,o)))return this.destroyBetween(this.index,s),this.top.children[this.index]=d,d.contentDOM&&(d.dirty=2,d.updateChildren(r,o+1),d.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(e,t,n,r,i,o){if(e.dirty||t.isAtom||!e.children.length||!e.node.content.eq(t.content)||!nn(n,e.outerDeco)||!r.eq(e.innerDeco))return null;let s=t2.create(this.top,t,n,r,i,o);if(s.contentDOM)for(let t of(s.children=e.children,e.children=[],s.children))t.parent=s;return e.destroy(),s}addNode(e,t,n,r,i){let o=t2.create(this.top,e,t,n,r,i);o.contentDOM&&o.updateChildren(r,i+1),this.top.children.splice(this.index++,0,o),this.changed=!0}placeWidget(e,t,n){let r=this.index<this.top.children.length?this.top.children[this.index]:null;if(r&&r.matchesWidget(e)&&(e==r.widget||!r.widget.type.toDOM.parentNode))this.index++;else{let r=new tZ(this.top,e,t,n);this.top.children.splice(this.index++,0,r),this.changed=!0}}addTextblockHacks(){let e=this.top.children[this.index-1],t=this.top;for(;e instanceof t1;)e=(t=e).children[t.children.length-1];(!e||!(e instanceof t4)||/\n$/.test(e.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(e.node.text))&&((tO||tN)&&e&&"false"==e.dom.contentEditable&&this.addHackNode("IMG",t),this.addHackNode("BR",this.top))}addHackNode(e,t){if(t==this.top&&this.index<t.children.length&&t.children[this.index].matchesHack(e))this.index++;else{let n=document.createElement(e);"IMG"==e&&(n.className="ProseMirror-separator",n.alt=""),"BR"==e&&(n.className="ProseMirror-trailingBreak");let r=new t5(this.top,[],n,null);t!=this.top?t.children.push(r):t.children.splice(this.index++,0,r),this.changed=!0}}isLocked(e){return this.lock&&(e==this.lock||1==e.nodeType&&e.contains(this.lock.parentNode))}}function no(e,t){return e.type.side-t.type.side}function ns(e,t,n,r,i){let o=[];for(let s=0,l=0;s<e.length;s++){let a=e[s],c=l,d=l+=a.size;c>=n||d<=t?o.push(a):(c<t&&o.push(a.slice(0,t-c,r)),i&&(o.push(i),i=void 0),d>n&&o.push(a.slice(n-c,a.size,r)))}return o}function nl(e,t=null){let n=e.domSelectionRange(),r=e.state.doc;if(!n.focusNode)return null;let i=e.docView.nearestDesc(n.focusNode),o=i&&0==i.size,s=e.docView.posFromDOM(n.focusNode,n.focusOffset,1);if(s<0)return null;let l=r.resolve(s),a,c;if(tf(n)){for(a=s;i&&!i.node;)i=i.parent;let e=i.node;if(i&&e.isAtom&&eQ.isSelectable(e)&&i.parent&&!(e.isInline&&function(e,t,n){for(let r=0==t,i=t==tp(e);r||i;){if(e==n)return!0;let t=ti(e);if(!(e=e.parentNode))return!1;r=r&&0==t,i=i&&t==tp(e)}}(n.focusNode,n.focusOffset,i.dom))){let e=i.posBefore;c=new eQ(s==e?l:r.resolve(e))}}else{if(n instanceof e.dom.ownerDocument.defaultView.Selection&&n.rangeCount>1){let t=s,i=s;for(let r=0;r<n.rangeCount;r++){let o=n.getRangeAt(r);t=Math.min(t,e.docView.posFromDOM(o.startContainer,o.startOffset,1)),i=Math.max(i,e.docView.posFromDOM(o.endContainer,o.endOffset,-1))}if(t<0)return null;[a,s]=i==e.state.selection.anchor?[i,t]:[t,i],l=r.resolve(s)}else a=e.docView.posFromDOM(n.anchorNode,n.anchorOffset,1);if(a<0)return null}let d=r.resolve(a);if(!c){let n="pointer"==t||e.state.selection.head<l.pos&&!o?1:-1;c=ng(e,d,l,n)}return c}function na(e){return e.editable?e.hasFocus():nb(e)&&document.activeElement&&document.activeElement.contains(e.dom)}function nc(e,t=!1){let n=e.state.selection;if(nf(e,n),na(e)){if(!t&&e.input.mouseDown&&e.input.mouseDown.allowDefault&&tN){let t=e.domSelectionRange(),n=e.domObserver.currentSelection;if(t.anchorNode&&n.anchorNode&&tc(t.anchorNode,t.anchorOffset,n.anchorNode,n.anchorOffset)){e.input.mouseDown.delayedSelectionSync=!0,e.domObserver.setCurSelection();return}}if(e.domObserver.disconnectSelection(),e.cursorWrapper)!function(e){let t=e.domSelection(),n=document.createRange();if(!t)return;let r=e.cursorWrapper.dom,i="IMG"==r.nodeName;i?n.setStart(r.parentNode,ti(r)+1):n.setStart(r,0),n.collapse(!0),t.removeAllRanges(),t.addRange(n),!i&&!e.state.selection.visible&&tk&&tS<=11&&(r.disabled=!0,r.disabled=!1)}(e);else{var r;let i,o,s,l,{anchor:a,head:c}=n,d,h;nd&&!(n instanceof eY)&&(n.$from.parent.inlineContent||(d=nh(e,n.from)),n.empty||n.$from.parent.inlineContent||(h=nh(e,n.to))),e.docView.setSelection(a,c,e,t),nd&&(d&&nu(d),h&&nu(h)),n.visible?e.dom.classList.remove("ProseMirror-hideselection"):(e.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&((i=(r=e).dom.ownerDocument).removeEventListener("selectionchange",r.input.hideSelectionGuard),s=(o=r.domSelectionRange()).anchorNode,l=o.anchorOffset,i.addEventListener("selectionchange",r.input.hideSelectionGuard=()=>{(o.anchorNode!=s||o.anchorOffset!=l)&&(i.removeEventListener("selectionchange",r.input.hideSelectionGuard),setTimeout(()=>{(!na(r)||r.state.selection.visible)&&r.dom.classList.remove("ProseMirror-hideselection")},20))})))}e.domObserver.setCurSelection(),e.domObserver.connectSelection()}}let nd=tO||tN&&tA<63;function nh(e,t){let{node:n,offset:r}=e.docView.domFromPos(t,0),i=r<n.childNodes.length?n.childNodes[r]:null,o=r?n.childNodes[r-1]:null;if(tO&&i&&"false"==i.contentEditable)return np(i);if((!i||"false"==i.contentEditable)&&(!o||"false"==o.contentEditable)){if(i)return np(i);else if(o)return np(o)}}function np(e){return e.contentEditable="true",tO&&e.draggable&&(e.draggable=!1,e.wasDraggable=!0),e}function nu(e){e.contentEditable="false",e.wasDraggable&&(e.draggable=!0,e.wasDraggable=null)}function nf(e,t){if(t instanceof eQ){let n=e.docView.descAt(t.from);n!=e.lastSelectedViewDesc&&(nm(e),n&&n.selectNode(),e.lastSelectedViewDesc=n)}else nm(e)}function nm(e){e.lastSelectedViewDesc&&(e.lastSelectedViewDesc.parent&&e.lastSelectedViewDesc.deselectNode(),e.lastSelectedViewDesc=void 0)}function ng(e,t,n,r){return e.someProp("createSelectionBetween",r=>r(e,t,n))||eY.between(t,n,r)}function ny(e){return(!e.editable||!!e.hasFocus())&&nb(e)}function nb(e){let t=e.domSelectionRange();if(!t.anchorNode)return!1;try{return e.dom.contains(3==t.anchorNode.nodeType?t.anchorNode.parentNode:t.anchorNode)&&(e.editable||e.dom.contains(3==t.focusNode.nodeType?t.focusNode.parentNode:t.focusNode))}catch(e){return!1}}function nw(e,t){let{$anchor:n,$head:r}=e.selection,i=t>0?n.max(r):n.min(r),o=i.parent.inlineContent?i.depth?e.doc.resolve(t>0?i.after():i.before()):null:i;return o&&eK.findFrom(o,t)}function nv(e,t){return e.dispatch(e.state.tr.setSelection(t).scrollIntoView()),!0}function nx(e,t,n){let r=e.state.selection;if(r instanceof eY){if(n.indexOf("s")>-1){let{$head:n}=r,i=n.textOffset?null:t<0?n.nodeBefore:n.nodeAfter;if(!i||i.isText||!i.isLeaf)return!1;let o=e.state.doc.resolve(n.pos+i.nodeSize*(t<0?-1:1));return nv(e,new eY(r.$anchor,o))}else if(!r.empty)return!1;else if(e.endOfTextblock(t>0?"forward":"backward")){let n=nw(e.state,t);return!!n&&n instanceof eQ&&nv(e,n)}else if(!(tE&&n.indexOf("m")>-1)){let n=r.$head,i=n.textOffset?null:t<0?n.nodeBefore:n.nodeAfter,o;if(!i||i.isText)return!1;let s=t<0?n.pos-i.nodeSize:n.pos;return!!(i.isAtom||(o=e.docView.descAt(s))&&!o.contentDOM)&&(eQ.isSelectable(i)?nv(e,new eQ(t<0?e.state.doc.resolve(n.pos-i.nodeSize):n)):!!tI&&nv(e,new eY(e.state.doc.resolve(t<0?s:s+i.nodeSize))))}}else{if(r instanceof eQ&&r.node.isInline)return nv(e,new eY(t>0?r.$to:r.$from));let n=nw(e.state,t);return!!n&&nv(e,n)}}function nk(e){return 3==e.nodeType?e.nodeValue.length:e.childNodes.length}function nS(e,t){let n=e.pmViewDesc;return n&&0==n.size&&(t<0||e.nextSibling||"BR"!=e.nodeName)}function nC(e,t){return t<0?function(e){let t=e.domSelectionRange(),n=t.focusNode,r=t.focusOffset;if(!n)return;let i,o,s=!1;for(tC&&1==n.nodeType&&r<nk(n)&&nS(n.childNodes[r],-1)&&(s=!0);;)if(r>0)if(1!=n.nodeType)break;else{let e=n.childNodes[r-1];if(nS(e,-1))i=n,o=--r;else if(3==e.nodeType)r=(n=e).nodeValue.length;else break}else if(nM(n))break;else{let t=n.previousSibling;for(;t&&nS(t,-1);)i=n.parentNode,o=ti(t),t=t.previousSibling;if(t)r=nk(n=t);else{if((n=n.parentNode)==e.dom)break;r=0}}s?nN(e,n,r):i&&nN(e,i,o)}(e):function(e){let t,n,r=e.domSelectionRange(),i=r.focusNode,o=r.focusOffset;if(!i)return;let s=nk(i);for(;;)if(o<s){if(1!=i.nodeType)break;if(nS(i.childNodes[o],1))t=i,n=++o;else break}else if(nM(i))break;else{let r=i.nextSibling;for(;r&&nS(r,1);)t=r.parentNode,n=ti(r)+1,r=r.nextSibling;if(r)o=0,s=nk(i=r);else{if((i=i.parentNode)==e.dom)break;o=s=0}}t&&nN(e,t,n)}(e)}function nM(e){let t=e.pmViewDesc;return t&&t.node&&t.node.isBlock}function nN(e,t,n){if(3!=t.nodeType){let e,r;(r=function(e,t){for(;e&&t==e.childNodes.length&&!tu(e);)t=ti(e)+1,e=e.parentNode;for(;e&&t<e.childNodes.length;){let n=e.childNodes[t];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;e=n,t=0}}(t,n))?(t=r,n=0):(e=function(e,t){for(;e&&!t&&!tu(e);)t=ti(e),e=e.parentNode;for(;e&&t;){let n=e.childNodes[t-1];if(3==n.nodeType)return n;if(1==n.nodeType&&"false"==n.contentEditable)break;t=(e=n).childNodes.length}}(t,n))&&(t=e,n=e.nodeValue.length)}let r=e.domSelection();if(!r)return;if(tf(r)){let e=document.createRange();e.setEnd(t,n),e.setStart(t,n),r.removeAllRanges(),r.addRange(e)}else r.extend&&r.extend(t,n);e.domObserver.setCurSelection();let{state:i}=e;setTimeout(()=>{e.state==i&&nc(e)},50)}function nA(e,t){let n=e.state.doc.resolve(t);if(!(tN||tD)&&n.parent.inlineContent){let r=e.coordsAtPos(t);if(t>n.start()){let n=e.coordsAtPos(t-1),i=(n.top+n.bottom)/2;if(i>r.top&&i<r.bottom&&Math.abs(n.left-r.left)>1)return n.left<r.left?"ltr":"rtl"}if(t<n.end()){let n=e.coordsAtPos(t+1),i=(n.top+n.bottom)/2;if(i>r.top&&i<r.bottom&&Math.abs(n.left-r.left)>1)return n.left>r.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(e.dom).direction?"rtl":"ltr"}function nO(e,t,n){let r=e.state.selection;if(r instanceof eY&&!r.empty||n.indexOf("s")>-1||tE&&n.indexOf("m")>-1)return!1;let{$from:i,$to:o}=r;if(!i.parent.inlineContent||e.endOfTextblock(t<0?"up":"down")){let n=nw(e.state,t);if(n&&n instanceof eQ)return nv(e,n)}if(!i.parent.inlineContent){let n=t<0?i:o,s=r instanceof e0?eK.near(n,t):eK.findFrom(n,t);return!!s&&nv(e,s)}return!1}function nT(e,t){if(!(e.state.selection instanceof eY))return!0;let{$head:n,$anchor:r,empty:i}=e.state.selection;if(!n.sameParent(r))return!0;if(!i)return!1;if(e.endOfTextblock(t>0?"forward":"backward"))return!0;let o=!n.textOffset&&(t<0?n.nodeBefore:n.nodeAfter);if(o&&!o.isText){let r=e.state.tr;return t<0?r.delete(n.pos-o.nodeSize,n.pos):r.delete(n.pos,n.pos+o.nodeSize),e.dispatch(r),!0}return!1}function nE(e,t,n){e.domObserver.stop(),t.contentEditable=n,e.domObserver.start()}function nD(e,t){e.someProp("transformCopied",n=>{t=n(t,e)});let n=[],{content:r,openStart:i,openEnd:o}=t;for(;i>1&&o>1&&1==r.childCount&&1==r.firstChild.childCount;){i--,o--;let e=r.firstChild;n.push(e.type.name,e.attrs!=e.type.defaultAttrs?e.attrs:null),r=e.content}let s=e.someProp("clipboardSerializer")||er.fromSchema(e.state.schema),l=nB(),a=l.createElement("div");a.appendChild(s.serializeFragment(r,{document:l}));let c=a.firstChild,d,h=0;for(;c&&1==c.nodeType&&(d=nz[c.nodeName.toLowerCase()]);){for(let e=d.length-1;e>=0;e--){let t=l.createElement(d[e]);for(;a.firstChild;)t.appendChild(a.firstChild);a.appendChild(t),h++}c=a.firstChild}return c&&1==c.nodeType&&c.setAttribute("data-pm-slice",`${i} ${o}${h?` -${h}`:""} ${JSON.stringify(n)}`),{dom:a,text:e.someProp("clipboardTextSerializer",n=>n(t,e))||t.content.textBetween(0,t.content.size,"\n\n"),slice:t}}function nR(e,t,n,r,i){let o,s,l=i.parent.type.spec.code;if(!n&&!t)return null;let a=t&&(r||l||!n);if(a){if(e.someProp("transformPastedText",n=>{t=n(t,l||r,e)}),l)return t?new b(p.from(e.state.schema.text(t.replace(/\r\n?/g,"\n"))),0,0):b.empty;let n=e.someProp("clipboardTextParser",n=>n(t,i,r,e));if(n)s=n;else{let n=i.marks(),{schema:r}=e.state,s=er.fromSchema(r);o=document.createElement("div"),t.split(/(?:\r\n?|\n)+/).forEach(e=>{let t=o.appendChild(document.createElement("p"));e&&t.appendChild(s.serializeNode(r.text(e,n)))})}}else e.someProp("transformPastedHTML",t=>{n=t(n,e)}),o=function(e){var t;let n,r=/^(\s*<meta [^>]*>)*/.exec(e);r&&(e=e.slice(r[0].length));let i=nB().createElement("div"),o=/<([a-z][^>\s]+)/i.exec(e),s;if((s=o&&nz[o[1].toLowerCase()])&&(e=s.map(e=>"<"+e+">").join("")+e+s.map(e=>"</"+e+">").reverse().join("")),i.innerHTML=(t=e,(n=window.trustedTypes)?(nH||(nH=n.defaultPolicy||n.createPolicy("ProseMirrorClipboard",{createHTML:e=>e})),nH.createHTML(t)):t),s)for(let e=0;e<s.length;e++)i=i.querySelector(s[e])||i;return i}(n),tI&&function(e){let t=e.querySelectorAll(tN?"span:not([class]):not([style])":"span.Apple-converted-space");for(let n=0;n<t.length;n++){let r=t[n];1==r.childNodes.length&&"\xa0"==r.textContent&&r.parentNode&&r.parentNode.replaceChild(e.ownerDocument.createTextNode(" "),r)}}(o);let c=o&&o.querySelector("[data-pm-slice]"),d=c&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(c.getAttribute("data-pm-slice")||"");if(d&&d[3])for(let e=+d[3];e>0;e--){let e=o.firstChild;for(;e&&1!=e.nodeType;)e=e.nextSibling;if(!e)break;o=e}if(s||(s=(e.someProp("clipboardParser")||e.someProp("domParser")||U.fromSchema(e.state.schema)).parseSlice(o,{preserveWhitespace:!!(a||d),context:i,ruleFromNode:e=>"BR"!=e.nodeName||e.nextSibling||!e.parentNode||nI.test(e.parentNode.nodeName)?null:{ignore:!0}})),d)s=function(e,t){if(!e.size)return e;let n=e.content.firstChild.type.schema,r;try{r=JSON.parse(t)}catch(t){return e}let{content:i,openStart:o,openEnd:s}=e;for(let e=r.length-2;e>=0;e-=2){let t=n.nodes[r[e]];if(!t||t.hasRequiredAttrs())break;i=p.from(t.create(r[e+1],i)),o++,s++}return new b(i,o,s)}(nj(s,+d[1],+d[2]),d[4]);else if((s=b.maxOpen(function(e,t){if(e.childCount<2)return e;for(let n=t.depth;n>=0;n--){let r=t.node(n).contentMatchAt(t.index(n)),i,o=[];if(e.forEach(e=>{if(!o)return;let t=r.findWrapping(e.type),n;if(!t)return o=null;if(n=o.length&&i.length&&function e(t,n,r,i,o){if(o<t.length&&o<n.length&&t[o]==n[o]){let s=e(t,n,r,i.lastChild,o+1);if(s)return i.copy(i.content.replaceChild(i.childCount-1,s));if(i.contentMatchAt(i.childCount).matchType(o==t.length-1?r.type:t[o+1]))return i.copy(i.content.append(p.from(nP(r,t,o+1))))}}(t,i,e,o[o.length-1],0))o[o.length-1]=n;else{o.length&&(o[o.length-1]=function e(t,n){if(0==n)return t;let r=t.content.replaceChild(t.childCount-1,e(t.lastChild,n-1)),i=t.contentMatchAt(t.childCount).fillBefore(p.empty,!0);return t.copy(r.append(i))}(o[o.length-1],i.length));let n=nP(e,t);o.push(n),r=r.matchType(n.type),i=t}}),o)return p.from(o)}return e}(s.content,i),!0)).openStart||s.openEnd){let e=0,t=0;for(let t=s.content.firstChild;e<s.openStart&&!t.type.spec.isolating;e++,t=t.firstChild);for(let e=s.content.lastChild;t<s.openEnd&&!e.type.spec.isolating;t++,e=e.lastChild);s=nj(s,e,t)}return e.someProp("transformPasted",t=>{s=t(s,e)}),s}let nI=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function nP(e,t,n=0){for(let r=t.length-1;r>=n;r--)e=t[r].create(null,p.from(e));return e}function nL(e,t,n,r,i,o){let s=t<0?e.firstChild:e.lastChild,l=s.content;return e.childCount>1&&(o=0),i<r-1&&(l=nL(l,t,n,r,i+1,o)),i>=n&&(l=t<0?s.contentMatchAt(0).fillBefore(l,o<=i).append(l):l.append(s.contentMatchAt(s.childCount).fillBefore(p.empty,!0))),e.replaceChild(t<0?0:e.childCount-1,s.copy(l))}function nj(e,t,n){return t<e.openStart&&(e=new b(nL(e.content,-1,t,e.openStart,0,e.openEnd),t,e.openEnd)),n<e.openEnd&&(e=new b(nL(e.content,1,n,e.openEnd,0,0),e.openStart,n)),e}let nz={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]},n$=null;function nB(){return n$||(n$=document.implementation.createHTMLDocument("title"))}let nH=null,nV={},nF={},nW={touchstart:!0,touchmove:!0};class n_{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function nq(e,t){e.input.lastSelectionOrigin=t,e.input.lastSelectionTime=Date.now()}function nK(e){e.someProp("handleDOMEvents",t=>{for(let n in t)e.input.eventHandlers[n]||e.dom.addEventListener(n,e.input.eventHandlers[n]=t=>nJ(e,t))})}function nJ(e,t){return e.someProp("handleDOMEvents",n=>{let r=n[t.type];return!!r&&(r(e,t)||t.defaultPrevented)})}function nU(e){return{left:e.clientX,top:e.clientY}}function nG(e,t,n,r,i){if(-1==r)return!1;let o=e.state.doc.resolve(r);for(let r=o.depth+1;r>0;r--)if(e.someProp(t,t=>r>o.depth?t(e,n,o.nodeAfter,o.before(r),i,!0):t(e,n,o.node(r),o.before(r),i,!1)))return!0;return!1}function nY(e,t,n){if(e.focused||e.focus(),e.state.selection.eq(t))return;let r=e.state.tr.setSelection(t);"pointer"==n&&r.setMeta("pointer",!0),e.dispatch(r)}nF.keydown=(e,t)=>{if((e.input.shiftKey=16==t.keyCode||t.shiftKey,!nZ(e,t))&&(e.input.lastKeyCode=t.keyCode,e.input.lastKeyCodeTime=Date.now(),!tR||!tN||13!=t.keyCode))if(229!=t.keyCode&&e.domObserver.forceFlush(),!tT||13!=t.keyCode||t.ctrlKey||t.altKey||t.metaKey)e.someProp("handleKeyDown",n=>n(e,t))||function(e,t){let n,r=t.keyCode,i=(n="",t.ctrlKey&&(n+="c"),t.metaKey&&(n+="m"),t.altKey&&(n+="a"),t.shiftKey&&(n+="s"),n);if(8==r||tE&&72==r&&"c"==i)return nT(e,-1)||nC(e,-1);if(46==r&&!t.shiftKey||tE&&68==r&&"c"==i)return nT(e,1)||nC(e,1);if(13==r||27==r)return!0;if(37==r||tE&&66==r&&"c"==i){let t=37==r?"ltr"==nA(e,e.state.selection.from)?-1:1:-1;return nx(e,t,i)||nC(e,t)}if(39==r||tE&&70==r&&"c"==i){let t=39==r?"ltr"==nA(e,e.state.selection.from)?1:-1:1;return nx(e,t,i)||nC(e,t)}else if(38==r||tE&&80==r&&"c"==i)return nO(e,-1,i)||nC(e,-1);else if(40==r||tE&&78==r&&"c"==i)return function(e){if(!tO||e.state.selection.$head.parentOffset>0)return!1;let{focusNode:t,focusOffset:n}=e.domSelectionRange();if(t&&1==t.nodeType&&0==n&&t.firstChild&&"false"==t.firstChild.contentEditable){let n=t.firstChild;nE(e,n,"true"),setTimeout(()=>nE(e,n,"false"),20)}return!1}(e)||nO(e,1,i)||nC(e,1);else if(i==(tE?"m":"c")&&(66==r||73==r||89==r||90==r))return!0;return!1}(e,t)?t.preventDefault():nq(e,"key");else{let t=Date.now();e.input.lastIOSEnter=t,e.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{e.input.lastIOSEnter==t&&(e.someProp("handleKeyDown",t=>t(e,tm(13,"Enter"))),e.input.lastIOSEnter=0)},200)}},nF.keyup=(e,t)=>{16==t.keyCode&&(e.input.shiftKey=!1)},nF.keypress=(e,t)=>{if(nZ(e,t)||!t.charCode||t.ctrlKey&&!t.altKey||tE&&t.metaKey)return;if(e.someProp("handleKeyPress",n=>n(e,t)))return void t.preventDefault();let n=e.state.selection;if(!(n instanceof eY)||!n.$from.sameParent(n.$to)){let r=String.fromCharCode(t.charCode),i=()=>e.state.tr.insertText(r).scrollIntoView();/[\r\n]/.test(r)||e.someProp("handleTextInput",t=>t(e,n.$from.pos,n.$to.pos,r,i))||e.dispatch(i()),t.preventDefault()}};let nX=tE?"metaKey":"ctrlKey";nV.mousedown=(e,t)=>{e.input.shiftKey=t.shiftKey;let n=n3(e),r=Date.now(),i="singleClick";r-e.input.lastClick.time<500&&function(e,t){let n=t.x-e.clientX,r=t.y-e.clientY;return n*n+r*r<100}(t,e.input.lastClick)&&!t[nX]&&e.input.lastClick.button==t.button&&("singleClick"==e.input.lastClick.type?i="doubleClick":"doubleClick"==e.input.lastClick.type&&(i="tripleClick")),e.input.lastClick={time:r,x:t.clientX,y:t.clientY,type:i,button:t.button};let o=e.posAtCoords(nU(t));o&&("singleClick"==i?(e.input.mouseDown&&e.input.mouseDown.done(),e.input.mouseDown=new nQ(e,o,t,!!n)):("doubleClick"==i?function(e,t,n,r){return nG(e,"handleDoubleClickOn",t,n,r)||e.someProp("handleDoubleClick",n=>n(e,t,r))}:function(e,t,n,r){return nG(e,"handleTripleClickOn",t,n,r)||e.someProp("handleTripleClick",n=>n(e,t,r))||function(e,t,n){if(0!=n.button)return!1;let r=e.state.doc;if(-1==t)return!!r.inlineContent&&(nY(e,eY.create(r,0,r.content.size),"pointer"),!0);let i=r.resolve(t);for(let t=i.depth+1;t>0;t--){let n=t>i.depth?i.nodeAfter:i.node(t),o=i.before(t);if(n.inlineContent)nY(e,eY.create(r,o+1,o+1+n.content.size),"pointer");else{if(!eQ.isSelectable(n))continue;nY(e,eQ.create(r,o),"pointer")}return!0}}(e,n,r)})(e,o.pos,o.inside,t)?t.preventDefault():nq(e,"pointer"))};class nQ{constructor(e,t,n,r){let i,o;if(this.view=e,this.pos=t,this.event=n,this.flushed=r,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=e.state.doc,this.selectNode=!!n[nX],this.allowDefault=n.shiftKey,t.inside>-1)i=e.state.doc.nodeAt(t.inside),o=t.inside;else{let n=e.state.doc.resolve(t.pos);i=n.parent,o=n.depth?n.before():0}let s=r?null:n.target,l=s?e.docView.nearestDesc(s,!0):null;this.target=l&&1==l.dom.nodeType?l.dom:null;let{selection:a}=e.state;(0==n.button&&i.type.spec.draggable&&!1!==i.type.spec.selectable||a instanceof eQ&&a.from<=o&&a.to>o)&&(this.mightDrag={node:i,pos:o,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&tC&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),e.root.addEventListener("mouseup",this.up=this.up.bind(this)),e.root.addEventListener("mousemove",this.move=this.move.bind(this)),nq(e,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>nc(this.view)),this.view.input.mouseDown=null}up(e){if(this.done(),!this.view.dom.contains(e.target))return;let t=this.pos;if(this.view.state.doc!=this.startDoc&&(t=this.view.posAtCoords(nU(e))),this.updateAllowDefault(e),this.allowDefault||!t)nq(this.view,"pointer");else{var n,r,i,o;(n=this.view,r=t.pos,i=t.inside,o=this.selectNode,nG(n,"handleClickOn",r,i,e)||n.someProp("handleClick",t=>t(n,r,e))||(o?function(e,t){if(-1==t)return!1;let n=e.state.selection,r,i;n instanceof eQ&&(r=n.node);let o=e.state.doc.resolve(t);for(let e=o.depth+1;e>0;e--){let t=e>o.depth?o.nodeAfter:o.node(e);if(eQ.isSelectable(t)){i=r&&n.$from.depth>0&&e>=n.$from.depth&&o.before(n.$from.depth+1)==n.$from.pos?o.before(n.$from.depth):o.before(e);break}}return null!=i&&(nY(e,eQ.create(e.state.doc,i),"pointer"),!0)}(n,i):function(e,t){if(-1==t)return!1;let n=e.state.doc.resolve(t),r=n.nodeAfter;return!!(r&&r.isAtom&&eQ.isSelectable(r))&&(nY(e,new eQ(n),"pointer"),!0)}(n,i)))?e.preventDefault():0==e.button&&(this.flushed||tO&&this.mightDrag&&!this.mightDrag.node.isAtom||tN&&!this.view.state.selection.visible&&2>=Math.min(Math.abs(t.pos-this.view.state.selection.from),Math.abs(t.pos-this.view.state.selection.to)))?(nY(this.view,eK.near(this.view.state.doc.resolve(t.pos)),"pointer"),e.preventDefault()):nq(this.view,"pointer")}}move(e){this.updateAllowDefault(e),nq(this.view,"pointer"),0==e.buttons&&this.done()}updateAllowDefault(e){!this.allowDefault&&(Math.abs(this.event.x-e.clientX)>4||Math.abs(this.event.y-e.clientY)>4)&&(this.allowDefault=!0)}}function nZ(e,t){return!!e.composing||!!(tO&&500>Math.abs(t.timeStamp-e.input.compositionEndedAt))&&(e.input.compositionEndedAt=-2e8,!0)}nV.touchstart=e=>{e.input.lastTouch=Date.now(),n3(e),nq(e,"pointer")},nV.touchmove=e=>{e.input.lastTouch=Date.now(),nq(e,"pointer")},nV.contextmenu=e=>n3(e);let n0=tR?5e3:-1;function n1(e,t){clearTimeout(e.input.composingTimeout),t>-1&&(e.input.composingTimeout=setTimeout(()=>n3(e),t))}function n2(e){let t;for(e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=((t=document.createEvent("Event")).initEvent("event",!0,!0),t.timeStamp));e.input.compositionNodes.length>0;)e.input.compositionNodes.pop().markParentsDirty()}function n3(e,t=!1){if(!tR||!(e.domObserver.flushingSoon>=0)){if(e.domObserver.forceFlush(),n2(e),t||e.docView&&e.docView.dirty){let n=nl(e),r=e.state.selection;return n&&!n.eq(r)?e.dispatch(e.state.tr.setSelection(n)):(e.markCursor||t)&&!r.$from.node(r.$from.sharedDepth(r.to)).inlineContent?e.dispatch(e.state.tr.deleteSelection()):e.updateState(e.state),!0}return!1}}nF.compositionstart=nF.compositionupdate=e=>{if(!e.composing){e.domObserver.flush();let{state:t}=e,n=t.selection.$to;if(t.selection instanceof eY&&(t.storedMarks||!n.textOffset&&n.parentOffset&&n.nodeBefore.marks.some(e=>!1===e.type.spec.inclusive)))e.markCursor=e.state.storedMarks||n.marks(),n3(e,!0),e.markCursor=null;else if(n3(e,!t.selection.empty),tC&&t.selection.empty&&n.parentOffset&&!n.textOffset&&n.nodeBefore.marks.length){let t=e.domSelectionRange();for(let n=t.focusNode,r=t.focusOffset;n&&1==n.nodeType&&0!=r;){let t=r<0?n.lastChild:n.childNodes[r-1];if(!t)break;if(3==t.nodeType){let n=e.domSelection();n&&n.collapse(t,t.nodeValue.length);break}n=t,r=-1}}e.input.composing=!0}n1(e,n0)},nF.compositionend=(e,t)=>{e.composing&&(e.input.composing=!1,e.input.compositionEndedAt=t.timeStamp,e.input.compositionPendingChanges=e.domObserver.pendingRecords().length?e.input.compositionID:0,e.input.compositionNode=null,e.input.compositionPendingChanges&&Promise.resolve().then(()=>e.domObserver.flush()),e.input.compositionID++,n1(e,20))};let n4=tk&&tS<15||tT&&tP<604;function n5(e,t,n,r,i){let o=nR(e,t,n,r,e.state.selection.$from);if(e.someProp("handlePaste",t=>t(e,i,o||b.empty)))return!0;if(!o)return!1;let s=0==o.openStart&&0==o.openEnd&&1==o.content.childCount?o.content.firstChild:null,l=s?e.state.tr.replaceSelectionWith(s,r):e.state.tr.replaceSelection(o);return e.dispatch(l.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function n6(e){let t=e.getData("text/plain")||e.getData("Text");if(t)return t;let n=e.getData("text/uri-list");return n?n.replace(/\r?\n/g," "):""}nV.copy=nF.cut=(e,t)=>{let n=e.state.selection,r="cut"==t.type;if(n.empty)return;let i=n4?null:t.clipboardData,{dom:o,text:s}=nD(e,n.content());i?(t.preventDefault(),i.clearData(),i.setData("text/html",o.innerHTML),i.setData("text/plain",s)):function(e,t){if(!e.dom.parentNode)return;let n=e.dom.parentNode.appendChild(document.createElement("div"));n.appendChild(t),n.style.cssText="position: fixed; left: -10000px; top: 10px";let r=getSelection(),i=document.createRange();i.selectNodeContents(t),e.dom.blur(),r.removeAllRanges(),r.addRange(i),setTimeout(()=>{n.parentNode&&n.parentNode.removeChild(n),e.focus()},50)}(e,o),r&&e.dispatch(e.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},nF.paste=(e,t)=>{if(e.composing&&!tR)return;let n=n4?null:t.clipboardData,r=e.input.shiftKey&&45!=e.input.lastKeyCode;n&&n5(e,n6(n),n.getData("text/html"),r,t)?t.preventDefault():function(e,t){if(!e.dom.parentNode)return;let n=e.input.shiftKey||e.state.selection.$from.parent.type.spec.code,r=e.dom.parentNode.appendChild(document.createElement(n?"textarea":"div"));n||(r.contentEditable="true"),r.style.cssText="position: fixed; left: -10000px; top: 10px",r.focus();let i=e.input.shiftKey&&45!=e.input.lastKeyCode;setTimeout(()=>{e.focus(),r.parentNode&&r.parentNode.removeChild(r),n?n5(e,r.value,null,i,t):n5(e,r.textContent,r.innerHTML,i,t)},50)}(e,t)};class n7{constructor(e,t,n){this.slice=e,this.move=t,this.node=n}}let n8=tE?"altKey":"ctrlKey";function n9(e,t){let n=e.someProp("dragCopies",e=>!e(t));return null!=n?n:!t[n8]}for(let e in nV.dragstart=(e,t)=>{let n,r=e.input.mouseDown;if(r&&r.done(),!t.dataTransfer)return;let i=e.state.selection,o=i.empty?null:e.posAtCoords(nU(t));if(o&&o.pos>=i.from&&o.pos<=(i instanceof eQ?i.to-1:i.to));else if(r&&r.mightDrag)n=eQ.create(e.state.doc,r.mightDrag.pos);else if(t.target&&1==t.target.nodeType){let r=e.docView.nearestDesc(t.target,!0);r&&r.node.type.spec.draggable&&r!=e.docView&&(n=eQ.create(e.state.doc,r.posBefore))}let s=(n||e.state.selection).content(),{dom:l,text:a,slice:c}=nD(e,s);t.dataTransfer.files.length&&tN&&!(tA>120)||t.dataTransfer.clearData(),t.dataTransfer.setData(n4?"Text":"text/html",l.innerHTML),t.dataTransfer.effectAllowed="copyMove",n4||t.dataTransfer.setData("text/plain",a),e.dragging=new n7(c,n9(e,t),n)},nV.dragend=e=>{let t=e.dragging;window.setTimeout(()=>{e.dragging==t&&(e.dragging=null)},50)},nF.dragover=nF.dragenter=(e,t)=>t.preventDefault(),nF.drop=(e,t)=>{let n=e.dragging;if(e.dragging=null,!t.dataTransfer)return;let r=e.posAtCoords(nU(t));if(!r)return;let i=e.state.doc.resolve(r.pos),o=n&&n.slice;o?e.someProp("transformPasted",t=>{o=t(o,e)}):o=nR(e,n6(t.dataTransfer),n4?null:t.dataTransfer.getData("text/html"),!1,i);let s=!!(n&&n9(e,t));if(e.someProp("handleDrop",n=>n(e,t,o||b.empty,s)))return void t.preventDefault();if(!o)return;t.preventDefault();let l=o?eR(e.state.doc,i.pos,o):i.pos;null==l&&(l=i.pos);let a=e.state.tr;if(s){let{node:e}=n;e?e.replace(a):a.deleteSelection()}let c=a.mapping.map(l),d=0==o.openStart&&0==o.openEnd&&1==o.content.childCount,h=a.doc;if(d?a.replaceRangeWith(c,c,o.content.firstChild):a.replaceRange(c,c,o),a.doc.eq(h))return;let p=a.doc.resolve(c);if(d&&eQ.isSelectable(o.content.firstChild)&&p.nodeAfter&&p.nodeAfter.sameMarkup(o.content.firstChild))a.setSelection(new eQ(p));else{let t=a.mapping.map(l);a.mapping.maps[a.mapping.maps.length-1].forEach((e,n,r,i)=>t=i),a.setSelection(ng(e,p,a.doc.resolve(t)))}e.focus(),e.dispatch(a.setMeta("uiEvent","drop"))},nV.focus=e=>{e.input.lastFocus=Date.now(),e.focused||(e.domObserver.stop(),e.dom.classList.add("ProseMirror-focused"),e.domObserver.start(),e.focused=!0,setTimeout(()=>{e.docView&&e.hasFocus()&&!e.domObserver.currentSelection.eq(e.domSelectionRange())&&nc(e)},20))},nV.blur=(e,t)=>{e.focused&&(e.domObserver.stop(),e.dom.classList.remove("ProseMirror-focused"),e.domObserver.start(),t.relatedTarget&&e.dom.contains(t.relatedTarget)&&e.domObserver.currentSelection.clear(),e.focused=!1)},nV.beforeinput=(e,t)=>{if(tN&&tR&&"deleteContentBackward"==t.inputType){e.domObserver.flushSoon();let{domChangeCount:t}=e.input;setTimeout(()=>{if(e.input.domChangeCount!=t||(e.dom.blur(),e.focus(),e.someProp("handleKeyDown",t=>t(e,tm(8,"Backspace")))))return;let{$cursor:n}=e.state.selection;n&&n.pos>0&&e.dispatch(e.state.tr.delete(n.pos-1,n.pos).scrollIntoView())},50)}},nF)nV[e]=nF[e];function re(e,t){if(e==t)return!0;for(let n in e)if(e[n]!==t[n])return!1;for(let n in t)if(!(n in e))return!1;return!0}class rt{constructor(e,t){this.toDOM=e,this.spec=t||rs,this.side=this.spec.side||0}map(e,t,n,r){let{pos:i,deleted:o}=e.mapResult(t.from+r,this.side<0?-1:1);return o?null:new ri(i-n,i-n,this)}valid(){return!0}eq(e){return this==e||e instanceof rt&&(this.spec.key&&this.spec.key==e.spec.key||this.toDOM==e.toDOM&&re(this.spec,e.spec))}destroy(e){this.spec.destroy&&this.spec.destroy(e)}}class rn{constructor(e,t){this.attrs=e,this.spec=t||rs}map(e,t,n,r){let i=e.map(t.from+r,this.spec.inclusiveStart?-1:1)-n,o=e.map(t.to+r,this.spec.inclusiveEnd?1:-1)-n;return i>=o?null:new ri(i,o,this)}valid(e,t){return t.from<t.to}eq(e){return this==e||e instanceof rn&&re(this.attrs,e.attrs)&&re(this.spec,e.spec)}static is(e){return e.type instanceof rn}destroy(){}}class rr{constructor(e,t){this.attrs=e,this.spec=t||rs}map(e,t,n,r){let i=e.mapResult(t.from+r,1);if(i.deleted)return null;let o=e.mapResult(t.to+r,-1);return o.deleted||o.pos<=i.pos?null:new ri(i.pos-n,o.pos-n,this)}valid(e,t){let{index:n,offset:r}=e.content.findIndex(t.from),i;return r==t.from&&!(i=e.child(n)).isText&&r+i.nodeSize==t.to}eq(e){return this==e||e instanceof rr&&re(this.attrs,e.attrs)&&re(this.spec,e.spec)}destroy(){}}class ri{constructor(e,t,n){this.from=e,this.to=t,this.type=n}copy(e,t){return new ri(e,t,this.type)}eq(e,t=0){return this.type.eq(e.type)&&this.from+t==e.from&&this.to+t==e.to}map(e,t,n){return this.type.map(e,this,t,n)}static widget(e,t,n){return new ri(e,e,new rt(t,n))}static inline(e,t,n,r){return new ri(e,t,new rn(n,r))}static node(e,t,n,r){return new ri(e,t,new rr(n,r))}get spec(){return this.type.spec}get inline(){return this.type instanceof rn}get widget(){return this.type instanceof rt}}let ro=[],rs={};class rl{constructor(e,t){this.local=e.length?e:ro,this.children=t.length?t:ro}static create(e,t){return t.length?ru(t,e,0,rs):ra}find(e,t,n){let r=[];return this.findInner(null==e?0:e,null==t?1e9:t,r,0,n),r}findInner(e,t,n,r,i){for(let o=0;o<this.local.length;o++){let s=this.local[o];s.from<=t&&s.to>=e&&(!i||i(s.spec))&&n.push(s.copy(s.from+r,s.to+r))}for(let o=0;o<this.children.length;o+=3)if(this.children[o]<t&&this.children[o+1]>e){let s=this.children[o]+1;this.children[o+2].findInner(e-s,t-s,n,r+s,i)}}map(e,t,n){return this==ra||0==e.maps.length?this:this.mapInner(e,t,0,0,n||rs)}mapInner(e,t,n,r,i){let o;for(let s=0;s<this.local.length;s++){let l=this.local[s].map(e,n,r);l&&l.type.valid(t,l)?(o||(o=[])).push(l):i.onRemove&&i.onRemove(this.local[s].spec)}return this.children.length?function(e,t,n,r,i,o,s){let l=e.slice();for(let e=0,t=o;e<n.maps.length;e++){let r=0;n.maps[e].forEach((e,n,i,o)=>{let s=o-i-(n-e);for(let i=0;i<l.length;i+=3){let o=l[i+1];if(o<0||e>o+t-r)continue;let a=l[i]+t-r;n>=a?l[i+1]=e<=a?-2:-1:e>=t&&s&&(l[i]+=s,l[i+1]+=s)}r+=s}),t=n.maps[e].map(t,-1)}let a=!1;for(let t=0;t<l.length;t+=3)if(l[t+1]<0){if(-2==l[t+1]){a=!0,l[t+1]=-1;continue}let c=n.map(e[t]+o),d=c-i;if(d<0||d>=r.content.size){a=!0;continue}let h=n.map(e[t+1]+o,-1)-i,{index:p,offset:u}=r.content.findIndex(d),f=r.maybeChild(p);if(f&&u==d&&u+f.nodeSize==h){let r=l[t+2].mapInner(n,f,c+1,e[t]+o+1,s);r!=ra?(l[t]=d,l[t+1]=h,l[t+2]=r):(l[t+1]=-2,a=!0)}else a=!0}if(a){let a=ru(function(e,t,n,r,i,o,s){for(let l=0;l<e.length;l+=3)-1==e[l+1]&&function e(t,o){for(let e=0;e<t.local.length;e++){let l=t.local[e].map(r,i,o);l?n.push(l):s.onRemove&&s.onRemove(t.local[e].spec)}for(let n=0;n<t.children.length;n+=3)e(t.children[n+2],t.children[n]+o+1)}(e[l+2],t[l]+o+1);return n}(l,e,t,n,i,o,s),r,0,s);t=a.local;for(let e=0;e<l.length;e+=3)l[e+1]<0&&(l.splice(e,3),e-=3);for(let e=0,t=0;e<a.children.length;e+=3){let n=a.children[e];for(;t<l.length&&l[t]<n;)t+=3;l.splice(t,0,a.children[e],a.children[e+1],a.children[e+2])}}return new rl(t.sort(rf),l)}(this.children,o||[],e,t,n,r,i):o?new rl(o.sort(rf),ro):ra}add(e,t){return t.length?this==ra?rl.create(e,t):this.addInner(e,t,0):this}addInner(e,t,n){let r,i=0;e.forEach((e,o)=>{let s=o+n,l;if(l=rh(t,e,s)){for(r||(r=this.children.slice());i<r.length&&r[i]<o;)i+=3;r[i]==o?r[i+2]=r[i+2].addInner(e,l,s+1):r.splice(i,0,o,o+e.nodeSize,ru(l,e,s+1,rs)),i+=3}});let o=rd(i?rp(t):t,-n);for(let t=0;t<o.length;t++)o[t].type.valid(e,o[t])||o.splice(t--,1);return new rl(o.length?this.local.concat(o).sort(rf):this.local,r||this.children)}remove(e){return 0==e.length||this==ra?this:this.removeInner(e,0)}removeInner(e,t){let n=this.children,r=this.local;for(let r=0;r<n.length;r+=3){let i,o=n[r]+t,s=n[r+1]+t;for(let t=0,n;t<e.length;t++)(n=e[t])&&n.from>o&&n.to<s&&(e[t]=null,(i||(i=[])).push(n));if(!i)continue;n==this.children&&(n=this.children.slice());let l=n[r+2].removeInner(i,o+1);l!=ra?n[r+2]=l:(n.splice(r,3),r-=3)}if(r.length){for(let n=0,i;n<e.length;n++)if(i=e[n])for(let e=0;e<r.length;e++)r[e].eq(i,t)&&(r==this.local&&(r=this.local.slice()),r.splice(e--,1))}return n==this.children&&r==this.local?this:r.length||n.length?new rl(r,n):ra}forChild(e,t){let n,r;if(this==ra)return this;if(t.isLeaf)return rl.empty;for(let t=0;t<this.children.length;t+=3)if(this.children[t]>=e){this.children[t]==e&&(n=this.children[t+2]);break}let i=e+1,o=i+t.content.size;for(let e=0;e<this.local.length;e++){let t=this.local[e];if(t.from<o&&t.to>i&&t.type instanceof rn){let e=Math.max(i,t.from)-i,n=Math.min(o,t.to)-i;e<n&&(r||(r=[])).push(t.copy(e,n))}}if(r){let e=new rl(r.sort(rf),ro);return n?new rc([e,n]):e}return n||ra}eq(e){if(this==e)return!0;if(!(e instanceof rl)||this.local.length!=e.local.length||this.children.length!=e.children.length)return!1;for(let t=0;t<this.local.length;t++)if(!this.local[t].eq(e.local[t]))return!1;for(let t=0;t<this.children.length;t+=3)if(this.children[t]!=e.children[t]||this.children[t+1]!=e.children[t+1]||!this.children[t+2].eq(e.children[t+2]))return!1;return!0}locals(e){return rm(this.localsInner(e))}localsInner(e){if(this==ra)return ro;if(e.inlineContent||!this.local.some(rn.is))return this.local;let t=[];for(let e=0;e<this.local.length;e++)this.local[e].type instanceof rn||t.push(this.local[e]);return t}forEachSet(e){e(this)}}rl.empty=new rl([],[]),rl.removeOverlap=rm;let ra=rl.empty;class rc{constructor(e){this.members=e}map(e,t){let n=this.members.map(n=>n.map(e,t,rs));return rc.from(n)}forChild(e,t){if(t.isLeaf)return rl.empty;let n=[];for(let r=0;r<this.members.length;r++){let i=this.members[r].forChild(e,t);i!=ra&&(i instanceof rc?n=n.concat(i.members):n.push(i))}return rc.from(n)}eq(e){if(!(e instanceof rc)||e.members.length!=this.members.length)return!1;for(let t=0;t<this.members.length;t++)if(!this.members[t].eq(e.members[t]))return!1;return!0}locals(e){let t,n=!0;for(let r=0;r<this.members.length;r++){let i=this.members[r].localsInner(e);if(i.length)if(t){n&&(t=t.slice(),n=!1);for(let e=0;e<i.length;e++)t.push(i[e])}else t=i}return t?rm(n?t:t.sort(rf)):ro}static from(e){switch(e.length){case 0:return ra;case 1:return e[0];default:return new rc(e.every(e=>e instanceof rl)?e:e.reduce((e,t)=>e.concat(t instanceof rl?t:t.members),[]))}}forEachSet(e){for(let t=0;t<this.members.length;t++)this.members[t].forEachSet(e)}}function rd(e,t){if(!t||!e.length)return e;let n=[];for(let r=0;r<e.length;r++){let i=e[r];n.push(new ri(i.from+t,i.to+t,i.type))}return n}function rh(e,t,n){if(t.isLeaf)return null;let r=n+t.nodeSize,i=null;for(let t=0,o;t<e.length;t++)(o=e[t])&&o.from>n&&o.to<r&&((i||(i=[])).push(o),e[t]=null);return i}function rp(e){let t=[];for(let n=0;n<e.length;n++)null!=e[n]&&t.push(e[n]);return t}function ru(e,t,n,r){let i=[],o=!1;t.forEach((t,s)=>{let l=rh(e,t,s+n);if(l){o=!0;let e=ru(l,t,n+s+1,r);e!=ra&&i.push(s,s+t.nodeSize,e)}});let s=rd(o?rp(e):e,-n).sort(rf);for(let e=0;e<s.length;e++)s[e].type.valid(t,s[e])||(r.onRemove&&r.onRemove(s[e].spec),s.splice(e--,1));return s.length||i.length?new rl(s,i):ra}function rf(e,t){return e.from-t.from||e.to-t.to}function rm(e){let t=e;for(let n=0;n<t.length-1;n++){let r=t[n];if(r.from!=r.to)for(let i=n+1;i<t.length;i++){let o=t[i];if(o.from==r.from){o.to!=r.to&&(t==e&&(t=e.slice()),t[i]=o.copy(o.from,r.to),rg(t,i+1,o.copy(r.to,o.to)));continue}o.from<r.to&&(t==e&&(t=e.slice()),t[n]=r.copy(r.from,o.from),rg(t,i,r.copy(o.from,r.to)));break}}return t}function rg(e,t,n){for(;t<e.length&&rf(n,e[t])>0;)t++;e.splice(t,0,n)}function ry(e){let t=[];return e.someProp("decorations",n=>{let r=n(e.state);r&&r!=ra&&t.push(r)}),e.cursorWrapper&&t.push(rl.create(e.state.doc,[e.cursorWrapper.deco])),rc.from(t)}let rb={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},rw=tk&&tS<=11;class rv{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(e){this.anchorNode=e.anchorNode,this.anchorOffset=e.anchorOffset,this.focusNode=e.focusNode,this.focusOffset=e.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(e){return e.anchorNode==this.anchorNode&&e.anchorOffset==this.anchorOffset&&e.focusNode==this.focusNode&&e.focusOffset==this.focusOffset}}class rx{constructor(e,t){this.view=e,this.handleDOMChange=t,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new rv,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(e=>{for(let t=0;t<e.length;t++)this.queue.push(e[t]);tk&&tS<=11&&e.some(e=>"childList"==e.type&&e.removedNodes.length||"characterData"==e.type&&e.oldValue.length>e.target.nodeValue.length)?this.flushSoon():this.flush()}),rw&&(this.onCharData=e=>{this.queue.push({target:e.target,type:"characterData",oldValue:e.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,rb)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let e=this.observer.takeRecords();if(e.length){for(let t=0;t<e.length;t++)this.queue.push(e[t]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(ny(this.view)){if(this.suppressingSelectionUpdates)return nc(this.view);if(tk&&tS<=11&&!this.view.state.selection.empty){let e=this.view.domSelectionRange();if(e.focusNode&&tc(e.focusNode,e.focusOffset,e.anchorNode,e.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(e){if(!e.focusNode)return!0;let t=new Set,n;for(let n=e.focusNode;n;n=to(n))t.add(n);for(let r=e.anchorNode;r;r=to(r))if(t.has(r)){n=r;break}let r=n&&this.view.docView.nearestDesc(n);if(r&&r.ignoreMutation({type:"selection",target:3==n.nodeType?n.parentNode:n}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let e of this.observer.takeRecords())this.queue.push(e);return this.queue}flush(){var e;let{view:t}=this;if(!t.docView||this.flushingSoon>-1)return;let n=this.pendingRecords();n.length&&(this.queue=[]);let r=t.domSelectionRange(),i=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(r)&&ny(t)&&!this.ignoreSelectionChange(r),o=-1,s=-1,l=!1,a=[];if(t.editable)for(let e=0;e<n.length;e++){let t=this.registerMutation(n[e],a);t&&(o=o<0?t.from:Math.min(t.from,o),s=s<0?t.to:Math.max(t.to,s),t.typeOver&&(l=!0))}if(tC&&a.length){let e=a.filter(e=>"BR"==e.nodeName);if(2==e.length){let[t,n]=e;t.parentNode&&t.parentNode.parentNode==n.parentNode?n.remove():t.remove()}else{let{focusNode:n}=this.currentSelection;for(let r of e){let e=r.parentNode;e&&"LI"==e.nodeName&&(!n||function(e,t){for(let n=t.parentNode;n&&n!=e.dom;n=n.parentNode){let t=e.docView.nearestDesc(n,!0);if(t&&t.node.isBlock)return n}return null}(t,n)!=e)&&r.remove()}}}let c=null;o<0&&i&&t.input.lastFocus>Date.now()-200&&Math.max(t.input.lastTouch,t.input.lastClick.time)<Date.now()-300&&tf(r)&&(c=nl(t))&&c.eq(eK.near(t.state.doc.resolve(0),1))?(t.input.lastFocus=0,nc(t),this.currentSelection.set(r),t.scrollToSelection()):(o>-1||i)&&(o>-1&&(t.docView.markDirty(o,s),e=t,!rk.has(e)&&(rk.set(e,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(e.dom).whiteSpace))&&(e.requiresGeckoHackNode=tC,rS||(console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),rS=!0))),this.handleDOMChange(o,s,l,a),t.docView&&t.docView.dirty?t.updateState(t.state):this.currentSelection.eq(r)||nc(t),this.currentSelection.set(r))}registerMutation(e,t){if(t.indexOf(e.target)>-1)return null;let n=this.view.docView.nearestDesc(e.target);if("attributes"==e.type&&(n==this.view.docView||"contenteditable"==e.attributeName||"style"==e.attributeName&&!e.oldValue&&!e.target.getAttribute("style"))||!n||n.ignoreMutation(e))return null;if("childList"==e.type){for(let n=0;n<e.addedNodes.length;n++){let r=e.addedNodes[n];t.push(r),3==r.nodeType&&(this.lastChangedTextNode=r)}if(n.contentDOM&&n.contentDOM!=n.dom&&!n.contentDOM.contains(e.target))return{from:n.posBefore,to:n.posAfter};let r=e.previousSibling,i=e.nextSibling;if(tk&&tS<=11&&e.addedNodes.length)for(let t=0;t<e.addedNodes.length;t++){let{previousSibling:n,nextSibling:o}=e.addedNodes[t];(!n||0>Array.prototype.indexOf.call(e.addedNodes,n))&&(r=n),(!o||0>Array.prototype.indexOf.call(e.addedNodes,o))&&(i=o)}let o=r&&r.parentNode==e.target?ti(r)+1:0,s=n.localPosFromDOM(e.target,o,-1),l=i&&i.parentNode==e.target?ti(i):e.target.childNodes.length;return{from:s,to:n.localPosFromDOM(e.target,l,1)}}return"attributes"==e.type?{from:n.posAtStart-n.border,to:n.posAtEnd+n.border}:(this.lastChangedTextNode=e.target,{from:n.posAtStart,to:n.posAtEnd,typeOver:e.target.nodeValue==e.oldValue})}}let rk=new WeakMap,rS=!1;function rC(e,t){let n=t.startContainer,r=t.startOffset,i=t.endContainer,o=t.endOffset,s=e.domAtPos(e.state.selection.anchor);return tc(s.node,s.offset,i,o)&&([n,r,i,o]=[i,o,n,r]),{anchorNode:n,anchorOffset:r,focusNode:i,focusOffset:o}}function rM(e){let t=e.pmViewDesc;if(t)return t.parseRule();if("BR"==e.nodeName&&e.parentNode){if(tO&&/^(ul|ol)$/i.test(e.parentNode.nodeName)){let e=document.createElement("div");return e.appendChild(document.createElement("li")),{skip:e}}else if(e.parentNode.lastChild==e||tO&&/^(tr|table)$/i.test(e.parentNode.nodeName))return{ignore:!0}}else if("IMG"==e.nodeName&&e.getAttribute("mark-placeholder"))return{ignore:!0};return null}let rN=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function rA(e,t,n){return Math.max(n.anchor,n.head)>t.content.size?null:ng(e,t.resolve(n.anchor),t.resolve(n.head))}function rO(e,t,n){let r=e.depth,i=t?e.end():e.pos;for(;r>0&&(t||e.indexAfter(r)==e.node(r).childCount);)r--,i++,t=!1;if(n){let t=e.node(r).maybeChild(e.indexAfter(r));for(;t&&!t.isLeaf;)t=t.firstChild,i++}return i}function rT(e){if(2!=e.length)return!1;let t=e.charCodeAt(0),n=e.charCodeAt(1);return t>=56320&&t<=57343&&n>=55296&&n<=56319}class rE{constructor(e,t){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new n_,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=t,this.state=t.state,this.directPlugins=t.plugins||[],this.directPlugins.forEach(rL),this.dispatch=this.dispatch.bind(this),this.dom=e&&e.mount||document.createElement("div"),e&&(e.appendChild?e.appendChild(this.dom):"function"==typeof e?e(this.dom):e.mount&&(this.mounted=!0)),this.editable=rI(this),rR(this),this.nodeViews=rP(this),this.docView=t3(this.state.doc,rD(this),ry(this),this.dom,this),this.domObserver=new rx(this,(e,t,n,r)=>(function(e,t,n,r,i){let o,s,l,a,c=e.input.compositionPendingChanges||(e.composing?e.input.compositionID:0);if(e.input.compositionPendingChanges=0,t<0){let t=e.input.lastSelectionTime>Date.now()-50?e.input.lastSelectionOrigin:null,n=nl(e,t);if(n&&!e.state.selection.eq(n)){if(tN&&tR&&13===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime&&e.someProp("handleKeyDown",t=>t(e,tm(13,"Enter"))))return;let r=e.state.tr.setSelection(n);"pointer"==t?r.setMeta("pointer",!0):"key"==t&&r.scrollIntoView(),c&&r.setMeta("composition",c),e.dispatch(r)}return}let d=e.state.doc.resolve(t),h=d.sharedDepth(n);t=d.before(h+1),n=e.state.doc.resolve(n).after(h+1);let u=e.state.selection,f=function(e,t,n){let r,{node:i,fromOffset:o,toOffset:s,from:l,to:a}=e.docView.parseRange(t,n),c=e.domSelectionRange(),d=c.anchorNode;if(d&&e.dom.contains(1==d.nodeType?d:d.parentNode)&&(r=[{node:d,offset:c.anchorOffset}],tf(c)||r.push({node:c.focusNode,offset:c.focusOffset})),tN&&8===e.input.lastKeyCode)for(let e=s;e>o;e--){let t=i.childNodes[e-1],n=t.pmViewDesc;if("BR"==t.nodeName&&!n){s=e;break}if(!n||n.size)break}let h=e.state.doc,p=e.someProp("domParser")||U.fromSchema(e.state.schema),u=h.resolve(l),f=null,m=p.parse(i,{topNode:u.parent,topMatch:u.parent.contentMatchAt(u.index()),topOpen:!0,from:o,to:s,preserveWhitespace:"pre"!=u.parent.type.whitespace||"full",findPositions:r,ruleFromNode:rM,context:u});if(r&&null!=r[0].pos){let e=r[0].pos,t=r[1]&&r[1].pos;null==t&&(t=e),f={anchor:e+l,head:t+l}}return{doc:m,sel:f,from:l,to:a}}(e,t,n),m=e.state.doc,g=m.slice(f.from,f.to);8===e.input.lastKeyCode&&Date.now()-100<e.input.lastKeyCodeTime?(o=e.state.selection.to,s="end"):(o=e.state.selection.from,s="start"),e.input.lastKeyCode=null;let y=function(e,t,n,r,i){let o=e.findDiffStart(t,n);if(null==o)return null;let{a:s,b:l}=e.findDiffEnd(t,n+e.size,n+t.size);if("end"==i){let e=Math.max(0,o-Math.min(s,l));r-=s+e-o}if(s<o&&e.size<t.size){let e=r<=o&&r>=s?o-r:0;(o-=e)&&o<t.size&&rT(t.textBetween(o-1,o+1))&&(o+=e?1:-1),l=o+(l-s),s=o}else if(l<o){let t=r<=o&&r>=l?o-r:0;(o-=t)&&o<e.size&&rT(e.textBetween(o-1,o+1))&&(o+=t?1:-1),s=o+(s-l),l=o}return{start:o,endA:s,endB:l}}(g.content,f.doc.content,f.from,o,s);if(y&&e.input.domChangeCount++,(tT&&e.input.lastIOSEnter>Date.now()-225||tR)&&i.some(e=>1==e.nodeType&&!rN.test(e.nodeName))&&(!y||y.endA>=y.endB)&&e.someProp("handleKeyDown",t=>t(e,tm(13,"Enter")))){e.input.lastIOSEnter=0;return}if(!y)if(r&&u instanceof eY&&!u.empty&&u.$head.sameParent(u.$anchor)&&!e.composing&&!(f.sel&&f.sel.anchor!=f.sel.head))y={start:u.from,endA:u.to,endB:u.to};else{if(f.sel){let t=rA(e,e.state.doc,f.sel);if(t&&!t.eq(e.state.selection)){let n=e.state.tr.setSelection(t);c&&n.setMeta("composition",c),e.dispatch(n)}}return}e.state.selection.from<e.state.selection.to&&y.start==y.endB&&e.state.selection instanceof eY&&(y.start>e.state.selection.from&&y.start<=e.state.selection.from+2&&e.state.selection.from>=f.from?y.start=e.state.selection.from:y.endA<e.state.selection.to&&y.endA>=e.state.selection.to-2&&e.state.selection.to<=f.to&&(y.endB+=e.state.selection.to-y.endA,y.endA=e.state.selection.to)),tk&&tS<=11&&y.endB==y.start+1&&y.endA==y.start&&y.start>f.from&&" \xa0"==f.doc.textBetween(y.start-f.from-1,y.start-f.from+1)&&(y.start--,y.endA--,y.endB--);let b=f.doc.resolveNoCache(y.start-f.from),w=f.doc.resolveNoCache(y.endB-f.from),v=m.resolve(y.start),x=b.sameParent(w)&&b.parent.inlineContent&&v.end()>=y.endA;if((tT&&e.input.lastIOSEnter>Date.now()-225&&(!x||i.some(e=>"DIV"==e.nodeName||"P"==e.nodeName))||!x&&b.pos<f.doc.content.size&&(!b.sameParent(w)||!b.parent.inlineContent)&&!/\S/.test(f.doc.textBetween(b.pos,w.pos,"",""))&&(l=eK.findFrom(f.doc.resolve(b.pos+1),1,!0))&&l.head>b.pos)&&e.someProp("handleKeyDown",t=>t(e,tm(13,"Enter")))){e.input.lastIOSEnter=0;return}if(e.state.selection.anchor>y.start&&function(e,t,n,r,i){if(n-t<=i.pos-r.pos||rO(r,!0,!1)<i.pos)return!1;let o=e.resolve(t);if(!r.parent.isTextblock){let e=o.nodeAfter;return null!=e&&n==t+e.nodeSize}if(o.parentOffset<o.parent.content.size||!o.parent.isTextblock)return!1;let s=e.resolve(rO(o,!0,!0));return!(!s.parent.isTextblock||s.pos>n||rO(s,!0,!1)<n)&&r.parent.content.cut(r.parentOffset).eq(s.parent.content)}(m,y.start,y.endA,b,w)&&e.someProp("handleKeyDown",t=>t(e,tm(8,"Backspace")))){tR&&tN&&e.domObserver.suppressSelectionUpdates();return}tN&&y.endB==y.start&&(e.input.lastChromeDelete=Date.now()),tR&&!x&&b.start()!=w.start()&&0==w.parentOffset&&b.depth==w.depth&&f.sel&&f.sel.anchor==f.sel.head&&f.sel.head==y.endA&&(y.endB-=2,w=f.doc.resolveNoCache(y.endB-f.from),setTimeout(()=>{e.someProp("handleKeyDown",function(t){return t(e,tm(13,"Enter"))})},20));let k=y.start,S=y.endA,C=t=>{let n=t||e.state.tr.replace(k,S,f.doc.slice(y.start-f.from,y.endB-f.from));if(f.sel){let t=rA(e,n.doc,f.sel);t&&!(tN&&e.composing&&t.empty&&(y.start!=y.endB||e.input.lastChromeDelete<Date.now()-100)&&(t.head==k||t.head==n.mapping.map(S)-1)||tk&&t.empty&&t.head==k)&&n.setSelection(t)}return c&&n.setMeta("composition",c),n.scrollIntoView()};if(x){if(b.pos==w.pos){tk&&tS<=11&&0==b.parentOffset&&(e.domObserver.suppressSelectionUpdates(),setTimeout(()=>nc(e),20));let t=C(e.state.tr.delete(k,S)),n=m.resolve(y.start).marksAcross(m.resolve(y.endA));n&&t.ensureMarks(n),e.dispatch(t)}else if(y.endA==y.endB&&(a=function(e,t){let n=e.firstChild.marks,r=t.firstChild.marks,i=n,o=r,s,l,a;for(let e=0;e<r.length;e++)i=r[e].removeFromSet(i);for(let e=0;e<n.length;e++)o=n[e].removeFromSet(o);if(1==i.length&&0==o.length)l=i[0],s="add",a=e=>e.mark(l.addToSet(e.marks));else{if(0!=i.length||1!=o.length)return null;l=o[0],s="remove",a=e=>e.mark(l.removeFromSet(e.marks))}let c=[];for(let e=0;e<t.childCount;e++)c.push(a(t.child(e)));if(p.from(c).eq(e))return{mark:l,type:s}}(b.parent.content.cut(b.parentOffset,w.parentOffset),v.parent.content.cut(v.parentOffset,y.endA-v.start())))){let t=C(e.state.tr);"add"==a.type?t.addMark(k,S,a.mark):t.removeMark(k,S,a.mark),e.dispatch(t)}else if(b.parent.child(b.index()).isText&&b.index()==w.index()-!w.textOffset){let t=b.parent.textBetween(b.parentOffset,w.parentOffset),n=()=>C(e.state.tr.insertText(t,k,S));e.someProp("handleTextInput",r=>r(e,k,S,t,n))||e.dispatch(n())}}else e.dispatch(C())})(this,e,t,n,r)),this.domObserver.start(),function(e){for(let t in nV){let n=nV[t];e.dom.addEventListener(t,e.input.eventHandlers[t]=t=>{!function(e,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let n=t.target;n!=e.dom;n=n.parentNode)if(!n||11==n.nodeType||n.pmViewDesc&&n.pmViewDesc.stopEvent(t))return!1;return!0}(e,t)||nJ(e,t)||!e.editable&&t.type in nF||n(e,t)},nW[t]?{passive:!0}:void 0)}tO&&e.dom.addEventListener("input",()=>null),nK(e)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let e=this._props;for(let t in this._props={},e)this._props[t]=e[t];this._props.state=this.state}return this._props}update(e){e.handleDOMEvents!=this._props.handleDOMEvents&&nK(this);let t=this._props;this._props=e,e.plugins&&(e.plugins.forEach(rL),this.directPlugins=e.plugins),this.updateStateInner(e.state,t)}setProps(e){let t={};for(let e in this._props)t[e]=this._props[e];for(let n in t.state=this.state,e)t[n]=e[n];this.update(t)}updateState(e){this.updateStateInner(e,this._props)}updateStateInner(e,t){var n,r,i;let o=this.state,s=!1,l=!1;e.storedMarks&&this.composing&&(n2(this),l=!0),this.state=e;let a=o.plugins!=e.plugins||this._props.plugins!=t.plugins;if(a||this._props.plugins!=t.plugins||this._props.nodeViews!=t.nodeViews){let e=rP(this);(function(e,t){let n=0,r=0;for(let r in e){if(e[r]!=t[r])return!0;n++}for(let e in t)r++;return n!=r})(e,this.nodeViews)&&(this.nodeViews=e,s=!0)}(a||t.handleDOMEvents!=this._props.handleDOMEvents)&&nK(this),this.editable=rI(this),rR(this);let c=ry(this),d=rD(this),h=o.plugins==e.plugins||o.doc.eq(e.doc)?e.scrollToSelection>o.scrollToSelection?"to selection":"preserve":"reset",p=s||!this.docView.matchesNode(e.doc,d,c);(p||!e.selection.eq(o.selection))&&(l=!0);let u="preserve"==h&&l&&null==this.dom.style.overflowAnchor&&function(e){let t,n,r=e.dom.getBoundingClientRect(),i=Math.max(0,r.top);for(let o=(r.left+r.right)/2,s=i+1;s<Math.min(innerHeight,r.bottom);s+=5){let r=e.root.elementFromPoint(o,s);if(!r||r==e.dom||!e.dom.contains(r))continue;let l=r.getBoundingClientRect();if(l.top>=i-20){t=r,n=l.top;break}}return{refDOM:t,refTop:n,stack:tz(e.dom)}}(this);if(l){let t,n,l;this.domObserver.stop();let a=p&&(tk||tN)&&!this.composing&&!o.selection.empty&&!e.selection.empty&&(r=o.selection,i=e.selection,l=Math.min(r.$anchor.sharedDepth(r.head),i.$anchor.sharedDepth(i.head)),r.$anchor.start(l)!=i.$anchor.start(l));if(p){let t=tN?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=function(e){let t=e.domSelectionRange();if(!t.focusNode)return null;let n=function(e,t){for(;;){if(3==e.nodeType&&t)return e;if(1==e.nodeType&&t>0){if("false"==e.contentEditable)return null;t=tp(e=e.childNodes[t-1])}else{if(!e.parentNode||tu(e))return null;t=ti(e),e=e.parentNode}}}(t.focusNode,t.focusOffset),r=function(e,t){for(;;){if(3==e.nodeType&&t<e.nodeValue.length)return e;if(1==e.nodeType&&t<e.childNodes.length){if("false"==e.contentEditable)return null;e=e.childNodes[t],t=0}else{if(!e.parentNode||tu(e))return null;t=ti(e)+1,e=e.parentNode}}}(t.focusNode,t.focusOffset);if(n&&r&&n!=r){let t=r.pmViewDesc,i=e.domObserver.lastChangedTextNode;if(n==i||r==i)return i;if(!t||!t.isText(r.nodeValue))return r;if(e.input.compositionNode==r){let e=n.pmViewDesc;if(!(!e||!e.isText(n.nodeValue)))return r}}return n||r}(this)),(s||!this.docView.update(e.doc,d,c,this))&&(this.docView.updateOuterDeco(d),this.docView.destroy(),this.docView=t3(e.doc,d,c,this.dom,this)),t&&!this.trackWrites&&(a=!0)}a||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&(t=this.docView.domFromPos(this.state.selection.anchor,0),n=this.domSelectionRange(),tc(t.node,t.offset,n.anchorNode,n.anchorOffset)))?nc(this,a):(nf(this,e.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(o),(null==(n=this.dragging)?void 0:n.node)&&!o.doc.eq(e.doc)&&this.updateDraggedNode(this.dragging,o),"reset"==h?this.dom.scrollTop=0:"to selection"==h?this.scrollToSelection():u&&function({refDOM:e,refTop:t,stack:n}){let r=e?e.getBoundingClientRect().top:0;t$(n,0==r?0:r-t)}(u)}scrollToSelection(){let e=this.domSelectionRange().focusNode;if(e&&this.dom.contains(1==e.nodeType?e:e.parentNode))if(this.someProp("handleScrollToSelection",e=>e(this)));else if(this.state.selection instanceof eQ){let t=this.docView.domAfterPos(this.state.selection.from);1==t.nodeType&&tj(this,t.getBoundingClientRect(),e)}else tj(this,this.coordsAtPos(this.state.selection.head,1),e)}destroyPluginViews(){let e;for(;e=this.pluginViews.pop();)e.destroy&&e.destroy()}updatePluginViews(e){if(e&&e.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let t=0;t<this.pluginViews.length;t++){let n=this.pluginViews[t];n.update&&n.update(this,e)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let e=0;e<this.directPlugins.length;e++){let t=this.directPlugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}for(let e=0;e<this.state.plugins.length;e++){let t=this.state.plugins[e];t.spec.view&&this.pluginViews.push(t.spec.view(this))}}}updateDraggedNode(e,t){let n=e.node,r=-1;if(this.state.doc.nodeAt(n.from)==n.node)r=n.from;else{let e=n.from+(this.state.doc.content.size-t.doc.content.size);(e>0&&this.state.doc.nodeAt(e))==n.node&&(r=e)}this.dragging=new n7(e.slice,e.move,r<0?void 0:eQ.create(this.state.doc,r))}someProp(e,t){let n=this._props&&this._props[e],r;if(null!=n&&(r=t?t(n):n))return r;for(let n=0;n<this.directPlugins.length;n++){let i=this.directPlugins[n].props[e];if(null!=i&&(r=t?t(i):i))return r}let i=this.state.plugins;if(i)for(let n=0;n<i.length;n++){let o=i[n].props[e];if(null!=o&&(r=t?t(o):o))return r}}hasFocus(){if(tk){let e=this.root.activeElement;if(e==this.dom)return!0;if(!e||!this.dom.contains(e))return!1;for(;e&&this.dom!=e&&this.dom.contains(e);){if("false"==e.contentEditable)return!1;e=e.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(e){if(e.setActive)return e.setActive();if(tB)return e.focus(tB);let t=tz(e);e.focus(null==tB?{get preventScroll(){return tB={preventScroll:!0},!0}}:void 0),tB||(tB=!1,t$(t,0))}(this.dom),nc(this),this.domObserver.start()}get root(){let e=this._root;if(null==e){for(let e=this.dom.parentNode;e;e=e.parentNode)if(9==e.nodeType||11==e.nodeType&&e.host)return e.getSelection||(Object.getPrototypeOf(e).getSelection=()=>e.ownerDocument.getSelection()),this._root=e}return e||document}updateRoot(){this._root=null}posAtCoords(e){return function(e,t){var n;let r,i,o=e.dom.ownerDocument,s,l=0,a=function(e,t,n){if(e.caretPositionFromPoint)try{let r=e.caretPositionFromPoint(t,n);if(r)return{node:r.offsetNode,offset:Math.min(tp(r.offsetNode),r.offset)}}catch(e){}if(e.caretRangeFromPoint){let r=e.caretRangeFromPoint(t,n);if(r)return{node:r.startContainer,offset:Math.min(tp(r.startContainer),r.startOffset)}}}(o,t.left,t.top);a&&({node:s,offset:l}=a);let c=(e.root.elementFromPoint?e.root:o).elementFromPoint(t.left,t.top);if(!c||!e.dom.contains(1!=c.nodeType?c.parentNode:c)){let n=e.dom.getBoundingClientRect();if(!tH(t,n)||!(c=function e(t,n,r){let i=t.childNodes.length;if(i&&r.top<r.bottom)for(let o=Math.max(0,Math.min(i-1,Math.floor(i*(n.top-r.top)/(r.bottom-r.top))-2)),s=o;;){let r=t.childNodes[s];if(1==r.nodeType){let t=r.getClientRects();for(let i=0;i<t.length;i++){let o=t[i];if(tH(n,o))return e(r,n,o)}}if((s=(s+1)%i)==o)break}return t}(e.dom,t,n)))return null}if(tO)for(let e=c;s&&e;e=to(e))e.draggable&&(s=void 0);if(c=(r=(n=c).parentNode)&&/^li$/i.test(r.nodeName)&&t.left<n.getBoundingClientRect().left?r:n,s){let n;if(tC&&1==s.nodeType&&(l=Math.min(l,s.childNodes.length))<s.childNodes.length){let e=s.childNodes[l],n;"IMG"==e.nodeName&&(n=e.getBoundingClientRect()).right<=t.left&&n.bottom>t.top&&l++}tI&&l&&1==s.nodeType&&1==(n=s.childNodes[l-1]).nodeType&&"false"==n.contentEditable&&n.getBoundingClientRect().top>=t.top&&l--,s==e.dom&&l==s.childNodes.length-1&&1==s.lastChild.nodeType&&t.top>s.lastChild.getBoundingClientRect().bottom?i=e.state.doc.content.size:(0==l||1!=s.nodeType||"BR"!=s.childNodes[l-1].nodeName)&&(i=function(e,t,n,r){let i=-1;for(let n=t,o=!1;n!=e.dom;){let t=e.docView.nearestDesc(n,!0),s;if(!t)return null;if(1==t.dom.nodeType&&(t.node.isBlock&&t.parent||!t.contentDOM)&&((s=t.dom.getBoundingClientRect()).width||s.height)&&(t.node.isBlock&&t.parent&&(!o&&s.left>r.left||s.top>r.top?i=t.posBefore:(!o&&s.right<r.left||s.bottom<r.top)&&(i=t.posAfter),o=!0),!t.contentDOM&&i<0&&!t.node.isText))return(t.node.isBlock?r.top<(s.top+s.bottom)/2:r.left<(s.left+s.right)/2)?t.posBefore:t.posAfter;n=t.dom.parentNode}return i>-1?i:e.docView.posFromDOM(t,n,-1)}(e,s,l,t))}null==i&&(i=function(e,t,n){let{node:r,offset:i}=function e(t,n){let r,i,o,s=2e8,l,a=0,c=n.top,d=n.top;for(let e=t.firstChild,h=0;e;e=e.nextSibling,h++){let t;if(1==e.nodeType)t=e.getClientRects();else{if(3!=e.nodeType)continue;t=tl(e).getClientRects()}for(let p=0;p<t.length;p++){let u=t[p];if(u.top<=c&&u.bottom>=d){c=Math.max(u.bottom,c),d=Math.min(u.top,d);let t=u.left>n.left?u.left-n.left:u.right<n.left?n.left-u.right:0;if(t<s){o=e,s=t,l=t&&3==o.nodeType?{left:u.right<n.left?u.right:u.left,top:n.top}:n,1==e.nodeType&&t&&(a=h+ +(n.left>=(u.left+u.right)/2));continue}}else u.top>n.top&&!r&&u.left<=n.left&&u.right>=n.left&&(r=e,i={left:Math.max(u.left,Math.min(u.right,n.left)),top:u.top});!o&&(n.left>=u.right&&n.top>=u.top||n.left>=u.left&&n.top>=u.bottom)&&(a=h+1)}}return(!o&&r&&(o=r,l=i,s=0),o&&3==o.nodeType)?function(e,t){let n=e.nodeValue.length,r=document.createRange();for(let i=0;i<n;i++){r.setEnd(e,i+1),r.setStart(e,i);let n=tF(r,1);if(n.top!=n.bottom&&tH(t,n))return{node:e,offset:i+ +(t.left>=(n.left+n.right)/2)}}return{node:e,offset:0}}(o,l):!o||s&&1==o.nodeType?{node:t,offset:a}:e(o,l)}(t,n),o=-1;if(1==r.nodeType&&!r.firstChild){let e=r.getBoundingClientRect();o=e.left!=e.right&&n.left>(e.left+e.right)/2?1:-1}return e.docView.posFromDOM(r,i,o)}(e,c,t));let d=e.docView.nearestDesc(c,!0);return{pos:i,inside:d?d.posAtStart-d.border:-1}}(this,e)}coordsAtPos(e,t=1){return t_(this,e,t)}domAtPos(e,t=0){return this.docView.domFromPos(e,t)}nodeDOM(e){let t=this.docView.descAt(e);return t?t.nodeDOM:null}posAtDOM(e,t,n=-1){let r=this.docView.posFromDOM(e,t,n);if(null==r)throw RangeError("DOM position not inside the editor");return r}endOfTextblock(e,t){return function(e,t,n){let r,i;return tG==t&&tY==n?tX:(tG=t,tY=n,tX="up"==n||"down"==n?(r=t.selection,i="up"==n?r.$from:r.$to,tJ(e,t,()=>{let{node:t}=e.docView.domFromPos(i.pos,"up"==n?-1:1);for(;;){let n=e.docView.nearestDesc(t,!0);if(!n)break;if(n.node.isBlock){t=n.contentDOM||n.dom;break}t=n.dom.parentNode}let r=t_(e,i.pos,1);for(let e=t.firstChild;e;e=e.nextSibling){let t;if(1==e.nodeType)t=e.getClientRects();else{if(3!=e.nodeType)continue;t=tl(e,0,e.nodeValue.length).getClientRects()}for(let e=0;e<t.length;e++){let i=t[e];if(i.bottom>i.top+1&&("up"==n?r.top-i.top>(i.bottom-r.top)*2:i.bottom-r.bottom>(r.bottom-i.top)*2))return!1}}return!0})):function(e,t,n){let{$head:r}=t.selection;if(!r.parent.isTextblock)return!1;let i=r.parentOffset,o=i==r.parent.content.size,s=e.domSelection();return s?tU.test(r.parent.textContent)&&s.modify?tJ(e,t,()=>{let{focusNode:t,focusOffset:i,anchorNode:o,anchorOffset:l}=e.domSelectionRange(),a=s.caretBidiLevel;s.modify("move",n,"character");let c=r.depth?e.docView.domAfterPos(r.before()):e.dom,{focusNode:d,focusOffset:h}=e.domSelectionRange(),p=d&&!c.contains(1==d.nodeType?d:d.parentNode)||t==d&&i==h;try{s.collapse(o,l),t&&(t!=o||i!=l)&&s.extend&&s.extend(t,i)}catch(e){}return null!=a&&(s.caretBidiLevel=a),p}):"left"==n||"backward"==n?!i:o:r.pos==r.start()||r.pos==r.end()}(e,t,n))}(this,t||this.state,e)}pasteHTML(e,t){return n5(this,"",e,!1,t||new ClipboardEvent("paste"))}pasteText(e,t){return n5(this,e,null,!0,t||new ClipboardEvent("paste"))}serializeForClipboard(e){return nD(this,e)}destroy(){if(this.docView){for(let e in this.domObserver.stop(),this.input.eventHandlers)this.dom.removeEventListener(e,this.input.eventHandlers[e]);clearTimeout(this.input.composingTimeout),clearTimeout(this.input.lastIOSEnterFallbackTimeout),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],ry(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,ta()}}get isDestroyed(){return null==this.docView}dispatchEvent(e){!nJ(this,e)&&nV[e.type]&&(this.editable||!(e.type in nF))&&nV[e.type](this,e)}domSelectionRange(){let e=this.domSelection();return e?tO&&11===this.root.nodeType&&function(e){let t=e.activeElement;for(;t&&t.shadowRoot;)t=t.shadowRoot.activeElement;return t}(this.dom.ownerDocument)==this.dom&&function(e,t){let n;if(t.getComposedRanges){let n=t.getComposedRanges(e.root)[0];if(n)return rC(e,n)}function r(e){e.preventDefault(),e.stopImmediatePropagation(),n=e.getTargetRanges()[0]}return e.dom.addEventListener("beforeinput",r,!0),document.execCommand("indent"),e.dom.removeEventListener("beforeinput",r,!0),n?rC(e,n):null}(this,e)||e:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function rD(e){let t=Object.create(null);return t.class="ProseMirror",t.contenteditable=String(e.editable),e.someProp("attributes",n=>{if("function"==typeof n&&(n=n(e.state)),n)for(let e in n)"class"==e?t.class+=" "+n[e]:"style"==e?t.style=(t.style?t.style+";":"")+n[e]:t[e]||"contenteditable"==e||"nodeName"==e||(t[e]=String(n[e]))}),t.translate||(t.translate="no"),[ri.node(0,e.state.doc.content.size,t)]}function rR(e){if(e.markCursor){let t=document.createElement("img");t.className="ProseMirror-separator",t.setAttribute("mark-placeholder","true"),t.setAttribute("alt",""),e.cursorWrapper={dom:t,deco:ri.widget(e.state.selection.from,t,{raw:!0,marks:e.markCursor})}}else e.cursorWrapper=null}function rI(e){return!e.someProp("editable",t=>!1===t(e.state))}function rP(e){let t=Object.create(null);function n(e){for(let n in e)Object.prototype.hasOwnProperty.call(t,n)||(t[n]=e[n])}return e.someProp("nodeViews",n),e.someProp("markViews",n),t}function rL(e){if(e.spec.state||e.spec.filterTransaction||e.spec.appendTransaction)throw RangeError("Plugins passed directly to the view must not have a state component")}rE.prototype.dispatch=function(e){let t=this._props.dispatchTransaction;t?t.call(this,e):this.updateState(this.state.apply(e))};for(var rj={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},rz={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},r$="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),rB="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),rH=0;rH<10;rH++)rj[48+rH]=rj[96+rH]=String(rH);for(var rH=1;rH<=24;rH++)rj[rH+111]="F"+rH;for(var rH=65;rH<=90;rH++)rj[rH]=String.fromCharCode(rH+32),rz[rH]=String.fromCharCode(rH);for(var rV in rj)rz.hasOwnProperty(rV)||(rz[rV]=rj[rV]);let rF="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),rW="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function r_(e,t,n=!0){return t.altKey&&(e="Alt-"+e),t.ctrlKey&&(e="Ctrl-"+e),t.metaKey&&(e="Meta-"+e),n&&t.shiftKey&&(e="Shift-"+e),e}function rq(e){let t=function(e){let t=Object.create(null);for(let n in e)t[function(e){let t,n,r,i,o=e.split(/-(?!$)/),s=o[o.length-1];"Space"==s&&(s=" ");for(let e=0;e<o.length-1;e++){let s=o[e];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))rF?i=!0:n=!0;else throw Error("Unrecognized modifier name: "+s)}return t&&(s="Alt-"+s),n&&(s="Ctrl-"+s),i&&(s="Meta-"+s),r&&(s="Shift-"+s),s}(n)]=e[n];return t}(e);return function(e,n){var r;let i=("Esc"==(r=!(r$&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||rB&&n.shiftKey&&n.key&&1==n.key.length||"Unidentified"==n.key)&&n.key||(n.shiftKey?rz:rj)[n.keyCode]||n.key||"Unidentified")&&(r="Escape"),"Del"==r&&(r="Delete"),"Left"==r&&(r="ArrowLeft"),"Up"==r&&(r="ArrowUp"),"Right"==r&&(r="ArrowRight"),"Down"==r&&(r="ArrowDown"),r),o,s=t[r_(i,n)];if(s&&s(e.state,e.dispatch,e))return!0;if(1==i.length&&" "!=i){if(n.shiftKey){let r=t[r_(i,n,!1)];if(r&&r(e.state,e.dispatch,e))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(rW&&n.ctrlKey&&n.altKey)&&(o=rj[n.keyCode])&&o!=i){let r=t[r_(o,n)];if(r&&r(e.state,e.dispatch,e))return!0}}return!1}}let rK=(e,t)=>!e.selection.empty&&(t&&t(e.tr.deleteSelection().scrollIntoView()),!0);function rJ(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("backward",e):!(n.parentOffset>0))?n:null}let rU=(e,t,n)=>{let r=rJ(e,n);if(!r)return!1;let i=r0(r);if(!i){let n=r.blockRange(),i=n&&eS(n);return null!=i&&(t&&t(e.tr.lift(n,i).scrollIntoView()),!0)}let o=i.nodeBefore;if(io(e,i,t,-1))return!0;if(0==r.parent.content.size&&(rQ(o,"end")||eQ.isSelectable(o)))for(let n=r.depth;;n--){let s=eI(e.doc,r.before(n),r.after(n),b.empty);if(s&&s.slice.size<s.to-s.from){if(t){let n=e.tr.step(s);n.setSelection(rQ(o,"end")?eK.findFrom(n.doc.resolve(n.mapping.map(i.pos,-1)),-1):eQ.create(n.doc,i.pos-o.nodeSize)),t(n.scrollIntoView())}return!0}if(1==n||r.node(n-1).childCount>1)break}return!!o.isAtom&&i.depth==r.depth-1&&(t&&t(e.tr.delete(i.pos-o.nodeSize,i.pos).scrollIntoView()),!0)},rG=(e,t,n)=>{let r=rJ(e,n);if(!r)return!1;let i=r0(r);return!!i&&rX(e,i,t)},rY=(e,t,n)=>{let r=r1(e,n);if(!r)return!1;let i=r4(r);return!!i&&rX(e,i,t)};function rX(e,t,n){let r=t.nodeBefore,i=t.pos-1;for(;!r.isTextblock;i--){if(r.type.spec.isolating)return!1;let e=r.lastChild;if(!e)return!1;r=e}let o=t.nodeAfter,s=t.pos+1;for(;!o.isTextblock;s++){if(o.type.spec.isolating)return!1;let e=o.firstChild;if(!e)return!1;o=e}let l=eI(e.doc,i,s,b.empty);if(!l||l.from!=i||l instanceof ew&&l.slice.size>=s-i)return!1;if(n){let t=e.tr.step(l);t.setSelection(eY.create(t.doc,i)),n(t.scrollIntoView())}return!0}function rQ(e,t,n=!1){for(let r=e;r;r="start"==t?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)break}return!1}let rZ=(e,t,n)=>{let{$head:r,empty:i}=e.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",e):r.parentOffset>0)return!1;o=r0(r)}let s=o&&o.nodeBefore;return!!s&&!!eQ.isSelectable(s)&&(t&&t(e.tr.setSelection(eQ.create(e.doc,o.pos-s.nodeSize)).scrollIntoView()),!0)};function r0(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){if(e.index(t)>0)return e.doc.resolve(e.before(t+1));if(e.node(t).type.spec.isolating)break}return null}function r1(e,t){let{$cursor:n}=e.selection;return n&&(t?t.endOfTextblock("forward",e):!(n.parentOffset<n.parent.content.size))?n:null}let r2=(e,t,n)=>{let r=r1(e,n);if(!r)return!1;let i=r4(r);if(!i)return!1;let o=i.nodeAfter;if(io(e,i,t,1))return!0;if(0==r.parent.content.size&&(rQ(o,"start")||eQ.isSelectable(o))){let n=eI(e.doc,r.before(),r.after(),b.empty);if(n&&n.slice.size<n.to-n.from){if(t){let r=e.tr.step(n);r.setSelection(rQ(o,"start")?eK.findFrom(r.doc.resolve(r.mapping.map(i.pos)),1):eQ.create(r.doc,r.mapping.map(i.pos))),t(r.scrollIntoView())}return!0}}return!!o.isAtom&&i.depth==r.depth-1&&(t&&t(e.tr.delete(i.pos,i.pos+o.nodeSize).scrollIntoView()),!0)},r3=(e,t,n)=>{let{$head:r,empty:i}=e.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",e):r.parentOffset<r.parent.content.size)return!1;o=r4(r)}let s=o&&o.nodeAfter;return!!s&&!!eQ.isSelectable(s)&&(t&&t(e.tr.setSelection(eQ.create(e.doc,o.pos)).scrollIntoView()),!0)};function r4(e){if(!e.parent.type.spec.isolating)for(let t=e.depth-1;t>=0;t--){let n=e.node(t);if(e.index(t)+1<n.childCount)return e.doc.resolve(e.after(t+1));if(n.type.spec.isolating)break}return null}let r5=(e,t)=>{let n=e.selection,r=n instanceof eQ,i;if(r){if(n.node.isTextblock||!eT(e.doc,n.from))return!1;i=n.from}else if(null==(i=eD(e.doc,n.from,-1)))return!1;if(t){let n=e.tr.join(i);r&&n.setSelection(eQ.create(n.doc,i-e.doc.resolve(i).nodeBefore.nodeSize)),t(n.scrollIntoView())}return!0},r6=(e,t)=>{let n=e.selection,r;if(n instanceof eQ){if(n.node.isTextblock||!eT(e.doc,n.to))return!1;r=n.to}else if(null==(r=eD(e.doc,n.to,1)))return!1;return t&&t(e.tr.join(r).scrollIntoView()),!0},r7=(e,t)=>{let{$from:n,$to:r}=e.selection,i=n.blockRange(r),o=i&&eS(i);return null!=o&&(t&&t(e.tr.lift(i,o).scrollIntoView()),!0)},r8=(e,t)=>{let{$head:n,$anchor:r}=e.selection;return!!n.parent.type.spec.code&&!!n.sameParent(r)&&(t&&t(e.tr.insertText("\n").scrollIntoView()),!0)};function r9(e){for(let t=0;t<e.edgeCount;t++){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}let ie=(e,t)=>{let{$head:n,$anchor:r}=e.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let i=n.node(-1),o=n.indexAfter(-1),s=r9(i.contentMatchAt(o));if(!s||!i.canReplaceWith(o,o,s))return!1;if(t){let r=n.after(),i=e.tr.replaceWith(r,r,s.createAndFill());i.setSelection(eK.near(i.doc.resolve(r),1)),t(i.scrollIntoView())}return!0},it=(e,t)=>{let n=e.selection,{$from:r,$to:i}=n;if(n instanceof e0||r.parent.inlineContent||i.parent.inlineContent)return!1;let o=r9(i.parent.contentMatchAt(i.indexAfter()));if(!o||!o.isTextblock)return!1;if(t){let n=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,s=e.tr.insert(n,o.createAndFill());s.setSelection(eY.create(s.doc,n+1)),t(s.scrollIntoView())}return!0},ir=(e,t)=>{let{$cursor:n}=e.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if(eO(e.doc,r))return t&&t(e.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),i=r&&eS(r);return null!=i&&(t&&t(e.tr.lift(r,i).scrollIntoView()),!0)},ii=(e,t)=>{let{$from:n,to:r}=e.selection,i,o=n.sharedDepth(r);return 0!=o&&(i=n.before(o),t&&t(e.tr.setSelection(eQ.create(e.doc,i))),!0)};function io(e,t,n,r){let i,o,s,l=t.nodeBefore,a=t.nodeAfter,c,d,h=l.type.spec.isolating||a.type.spec.isolating;if(!h&&(i=t.nodeBefore,o=t.nodeAfter,s=t.index(),i&&o&&i.type.compatibleContent(o.type)&&(!i.content.size&&t.parent.canReplace(s-1,s)?(n&&n(e.tr.delete(t.pos-i.nodeSize,t.pos).scrollIntoView()),!0):!!t.parent.canReplace(s,s+1)&&!!(o.isTextblock||eT(e.doc,t.pos))&&(n&&n(e.tr.join(t.pos).scrollIntoView()),!0))))return!0;let u=!h&&t.parent.canReplace(t.index(),t.index()+1);if(u&&(c=(d=l.contentMatchAt(l.childCount)).findWrapping(a.type))&&d.matchType(c[0]||a.type).validEnd){if(n){let r=t.pos+a.nodeSize,i=p.empty;for(let e=c.length-1;e>=0;e--)i=p.from(c[e].create(null,i));i=p.from(l.copy(i));let o=e.tr.step(new ev(t.pos-1,r,t.pos,r,new b(i,1,0),c.length,!0)),s=o.doc.resolve(r+2*c.length);s.nodeAfter&&s.nodeAfter.type==l.type&&eT(o.doc,s.pos)&&o.join(s.pos),n(o.scrollIntoView())}return!0}let f=a.type.spec.isolating||r>0&&h?null:eK.findFrom(t,1),m=f&&f.$from.blockRange(f.$to),g=m&&eS(m);if(null!=g&&g>=t.depth)return n&&n(e.tr.lift(m,g).scrollIntoView()),!0;if(u&&rQ(a,"start",!0)&&rQ(l,"end")){let r=l,i=[];for(;i.push(r),!r.isTextblock;)r=r.lastChild;let o=a,s=1;for(;!o.isTextblock;o=o.firstChild)s++;if(r.canReplace(r.childCount,r.childCount,o.content)){if(n){let r=p.empty;for(let e=i.length-1;e>=0;e--)r=p.from(i[e].copy(r));n(e.tr.step(new ev(t.pos-i.length,t.pos+a.nodeSize,t.pos+s,t.pos+a.nodeSize-s,new b(r,i.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function is(e){return function(t,n){let r=t.selection,i=e<0?r.$from:r.$to,o=i.depth;for(;i.node(o).isInline;){if(!o)return!1;o--}return!!i.node(o).isTextblock&&(n&&n(t.tr.setSelection(eY.create(t.doc,e<0?i.start(o):i.end(o)))),!0)}}let il=is(-1),ia=is(1);function ic(e,t=null){return function(n,r){let i=!1;for(let r=0;r<n.selection.ranges.length&&!i;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];n.doc.nodesBetween(o,s,(r,o)=>{if(i)return!1;if(!(!r.isTextblock||r.hasMarkup(e,t)))if(r.type==e)i=!0;else{let t=n.doc.resolve(o),r=t.index();i=t.parent.canReplaceWith(r,r+1,e)}})}if(!i)return!1;if(r){let i=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];i.setBlockType(o,s,e,t)}r(i.scrollIntoView())}return!0}}function id(...e){return function(t,n,r){for(let i=0;i<e.length;i++)if(e[i](t,n,r))return!0;return!1}}let ih=id(rK,rU,rZ),ip=id(rK,r2,r3),iu={Enter:id(r8,it,ir,(e,t)=>{let{$from:n,$to:r}=e.selection;if(e.selection instanceof eQ&&e.selection.node.isBlock)return!!n.parentOffset&&!!eO(e.doc,n.pos)&&(t&&t(e.tr.split(n.pos).scrollIntoView()),!0);if(!n.depth)return!1;let i=[],o,s,l=!1,a=!1;for(let e=n.depth;;e--){if(n.node(e).isBlock){let t;l=n.end(e)==n.pos+(n.depth-e),a=n.start(e)==n.pos-(n.depth-e),s=r9(n.node(e-1).contentMatchAt(n.indexAfter(e-1)));i.unshift(t||(l&&s?{type:s}:null)),o=e;break}if(1==e)return!1;i.unshift(null)}let c=e.tr;(e.selection instanceof eY||e.selection instanceof e0)&&c.deleteSelection();let d=c.mapping.map(n.pos),h=eO(c.doc,d,i.length,i);if(h||(i[0]=s?{type:s}:null,h=eO(c.doc,d,i.length,i)),!h)return!1;if(c.split(d,i.length,i),!l&&a&&n.node(o).type!=s){let e=c.mapping.map(n.before(o)),t=c.doc.resolve(e);s&&n.node(o-1).canReplaceWith(t.index(),t.index()+1,s)&&c.setNodeMarkup(c.mapping.map(n.before(o)),s)}return t&&t(c.scrollIntoView()),!0}),"Mod-Enter":ie,Backspace:ih,"Mod-Backspace":ih,"Shift-Backspace":ih,Delete:ip,"Mod-Delete":ip,"Mod-a":(e,t)=>(t&&t(e.tr.setSelection(new e0(e.doc))),!0)},im={"Ctrl-h":iu.Backspace,"Alt-Backspace":iu["Mod-Backspace"],"Ctrl-d":iu.Delete,"Ctrl-Alt-Backspace":iu["Mod-Delete"],"Alt-Delete":iu["Mod-Delete"],"Alt-d":iu["Mod-Delete"],"Ctrl-a":il,"Ctrl-e":ia};for(let e in iu)im[e]=iu[e];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform();let ig=["ol",0],iy=["ul",0],ib=["li",0];function iw(e){let{state:t,transaction:n}=e,{selection:r}=n,{doc:i}=n,{storedMarks:o}=n;return{...t,apply:t.apply.bind(t),applyTransaction:t.applyTransaction.bind(t),plugins:t.plugins,schema:t.schema,reconfigure:t.reconfigure.bind(t),toJSON:t.toJSON.bind(t),get storedMarks(){return o},get selection(){return r},get doc(){return i},get tr(){return r=n.selection,i=n.doc,o=n.storedMarks,n}}}class iv{constructor(e){this.editor=e.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=e.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){let{rawCommands:e,editor:t,state:n}=this,{view:r}=t,{tr:i}=n,o=this.buildProps(i);return Object.fromEntries(Object.entries(e).map(([e,t])=>[e,(...e)=>{let n=t(...e)(o);return i.getMeta("preventDispatch")||this.hasCustomState||r.dispatch(i),n}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(e,t=!0){let{rawCommands:n,editor:r,state:i}=this,{view:o}=r,s=[],l=!!e,a=e||i.tr,c={...Object.fromEntries(Object.entries(n).map(([e,n])=>[e,(...e)=>{let r=this.buildProps(a,t),i=n(...e)(r);return s.push(i),c}])),run:()=>(l||!t||a.getMeta("preventDispatch")||this.hasCustomState||o.dispatch(a),s.every(e=>!0===e))};return c}createCan(e){let{rawCommands:t,state:n}=this,r=e||n.tr,i=this.buildProps(r,!1);return{...Object.fromEntries(Object.entries(t).map(([e,t])=>[e,(...e)=>t(...e)({...i,dispatch:void 0})])),chain:()=>this.createChain(r,!1)}}buildProps(e,t=!0){let{rawCommands:n,editor:r,state:i}=this,{view:o}=r,s={tr:e,editor:r,view:o,state:iw({state:i,transaction:e}),dispatch:t?()=>void 0:void 0,chain:()=>this.createChain(e,t),can:()=>this.createCan(e),get commands(){return Object.fromEntries(Object.entries(n).map(([e,t])=>[e,(...e)=>t(...e)(s)]))}};return s}}class ix{constructor(){this.callbacks={}}on(e,t){return this.callbacks[e]||(this.callbacks[e]=[]),this.callbacks[e].push(t),this}emit(e,...t){let n=this.callbacks[e];return n&&n.forEach(e=>e.apply(this,t)),this}off(e,t){let n=this.callbacks[e];return n&&(t?this.callbacks[e]=n.filter(e=>e!==t):delete this.callbacks[e]),this}once(e,t){let n=(...r)=>{this.off(e,n),t.apply(this,r)};return this.on(e,n)}removeAllListeners(){this.callbacks={}}}function ik(e,t,n){return void 0===e.config[t]&&e.parent?ik(e.parent,t,n):"function"==typeof e.config[t]?e.config[t].bind({...n,parent:e.parent?ik(e.parent,t,n):null}):e.config[t]}function iS(e){let t=e.filter(e=>"extension"===e.type);return{baseExtensions:t,nodeExtensions:e.filter(e=>"node"===e.type),markExtensions:e.filter(e=>"mark"===e.type)}}function iC(e){let t=[],{nodeExtensions:n,markExtensions:r}=iS(e),i=[...n,...r],o={default:null,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return e.forEach(e=>{let n={name:e.name,options:e.options,storage:e.storage,extensions:i},r=ik(e,"addGlobalAttributes",n);r&&r().forEach(e=>{e.types.forEach(n=>{Object.entries(e.attributes).forEach(([e,r])=>{t.push({type:n,name:e,attribute:{...o,...r}})})})})}),i.forEach(e=>{let n={name:e.name,options:e.options,storage:e.storage},r=ik(e,"addAttributes",n);r&&Object.entries(r()).forEach(([n,r])=>{let i={...o,...r};"function"==typeof(null==i?void 0:i.default)&&(i.default=i.default()),(null==i?void 0:i.isRequired)&&(null==i?void 0:i.default)===void 0&&delete i.default,t.push({type:e.name,name:n,attribute:i})})}),t}function iM(e,t){if("string"==typeof e){if(!t.nodes[e])throw Error(`There is no node type named '${e}'. Maybe you forgot to add the extension?`);return t.nodes[e]}return e}function iN(...e){return e.filter(e=>!!e).reduce((e,t)=>{let n={...e};return Object.entries(t).forEach(([e,t])=>{if(!n[e]){n[e]=t;return}if("class"===e){let r=t?String(t).split(" "):[],i=n[e]?n[e].split(" "):[],o=r.filter(e=>!i.includes(e));n[e]=[...i,...o].join(" ")}else if("style"===e){let r=t?t.split(";").map(e=>e.trim()).filter(Boolean):[],i=n[e]?n[e].split(";").map(e=>e.trim()).filter(Boolean):[],o=new Map;i.forEach(e=>{let[t,n]=e.split(":").map(e=>e.trim());o.set(t,n)}),r.forEach(e=>{let[t,n]=e.split(":").map(e=>e.trim());o.set(t,n)}),n[e]=Array.from(o.entries()).map(([e,t])=>`${e}: ${t}`).join("; ")}else n[e]=t}),n},{})}function iA(e,t){return t.filter(t=>t.type===e.type.name).filter(e=>e.attribute.rendered).map(t=>t.attribute.renderHTML?t.attribute.renderHTML(e.attrs)||{}:{[t.name]:e.attrs[t.name]}).reduce((e,t)=>iN(e,t),{})}function iO(e){return"function"==typeof e}function iT(e,t,...n){return iO(e)?t?e.bind(t)(...n):e(...n):e}function iE(e,t){return"style"in e?e:{...e,getAttrs:n=>{let r=e.getAttrs?e.getAttrs(n):e.attrs;if(!1===r)return!1;let i=t.reduce((e,t)=>{var r;let i=t.attribute.parseHTML?t.attribute.parseHTML(n):"string"!=typeof(r=n.getAttribute(t.name))?r:r.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(r):"true"===r||"false"!==r&&r;return null==i?e:{...e,[t.name]:i}},{});return{...r,...i}}}}function iD(e){return Object.fromEntries(Object.entries(e).filter(([e,t])=>!("attrs"===e&&function(e={}){return 0===Object.keys(e).length&&e.constructor===Object}(t))&&null!=t))}function iR(e,t){var n;let r=iC(e),{nodeExtensions:i,markExtensions:o}=iS(e),s=null==(n=i.find(e=>ik(e,"topNode")))?void 0:n.name;return new K({topNode:s,nodes:Object.fromEntries(i.map(n=>{let i=r.filter(e=>e.type===n.name),o={name:n.name,options:n.options,storage:n.storage,editor:t},s=iD({...e.reduce((e,t)=>{let r=ik(t,"extendNodeSchema",o);return{...e,...r?r(n):{}}},{}),content:iT(ik(n,"content",o)),marks:iT(ik(n,"marks",o)),group:iT(ik(n,"group",o)),inline:iT(ik(n,"inline",o)),atom:iT(ik(n,"atom",o)),selectable:iT(ik(n,"selectable",o)),draggable:iT(ik(n,"draggable",o)),code:iT(ik(n,"code",o)),whitespace:iT(ik(n,"whitespace",o)),linebreakReplacement:iT(ik(n,"linebreakReplacement",o)),defining:iT(ik(n,"defining",o)),isolating:iT(ik(n,"isolating",o)),attrs:Object.fromEntries(i.map(e=>{var t;return[e.name,{default:null==(t=null==e?void 0:e.attribute)?void 0:t.default}]}))}),l=iT(ik(n,"parseHTML",o));l&&(s.parseDOM=l.map(e=>iE(e,i)));let a=ik(n,"renderHTML",o);a&&(s.toDOM=e=>a({node:e,HTMLAttributes:iA(e,i)}));let c=ik(n,"renderText",o);return c&&(s.toText=c),[n.name,s]})),marks:Object.fromEntries(o.map(n=>{let i=r.filter(e=>e.type===n.name),o={name:n.name,options:n.options,storage:n.storage,editor:t},s=iD({...e.reduce((e,t)=>{let r=ik(t,"extendMarkSchema",o);return{...e,...r?r(n):{}}},{}),inclusive:iT(ik(n,"inclusive",o)),excludes:iT(ik(n,"excludes",o)),group:iT(ik(n,"group",o)),spanning:iT(ik(n,"spanning",o)),code:iT(ik(n,"code",o)),attrs:Object.fromEntries(i.map(e=>{var t;return[e.name,{default:null==(t=null==e?void 0:e.attribute)?void 0:t.default}]}))}),l=iT(ik(n,"parseHTML",o));l&&(s.parseDOM=l.map(e=>iE(e,i)));let a=ik(n,"renderHTML",o);return a&&(s.toDOM=e=>a({mark:e,HTMLAttributes:iA(e,i)})),[n.name,s]}))})}function iI(e,t){return t.nodes[e]||t.marks[e]||null}function iP(e,t){return Array.isArray(t)?t.some(t=>("string"==typeof t?t:t.name)===e.name):t}function iL(e,t){let n=er.fromSchema(t).serializeFragment(e),r=document.implementation.createHTMLDocument().createElement("div");return r.appendChild(n),r.innerHTML}let ij=(e,t=500)=>{let n="",r=e.parentOffset;return e.parent.nodesBetween(Math.max(0,r-t),r,(e,t,i,o)=>{var s,l;let a=(null==(l=(s=e.type.spec).toText)?void 0:l.call(s,{node:e,pos:t,parent:i,index:o}))||e.textContent||"%leaf%";n+=e.isAtom&&!e.isText?a:a.slice(0,Math.max(0,r-t))}),n};function iz(e){return"[object RegExp]"===Object.prototype.toString.call(e)}class i${constructor(e){this.find=e.find,this.handler=e.handler}}let iB=(e,t)=>{if(iz(t))return t.exec(e);let n=t(e);if(!n)return null;let r=[n.text];return r.index=n.index,r.input=e,r.data=n.data,n.replaceWith&&(n.text.includes(n.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),r.push(n.replaceWith)),r};function iH(e){var t;let{editor:n,from:r,to:i,text:o,rules:s,plugin:l}=e,{view:a}=n;if(a.composing)return!1;let c=a.state.doc.resolve(r);if(c.parent.type.spec.code||(null==(t=c.nodeBefore||c.nodeAfter)?void 0:t.marks.find(e=>e.type.spec.code)))return!1;let d=!1,h=ij(c)+o;return s.forEach(e=>{if(d)return;let t=iB(h,e.find);if(!t)return;let s=a.state.tr,c=iw({state:a.state,transaction:s}),p={from:r-(t[0].length-o.length),to:i},{commands:u,chain:f,can:m}=new iv({editor:n,state:c});null!==e.handler({state:c,range:p,match:t,commands:u,chain:f,can:m})&&s.steps.length&&(s.setMeta(l,{transform:s,from:r,to:i,text:o}),a.dispatch(s),d=!0)}),d}function iV(e){return"Object"===Object.prototype.toString.call(e).slice(8,-1)&&e.constructor===Object&&Object.getPrototypeOf(e)===Object.prototype}function iF(e,t){let n={...e};return iV(e)&&iV(t)&&Object.keys(t).forEach(r=>{iV(t[r])&&iV(e[r])?n[r]=iF(e[r],t[r]):n[r]=t[r]}),n}class iW{constructor(e={}){this.type="mark",this.name="mark",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=iT(ik(this,"addOptions",{name:this.name}))),this.storage=iT(ik(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new iW(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>iF(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new iW(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=iT(ik(t,"addOptions",{name:t.name})),t.storage=iT(ik(t,"addStorage",{name:t.name,options:t.options})),t}static handleExit({editor:e,mark:t}){let{tr:n}=e.state,r=e.state.selection.$from;if(r.pos===r.end()){let i=r.marks();if(!i.find(e=>(null==e?void 0:e.type.name)===t.name))return!1;let o=i.find(e=>(null==e?void 0:e.type.name)===t.name);return o&&n.removeStoredMark(o),n.insertText(" ",r.pos),e.view.dispatch(n),!0}return!1}}class i_{constructor(e){this.find=e.find,this.handler=e.handler}}let iq=(e,t,n)=>{if(iz(t))return[...e.matchAll(t)];let r=t(e,n);return r?r.map(t=>{let n=[t.text];return n.index=t.index,n.input=e,n.data=t.data,t.replaceWith&&(t.text.includes(t.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),n.push(t.replaceWith)),n}):[]},iK=null,iJ=e=>{var t;let n=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null==(t=n.clipboardData)||t.setData("text/html",e),n};class iU{constructor(e,t){this.splittableMarks=[],this.editor=t,this.extensions=iU.resolve(e),this.schema=iR(this.extensions,t),this.setupExtensions()}static resolve(e){var t;let n=iU.sort(iU.flatten(e)),r=Array.from(new Set((t=n.map(e=>e.name)).filter((e,n)=>t.indexOf(e)!==n)));return r.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${r.map(e=>`'${e}'`).join(", ")}]. This can lead to issues.`),n}static flatten(e){return e.map(e=>{let t={name:e.name,options:e.options,storage:e.storage},n=ik(e,"addExtensions",t);return n?[e,...this.flatten(n())]:e}).flat(10)}static sort(e){return e.sort((e,t)=>{let n=ik(e,"priority")||100,r=ik(t,"priority")||100;return n>r?-1:+(n<r)})}get commands(){return this.extensions.reduce((e,t)=>{let n={name:t.name,options:t.options,storage:t.storage,editor:this.editor,type:iI(t.name,this.schema)},r=ik(t,"addCommands",n);return r?{...e,...r()}:e},{})}get plugins(){let{editor:e}=this,t=iU.sort([...this.extensions].reverse()),n=[],r=[],i=t.map(t=>{let i={name:t.name,options:t.options,storage:t.storage,editor:e,type:iI(t.name,this.schema)},o=[],s=ik(t,"addKeyboardShortcuts",i),l={};if("mark"===t.type&&ik(t,"exitable",i)&&(l.ArrowRight=()=>iW.handleExit({editor:e,mark:t})),s){let t=Object.fromEntries(Object.entries(s()).map(([t,n])=>[t,()=>n({editor:e})]));l={...l,...t}}let a=new te({props:{handleKeyDown:rq(l)}});o.push(a);let c=ik(t,"addInputRules",i);iP(t,e.options.enableInputRules)&&c&&n.push(...c());let d=ik(t,"addPasteRules",i);iP(t,e.options.enablePasteRules)&&d&&r.push(...d());let h=ik(t,"addProseMirrorPlugins",i);if(h){let e=h();o.push(...e)}return o}).flat();return[function(e){let{editor:t,rules:n}=e,r=new te({state:{init:()=>null,apply(e,i,o){let s=e.getMeta(r);if(s)return s;let l=e.getMeta("applyInputRules");return l&&setTimeout(()=>{let{text:e}=l;"string"==typeof e||(e=iL(p.from(e),o.schema));let{from:i}=l,s=i+e.length;iH({editor:t,from:i,to:s,text:e,rules:n,plugin:r})}),e.selectionSet||e.docChanged?null:i}},props:{handleTextInput:(e,i,o,s)=>iH({editor:t,from:i,to:o,text:s,rules:n,plugin:r}),handleDOMEvents:{compositionend:e=>(setTimeout(()=>{let{$cursor:i}=e.state.selection;i&&iH({editor:t,from:i.pos,to:i.pos,text:"",rules:n,plugin:r})}),!1)},handleKeyDown(e,i){if("Enter"!==i.key)return!1;let{$cursor:o}=e.state.selection;return!!o&&iH({editor:t,from:o.pos,to:o.pos,text:"\n",rules:n,plugin:r})}},isInputRules:!0});return r}({editor:e,rules:n}),...function(e){let t,{editor:n,rules:r}=e,i=null,o=!1,s=!1,l="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{t="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{t=null}let a=({state:e,from:r,to:i,rule:o,pasteEvt:s})=>{let a=e.tr;if(function(e){let{editor:t,state:n,from:r,to:i,rule:o,pasteEvent:s,dropEvent:l}=e,{commands:a,chain:c,can:d}=new iv({editor:t,state:n}),h=[];return n.doc.nodesBetween(r,i,(e,t)=>{if(!e.isTextblock||e.type.spec.code)return;let p=Math.max(r,t),u=Math.min(i,t+e.content.size);iq(e.textBetween(p-t,u-t,void 0,"￼"),o.find,s).forEach(e=>{if(void 0===e.index)return;let t=p+e.index+1,r=t+e[0].length,i={from:n.tr.mapping.map(t),to:n.tr.mapping.map(r)},u=o.handler({state:n,range:i,match:e,commands:a,chain:c,can:d,pasteEvent:s,dropEvent:l});h.push(u)})}),h.every(e=>null!==e)}({editor:n,state:iw({state:e,transaction:a}),from:Math.max(r-1,0),to:i.b-1,rule:o,pasteEvent:s,dropEvent:t})&&a.steps.length){try{t="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{t=null}return l="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,a}};return r.map(e=>new te({view(e){let t=t=>{var r;(i=(null==(r=e.dom.parentElement)?void 0:r.contains(t.target))?e.dom.parentElement:null)&&(iK=n)},r=()=>{iK&&(iK=null)};return window.addEventListener("dragstart",t),window.addEventListener("dragend",r),{destroy(){window.removeEventListener("dragstart",t),window.removeEventListener("dragend",r)}}},props:{handleDOMEvents:{drop:(e,n)=>{if(s=i===e.dom.parentElement,t=n,!s){let e=iK;(null==e?void 0:e.isEditable)&&setTimeout(()=>{let t=e.state.selection;t&&e.commands.deleteRange({from:t.from,to:t.to})},10)}return!1},paste:(e,t)=>{var n;let r=null==(n=t.clipboardData)?void 0:n.getData("text/html");return l=t,o=!!(null==r?void 0:r.includes("data-pm-slice")),!1}}},appendTransaction:(t,n,r)=>{let i=t[0],c="paste"===i.getMeta("uiEvent")&&!o,d="drop"===i.getMeta("uiEvent")&&!s,h=i.getMeta("applyPasteRules"),u=!!h;if(!c&&!d&&!u)return;if(u){let{text:t}=h;"string"==typeof t||(t=iL(p.from(t),r.schema));let{from:n}=h,i=n+t.length;return a({rule:e,state:r,from:n,to:{b:i},pasteEvt:iJ(t)})}let f=n.doc.content.findDiffStart(r.doc.content),m=n.doc.content.findDiffEnd(r.doc.content);if("number"==typeof f&&m&&f!==m.b)return a({rule:e,state:r,from:f,to:m,pasteEvt:l})}}))}({editor:e,rules:r}),...i]}get attributes(){return iC(this.extensions)}get nodeViews(){let{editor:e}=this,{nodeExtensions:t}=iS(this.extensions);return Object.fromEntries(t.filter(e=>!!ik(e,"addNodeView")).map(t=>{let n=this.attributes.filter(e=>e.type===t.name),r={name:t.name,options:t.options,storage:t.storage,editor:e,type:iM(t.name,this.schema)},i=ik(t,"addNodeView",r);return i?[t.name,(r,o,s,l,a)=>{let c=iA(r,n);return i()({node:r,view:o,getPos:s,decorations:l,innerDecorations:a,editor:e,extension:t,HTMLAttributes:c})}]:[]}))}setupExtensions(){this.extensions.forEach(e=>{var t;this.editor.extensionStorage[e.name]=e.storage;let n={name:e.name,options:e.options,storage:e.storage,editor:this.editor,type:iI(e.name,this.schema)};"mark"===e.type&&(null==(t=iT(ik(e,"keepOnSplit",n)))||t)&&this.splittableMarks.push(e.name);let r=ik(e,"onBeforeCreate",n),i=ik(e,"onCreate",n),o=ik(e,"onUpdate",n),s=ik(e,"onSelectionUpdate",n),l=ik(e,"onTransaction",n),a=ik(e,"onFocus",n),c=ik(e,"onBlur",n),d=ik(e,"onDestroy",n);r&&this.editor.on("beforeCreate",r),i&&this.editor.on("create",i),o&&this.editor.on("update",o),s&&this.editor.on("selectionUpdate",s),l&&this.editor.on("transaction",l),a&&this.editor.on("focus",a),c&&this.editor.on("blur",c),d&&this.editor.on("destroy",d)})}}class iG{constructor(e={}){this.type="extension",this.name="extension",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=iT(ik(this,"addOptions",{name:this.name}))),this.storage=iT(ik(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new iG(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>iF(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new iG({...this.config,...e});return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=iT(ik(t,"addOptions",{name:t.name})),t.storage=iT(ik(t,"addStorage",{name:t.name,options:t.options})),t}}function iY(e,t,n){let{from:r,to:i}=t,{blockSeparator:o="\n\n",textSerializers:s={}}=n||{},l="";return e.nodesBetween(r,i,(e,n,a,c)=>{var d;e.isBlock&&n>r&&(l+=o);let h=null==s?void 0:s[e.type.name];if(h)return a&&(l+=h({node:e,pos:n,parent:a,index:c,range:t})),!1;e.isText&&(l+=null==(d=null==e?void 0:e.text)?void 0:d.slice(Math.max(r,n)-n,i-n))}),l}function iX(e){return Object.fromEntries(Object.entries(e.nodes).filter(([,e])=>e.spec.toText).map(([e,t])=>[e,t.spec.toText]))}let iQ=iG.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new te({key:new tr("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{let{editor:e}=this,{state:t,schema:n}=e,{doc:r,selection:i}=t,{ranges:o}=i,s=Math.min(...o.map(e=>e.$from.pos)),l=Math.max(...o.map(e=>e.$to.pos)),a=iX(n);return iY(r,{from:s,to:l},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:a})}}})]}});function iZ(e,t,n={strict:!0}){let r=Object.keys(t);return!r.length||r.every(r=>n.strict?t[r]===e[r]:iz(t[r])?t[r].test(e[r]):t[r]===e[r])}function i0(e,t,n={}){return e.find(e=>e.type===t&&iZ(Object.fromEntries(Object.keys(n).map(t=>[t,e.attrs[t]])),n))}function i1(e,t,n={}){return!!i0(e,t,n)}function i2(e,t,n){var r;if(!e||!t)return;let i=e.parent.childAfter(e.parentOffset);if(i.node&&i.node.marks.some(e=>e.type===t)||(i=e.parent.childBefore(e.parentOffset)),!i.node||!i.node.marks.some(e=>e.type===t)||(n=n||(null==(r=i.node.marks[0])?void 0:r.attrs),!i0([...i.node.marks],t,n)))return;let o=i.index,s=e.start()+i.offset,l=o+1,a=s+i.node.nodeSize;for(;o>0&&i1([...e.parent.child(o-1).marks],t,n);)o-=1,s-=e.parent.child(o).nodeSize;for(;l<e.parent.childCount&&i1([...e.parent.child(l).marks],t,n);)a+=e.parent.child(l).nodeSize,l+=1;return{from:s,to:a}}function i3(e,t){if("string"==typeof e){if(!t.marks[e])throw Error(`There is no mark type named '${e}'. Maybe you forgot to add the extension?`);return t.marks[e]}return e}function i4(e){return e instanceof eY}function i5(e=0,t=0,n=0){return Math.min(Math.max(e,t),n)}function i6(e,t=null){if(!t)return null;let n=eK.atStart(e),r=eK.atEnd(e);if("start"===t||!0===t)return n;if("end"===t)return r;let i=n.from,o=r.to;return"all"===t?eY.create(e,i5(0,i,o),i5(e.content.size,i,o)):eY.create(e,i5(t,i,o),i5(t,i,o))}function i7(){return"Android"===navigator.platform||/android/i.test(navigator.userAgent)}function i8(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}let i9=e=>{let t=e.childNodes;for(let n=t.length-1;n>=0;n-=1){let r=t[n];3===r.nodeType&&r.nodeValue&&/^(\n\s\s|\n)$/.test(r.nodeValue)?e.removeChild(r):1===r.nodeType&&i9(r)}return e};function oe(e){let t=`<body>${e}</body>`;return i9(new window.DOMParser().parseFromString(t,"text/html").body)}function ot(e,t,n){if(e instanceof D||e instanceof p)return e;n={slice:!0,parseOptions:{},...n};let r="object"==typeof e&&null!==e,i="string"==typeof e;if(r)try{if(Array.isArray(e)&&e.length>0)return p.fromArray(e.map(e=>t.nodeFromJSON(e)));let r=t.nodeFromJSON(e);return n.errorOnInvalidContent&&r.check(),r}catch(r){if(n.errorOnInvalidContent)throw Error("[tiptap error]: Invalid JSON content",{cause:r});return console.warn("[tiptap warn]: Invalid content.","Passed value:",e,"Error:",r),ot("",t,n)}if(i){if(n.errorOnInvalidContent){let r=!1,i="",o=new K({topNode:t.spec.topNode,marks:t.spec.marks,nodes:t.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:e=>(r=!0,i="string"==typeof e?e:e.outerHTML,null)}]}})});if(n.slice?U.fromSchema(o).parseSlice(oe(e),n.parseOptions):U.fromSchema(o).parse(oe(e),n.parseOptions),n.errorOnInvalidContent&&r)throw Error("[tiptap error]: Invalid HTML content",{cause:Error(`Invalid element found: ${i}`)})}let r=U.fromSchema(t);return n.slice?r.parseSlice(oe(e),n.parseOptions).content:r.parse(oe(e),n.parseOptions)}return ot("",t,n)}let on=e=>!("type"in e);function or(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}function oi(e,t,n={}){let{from:r,to:i,empty:o}=e.selection,s=t?iM(t,e.schema):null,l=[];e.doc.nodesBetween(r,i,(e,t)=>{if(e.isText)return;let n=Math.max(r,t),o=Math.min(i,t+e.nodeSize);l.push({node:e,from:n,to:o})});let a=i-r,c=l.filter(e=>!s||s.name===e.node.type.name).filter(e=>iZ(e.node.attrs,n,{strict:!1}));return o?!!c.length:c.reduce((e,t)=>e+t.to-t.from,0)>=a}function oo(e,t){return t.nodes[e]?"node":t.marks[e]?"mark":null}function ol(e,t){let n="string"==typeof t?[t]:t;return Object.keys(e).reduce((t,r)=>(n.includes(r)||(t[r]=e[r]),t),{})}function oa(e,t,n={},r={}){return ot(e,t,{slice:!1,parseOptions:n,errorOnInvalidContent:r.errorOnInvalidContent})}function oc(e,t){let n=i3(t,e.schema),{from:r,to:i,empty:o}=e.selection,s=[];o?(e.storedMarks&&s.push(...e.storedMarks),s.push(...e.selection.$head.marks())):e.doc.nodesBetween(r,i,e=>{s.push(...e.marks)});let l=s.find(e=>e.type.name===n.name);return l?{...l.attrs}:{}}function od(e,t){for(let n=e.depth;n>0;n-=1){let r=e.node(n);if(t(r))return{pos:n>0?e.before(n):0,start:e.start(n),depth:n,node:r}}}function oh(e){return t=>od(t.$from,e)}function op(e,t){let n=oo("string"==typeof t?t:t.name,e.schema);if("node"===n){let n=iM(t,e.schema),{from:r,to:i}=e.selection,o=[];e.doc.nodesBetween(r,i,e=>{o.push(e)});let s=o.reverse().find(e=>e.type.name===n.name);return s?{...s.attrs}:{}}return"mark"===n?oc(e,t):{}}function ou(e,t,n){let r=[];return e===t?n.resolve(e).marks().forEach(t=>{let i=i2(n.resolve(e),t.type);i&&r.push({mark:t,...i})}):n.nodesBetween(e,t,(e,t)=>{e&&(null==e?void 0:e.nodeSize)!==void 0&&r.push(...e.marks.map(n=>({from:t,to:t+e.nodeSize,mark:n})))}),r}function of(e,t,n){return Object.fromEntries(Object.entries(n).filter(([n])=>{let r=e.find(e=>e.type===t&&e.name===n);return!!r&&r.attribute.keepOnSplit}))}function om(e,t,n={}){let{empty:r,ranges:i}=e.selection,o=t?i3(t,e.schema):null;if(r)return!!(e.storedMarks||e.selection.$from.marks()).filter(e=>!o||o.name===e.type.name).find(e=>iZ(e.attrs,n,{strict:!1}));let s=0,l=[];if(i.forEach(({$from:t,$to:n})=>{let r=t.pos,i=n.pos;e.doc.nodesBetween(r,i,(e,t)=>{if(!e.isText&&!e.marks.length)return;let n=Math.max(r,t),o=Math.min(i,t+e.nodeSize);s+=o-n,l.push(...e.marks.map(e=>({mark:e,from:n,to:o})))})}),0===s)return!1;let a=l.filter(e=>!o||o.name===e.mark.type.name).filter(e=>iZ(e.mark.attrs,n,{strict:!1})).reduce((e,t)=>e+t.to-t.from,0),c=l.filter(e=>!o||e.mark.type!==o&&e.mark.type.excludes(o)).reduce((e,t)=>e+t.to-t.from,0);return(a>0?a+c:a)>=s}function og(e,t){let{nodeExtensions:n}=iS(t),r=n.find(t=>t.name===e);if(!r)return!1;let i={name:r.name,options:r.options,storage:r.storage},o=iT(ik(r,"group",i));return"string"==typeof o&&o.split(" ").includes("list")}function oy(e,{checkChildren:t=!0,ignoreWhitespace:n=!1}={}){var r;if(n){if("hardBreak"===e.type.name)return!0;if(e.isText)return/^\s*$/m.test(null!=(r=e.text)?r:"")}if(e.isText)return!e.text;if(e.isAtom||e.isLeaf)return!1;if(0===e.content.childCount)return!0;if(t){let r=!0;return e.content.forEach(e=>{!1!==r&&(oy(e,{ignoreWhitespace:n,checkChildren:t})||(r=!1))}),r}return!1}function ob(e,t){let n=e.storedMarks||e.selection.$to.parentOffset&&e.selection.$from.marks();if(n){let r=n.filter(e=>null==t?void 0:t.includes(e.type.name));e.tr.ensureMarks(r)}}let ow=(e,t)=>{let n=oh(e=>e.type===t)(e.selection);if(!n)return!0;let r=e.doc.resolve(Math.max(0,n.pos-1)).before(n.depth);if(void 0===r)return!0;let i=e.doc.nodeAt(r);return!(n.node.type===(null==i?void 0:i.type)&&eT(e.doc,n.pos))||(e.join(n.pos),!0)},ov=(e,t)=>{let n=oh(e=>e.type===t)(e.selection);if(!n)return!0;let r=e.doc.resolve(n.start).after(n.depth);if(void 0===r)return!0;let i=e.doc.nodeAt(r);return!(n.node.type===(null==i?void 0:i.type)&&eT(e.doc,r))||(e.join(r),!0)};var ox=Object.freeze({__proto__:null,blur:()=>({editor:e,view:t})=>(requestAnimationFrame(()=>{var n;e.isDestroyed||(t.dom.blur(),null==(n=null==window?void 0:window.getSelection())||n.removeAllRanges())}),!0),clearContent:(e=!1)=>({commands:t})=>t.setContent("",e),clearNodes:()=>({state:e,tr:t,dispatch:n})=>{let{selection:r}=t,{ranges:i}=r;return!n||(i.forEach(({$from:n,$to:r})=>{e.doc.nodesBetween(n.pos,r.pos,(e,n)=>{if(e.type.isText)return;let{doc:r,mapping:i}=t,o=r.resolve(i.map(n)),s=r.resolve(i.map(n+e.nodeSize)),l=o.blockRange(s);if(!l)return;let a=eS(l);if(e.type.isTextblock){let{defaultType:e}=o.parent.contentMatchAt(o.index());t.setNodeMarkup(l.start,e)}(a||0===a)&&t.lift(l,a)})}),!0)},command:e=>t=>e(t),createParagraphNear:()=>({state:e,dispatch:t})=>it(e,t),cut:(e,t)=>({editor:n,tr:r})=>{let{state:i}=n,o=i.doc.slice(e.from,e.to);r.deleteRange(e.from,e.to);let s=r.mapping.map(t);return r.insert(s,o.content),r.setSelection(new eY(r.doc.resolve(Math.max(s-1,0)))),!0},deleteCurrentNode:()=>({tr:e,dispatch:t})=>{let{selection:n}=e,r=n.$anchor.node();if(r.content.size>0)return!1;let i=e.selection.$anchor;for(let n=i.depth;n>0;n-=1)if(i.node(n).type===r.type){if(t){let t=i.before(n),r=i.after(n);e.delete(t,r).scrollIntoView()}return!0}return!1},deleteNode:e=>({tr:t,state:n,dispatch:r})=>{let i=iM(e,n.schema),o=t.selection.$anchor;for(let e=o.depth;e>0;e-=1)if(o.node(e).type===i){if(r){let n=o.before(e),r=o.after(e);t.delete(n,r).scrollIntoView()}return!0}return!1},deleteRange:e=>({tr:t,dispatch:n})=>{let{from:r,to:i}=e;return n&&t.delete(r,i),!0},deleteSelection:()=>({state:e,dispatch:t})=>rK(e,t),enter:()=>({commands:e})=>e.keyboardShortcut("Enter"),exitCode:()=>({state:e,dispatch:t})=>ie(e,t),extendMarkRange:(e,t={})=>({tr:n,state:r,dispatch:i})=>{let o=i3(e,r.schema),{doc:s,selection:l}=n,{$from:a,from:c,to:d}=l;if(i){let e=i2(a,o,t);if(e&&e.from<=c&&e.to>=d){let t=eY.create(s,e.from,e.to);n.setSelection(t)}}return!0},first:e=>t=>{let n="function"==typeof e?e(t):e;for(let e=0;e<n.length;e+=1)if(n[e](t))return!0;return!1},focus:(e=null,t={})=>({editor:n,view:r,tr:i,dispatch:o})=>{t={scrollIntoView:!0,...t};let s=()=>{(i8()||i7())&&r.dom.focus(),requestAnimationFrame(()=>{!n.isDestroyed&&(r.focus(),(null==t?void 0:t.scrollIntoView)&&n.commands.scrollIntoView())})};if(r.hasFocus()&&null===e||!1===e)return!0;if(o&&null===e&&!i4(n.state.selection))return s(),!0;let l=i6(i.doc,e)||n.state.selection,a=n.state.selection.eq(l);return o&&(a||i.setSelection(l),a&&i.storedMarks&&i.setStoredMarks(i.storedMarks),s()),!0},forEach:(e,t)=>n=>e.every((e,r)=>t(e,{...n,index:r})),insertContent:(e,t)=>({tr:n,commands:r})=>r.insertContentAt({from:n.selection.from,to:n.selection.to},e,t),insertContentAt:(e,t,n)=>({tr:r,dispatch:i,editor:o})=>{var s;if(i){let i,l;n={parseOptions:o.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...n};let a=e=>{o.emit("contentError",{editor:o,error:e,disableCollaboration:()=>{o.storage.collaboration&&(o.storage.collaboration.isDisabled=!0)}})},c={preserveWhitespace:"full",...n.parseOptions};if(!n.errorOnInvalidContent&&!o.options.enableContentCheck&&o.options.emitContentError)try{ot(t,o.schema,{parseOptions:c,errorOnInvalidContent:!0})}catch(e){a(e)}try{i=ot(t,o.schema,{parseOptions:c,errorOnInvalidContent:null!=(s=n.errorOnInvalidContent)?s:o.options.enableContentCheck})}catch(e){return a(e),!1}let{from:d,to:h}="number"==typeof e?{from:e,to:e}:{from:e.from,to:e.to},u=!0,f=!0;if((on(i)?i:[i]).forEach(e=>{e.check(),u=!!u&&e.isText&&0===e.marks.length,f=!!f&&e.isBlock}),d===h&&f){let{parent:e}=r.doc.resolve(d);!e.isTextblock||e.type.spec.code||e.childCount||(d-=1,h+=1)}if(u){if(Array.isArray(t))l=t.map(e=>e.text||"").join("");else if(t instanceof p){let e="";t.forEach(t=>{t.text&&(e+=t.text)}),l=e}else l="object"==typeof t&&t&&t.text?t.text:t;r.insertText(l,d,h)}else l=i,r.replaceWith(d,h,l);n.updateSelection&&function(e,t,n){let r=e.steps.length-1;if(r<t)return;let i=e.steps[r];if(!(i instanceof ew||i instanceof ev))return;let o=e.mapping.maps[r],s=0;o.forEach((e,t,n,r)=>{0===s&&(s=r)}),e.setSelection(eK.near(e.doc.resolve(s),-1))}(r,r.steps.length-1,0),n.applyInputRules&&r.setMeta("applyInputRules",{from:d,text:l}),n.applyPasteRules&&r.setMeta("applyPasteRules",{from:d,text:l})}return!0},joinBackward:()=>({state:e,dispatch:t})=>rU(e,t),joinDown:()=>({state:e,dispatch:t})=>r6(e,t),joinForward:()=>({state:e,dispatch:t})=>r2(e,t),joinItemBackward:()=>({state:e,dispatch:t,tr:n})=>{try{let r=eD(e.doc,e.selection.$from.pos,-1);if(null==r)return!1;return n.join(r,2),t&&t(n),!0}catch{return!1}},joinItemForward:()=>({state:e,dispatch:t,tr:n})=>{try{let r=eD(e.doc,e.selection.$from.pos,1);if(null==r)return!1;return n.join(r,2),t&&t(n),!0}catch{return!1}},joinTextblockBackward:()=>({state:e,dispatch:t})=>rG(e,t),joinTextblockForward:()=>({state:e,dispatch:t})=>rY(e,t),joinUp:()=>({state:e,dispatch:t})=>r5(e,t),keyboardShortcut:e=>({editor:t,view:n,tr:r,dispatch:i})=>{let o=(function(e){let t,n,r,i,o=e.split(/-(?!$)/),s=o[o.length-1];"Space"===s&&(s=" ");for(let e=0;e<o.length-1;e+=1){let s=o[e];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))t=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))i8()||or()?i=!0:n=!0;else throw Error(`Unrecognized modifier name: ${s}`)}return t&&(s=`Alt-${s}`),n&&(s=`Ctrl-${s}`),i&&(s=`Meta-${s}`),r&&(s=`Shift-${s}`),s})(e).split(/-(?!$)/),s=o.find(e=>!["Alt","Ctrl","Meta","Shift"].includes(e)),l=new KeyboardEvent("keydown",{key:"Space"===s?" ":s,altKey:o.includes("Alt"),ctrlKey:o.includes("Ctrl"),metaKey:o.includes("Meta"),shiftKey:o.includes("Shift"),bubbles:!0,cancelable:!0}),a=t.captureTransaction(()=>{n.someProp("handleKeyDown",e=>e(n,l))});return null==a||a.steps.forEach(e=>{let t=e.map(r.mapping);t&&i&&r.maybeStep(t)}),!0},lift:(e,t={})=>({state:n,dispatch:r})=>{let i=iM(e,n.schema);return!!oi(n,i,t)&&r7(n,r)},liftEmptyBlock:()=>({state:e,dispatch:t})=>ir(e,t),liftListItem:e=>({state:t,dispatch:n})=>(function(e){return function(t,n){let{$from:r,$to:i}=t.selection,o=r.blockRange(i,t=>t.childCount>0&&t.firstChild.type==e);return!!o&&(!n||(r.node(o.depth-1).type==e?function(e,t,n,r){let i=e.tr,o=r.end,s=r.$to.end(r.depth);o<s&&(i.step(new ev(o-1,s,o,s,new b(p.from(n.create(null,r.parent.copy())),1,0),1,!0)),r=new T(i.doc.resolve(r.$from.pos),i.doc.resolve(s),r.depth));let l=eS(r);if(null==l)return!1;i.lift(r,l);let a=i.doc.resolve(i.mapping.map(o,-1)-1);return eT(i.doc,a.pos)&&a.nodeBefore.type==a.nodeAfter.type&&i.join(a.pos),t(i.scrollIntoView()),!0}(t,n,e,o):function(e,t,n){let r=e.tr,i=n.parent;for(let e=n.end,t=n.endIndex-1,o=n.startIndex;t>o;t--)e-=i.child(t).nodeSize,r.delete(e-1,e+1);let o=r.doc.resolve(n.start),s=o.nodeAfter;if(r.mapping.map(n.end)!=n.start+o.nodeAfter.nodeSize)return!1;let l=0==n.startIndex,a=n.endIndex==i.childCount,c=o.node(-1),d=o.index(-1);if(!c.canReplace(d+ +!l,d+1,s.content.append(a?p.empty:p.from(i))))return!1;let h=o.pos,u=h+s.nodeSize;return r.step(new ev(h-!!l,u+ +!!a,h+1,u-1,new b((l?p.empty:p.from(i.copy(p.empty))).append(a?p.empty:p.from(i.copy(p.empty))),+!l,+!a),+!l)),t(r.scrollIntoView()),!0}(t,n,o)))}})(iM(e,t.schema))(t,n),newlineInCode:()=>({state:e,dispatch:t})=>r8(e,t),resetAttributes:(e,t)=>({tr:n,state:r,dispatch:i})=>{let o=null,s=null,l=oo("string"==typeof e?e:e.name,r.schema);return!!l&&("node"===l&&(o=iM(e,r.schema)),"mark"===l&&(s=i3(e,r.schema)),i&&n.selection.ranges.forEach(e=>{r.doc.nodesBetween(e.$from.pos,e.$to.pos,(e,r)=>{o&&o===e.type&&n.setNodeMarkup(r,void 0,ol(e.attrs,t)),s&&e.marks.length&&e.marks.forEach(i=>{s===i.type&&n.addMark(r,r+e.nodeSize,s.create(ol(i.attrs,t)))})})}),!0)},scrollIntoView:()=>({tr:e,dispatch:t})=>(t&&e.scrollIntoView(),!0),selectAll:()=>({tr:e,dispatch:t})=>{if(t){let t=new e0(e.doc);e.setSelection(t)}return!0},selectNodeBackward:()=>({state:e,dispatch:t})=>rZ(e,t),selectNodeForward:()=>({state:e,dispatch:t})=>r3(e,t),selectParentNode:()=>({state:e,dispatch:t})=>ii(e,t),selectTextblockEnd:()=>({state:e,dispatch:t})=>ia(e,t),selectTextblockStart:()=>({state:e,dispatch:t})=>il(e,t),setContent:(e,t=!1,n={},r={})=>({editor:i,tr:o,dispatch:s,commands:l})=>{var a,c;let{doc:d}=o;if("full"!==n.preserveWhitespace){let l=oa(e,i.schema,n,{errorOnInvalidContent:null!=(a=r.errorOnInvalidContent)?a:i.options.enableContentCheck});return s&&o.replaceWith(0,d.content.size,l).setMeta("preventUpdate",!t),!0}return s&&o.setMeta("preventUpdate",!t),l.insertContentAt({from:0,to:d.content.size},e,{parseOptions:n,errorOnInvalidContent:null!=(c=r.errorOnInvalidContent)?c:i.options.enableContentCheck})},setMark:(e,t={})=>({tr:n,state:r,dispatch:i})=>{let{selection:o}=n,{empty:s,ranges:l}=o,a=i3(e,r.schema);if(i)if(s){let e=oc(r,a);n.addStoredMark(a.create({...e,...t}))}else l.forEach(e=>{let i=e.$from.pos,o=e.$to.pos;r.doc.nodesBetween(i,o,(e,r)=>{let s=Math.max(r,i),l=Math.min(r+e.nodeSize,o);e.marks.find(e=>e.type===a)?e.marks.forEach(e=>{a===e.type&&n.addMark(s,l,a.create({...e.attrs,...t}))}):n.addMark(s,l,a.create(t))})});return function(e,t,n){var r;let{selection:i}=t,o=null;if(i4(i)&&(o=i.$cursor),o){let t=null!=(r=e.storedMarks)?r:o.marks();return!!n.isInSet(t)||!t.some(e=>e.type.excludes(n))}let{ranges:s}=i;return s.some(({$from:t,$to:r})=>{let i=0===t.depth&&e.doc.inlineContent&&e.doc.type.allowsMarkType(n);return e.doc.nodesBetween(t.pos,r.pos,(e,t,r)=>{if(i)return!1;if(e.isInline){let t=!r||r.type.allowsMarkType(n),o=!!n.isInSet(e.marks)||!e.marks.some(e=>e.type.excludes(n));i=t&&o}return!i}),i})}(r,n,a)},setMeta:(e,t)=>({tr:n})=>(n.setMeta(e,t),!0),setNode:(e,t={})=>({state:n,dispatch:r,chain:i})=>{let o,s=iM(e,n.schema);return(n.selection.$anchor.sameParent(n.selection.$head)&&(o=n.selection.$anchor.parent.attrs),s.isTextblock)?i().command(({commands:e})=>!!ic(s,{...o,...t})(n)||e.clearNodes()).command(({state:e})=>ic(s,{...o,...t})(e,r)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},setNodeSelection:e=>({tr:t,dispatch:n})=>{if(n){let{doc:n}=t,r=i5(e,0,n.content.size),i=eQ.create(n,r);t.setSelection(i)}return!0},setTextSelection:e=>({tr:t,dispatch:n})=>{if(n){let{doc:n}=t,{from:r,to:i}="number"==typeof e?{from:e,to:e}:e,o=eY.atStart(n).from,s=eY.atEnd(n).to,l=i5(r,o,s),a=i5(i,o,s),c=eY.create(n,l,a);t.setSelection(c)}return!0},sinkListItem:e=>({state:t,dispatch:n})=>(function(e){return function(t,n){let{$from:r,$to:i}=t.selection,o=r.blockRange(i,t=>t.childCount>0&&t.firstChild.type==e);if(!o)return!1;let s=o.startIndex;if(0==s)return!1;let l=o.parent,a=l.child(s-1);if(a.type!=e)return!1;if(n){let r=a.lastChild&&a.lastChild.type==l.type,i=p.from(r?e.create():null),s=new b(p.from(e.create(null,p.from(l.type.create(null,i)))),r?3:1,0),c=o.start,d=o.end;n(t.tr.step(new ev(c-(r?3:1),d,c,d,s,1,!0)).scrollIntoView())}return!0}})(iM(e,t.schema))(t,n),splitBlock:({keepMarks:e=!0}={})=>({tr:t,state:n,dispatch:r,editor:i})=>{let{selection:o,doc:s}=t,{$from:l,$to:a}=o,c=of(i.extensionManager.attributes,l.node().type.name,l.node().attrs);if(o instanceof eQ&&o.node.isBlock)return!!l.parentOffset&&!!eO(s,l.pos)&&(r&&(e&&ob(n,i.extensionManager.splittableMarks),t.split(l.pos).scrollIntoView()),!0);if(!l.parent.isBlock)return!1;let d=a.parentOffset===a.parent.content.size,h=0===l.depth?void 0:function(e){for(let t=0;t<e.edgeCount;t+=1){let{type:n}=e.edge(t);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}(l.node(-1).contentMatchAt(l.indexAfter(-1))),p=d&&h?[{type:h,attrs:c}]:void 0,u=eO(t.doc,t.mapping.map(l.pos),1,p);if(!p&&!u&&eO(t.doc,t.mapping.map(l.pos),1,h?[{type:h}]:void 0)&&(u=!0,p=h?[{type:h,attrs:c}]:void 0),r){if(u&&(o instanceof eY&&t.deleteSelection(),t.split(t.mapping.map(l.pos),1,p),h&&!d&&!l.parentOffset&&l.parent.type!==h)){let e=t.mapping.map(l.before()),n=t.doc.resolve(e);l.node(-1).canReplaceWith(n.index(),n.index()+1,h)&&t.setNodeMarkup(t.mapping.map(l.before()),h)}e&&ob(n,i.extensionManager.splittableMarks),t.scrollIntoView()}return u},splitListItem:(e,t={})=>({tr:n,state:r,dispatch:i,editor:o})=>{var s;let l=iM(e,r.schema),{$from:a,$to:c}=r.selection,d=r.selection.node;if(d&&d.isBlock||a.depth<2||!a.sameParent(c))return!1;let h=a.node(-1);if(h.type!==l)return!1;let u=o.extensionManager.attributes;if(0===a.parent.content.size&&a.node(-1).childCount===a.indexAfter(-1)){if(2===a.depth||a.node(-3).type!==l||a.index(-2)!==a.node(-2).childCount-1)return!1;if(i){let e=p.empty,r=a.index(-1)?1:a.index(-2)?2:3;for(let t=a.depth-r;t>=a.depth-3;t-=1)e=p.from(a.node(t).copy(e));let i=a.indexAfter(-1)<a.node(-2).childCount?1:a.indexAfter(-2)<a.node(-3).childCount?2:3,o={...of(u,a.node().type.name,a.node().attrs),...t},c=(null==(s=l.contentMatch.defaultType)?void 0:s.createAndFill(o))||void 0;e=e.append(p.from(l.createAndFill(null,c)||void 0));let d=a.before(a.depth-(r-1));n.replace(d,a.after(-i),new b(e,4-r,0));let h=-1;n.doc.nodesBetween(d,n.doc.content.size,(e,t)=>{if(h>-1)return!1;e.isTextblock&&0===e.content.size&&(h=t+1)}),h>-1&&n.setSelection(eY.near(n.doc.resolve(h))),n.scrollIntoView()}return!0}let f=c.pos===a.end()?h.contentMatchAt(0).defaultType:null,m={...of(u,h.type.name,h.attrs),...t},g={...of(u,a.node().type.name,a.node().attrs),...t};n.delete(a.pos,c.pos);let y=f?[{type:l,attrs:m},{type:f,attrs:g}]:[{type:l,attrs:m}];if(!eO(n.doc,a.pos,2))return!1;if(i){let{selection:e,storedMarks:t}=r,{splittableMarks:s}=o.extensionManager,l=t||e.$to.parentOffset&&e.$from.marks();if(n.split(a.pos,2,y).scrollIntoView(),!l||!i)return!0;let c=l.filter(e=>s.includes(e.type.name));n.ensureMarks(c)}return!0},toggleList:(e,t,n,r={})=>({editor:i,tr:o,state:s,dispatch:l,chain:a,commands:c,can:d})=>{let{extensions:h,splittableMarks:p}=i.extensionManager,u=iM(e,s.schema),f=iM(t,s.schema),{selection:m,storedMarks:g}=s,{$from:y,$to:b}=m,w=y.blockRange(b),v=g||m.$to.parentOffset&&m.$from.marks();if(!w)return!1;let x=oh(e=>og(e.type.name,h))(m);if(w.depth>=1&&x&&w.depth-x.depth<=1){if(x.node.type===u)return c.liftListItem(f);if(og(x.node.type.name,h)&&u.validContent(x.node.content)&&l)return a().command(()=>(o.setNodeMarkup(x.pos,u),!0)).command(()=>ow(o,u)).command(()=>ov(o,u)).run()}return n&&v&&l?a().command(()=>{let e=d().wrapInList(u,r),t=v.filter(e=>p.includes(e.type.name));return o.ensureMarks(t),!!e||c.clearNodes()}).wrapInList(u,r).command(()=>ow(o,u)).command(()=>ov(o,u)).run():a().command(()=>!!d().wrapInList(u,r)||c.clearNodes()).wrapInList(u,r).command(()=>ow(o,u)).command(()=>ov(o,u)).run()},toggleMark:(e,t={},n={})=>({state:r,commands:i})=>{let{extendEmptyMarkRange:o=!1}=n,s=i3(e,r.schema);return om(r,s,t)?i.unsetMark(s,{extendEmptyMarkRange:o}):i.setMark(s,t)},toggleNode:(e,t,n={})=>({state:r,commands:i})=>{let o,s=iM(e,r.schema),l=iM(t,r.schema),a=oi(r,s,n);return(r.selection.$anchor.sameParent(r.selection.$head)&&(o=r.selection.$anchor.parent.attrs),a)?i.setNode(l,o):i.setNode(s,{...o,...n})},toggleWrap:(e,t={})=>({state:n,commands:r})=>{let i=iM(e,n.schema);return oi(n,i,t)?r.lift(i):r.wrapIn(i,t)},undoInputRule:()=>({state:e,dispatch:t})=>{let n=e.plugins;for(let r=0;r<n.length;r+=1){let i,o=n[r];if(o.spec.isInputRules&&(i=o.getState(e))){if(t){let t=e.tr,n=i.transform;for(let e=n.steps.length-1;e>=0;e-=1)t.step(n.steps[e].invert(n.docs[e]));if(i.text){let n=t.doc.resolve(i.from).marks();t.replaceWith(i.from,i.to,e.schema.text(i.text,n))}else t.delete(i.from,i.to)}return!0}}return!1},unsetAllMarks:()=>({tr:e,dispatch:t})=>{let{selection:n}=e,{empty:r,ranges:i}=n;return!!r||(t&&i.forEach(t=>{e.removeMark(t.$from.pos,t.$to.pos)}),!0)},unsetMark:(e,t={})=>({tr:n,state:r,dispatch:i})=>{var o;let{extendEmptyMarkRange:s=!1}=t,{selection:l}=n,a=i3(e,r.schema),{$from:c,empty:d,ranges:h}=l;if(!i)return!0;if(d&&s){let{from:e,to:t}=l,r=null==(o=c.marks().find(e=>e.type===a))?void 0:o.attrs,i=i2(c,a,r);i&&(e=i.from,t=i.to),n.removeMark(e,t,a)}else h.forEach(e=>{n.removeMark(e.$from.pos,e.$to.pos,a)});return n.removeStoredMark(a),!0},updateAttributes:(e,t={})=>({tr:n,state:r,dispatch:i})=>{let o=null,s=null,l=oo("string"==typeof e?e:e.name,r.schema);return!!l&&("node"===l&&(o=iM(e,r.schema)),"mark"===l&&(s=i3(e,r.schema)),i&&n.selection.ranges.forEach(e=>{let i,l,a,c,d=e.$from.pos,h=e.$to.pos;n.selection.empty?r.doc.nodesBetween(d,h,(e,t)=>{o&&o===e.type&&(a=Math.max(t,d),c=Math.min(t+e.nodeSize,h),i=t,l=e)}):r.doc.nodesBetween(d,h,(e,r)=>{r<d&&o&&o===e.type&&(a=Math.max(r,d),c=Math.min(r+e.nodeSize,h),i=r,l=e),r>=d&&r<=h&&(o&&o===e.type&&n.setNodeMarkup(r,void 0,{...e.attrs,...t}),s&&e.marks.length&&e.marks.forEach(i=>{if(s===i.type){let o=Math.max(r,d),l=Math.min(r+e.nodeSize,h);n.addMark(o,l,s.create({...i.attrs,...t}))}}))}),l&&(void 0!==i&&n.setNodeMarkup(i,void 0,{...l.attrs,...t}),s&&l.marks.length&&l.marks.forEach(e=>{s===e.type&&n.addMark(a,c,s.create({...e.attrs,...t}))}))}),!0)},wrapIn:(e,t={})=>({state:n,dispatch:r})=>(function(e,t=null){return function(n,r){let{$from:i,$to:o}=n.selection,s=i.blockRange(o),l=s&&eC(s,e,t);return!!l&&(r&&r(n.tr.wrap(s,l).scrollIntoView()),!0)}})(iM(e,n.schema),t)(n,r),wrapInList:(e,t={})=>({state:n,dispatch:r})=>(function(e,t=null){return function(n,r){let{$from:i,$to:o}=n.selection,s=i.blockRange(o);if(!s)return!1;let l=r?n.tr:null;return!!function(e,t,n,r=null){let i=!1,o=t,s=t.$from.doc;if(t.depth>=2&&t.$from.node(t.depth-1).type.compatibleContent(n)&&0==t.startIndex){if(0==t.$from.index(t.depth-1))return!1;let e=s.resolve(t.start-2);o=new T(e,e,t.depth),t.endIndex<t.parent.childCount&&(t=new T(t.$from,s.resolve(t.$to.end(t.depth)),t.depth)),i=!0}let l=eC(o,n,r,t);return!!l&&(e&&function(e,t,n,r,i){let o=p.empty;for(let e=n.length-1;e>=0;e--)o=p.from(n[e].type.create(n[e].attrs,o));e.step(new ev(t.start-2*!!r,t.end,t.start,t.end,new b(o,0,0),n.length,!0));let s=0;for(let e=0;e<n.length;e++)n[e].type==i&&(s=e+1);let l=n.length-s,a=t.start+n.length-2*!!r,c=t.parent;for(let n=t.startIndex,r=t.endIndex,i=!0;n<r;n++,i=!1)!i&&eO(e.doc,a,l)&&(e.split(a,l),a+=2*l),a+=c.child(n).nodeSize}(e,t,l,i,n),!0)}(l,s,e,t)&&(r&&r(l.scrollIntoView()),!0)}})(iM(e,n.schema),t)(n,r)});let ok=iG.create({name:"commands",addCommands:()=>({...ox})}),oS=iG.create({name:"drop",addProseMirrorPlugins(){return[new te({key:new tr("tiptapDrop"),props:{handleDrop:(e,t,n,r)=>{this.editor.emit("drop",{editor:this.editor,event:t,slice:n,moved:r})}}})]}}),oC=iG.create({name:"editable",addProseMirrorPlugins(){return[new te({key:new tr("editable"),props:{editable:()=>this.editor.options.editable}})]}}),oM=new tr("focusEvents"),oN=iG.create({name:"focusEvents",addProseMirrorPlugins(){let{editor:e}=this;return[new te({key:oM,props:{handleDOMEvents:{focus:(t,n)=>{e.isFocused=!0;let r=e.state.tr.setMeta("focus",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1},blur:(t,n)=>{e.isFocused=!1;let r=e.state.tr.setMeta("blur",{event:n}).setMeta("addToHistory",!1);return t.dispatch(r),!1}}}})]}}),oA=iG.create({name:"keymap",addKeyboardShortcuts(){let e=()=>this.editor.commands.first(({commands:e})=>[()=>e.undoInputRule(),()=>e.command(({tr:t})=>{let{selection:n,doc:r}=t,{empty:i,$anchor:o}=n,{pos:s,parent:l}=o,a=o.parent.isTextblock&&s>0?t.doc.resolve(s-1):o,c=a.parent.type.spec.isolating,d=o.pos-o.parentOffset,h=c&&1===a.parent.childCount?d===o.pos:eK.atStart(r).from===s;return!!i&&!!l.type.isTextblock&&!l.textContent.length&&!!h&&(!h||"paragraph"!==o.parent.type.name)&&e.clearNodes()}),()=>e.deleteSelection(),()=>e.joinBackward(),()=>e.selectNodeBackward()]),t=()=>this.editor.commands.first(({commands:e})=>[()=>e.deleteSelection(),()=>e.deleteCurrentNode(),()=>e.joinForward(),()=>e.selectNodeForward()]),n={Enter:()=>this.editor.commands.first(({commands:e})=>[()=>e.newlineInCode(),()=>e.createParagraphNear(),()=>e.liftEmptyBlock(),()=>e.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:e,"Mod-Backspace":e,"Shift-Backspace":e,Delete:t,"Mod-Delete":t,"Mod-a":()=>this.editor.commands.selectAll()},r={...n},i={...n,"Ctrl-h":e,"Alt-Backspace":e,"Ctrl-d":t,"Ctrl-Alt-Backspace":t,"Alt-Delete":t,"Alt-d":t,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return i8()||or()?i:r},addProseMirrorPlugins(){return[new te({key:new tr("clearDocument"),appendTransaction:(e,t,n)=>{if(e.some(e=>e.getMeta("composition")))return;let r=e.some(e=>e.docChanged)&&!t.doc.eq(n.doc),i=e.some(e=>e.getMeta("preventClearDocument"));if(!r||i)return;let{empty:o,from:s,to:l}=t.selection,a=eK.atStart(t.doc).from,c=eK.atEnd(t.doc).to;if(o||s!==a||l!==c||!oy(n.doc))return;let d=n.tr,h=iw({state:n,transaction:d}),{commands:p}=new iv({editor:this.editor,state:h});if(p.clearNodes(),d.steps.length)return d}})]}}),oO=iG.create({name:"paste",addProseMirrorPlugins(){return[new te({key:new tr("tiptapPaste"),props:{handlePaste:(e,t,n)=>{this.editor.emit("paste",{editor:this.editor,event:t,slice:n})}}})]}}),oT=iG.create({name:"tabindex",addProseMirrorPlugins(){return[new te({key:new tr("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}});class oE{get name(){return this.node.type.name}constructor(e,t,n=!1,r=null){this.currentNode=null,this.actualDepth=null,this.isBlock=n,this.resolvedPos=e,this.editor=t,this.currentNode=r}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var e;return null!=(e=this.actualDepth)?e:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(e){let t=this.from,n=this.to;if(this.isBlock){if(0===this.content.size)return void console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);t=this.from+1,n=this.to-1}this.editor.commands.insertContentAt({from:t,to:n},e)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+ +!this.node.isText}get parent(){if(0===this.depth)return null;let e=this.resolvedPos.start(this.resolvedPos.depth-1);return new oE(this.resolvedPos.doc.resolve(e),this.editor)}get before(){let e=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.from-3)),new oE(e,this.editor)}get after(){let e=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return e.depth!==this.depth&&(e=this.resolvedPos.doc.resolve(this.to+3)),new oE(e,this.editor)}get children(){let e=[];return this.node.content.forEach((t,n)=>{let r=t.isBlock&&!t.isTextblock,i=t.isAtom&&!t.isText,o=this.pos+n+ +!i;if(o<0||o>this.resolvedPos.doc.nodeSize-2)return;let s=this.resolvedPos.doc.resolve(o);if(!r&&s.depth<=this.depth)return;let l=new oE(s,this.editor,r,r?t:null);r&&(l.actualDepth=this.depth+1),e.push(new oE(s,this.editor,r,r?t:null))}),e}get firstChild(){return this.children[0]||null}get lastChild(){let e=this.children;return e[e.length-1]||null}closest(e,t={}){let n=null,r=this.parent;for(;r&&!n;){if(r.node.type.name===e)if(Object.keys(t).length>0){let e=r.node.attrs,n=Object.keys(t);for(let r=0;r<n.length;r+=1){let i=n[r];if(e[i]!==t[i])break}}else n=r;r=r.parent}return n}querySelector(e,t={}){return this.querySelectorAll(e,t,!0)[0]||null}querySelectorAll(e,t={},n=!1){let r=[];if(!this.children||0===this.children.length)return r;let i=Object.keys(t);return this.children.forEach(o=>{(!n||!(r.length>0))&&(o.node.type.name===e&&i.every(e=>t[e]===o.node.attrs[e])&&r.push(o),n&&r.length>0||(r=r.concat(o.querySelectorAll(e,t,n))))}),r}setAttribute(e){let{tr:t}=this.editor.state;t.setNodeMarkup(this.from,void 0,{...this.node.attrs,...e}),this.editor.view.dispatch(t)}}let oD=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}

.tippy-box[data-animation=fade][data-state=hidden] {
  opacity: 0
}`;class oR extends ix{constructor(e={}){super(),this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.options={element:document.createElement("div"),content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:e})=>{throw e},onPaste:()=>null,onDrop:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(e),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.createView(),this.injectCSS(),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:e,slice:t,moved:n})=>this.options.onDrop(e,t,n)),this.on("paste",({event:e,slice:t})=>this.options.onPaste(e,t)),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&document&&(this.css=function(e,t,n){let r=document.querySelector("style[data-tiptap-style]");if(null!==r)return r;let i=document.createElement("style");return t&&i.setAttribute("nonce",t),i.setAttribute("data-tiptap-style",""),i.innerHTML=e,document.getElementsByTagName("head")[0].appendChild(i),i}(oD,this.options.injectNonce))}setOptions(e={}){this.options={...this.options,...e},this.view&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(e,t=!0){this.setOptions({editable:e}),t&&this.emit("update",{editor:this,transaction:this.state.tr})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get state(){return this.view.state}registerPlugin(e,t){let n=iO(t)?t(e,[...this.state.plugins]):[...this.state.plugins,e],r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}unregisterPlugin(e){if(this.isDestroyed)return;let t=this.state.plugins,n=t;if([].concat(e).forEach(e=>{let t="string"==typeof e?`${e}$`:e.key;n=n.filter(e=>!e.key.startsWith(t))}),t.length===n.length)return;let r=this.state.reconfigure({plugins:n});return this.view.updateState(r),r}createExtensionManager(){var e,t;let n=[...this.options.enableCoreExtensions?[oC,iQ.configure({blockSeparator:null==(t=null==(e=this.options.coreExtensionOptions)?void 0:e.clipboardTextSerializer)?void 0:t.blockSeparator}),ok,oN,oA,oT,oS,oO].filter(e=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[e.name]):[],...this.options.extensions].filter(e=>["extension","node","mark"].includes(null==e?void 0:e.type));this.extensionManager=new iU(n,this)}createCommandManager(){this.commandManager=new iv({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createView(){var e;let t;try{t=oa(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(e){if(!(e instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(e.message))throw e;this.emit("contentError",{editor:this,error:e,disableCollaboration:()=>{this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(e=>"collaboration"!==e.name),this.createExtensionManager()}}),t=oa(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}let n=i6(t,this.options.autofocus);this.view=new rE(this.options.element,{...this.options.editorProps,attributes:{role:"textbox",...null==(e=this.options.editorProps)?void 0:e.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:e9.create({doc:t,selection:n||void 0})});let r=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(r),this.createNodeViews(),this.prependClass(),this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(e){this.isCapturingTransaction=!0,e(),this.isCapturingTransaction=!1;let t=this.capturedTransaction;return this.capturedTransaction=null,t}dispatchTransaction(e){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=e;return}e.steps.forEach(e=>{var t;return null==(t=this.capturedTransaction)?void 0:t.step(e)});return}let t=this.state.apply(e),n=!this.state.selection.eq(t.selection);this.emit("beforeTransaction",{editor:this,transaction:e,nextState:t}),this.view.updateState(t),this.emit("transaction",{editor:this,transaction:e}),n&&this.emit("selectionUpdate",{editor:this,transaction:e});let r=e.getMeta("focus"),i=e.getMeta("blur");r&&this.emit("focus",{editor:this,event:r.event,transaction:e}),i&&this.emit("blur",{editor:this,event:i.event,transaction:e}),!e.docChanged||e.getMeta("preventUpdate")||this.emit("update",{editor:this,transaction:e})}getAttributes(e){return op(this.state,e)}isActive(e,t){let n="string"==typeof e?e:null,r="string"==typeof e?t:e;return function(e,t,n={}){if(!t)return oi(e,null,n)||om(e,null,n);let r=oo(t,e.schema);return"node"===r?oi(e,t,n):"mark"===r&&om(e,t,n)}(this.state,n,r)}getJSON(){return this.state.doc.toJSON()}getHTML(){return iL(this.state.doc.content,this.schema)}getText(e){let{blockSeparator:t="\n\n",textSerializers:n={}}=e||{};return function(e,t){let n={from:0,to:e.content.size};return iY(e,n,t)}(this.state.doc,{blockSeparator:t,textSerializers:{...iX(this.schema),...n}})}get isEmpty(){return oy(this.state.doc)}getCharacterCount(){return console.warn('[tiptap warn]: "editor.getCharacterCount()" is deprecated. Please use "editor.storage.characterCount.characters()" instead.'),this.state.doc.content.size-2}destroy(){if(this.emit("destroy"),this.view){let e=this.view.dom;e&&e.editor&&delete e.editor,this.view.destroy()}this.removeAllListeners()}get isDestroyed(){var e;return!(null==(e=this.view)?void 0:e.docView)}$node(e,t){var n;return(null==(n=this.$doc)?void 0:n.querySelector(e,t))||null}$nodes(e,t){var n;return(null==(n=this.$doc)?void 0:n.querySelectorAll(e,t))||null}$pos(e){return new oE(this.state.doc.resolve(e),this)}get $doc(){return this.$pos(0)}}function oI(e){return new i$({find:e.find,handler:({state:t,range:n,match:r})=>{let i=iT(e.getAttributes,void 0,r);if(!1===i||null===i)return null;let{tr:o}=t,s=r[r.length-1],l=r[0];if(s){let r=l.search(/\S/),a=n.from+l.indexOf(s),c=a+s.length;if(ou(n.from,n.to,t.doc).filter(t=>t.mark.type.excluded.find(n=>n===e.type&&n!==t.mark.type)).filter(e=>e.to>a).length)return null;c<n.to&&o.delete(c,n.to),a>n.from&&o.delete(n.from+r,a);let d=n.from+r+s.length;o.addMark(n.from+r,d,e.type.create(i||{})),o.removeStoredMark(e.type)}}})}function oP(e){return new i$({find:e.find,handler:({state:t,range:n,match:r})=>{let i=t.doc.resolve(n.from),o=iT(e.getAttributes,void 0,r)||{};if(!i.node(-1).canReplaceWith(i.index(-1),i.indexAfter(-1),e.type))return null;t.tr.delete(n.from,n.to).setBlockType(n.from,n.from,e.type,o)}})}function oL(e){return new i$({find:e.find,handler:({state:t,range:n,match:r,chain:i})=>{let o=iT(e.getAttributes,void 0,r)||{},s=t.tr.delete(n.from,n.to),l=s.doc.resolve(n.from).blockRange(),a=l&&eC(l,e.type,o);if(!a)return null;if(s.wrap(l,a),e.keepMarks&&e.editor){let{selection:n,storedMarks:r}=t,{splittableMarks:i}=e.editor.extensionManager,o=r||n.$to.parentOffset&&n.$from.marks();if(o){let e=o.filter(e=>i.includes(e.type.name));s.ensureMarks(e)}}if(e.keepAttributes){let t="bulletList"===e.type.name||"orderedList"===e.type.name?"listItem":"taskList";i().updateAttributes(t,o).run()}let c=s.doc.resolve(n.from-1).nodeBefore;c&&c.type===e.type&&eT(s.doc,n.from-1)&&(!e.joinPredicate||e.joinPredicate(r,c))&&s.join(n.from-1)}})}class oj{constructor(e={}){this.type="node",this.name="node",this.parent=null,this.child=null,this.config={name:this.name,defaultOptions:{}},this.config={...this.config,...e},this.name=this.config.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${this.name}".`),this.options=this.config.defaultOptions,this.config.addOptions&&(this.options=iT(ik(this,"addOptions",{name:this.name}))),this.storage=iT(ik(this,"addStorage",{name:this.name,options:this.options}))||{}}static create(e={}){return new oj(e)}configure(e={}){let t=this.extend({...this.config,addOptions:()=>iF(this.options,e)});return t.name=this.name,t.parent=this.parent,t}extend(e={}){let t=new oj(e);return t.parent=this,this.child=t,t.name=e.name?e.name:t.parent.name,e.defaultOptions&&Object.keys(e.defaultOptions).length>0&&console.warn(`[tiptap warn]: BREAKING CHANGE: "defaultOptions" is deprecated. Please use "addOptions" instead. Found in extension: "${t.name}".`),t.options=iT(ik(t,"addOptions",{name:t.name})),t.storage=iT(ik(t,"addStorage",{name:t.name,options:t.options})),t}}class oz{constructor(e,t,n){this.isDragging=!1,this.component=e,this.editor=t.editor,this.options={stopEvent:null,ignoreMutation:null,...n},this.extension=t.extension,this.node=t.node,this.decorations=t.decorations,this.innerDecorations=t.innerDecorations,this.view=t.view,this.HTMLAttributes=t.HTMLAttributes,this.getPos=t.getPos,this.mount()}mount(){}get dom(){return this.editor.view.dom}get contentDOM(){return null}onDragStart(e){var t,n,r,i,o,s,l;let{view:a}=this.editor,c=e.target,d=3===c.nodeType?null==(t=c.parentElement)?void 0:t.closest("[data-drag-handle]"):c.closest("[data-drag-handle]");if(!this.dom||(null==(n=this.contentDOM)?void 0:n.contains(c))||!d)return;let h=0,p=0;if(this.dom!==d){let t=this.dom.getBoundingClientRect(),n=d.getBoundingClientRect(),l=null!=(r=e.offsetX)?r:null==(i=e.nativeEvent)?void 0:i.offsetX,a=null!=(o=e.offsetY)?o:null==(s=e.nativeEvent)?void 0:s.offsetY;h=n.x-t.x+l,p=n.y-t.y+a}let u=this.dom.cloneNode(!0);null==(l=e.dataTransfer)||l.setDragImage(u,h,p);let f=this.getPos();if("number"!=typeof f)return;let m=eQ.create(a.state.doc,f),g=a.state.tr.setSelection(m);a.dispatch(g)}stopEvent(e){var t;if(!this.dom)return!1;if("function"==typeof this.options.stopEvent)return this.options.stopEvent({event:e});let n=e.target;if(!(this.dom.contains(n)&&!(null==(t=this.contentDOM)?void 0:t.contains(n))))return!1;let r=e.type.startsWith("drag"),i="drop"===e.type;if((["INPUT","BUTTON","SELECT","TEXTAREA"].includes(n.tagName)||n.isContentEditable)&&!i&&!r)return!0;let{isEditable:o}=this.editor,{isDragging:s}=this,l=!!this.node.type.spec.draggable,a=eQ.isSelectable(this.node),c="copy"===e.type,d="paste"===e.type,h="cut"===e.type,p="mousedown"===e.type;if(!l&&a&&r&&e.target===this.dom&&e.preventDefault(),l&&r&&!s&&e.target===this.dom)return e.preventDefault(),!1;if(l&&o&&!s&&p){let e=n.closest("[data-drag-handle]");e&&(this.dom===e||this.dom.contains(e))&&(this.isDragging=!0,document.addEventListener("dragend",()=>{this.isDragging=!1},{once:!0}),document.addEventListener("drop",()=>{this.isDragging=!1},{once:!0}),document.addEventListener("mouseup",()=>{this.isDragging=!1},{once:!0}))}return!s&&!i&&!c&&!d&&!h&&(!p||!a)}ignoreMutation(e){return!this.dom||!this.contentDOM||("function"==typeof this.options.ignoreMutation?this.options.ignoreMutation({mutation:e}):!!this.node.isLeaf||!!this.node.isAtom||!("selection"===e.type||this.dom.contains(e.target)&&"childList"===e.type&&(i8()||i7())&&this.editor.isFocused&&[...Array.from(e.addedNodes),...Array.from(e.removedNodes)].every(e=>e.isContentEditable))&&(this.contentDOM===e.target&&"attributes"===e.type||!this.contentDOM.contains(e.target)))}updateAttributes(e){this.editor.commands.command(({tr:t})=>{let n=this.getPos();return"number"==typeof n&&(t.setNodeMarkup(n,void 0,{...this.node.attrs,...e}),!0)})}deleteNode(){let e=this.getPos();if("number"!=typeof e)return;let t=e+this.node.nodeSize;this.editor.commands.deleteRange({from:e,to:t})}}function o$(e){return new i_({find:e.find,handler:({state:t,range:n,match:r,pasteEvent:i})=>{let o=iT(e.getAttributes,void 0,r,i);if(!1===o||null===o)return null;let{tr:s}=t,l=r[r.length-1],a=r[0],c=n.to;if(l){let r=a.search(/\S/),i=n.from+a.indexOf(l),d=i+l.length;if(ou(n.from,n.to,t.doc).filter(t=>t.mark.type.excluded.find(n=>n===e.type&&n!==t.mark.type)).filter(e=>e.to>i).length)return null;d<n.to&&s.delete(d,n.to),i>n.from&&s.delete(n.from+r,i),c=n.from+r+l.length,s.addMark(n.from+r,c,e.type.create(o||{})),s.removeStoredMark(e.type)}}})}var oB={exports:{}},oH={};oB.exports=function(){if(i)return oH;i=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=c.useState,n=c.useEffect,r=c.useLayoutEffect,o=c.useDebugValue;function s(t){var n=t.getSnapshot;t=t.value;try{var r=n();return!e(t,r)}catch(e){return!0}}var l="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,i){var l=i(),a=t({inst:{value:l,getSnapshot:i}}),c=a[0].inst,d=a[1];return r(function(){c.value=l,c.getSnapshot=i,s(c)&&d({inst:c})},[e,l,i]),n(function(){return s(c)&&d({inst:c}),e(function(){s(c)&&d({inst:c})})},[e]),o(l),l};return oH.useSyncExternalStore=void 0!==c.useSyncExternalStore?c.useSyncExternalStore:l,oH}();var oV=oB.exports;let oF=(...e)=>t=>{e.forEach(e=>{"function"==typeof e?e(t):e&&(e.current=t)})},oW=({contentComponent:e})=>{let t=oV.useSyncExternalStore(e.subscribe,e.getSnapshot,e.getServerSnapshot);return c.createElement(c.Fragment,null,Object.values(t))};class o_ extends c.Component{constructor(e){var t;super(e),this.editorContentRef=c.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null==(t=e.editor)?void 0:t.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let e=this.props.editor;if(e&&!e.isDestroyed&&e.options.element){if(e.contentComponent)return;let t=this.editorContentRef.current;t.append(...e.options.element.childNodes),e.setOptions({element:t}),e.contentComponent=function(){let e=new Set,t={};return{subscribe:t=>(e.add(t),()=>{e.delete(t)}),getSnapshot:()=>t,getServerSnapshot:()=>t,setRenderer(n,r){t={...t,[n]:d.createPortal(r.reactElement,r.element,n)},e.forEach(e=>e())},removeRenderer(n){let r={...t};delete r[n],t=r,e.forEach(e=>e())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=e.contentComponent.subscribe(()=>{this.setState(e=>e.hasContentComponentInitialized?e:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),e.createNodeViews(),this.initialized=!0}}componentWillUnmount(){let e=this.props.editor;if(!e||(this.initialized=!1,e.isDestroyed||e.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),e.contentComponent=null,!e.options.element.firstChild))return;let t=document.createElement("div");t.append(...e.options.element.childNodes),e.setOptions({element:t})}render(){let{editor:e,innerRef:t,...n}=this.props;return c.createElement(c.Fragment,null,c.createElement("div",{ref:oF(t,this.editorContentRef),...n}),(null==e?void 0:e.contentComponent)&&c.createElement(oW,{contentComponent:e.contentComponent}))}}let oq=(0,c.forwardRef)((e,t)=>{let n=c.useMemo(()=>Math.floor(0xffffffff*Math.random()).toString(),[e.editor]);return c.createElement(o_,{key:n,innerRef:t,...e})}),oK=c.memo(oq);var oJ=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;if(Array.isArray(t)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(!e(t[i],n[i]))return!1;return!0}if(t instanceof Map&&n instanceof Map){if(t.size!==n.size)return!1;for(i of t.entries())if(!n.has(i[0]))return!1;for(i of t.entries())if(!e(i[1],n.get(i[0])))return!1;return!0}if(t instanceof Set&&n instanceof Set){if(t.size!==n.size)return!1;for(i of t.entries())if(!n.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(t)&&ArrayBuffer.isView(n)){if((r=t.length)!=n.length)return!1;for(i=r;0!=i--;)if(t[i]!==n[i])return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((r=(o=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(i=r;0!=i--;)if(!Object.prototype.hasOwnProperty.call(n,o[i]))return!1;for(i=r;0!=i--;){var r,i,o,s=o[i];if(("_owner"!==s||!t.$$typeof)&&!e(t[s],n[s]))return!1}return!0}return t!=t&&n!=n}),oU={exports:{}},oG={};oU.exports=function(){if(o)return oG;o=1;var e="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},t=oV.useSyncExternalStore,n=c.useRef,r=c.useEffect,i=c.useMemo,s=c.useDebugValue;return oG.useSyncExternalStoreWithSelector=function(o,l,a,c,d){var h=n(null);if(null===h.current){var p={hasValue:!1,value:null};h.current=p}else p=h.current;var u=t(o,(h=i(function(){function t(t){if(!i){if(i=!0,n=t,t=c(t),void 0!==d&&p.hasValue){var o=p.value;if(d(o,t))return r=o}return r=t}if(o=r,e(n,t))return o;var s=c(t);return void 0!==d&&d(o,s)?o:(n=t,r=s)}var n,r,i=!1,o=void 0===a?null:a;return[function(){return t(l())},null===o?void 0:function(){return t(o())}]},[l,a,c,d]))[0],h[1]);return r(function(){p.hasValue=!0,p.value=u},[u]),s(u),u},oG}();var oY=oU.exports;let oX="undefined"!=typeof window?c.useLayoutEffect:c.useEffect;class oQ{constructor(e){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=e,this.lastSnapshot={editor:e,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(e){return this.subscribers.add(e),()=>{this.subscribers.delete(e)}}watch(e){if(this.editor=e,this.editor){let e=()=>{this.transactionNumber+=1,this.subscribers.forEach(e=>e())},t=this.editor;return t.on("transaction",e),()=>{t.off("transaction",e)}}}}let oZ="undefined"==typeof window,o0=oZ||!!("undefined"!=typeof window&&window.next);class o1{constructor(e){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=e,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(e){this.editor=e,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(e=>e())}getInitialEditor(){return void 0===this.options.current.immediatelyRender?oZ||o0?null:this.createEditor():(this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null)}createEditor(){return new oR({...this.options.current,onBeforeCreate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onBeforeCreate)?void 0:n.call(t,...e)},onBlur:(...e)=>{var t,n;return null==(n=(t=this.options.current).onBlur)?void 0:n.call(t,...e)},onCreate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onCreate)?void 0:n.call(t,...e)},onDestroy:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDestroy)?void 0:n.call(t,...e)},onFocus:(...e)=>{var t,n;return null==(n=(t=this.options.current).onFocus)?void 0:n.call(t,...e)},onSelectionUpdate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onSelectionUpdate)?void 0:n.call(t,...e)},onTransaction:(...e)=>{var t,n;return null==(n=(t=this.options.current).onTransaction)?void 0:n.call(t,...e)},onUpdate:(...e)=>{var t,n;return null==(n=(t=this.options.current).onUpdate)?void 0:n.call(t,...e)},onContentError:(...e)=>{var t,n;return null==(n=(t=this.options.current).onContentError)?void 0:n.call(t,...e)},onDrop:(...e)=>{var t,n;return null==(n=(t=this.options.current).onDrop)?void 0:n.call(t,...e)},onPaste:(...e)=>{var t,n;return null==(n=(t=this.options.current).onPaste)?void 0:n.call(t,...e)}})}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(e){return this.subscriptions.add(e),()=>{this.subscriptions.delete(e)}}static compareOptions(e,t){return Object.keys(e).every(n=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)||("extensions"===n&&e.extensions&&t.extensions?e.extensions.length===t.extensions.length&&e.extensions.every((e,n)=>{var r;return e===(null==(r=t.extensions)?void 0:r[n])}):e[n]===t[n]))}onRender(e){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===e.length?o1.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(e),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(e){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=e;return}if(this.previousDeps.length===e.length&&this.previousDeps.every((t,n)=>t===e[n]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=e}scheduleDestroy(){let e=this.instanceId,t=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===e){t&&t.setOptions(this.options.current);return}t&&!t.isDestroyed&&(t.destroy(),this.instanceId===e&&this.setEditor(null))},1)}}let o2=((0,c.createContext)({editor:null}).Consumer,(0,c.createContext)({onDragStart:void 0})),o3=()=>(0,c.useContext)(o2);function o4(e){return!!("function"==typeof e&&e.prototype&&e.prototype.isReactComponent)}function o5(e){return!!("object"==typeof e&&e.$$typeof&&("Symbol(react.forward_ref)"===e.$$typeof.toString()||"react.forward_ref"===e.$$typeof.description))}c.forwardRef((e,t)=>{let{onDragStart:n}=o3(),r=e.as||"div";return c.createElement(r,{...e,ref:t,"data-node-view-wrapper":"",onDragStart:n,style:{whiteSpace:"normal",...e.style}})});class o6{constructor(e,{editor:t,props:n={},as:r="div",className:i=""}){this.ref=null,this.id=Math.floor(0xffffffff*Math.random()).toString(),this.component=e,this.editor=t,this.props=n,this.element=document.createElement(r),this.element.classList.add("react-renderer"),i&&this.element.classList.add(...i.split(" ")),queueMicrotask(()=>{this.render()})}render(){var e;let t=this.component,n=this.props,r=this.editor,i=function(){try{if(c.version)return parseInt(c.version.split(".")[0],10)>=19}catch{}return!1}(),o=function(e){if(o4(e)||o5(e))return!0;if("object"==typeof e&&e.$$typeof&&("Symbol(react.memo)"===e.$$typeof.toString()||"react.memo"===e.$$typeof.description)){let t=e.type;if(t)return o4(t)||o5(t)}return!1}(t),s={...n};s.ref&&!(i||o)&&delete s.ref,!s.ref&&(i||o)&&(s.ref=e=>{this.ref=e}),this.reactElement=c.createElement(t,{...s}),null==(e=null==r?void 0:r.contentComponent)||e.setRenderer(this.id,this)}updateProps(e={}){this.props={...this.props,...e},this.render()}destroy(){var e;let t=this.editor;null==(e=null==t?void 0:t.contentComponent)||e.removeRenderer(this.id)}updateAttributes(e){Object.keys(e).forEach(t=>{this.element.setAttribute(t,e[t])})}}let o7=/^\s*>\s$/,o8=oj.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:e}){return["blockquote",iN(this.options.HTMLAttributes,e),0]},addCommands(){return{setBlockquote:()=>({commands:e})=>e.wrapIn(this.name),toggleBlockquote:()=>({commands:e})=>e.toggleWrap(this.name),unsetBlockquote:()=>({commands:e})=>e.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[oL({find:o7,type:this.type})]}}),o9=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,se=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,st=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,sn=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,sr=iW.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:e=>"normal"!==e.style.fontWeight&&null},{style:"font-weight=400",clearMark:e=>e.type.name===this.name},{style:"font-weight",getAttrs:e=>/^(bold(er)?|[5-9]\d{2,})$/.test(e)&&null}]},renderHTML({HTMLAttributes:e}){return["strong",iN(this.options.HTMLAttributes,e),0]},addCommands(){return{setBold:()=>({commands:e})=>e.setMark(this.name),toggleBold:()=>({commands:e})=>e.toggleMark(this.name),unsetBold:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[oI({find:o9,type:this.type}),oI({find:st,type:this.type})]},addPasteRules(){return[o$({find:se,type:this.type}),o$({find:sn,type:this.type})]}}),si="textStyle",so=/^\s*([-+*])\s$/,ss=oj.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:e}){return["ul",iN(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleBulletList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(si)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let e=oL({find:so,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(e=oL({find:so,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(si),editor:this.editor})),[e]}}),sl=/(^|[^`])`([^`]+)`(?!`)/,sa=/(^|[^`])`([^`]+)`(?!`)/g,sc=iW.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:e}){return["code",iN(this.options.HTMLAttributes,e),0]},addCommands(){return{setCode:()=>({commands:e})=>e.setMark(this.name),toggleCode:()=>({commands:e})=>e.toggleMark(this.name),unsetCode:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[oI({find:sl,type:this.type})]},addPasteRules(){return[o$({find:sa,type:this.type})]}}),sd=/^```([a-z]+)?[\s\n]$/,sh=/^~~~([a-z]+)?[\s\n]$/,sp=oj.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:e=>{var t;let{languageClassPrefix:n}=this.options,r=[...(null==(t=e.firstElementChild)?void 0:t.classList)||[]].filter(e=>e.startsWith(n)).map(e=>e.replace(n,""))[0];return r||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:e,HTMLAttributes:t}){return["pre",iN(this.options.HTMLAttributes,t),["code",{class:e.attrs.language?this.options.languageClassPrefix+e.attrs.language:null},0]]},addCommands(){return{setCodeBlock:e=>({commands:t})=>t.setNode(this.name,e),toggleCodeBlock:e=>({commands:t})=>t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:e,$anchor:t}=this.editor.state.selection,n=1===t.pos;return!!e&&t.parent.type.name===this.name&&(!!n||!t.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:e})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:t}=e,{selection:n}=t,{$from:r,empty:i}=n;if(!i||r.parent.type!==this.type)return!1;let o=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith("\n\n");return!!o&&!!s&&e.chain().command(({tr:e})=>(e.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:e})=>{if(!this.options.exitOnArrowDown)return!1;let{state:t}=e,{selection:n,doc:r}=t,{$from:i,empty:o}=n;if(!o||i.parent.type!==this.type||i.parentOffset!==i.parent.nodeSize-2)return!1;let s=i.after();return void 0!==s&&(r.nodeAt(s)?e.commands.command(({tr:e})=>(e.setSelection(eK.near(r.resolve(s))),!0)):e.commands.exitCode())}}},addInputRules(){return[oP({find:sd,type:this.type,getAttributes:e=>({language:e[1]})}),oP({find:sh,type:this.type,getAttributes:e=>({language:e[1]})})]},addProseMirrorPlugins(){return[new te({key:new tr("codeBlockVSCodeHandler"),props:{handlePaste:(e,t)=>{if(!t.clipboardData||this.editor.isActive(this.type.name))return!1;let n=t.clipboardData.getData("text/plain"),r=t.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,o=null==i?void 0:i.mode;if(!n||!o)return!1;let{tr:s,schema:l}=e.state,a=l.text(n.replace(/\r\n?/g,"\n"));return s.replaceSelectionWith(this.type.create({language:o},a)),s.selection.$from.parent.type!==this.type&&s.setSelection(eY.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),e.dispatch(s),!0}}})]}}),su=oj.create({name:"doc",topNode:!0,content:"block+"});class sf{constructor(e,t){var n;this.editorView=e,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!=(n=t.width)?n:1,this.color=!1===t.color?void 0:t.color||"black",this.class=t.class,this.handlers=["dragover","dragend","drop","dragleave"].map(t=>{let n=e=>{this[t](e)};return e.dom.addEventListener(t,n),{name:t,handler:n}})}destroy(){this.handlers.forEach(({name:e,handler:t})=>this.editorView.dom.removeEventListener(e,t))}update(e,t){null!=this.cursorPos&&t.doc!=e.state.doc&&(this.cursorPos>e.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(e){e!=this.cursorPos&&(this.cursorPos=e,null==e?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let e,t,n=this.editorView.state.doc.resolve(this.cursorPos),r=!n.parent.inlineContent,i,o=this.editorView.dom,s=o.getBoundingClientRect(),l=s.width/o.offsetWidth,a=s.height/o.offsetHeight;if(r){let e=n.nodeBefore,t=n.nodeAfter;if(e||t){let n=this.editorView.nodeDOM(this.cursorPos-(e?e.nodeSize:0));if(n){let r=n.getBoundingClientRect(),o=e?r.bottom:r.top;e&&t&&(o=(o+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let s=this.width/2*a;i={left:r.left,right:r.right,top:o-s,bottom:o+s}}}}if(!i){let e=this.editorView.coordsAtPos(this.cursorPos),t=this.width/2*l;i={left:e.left-t,right:e.left+t,top:e.top,bottom:e.bottom}}let c=this.editorView.dom.offsetParent;if(!this.element&&(this.element=c.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",r),this.element.classList.toggle("prosemirror-dropcursor-inline",!r),c&&(c!=document.body||"static"!=getComputedStyle(c).position)){let n=c.getBoundingClientRect(),r=n.width/c.offsetWidth,i=n.height/c.offsetHeight;e=n.left-c.scrollLeft*r,t=n.top-c.scrollTop*i}else e=-pageXOffset,t=-pageYOffset;this.element.style.left=(i.left-e)/l+"px",this.element.style.top=(i.top-t)/a+"px",this.element.style.width=(i.right-i.left)/l+"px",this.element.style.height=(i.bottom-i.top)/a+"px"}scheduleRemoval(e){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),e)}dragover(e){if(!this.editorView.editable)return;let t=this.editorView.posAtCoords({left:e.clientX,top:e.clientY}),n=t&&t.inside>=0&&this.editorView.state.doc.nodeAt(t.inside),r=n&&n.type.spec.disableDropCursor,i="function"==typeof r?r(this.editorView,t,e):r;if(t&&!i){let e=t.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let t=eR(this.editorView.state.doc,e,this.editorView.dragging.slice);null!=t&&(e=t)}this.setCursor(e),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(e){this.editorView.dom.contains(e.relatedTarget)||this.setCursor(null)}}let sm=iG.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(e={}){return new te({view:t=>new sf(t,e)})}(this.options)]}});class sg extends eK{constructor(e){super(e,e)}map(e,t){let n=e.resolve(t.map(this.head));return sg.valid(n)?new sg(n):eK.near(n)}content(){return b.empty}eq(e){return e instanceof sg&&e.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(e,t){if("number"!=typeof t.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new sg(e.resolve(t.pos))}getBookmark(){return new sy(this.anchor)}static valid(e){let t=e.parent;if(t.isTextblock||!function(e){for(let t=e.depth;t>=0;t--){let n=e.index(t),r=e.node(t);if(0==n){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n-1);;e=e.lastChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e)||!function(e){for(let t=e.depth;t>=0;t--){let n=e.indexAfter(t),r=e.node(t);if(n==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let e=r.child(n);;e=e.firstChild){if(0==e.childCount&&!e.inlineContent||e.isAtom||e.type.spec.isolating)return!0;if(e.inlineContent)return!1}}return!0}(e))return!1;let n=t.type.spec.allowGapCursor;if(null!=n)return n;let r=t.contentMatchAt(e.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(e,t,n=!1){r:for(;;){if(!n&&sg.valid(e))return e;let r=e.pos,i=null;for(let n=e.depth;;n--){let o=e.node(n);if(t>0?e.indexAfter(n)<o.childCount:e.index(n)>0){i=o.child(t>0?e.indexAfter(n):e.index(n)-1);break}if(0==n)return null;r+=t;let s=e.doc.resolve(r);if(sg.valid(s))return s}for(;;){let o=t>0?i.firstChild:i.lastChild;if(!o){if(i.isAtom&&!i.isText&&!eQ.isSelectable(i)){e=e.doc.resolve(r+i.nodeSize*t),n=!1;continue r}break}i=o,r+=t;let s=e.doc.resolve(r);if(sg.valid(s))return s}return null}}}sg.prototype.visible=!1,sg.findFrom=sg.findGapCursorFrom,eK.jsonID("gapcursor",sg);class sy{constructor(e){this.pos=e}map(e){return new sy(e.map(this.pos))}resolve(e){let t=e.resolve(this.pos);return sg.valid(t)?new sg(t):eK.near(t)}}let sb=rq({ArrowLeft:sw("horiz",-1),ArrowRight:sw("horiz",1),ArrowUp:sw("vert",-1),ArrowDown:sw("vert",1)});function sw(e,t){let n="vert"==e?t>0?"down":"up":t>0?"right":"left";return function(e,r,i){let o=e.selection,s=t>0?o.$to:o.$from,l=o.empty;if(o instanceof eY){if(!i.endOfTextblock(n)||0==s.depth)return!1;l=!1,s=e.doc.resolve(t>0?s.after():s.before())}let a=sg.findGapCursorFrom(s,t,l);return!!a&&(r&&r(e.tr.setSelection(new sg(a))),!0)}}function sv(e,t,n){if(!e||!e.editable)return!1;let r=e.state.doc.resolve(t);if(!sg.valid(r))return!1;let i=e.posAtCoords({left:n.clientX,top:n.clientY});return!(i&&i.inside>-1&&eQ.isSelectable(e.state.doc.nodeAt(i.inside)))&&(e.dispatch(e.state.tr.setSelection(new sg(r))),!0)}function sx(e,t){if("insertCompositionText"!=t.inputType||!(e.state.selection instanceof sg))return!1;let{$from:n}=e.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(e.state.schema.nodes.text);if(!r)return!1;let i=p.empty;for(let e=r.length-1;e>=0;e--)i=p.from(r[e].createAndFill(null,i));let o=e.state.tr.replace(n.pos,n.pos,new b(i,0,0));return o.setSelection(eY.near(o.doc.resolve(n.pos+1))),e.dispatch(o),!1}function sk(e){if(!(e.selection instanceof sg))return null;let t=document.createElement("div");return t.className="ProseMirror-gapcursor",rl.create(e.doc,[ri.widget(e.selection.head,t,{key:"gapcursor"})])}let sS=iG.create({name:"gapCursor",addProseMirrorPlugins:()=>[new te({props:{decorations:sk,createSelectionBetween:(e,t,n)=>t.pos==n.pos&&sg.valid(n)?new sg(n):null,handleClick:sv,handleKeyDown:sb,handleDOMEvents:{beforeinput:sx}}})],extendNodeSchema(e){var t;let n={name:e.name,options:e.options,storage:e.storage};return{allowGapCursor:null!=(t=iT(ik(e,"allowGapCursor",n)))?t:null}}}),sC=oj.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:e}){return["br",iN(this.options.HTMLAttributes,e)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:e,chain:t,state:n,editor:r})=>e.first([()=>e.exitCode(),()=>e.command(()=>{let{selection:e,storedMarks:i}=n;if(e.$from.parent.type.spec.isolating)return!1;let{keepMarks:o}=this.options,{splittableMarks:s}=r.extensionManager,l=i||e.$to.parentOffset&&e.$from.marks();return t().insertContent({type:this.name}).command(({tr:e,dispatch:t})=>{if(t&&l&&o){let t=l.filter(e=>s.includes(e.type.name));e.ensureMarks(t)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),sM=oj.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(e=>({tag:`h${e}`,attrs:{level:e}}))},renderHTML({node:e,HTMLAttributes:t}){let n=this.options.levels.includes(e.attrs.level)?e.attrs.level:this.options.levels[0];return[`h${n}`,iN(this.options.HTMLAttributes,t),0]},addCommands(){return{setHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.setNode(this.name,e),toggleHeading:e=>({commands:t})=>!!this.options.levels.includes(e.level)&&t.toggleNode(this.name,"paragraph",e)}},addKeyboardShortcuts(){return this.options.levels.reduce((e,t)=>({...e,...{[`Mod-Alt-${t}`]:()=>this.editor.commands.toggleHeading({level:t})}}),{})},addInputRules(){return this.options.levels.map(e=>oP({find:RegExp(`^(#{${Math.min(...this.options.levels)},${e}})\\s$`),type:this.type,getAttributes:{level:e}}))}});var sN=function(){};sN.prototype.append=function(e){return e.length?(e=sN.from(e),!this.length&&e||e.length<200&&this.leafAppend(e)||this.length<200&&e.leafPrepend(this)||this.appendInner(e)):this},sN.prototype.prepend=function(e){return e.length?sN.from(e).append(this):this},sN.prototype.appendInner=function(e){return new sO(this,e)},sN.prototype.slice=function(e,t){return(void 0===e&&(e=0),void 0===t&&(t=this.length),e>=t)?sN.empty:this.sliceInner(Math.max(0,e),Math.min(this.length,t))},sN.prototype.get=function(e){if(!(e<0)&&!(e>=this.length))return this.getInner(e)},sN.prototype.forEach=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length),t<=n?this.forEachInner(e,t,n,0):this.forEachInvertedInner(e,t,n,0)},sN.prototype.map=function(e,t,n){void 0===t&&(t=0),void 0===n&&(n=this.length);var r=[];return this.forEach(function(t,n){return r.push(e(t,n))},t,n),r},sN.from=function(e){return e instanceof sN?e:e&&e.length?new sA(e):sN.empty};var sA=function(e){function t(t){e.call(this),this.values=t}e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t;var n={length:{configurable:!0},depth:{configurable:!0}};return t.prototype.flatten=function(){return this.values},t.prototype.sliceInner=function(e,n){return 0==e&&n==this.length?this:new t(this.values.slice(e,n))},t.prototype.getInner=function(e){return this.values[e]},t.prototype.forEachInner=function(e,t,n,r){for(var i=t;i<n;i++)if(!1===e(this.values[i],r+i))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){for(var i=t-1;i>=n;i--)if(!1===e(this.values[i],r+i))return!1},t.prototype.leafAppend=function(e){if(this.length+e.length<=200)return new t(this.values.concat(e.flatten()))},t.prototype.leafPrepend=function(e){if(this.length+e.length<=200)return new t(e.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(t.prototype,n),t}(sN);sN.empty=new sA([]);var sO=function(e){function t(t,n){e.call(this),this.left=t,this.right=n,this.length=t.length+n.length,this.depth=Math.max(t.depth,n.depth)+1}return e&&(t.__proto__=e),t.prototype=Object.create(e&&e.prototype),t.prototype.constructor=t,t.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},t.prototype.getInner=function(e){return e<this.left.length?this.left.get(e):this.right.get(e-this.left.length)},t.prototype.forEachInner=function(e,t,n,r){var i=this.left.length;if(t<i&&!1===this.left.forEachInner(e,t,Math.min(n,i),r)||n>i&&!1===this.right.forEachInner(e,Math.max(t-i,0),Math.min(this.length,n)-i,r+i))return!1},t.prototype.forEachInvertedInner=function(e,t,n,r){var i=this.left.length;if(t>i&&!1===this.right.forEachInvertedInner(e,t-i,Math.max(n,i)-i,r+i)||n<i&&!1===this.left.forEachInvertedInner(e,Math.min(t,i),n,r))return!1},t.prototype.sliceInner=function(e,t){if(0==e&&t==this.length)return this;var n=this.left.length;return t<=n?this.left.slice(e,t):e>=n?this.right.slice(e-n,t-n):this.left.slice(e,n).append(this.right.slice(0,t-n))},t.prototype.leafAppend=function(e){var n=this.right.leafAppend(e);if(n)return new t(this.left,n)},t.prototype.leafPrepend=function(e){var n=this.left.leafPrepend(e);if(n)return new t(n,this.right)},t.prototype.appendInner=function(e){return this.left.depth>=Math.max(this.right.depth,e.depth)+1?new t(this.left,new t(this.right,e)):new t(this,e)},t}(sN);class sT{constructor(e,t){this.items=e,this.eventCount=t}popEvent(e,t){let n,r,i,o;if(0==this.eventCount)return null;let s=this.items.length;for(;;s--)if(this.items.get(s-1).selection){--s;break}t&&(r=(n=this.remapping(s,this.items.length)).maps.length);let l=e.tr,a=[],c=[];return this.items.forEach((e,t)=>{if(!e.step){n||(r=(n=this.remapping(s,t+1)).maps.length),r--,c.push(e);return}if(n){c.push(new sE(e.map));let t=e.step.map(n.slice(r)),i;t&&l.maybeStep(t).doc&&(i=l.mapping.maps[l.mapping.maps.length-1],a.push(new sE(i,void 0,void 0,a.length+c.length))),r--,i&&n.appendMap(i,r)}else l.maybeStep(e.step);if(e.selection)return i=n?e.selection.map(n.slice(r)):e.selection,o=new sT(this.items.slice(0,s).append(c.reverse().concat(a)),this.eventCount-1),!1},this.items.length,0),{remaining:o,transform:l,selection:i}}addTransform(e,t,n,r){var i,o;let s,l=[],a=this.eventCount,c=this.items,d=!r&&c.length?c.get(c.length-1):null;for(let n=0;n<e.steps.length;n++){let i=e.steps[n].invert(e.docs[n]),o=new sE(e.mapping.maps[n],i,t),s;(s=d&&d.merge(o))&&(o=s,n?l.pop():c=c.slice(0,c.length-1)),l.push(o),t&&(a++,t=void 0),r||(d=o)}let h=a-n.depth;return h>sR&&(i=c,o=h,i.forEach((e,t)=>{if(e.selection&&0==o--)return s=t,!1}),c=i.slice(s),a-=h),new sT(c.append(l),a)}remapping(e,t){let n=new ed;return this.items.forEach((t,r)=>{let i=null!=t.mirrorOffset&&r-t.mirrorOffset>=e?n.maps.length-t.mirrorOffset:void 0;n.appendMap(t.map,i)},e,t),n}addMaps(e){return 0==this.eventCount?this:new sT(this.items.append(e.map(e=>new sE(e))),this.eventCount)}rebased(e,t){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-t),i=e.mapping,o=e.steps.length,s=this.eventCount;this.items.forEach(e=>{e.selection&&s--},r);let l=t;this.items.forEach(t=>{let r=i.getMirror(--l);if(null==r)return;o=Math.min(o,r);let a=i.maps[r];if(t.step){let o=e.steps[r].invert(e.docs[r]),c=t.selection&&t.selection.map(i.slice(l+1,r));c&&s++,n.push(new sE(a,o,c))}else n.push(new sE(a))},r);let a=[];for(let e=t;e<o;e++)a.push(new sE(i.maps[e]));let c=new sT(this.items.slice(0,r).append(a).append(n),s);return c.emptyItemCount()>500&&(c=c.compress(this.items.length-n.length)),c}emptyItemCount(){let e=0;return this.items.forEach(t=>{!t.step&&e++}),e}compress(e=this.items.length){let t=this.remapping(0,e),n=t.maps.length,r=[],i=0;return this.items.forEach((o,s)=>{if(s>=e)r.push(o),o.selection&&i++;else if(o.step){let e=o.step.map(t.slice(n)),s=e&&e.getMap();if(n--,s&&t.appendMap(s,n),e){let l=o.selection&&o.selection.map(t.slice(n));l&&i++;let a=new sE(s.invert(),e,l),c,d=r.length-1;(c=r.length&&r[d].merge(a))?r[d]=c:r.push(a)}}else o.map&&n--},this.items.length,0),new sT(sN.from(r.reverse()),i)}}sT.empty=new sT(sN.empty,0);class sE{constructor(e,t,n,r){this.map=e,this.step=t,this.selection=n,this.mirrorOffset=r}merge(e){if(this.step&&e.step&&!e.selection){let t=e.step.merge(this.step);if(t)return new sE(t.getMap().invert(),t,this.selection)}}}class sD{constructor(e,t,n,r,i){this.done=e,this.undone=t,this.prevRanges=n,this.prevTime=r,this.prevComposition=i}}let sR=20;function sI(e){let t=[];for(let n=e.length-1;n>=0&&0==t.length;n--)e[n].forEach((e,n,r,i)=>t.push(r,i));return t}function sP(e,t){if(!e)return null;let n=[];for(let r=0;r<e.length;r+=2){let i=t.map(e[r],1),o=t.map(e[r+1],-1);i<=o&&n.push(i,o)}return n}let sL=!1,sj=null;function sz(e){let t=e.plugins;if(sj!=t){sL=!1,sj=t;for(let e=0;e<t.length;e++)if(t[e].spec.historyPreserveItems){sL=!0;break}}return sL}let s$=new tr("history"),sB=new tr("closeHistory");function sH(e,t){return(n,r)=>{let i=s$.getState(n);if(!i||0==(e?i.undone:i.done).eventCount)return!1;if(r){let o=function(e,t,n){let r=sz(t),i=s$.get(t).spec.config,o=(n?e.undone:e.done).popEvent(t,r);if(!o)return null;let s=o.selection.resolve(o.transform.doc),l=(n?e.done:e.undone).addTransform(o.transform,t.selection.getBookmark(),i,r),a=new sD(n?l:o.remaining,n?o.remaining:l,null,0,-1);return o.transform.setSelection(s).setMeta(s$,{redo:n,historyState:a})}(i,n,e);o&&r(t?o.scrollIntoView():o)}return!0}}let sV=sH(!1,!0),sF=sH(!0,!0);sH(!1,!1),sH(!0,!1);let sW=iG.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:e,dispatch:t})=>sV(e,t),redo:()=>({state:e,dispatch:t})=>sF(e,t)}),addProseMirrorPlugins(){return[function(e={}){return new te({key:s$,state:{init:()=>new sD(sT.empty,sT.empty,null,0,-1),apply:(t,n,r)=>(function(e,t,n,r){let i=n.getMeta(s$),o;if(i)return i.historyState;n.getMeta(sB)&&(e=new sD(e.done,e.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(0==n.steps.length)return e;if(s&&s.getMeta(s$))if(s.getMeta(s$).redo)return new sD(e.done.addTransform(n,void 0,r,sz(t)),e.undone,sI(n.mapping.maps),e.prevTime,e.prevComposition);else return new sD(e.done,e.undone.addTransform(n,void 0,r,sz(t)),null,e.prevTime,e.prevComposition);if(!1===n.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))if(o=n.getMeta("rebased"))return new sD(e.done.rebased(n,o),e.undone.rebased(n,o),sP(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);else return new sD(e.done.addMaps(n.mapping.maps),e.undone.addMaps(n.mapping.maps),sP(e.prevRanges,n.mapping),e.prevTime,e.prevComposition);{let i=n.getMeta("composition"),o=0==e.prevTime||!s&&e.prevComposition!=i&&(e.prevTime<(n.time||0)-r.newGroupDelay||!function(e,t){if(!t)return!1;if(!e.docChanged)return!0;let n=!1;return e.mapping.maps[0].forEach((e,r)=>{for(let i=0;i<t.length;i+=2)e<=t[i+1]&&r>=t[i]&&(n=!0)}),n}(n,e.prevRanges)),l=s?sP(e.prevRanges,n.mapping):sI(n.mapping.maps);return new sD(e.done.addTransform(n,o?t.selection.getBookmark():void 0,r,sz(t)),sT.empty,l,n.time,null==i?e.prevComposition:i)}})(n,r,t,e)},config:e={depth:e.depth||100,newGroupDelay:e.newGroupDelay||500},props:{handleDOMEvents:{beforeinput(e,t){let n=t.inputType,r="historyUndo"==n?sV:"historyRedo"==n?sF:null;return!!r&&(t.preventDefault(),r(e.state,e.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),s_=oj.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:e}){return["hr",iN(this.options.HTMLAttributes,e)]},addCommands(){return{setHorizontalRule:()=>({chain:e,state:t})=>{if(!function(e,t){let{selection:n}=e,{$from:r}=n;if(n instanceof eQ){let e=r.index();return r.parent.canReplaceWith(e,e+1,t)}let i=r.depth;for(;i>=0;){let e=r.index(i);if(r.node(i).contentMatchAt(e).matchType(t))return!0;i-=1}return!1}(t,t.schema.nodes[this.name]))return!1;let{selection:n}=t,{$from:r,$to:i}=n,o=e();return 0===r.parentOffset?o.insertContentAt({from:Math.max(r.pos-1,0),to:i.pos},{type:this.name}):n instanceof eQ?o.insertContentAt(i.pos,{type:this.name}):o.insertContent({type:this.name}),o.command(({tr:e,dispatch:t})=>{var n;if(t){let{$to:t}=e.selection,r=t.end();if(t.nodeAfter)t.nodeAfter.isTextblock?e.setSelection(eY.create(e.doc,t.pos+1)):t.nodeAfter.isBlock?e.setSelection(eQ.create(e.doc,t.pos)):e.setSelection(eY.create(e.doc,t.pos));else{let i=null==(n=t.parent.type.contentMatch.defaultType)?void 0:n.create();i&&(e.insert(r,i),e.setSelection(eY.create(e.doc,r+1)))}e.scrollIntoView()}return!0}).run()}}},addInputRules(){var e;return[new i$({find:(e={find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type}).find,handler:({state:t,range:n,match:r})=>{let i=iT(e.getAttributes,void 0,r)||{},{tr:o}=t,s=n.from,l=n.to,a=e.type.create(i);if(r[1]){let e=s+r[0].lastIndexOf(r[1]);e>l?e=l:l=e+r[1].length;let t=r[0][r[0].length-1];o.insertText(t,s+r[0].length-1),o.replaceWith(e,l,a)}else if(r[0]){let t=e.type.isInline?s:s-1;o.insert(t,e.type.create(i)).delete(o.mapping.map(s),o.mapping.map(l))}o.scrollIntoView()}})]}}),sq=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,sK=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,sJ=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,sU=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,sG=iW.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:e=>"normal"!==e.style.fontStyle&&null},{style:"font-style=normal",clearMark:e=>e.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:e}){return["em",iN(this.options.HTMLAttributes,e),0]},addCommands(){return{setItalic:()=>({commands:e})=>e.setMark(this.name),toggleItalic:()=>({commands:e})=>e.toggleMark(this.name),unsetItalic:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[oI({find:sq,type:this.type}),oI({find:sJ,type:this.type})]},addPasteRules(){return[o$({find:sK,type:this.type}),o$({find:sU,type:this.type})]}}),sY=oj.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:e}){return["li",iN(this.options.HTMLAttributes,e),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),sX="textStyle",sQ=/^(\d+)\.\s$/,sZ=oj.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:e=>e.hasAttribute("start")?parseInt(e.getAttribute("start")||"",10):1},type:{default:null,parseHTML:e=>e.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:e}){let{start:t,...n}=e;return 1===t?["ol",iN(this.options.HTMLAttributes,n),0]:["ol",iN(this.options.HTMLAttributes,e),0]},addCommands(){return{toggleOrderedList:()=>({commands:e,chain:t})=>this.options.keepAttributes?t().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(sX)).run():e.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let e=oL({find:sQ,type:this.type,getAttributes:e=>({start:+e[1]}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(e=oL({find:sQ,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:e=>({start:+e[1],...this.editor.getAttributes(sX)}),joinPredicate:(e,t)=>t.childCount+t.attrs.start===+e[1],editor:this.editor})),[e]}}),s0=oj.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:e}){return["p",iN(this.options.HTMLAttributes,e),0]},addCommands(){return{setParagraph:()=>({commands:e})=>e.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),s1=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,s2=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,s3=iW.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:e=>!!e.includes("line-through")&&{}}],renderHTML({HTMLAttributes:e}){return["s",iN(this.options.HTMLAttributes,e),0]},addCommands(){return{setStrike:()=>({commands:e})=>e.setMark(this.name),toggleStrike:()=>({commands:e})=>e.toggleMark(this.name),unsetStrike:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[oI({find:s1,type:this.type})]},addPasteRules(){return[o$({find:s2,type:this.type})]}}),s4=oj.create({name:"text",group:"inline"}),s5=iG.create({name:"starterKit",addExtensions(){let e=[];return!1!==this.options.bold&&e.push(sr.configure(this.options.bold)),!1!==this.options.blockquote&&e.push(o8.configure(this.options.blockquote)),!1!==this.options.bulletList&&e.push(ss.configure(this.options.bulletList)),!1!==this.options.code&&e.push(sc.configure(this.options.code)),!1!==this.options.codeBlock&&e.push(sp.configure(this.options.codeBlock)),!1!==this.options.document&&e.push(su.configure(this.options.document)),!1!==this.options.dropcursor&&e.push(sm.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&e.push(sS.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&e.push(sC.configure(this.options.hardBreak)),!1!==this.options.heading&&e.push(sM.configure(this.options.heading)),!1!==this.options.history&&e.push(sW.configure(this.options.history)),!1!==this.options.horizontalRule&&e.push(s_.configure(this.options.horizontalRule)),!1!==this.options.italic&&e.push(sG.configure(this.options.italic)),!1!==this.options.listItem&&e.push(sY.configure(this.options.listItem)),!1!==this.options.orderedList&&e.push(sZ.configure(this.options.orderedList)),!1!==this.options.paragraph&&e.push(s0.configure(this.options.paragraph)),!1!==this.options.strike&&e.push(s3.configure(this.options.strike)),!1!==this.options.text&&e.push(s4.configure(this.options.text)),e}}),s6=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,s7=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,s8=iW.create({name:"highlight",addOptions:()=>({multicolor:!1,HTMLAttributes:{}}),addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:e=>e.getAttribute("data-color")||e.style.backgroundColor,renderHTML:e=>e.color?{"data-color":e.color,style:`background-color: ${e.color}; color: inherit`}:{}}}:{}},parseHTML:()=>[{tag:"mark"}],renderHTML({HTMLAttributes:e}){return["mark",iN(this.options.HTMLAttributes,e),0]},addCommands(){return{setHighlight:e=>({commands:t})=>t.setMark(this.name,e),toggleHighlight:e=>({commands:t})=>t.toggleMark(this.name,e),unsetHighlight:()=>({commands:e})=>e.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[oI({find:s6,type:this.type})]},addPasteRules(){return[o$({find:s7,type:this.type})]}}),s9=(e,t)=>{for(let n in t)e[n]=t[n];return e},le="numeric",lt="ascii",ln="alpha",lr="asciinumeric",li="alphanumeric",lo="domain",ls="emoji",ll="whitespace";function la(e,t,n){for(let r in t[le]&&(t[lr]=!0,t[li]=!0),t[lt]&&(t[lr]=!0,t[ln]=!0),t[lr]&&(t[li]=!0),t[ln]&&(t[li]=!0),t[li]&&(t[lo]=!0),t[ls]&&(t[lo]=!0),t){let t=(r in n||(n[r]=[]),n[r]);0>t.indexOf(e)&&t.push(e)}}function lc(e=null){this.j={},this.jr=[],this.jd=null,this.t=e}lc.groups={},lc.prototype={accepts(){return!!this.t},go(e){let t=this.j[e];if(t)return t;for(let t=0;t<this.jr.length;t++){let n=this.jr[t][0],r=this.jr[t][1];if(r&&n.test(e))return r}return this.jd},has(e,t=!1){return t?e in this.j:!!this.go(e)},ta(e,t,n,r){for(let i=0;i<e.length;i++)this.tt(e[i],t,n,r)},tr(e,t,n,r){let i;return r=r||lc.groups,t&&t.j?i=t:(i=new lc(t),n&&r&&la(t,n,r)),this.jr.push([e,i]),i},ts(e,t,n,r){let i=this,o=e.length;if(!o)return i;for(let t=0;t<o-1;t++)i=i.tt(e[t]);return i.tt(e[o-1],t,n,r)},tt(e,t,n,r){if(r=r||lc.groups,t&&t.j)return this.j[e]=t,t;let i,o=this.go(e);return o?(s9((i=new lc).j,o.j),i.jr.push.apply(i.jr,o.jr),i.jd=o.jd,i.t=o.t):i=new lc,t&&(r&&(i.t&&"string"==typeof i.t?la(t,s9(function(e,t){let n={};for(let r in t)t[r].indexOf(e)>=0&&(n[r]=!0);return n}(i.t,r),n),r):n&&la(t,n,r)),i.t=t),this.j[e]=i,i}};let ld=(e,t,n,r,i)=>e.ta(t,n,r,i),lh=(e,t,n,r,i)=>e.tr(t,n,r,i),lp=(e,t,n,r,i)=>e.ts(t,n,r,i),lu=(e,t,n,r,i)=>e.tt(t,n,r,i),lf="WORD",lm="UWORD",lg="ASCIINUMERICAL",ly="ALPHANUMERICAL",lb="LOCALHOST",lw="UTLD",lv="SCHEME",lx="SLASH_SCHEME",lk="OPENBRACE",lS="CLOSEBRACE",lC="OPENBRACKET",lM="CLOSEBRACKET",lN="OPENPAREN",lA="CLOSEPAREN",lO="OPENANGLEBRACKET",lT="CLOSEANGLEBRACKET",lE="FULLWIDTHLEFTPAREN",lD="FULLWIDTHRIGHTPAREN",lR="LEFTCORNERBRACKET",lI="RIGHTCORNERBRACKET",lP="LEFTWHITECORNERBRACKET",lL="RIGHTWHITECORNERBRACKET",lj="FULLWIDTHLESSTHAN",lz="FULLWIDTHGREATERTHAN",l$="AMPERSAND",lB="APOSTROPHE",lH="ASTERISK",lV="BACKSLASH",lF="BACKTICK",lW="CARET",l_="COLON",lq="COMMA",lK="DOLLAR",lJ="EQUALS",lU="EXCLAMATION",lG="HYPHEN",lY="PERCENT",lX="PIPE",lQ="PLUS",lZ="POUND",l0="QUERY",l1="QUOTE",l2="FULLWIDTHMIDDLEDOT",l3="SEMI",l4="SLASH",l5="TILDE",l6="UNDERSCORE",l7="EMOJI";var l8=Object.freeze({__proto__:null,ALPHANUMERICAL:ly,AMPERSAND:l$,APOSTROPHE:lB,ASCIINUMERICAL:lg,ASTERISK:lH,AT:"AT",BACKSLASH:lV,BACKTICK:lF,CARET:lW,CLOSEANGLEBRACKET:lT,CLOSEBRACE:lS,CLOSEBRACKET:lM,CLOSEPAREN:lA,COLON:l_,COMMA:lq,DOLLAR:lK,DOT:"DOT",EMOJI:l7,EQUALS:lJ,EXCLAMATION:lU,FULLWIDTHGREATERTHAN:lz,FULLWIDTHLEFTPAREN:lE,FULLWIDTHLESSTHAN:lj,FULLWIDTHMIDDLEDOT:l2,FULLWIDTHRIGHTPAREN:lD,HYPHEN:lG,LEFTCORNERBRACKET:lR,LEFTWHITECORNERBRACKET:lP,LOCALHOST:lb,NL:"NL",NUM:"NUM",OPENANGLEBRACKET:lO,OPENBRACE:lk,OPENBRACKET:lC,OPENPAREN:lN,PERCENT:lY,PIPE:lX,PLUS:lQ,POUND:lZ,QUERY:l0,QUOTE:l1,RIGHTCORNERBRACKET:lI,RIGHTWHITECORNERBRACKET:lL,SCHEME:lv,SEMI:l3,SLASH:l4,SLASH_SCHEME:lx,SYM:"SYM",TILDE:l5,TLD:"TLD",UNDERSCORE:l6,UTLD:lw,UWORD:lm,WORD:lf,WS:"WS"});let l9=/[a-z]/,ae=/\p{L}/u,at=/\p{Emoji}/u,an=/\d/,ar=/\s/,ai=null,ao=null;function as(e,t){let n=function(e){let t=[],n=e.length,r=0;for(;r<n;){let i,o=e.charCodeAt(r),s=o<55296||o>56319||r+1===n||(i=e.charCodeAt(r+1))<56320||i>57343?e[r]:e.slice(r,r+2);t.push(s),r+=s.length}return t}(t.replace(/[A-Z]/g,e=>e.toLowerCase())),r=n.length,i=[],o=0,s=0;for(;s<r;){let l=e,a=null,c=0,d=null,h=-1,p=-1;for(;s<r&&(a=l.go(n[s]));)(l=a).accepts()?(h=0,p=0,d=l):h>=0&&(h+=n[s].length,p++),c+=n[s].length,o+=n[s].length,s++;o-=h,s-=p,c-=h,i.push({t:d.t,v:t.slice(o-c,o),s:o-c,e:o})}return i}function al(e,t,n,r,i){let o,s=t.length;for(let n=0;n<s-1;n++){let s=t[n];e.j[s]?o=e.j[s]:((o=new lc(r)).jr=i.slice(),e.j[s]=o),e=o}return(o=new lc(n)).jr=i.slice(),e.j[t[s-1]]=o,o}function aa(e){let t=[],n=[],r=0;for(;r<e.length;){let i=0;for(;"0123456789".indexOf(e[r+i])>=0;)i++;if(i>0){t.push(n.join(""));for(let t=parseInt(e.substring(r,r+i),10);t>0;t--)n.pop();r+=i}else n.push(e[r]),r++}return t}let ac={defaultProtocol:"http",events:null,format:ah,formatHref:ah,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function ad(e,t=null){let n=s9({},ac);e&&(n=s9(n,e instanceof ad?e.o:e));let r=n.ignoreTags,i=[];for(let e=0;e<r.length;e++)i.push(r[e].toUpperCase());this.o=n,t&&(this.defaultRender=t),this.ignoreTags=i}function ah(e){return e}function ap(e,t){this.t="token",this.v=e,this.tk=t}function au(e,t){class n extends ap{constructor(t,n){super(t,n),this.t=e}}for(let e in t)n.prototype[e]=t[e];return n.t=e,n}ad.prototype={o:ac,ignoreTags:[],defaultRender:e=>e,check(e){return this.get("validate",e.toString(),e)},get(e,t,n){let r=null!=t,i=this.o[e];return i&&("object"==typeof i?"function"==typeof(i=n.t in i?i[n.t]:ac[e])&&r&&(i=i(t,n)):"function"==typeof i&&r&&(i=i(t,n.t,n))),i},getObj(e,t,n){let r=this.o[e];return"function"==typeof r&&null!=t&&(r=r(t,n.t,n)),r},render(e){let t=e.render(this);return(this.get("render",null,e)||this.defaultRender)(t,e.t,e)}},ap.prototype={isLink:!1,toString(){return this.v},toHref(e){return this.toString()},toFormattedString(e){let t=this.toString(),n=e.get("truncate",t,this),r=e.get("format",t,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(e){return e.get("formatHref",this.toHref(e.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(e=ac.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(e),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(e){return{type:this.t,value:this.toFormattedString(e),isLink:this.isLink,href:this.toFormattedHref(e),start:this.startIndex(),end:this.endIndex()}},validate(e){return e.get("validate",this.toString(),this)},render(e){let t=this.toHref(e.get("defaultProtocol")),n=e.get("formatHref",t,this),r=e.get("tagName",t,this),i=this.toFormattedString(e),o={},s=e.get("className",t,this),l=e.get("target",t,this),a=e.get("rel",t,this),c=e.getObj("attributes",t,this),d=e.getObj("events",t,this);return o.href=n,s&&(o.class=s),l&&(o.target=l),a&&(o.rel=a),c&&s9(o,c),{tagName:r,attributes:o,content:i,eventListeners:d}}};let af=au("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),am=au("text"),ag=au("nl"),ay=au("url",{isLink:!0,toHref(e=ac.defaultProtocol){return this.hasProtocol()?this.v:`${e}://${this.v}`},hasProtocol(){let e=this.tk;return e.length>=2&&e[0].t!==lb&&e[1].t===l_}}),ab=e=>new lc(e);function aw(e,t,n){let r=n[0].s,i=n[n.length-1].e;return new e(t.slice(r,i),n)}let av="undefined"!=typeof console&&console&&console.warn||(()=>{}),ax={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function ak(e,t=!1){if(ax.initialized&&av(`linkifyjs: already initialized - will not register custom scheme "${e}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(e))throw Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);ax.customSchemes.push([e,t])}function aS(e){return ax.initialized||function(){ax.scanner=function(e=[]){let t={};lc.groups=t;let n=new lc;null==ai&&(ai=aa("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xf6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==ao&&(ao=aa("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),lu(n,"'",lB),lu(n,"{",lk),lu(n,"}",lS),lu(n,"[",lC),lu(n,"]",lM),lu(n,"(",lN),lu(n,")",lA),lu(n,"<",lO),lu(n,">",lT),lu(n,"（",lE),lu(n,"）",lD),lu(n,"「",lR),lu(n,"」",lI),lu(n,"『",lP),lu(n,"』",lL),lu(n,"＜",lj),lu(n,"＞",lz),lu(n,"&",l$),lu(n,"*",lH),lu(n,"@","AT"),lu(n,"`",lF),lu(n,"^",lW),lu(n,":",l_),lu(n,",",lq),lu(n,"$",lK),lu(n,".","DOT"),lu(n,"=",lJ),lu(n,"!",lU),lu(n,"-",lG),lu(n,"%",lY),lu(n,"|",lX),lu(n,"+",lQ),lu(n,"#",lZ),lu(n,"?",l0),lu(n,'"',l1),lu(n,"/",l4),lu(n,";",l3),lu(n,"~",l5),lu(n,"_",l6),lu(n,"\\",lV),lu(n,"・",l2);let r=lh(n,an,"NUM",{[le]:!0});lh(r,an,r);let i=lh(r,l9,lg,{[lr]:!0}),o=lh(r,ae,ly,{[li]:!0}),s=lh(n,l9,lf,{[lt]:!0});lh(s,an,i),lh(s,l9,s),lh(i,an,i),lh(i,l9,i);let l=lh(n,ae,lm,{[ln]:!0});lh(l,l9),lh(l,an,o),lh(l,ae,l),lh(o,an,o),lh(o,l9),lh(o,ae,o);let a=lu(n,"\n","NL",{[ll]:!0}),c=lu(n,"\r","WS",{[ll]:!0}),d=lh(n,ar,"WS",{[ll]:!0});lu(n,"￼",d),lu(c,"\n",a),lu(c,"￼",d),lh(c,ar,d),lu(d,"\r"),lu(d,"\n"),lh(d,ar,d),lu(d,"￼",d);let h=lh(n,at,l7,{[ls]:!0});lu(h,"#"),lh(h,at,h),lu(h,"️",h);let p=lu(h,"‍");lu(p,"#"),lh(p,at,h);let u=[[l9,s],[an,i]],f=[[l9,null],[ae,l],[an,o]];for(let e=0;e<ai.length;e++)al(n,ai[e],"TLD",lf,u);for(let e=0;e<ao.length;e++)al(n,ao[e],lw,lm,f);la("TLD",{tld:!0,ascii:!0},t),la(lw,{utld:!0,alpha:!0},t),al(n,"file",lv,lf,u),al(n,"mailto",lv,lf,u),al(n,"http",lx,lf,u),al(n,"https",lx,lf,u),al(n,"ftp",lx,lf,u),al(n,"ftps",lx,lf,u),la(lv,{scheme:!0,ascii:!0},t),la(lx,{slashscheme:!0,ascii:!0},t),e=e.sort((e,t)=>e[0]>t[0]?1:-1);for(let t=0;t<e.length;t++){let r=e[t][0],i=e[t][1]?{scheme:!0}:{slashscheme:!0};r.indexOf("-")>=0?i[lo]=!0:l9.test(r)?an.test(r)?i[lr]=!0:i[lt]=!0:i[le]=!0,lp(n,r,r,i)}return lp(n,"localhost",lb,{ascii:!0}),n.jd=new lc("SYM"),{start:n,tokens:s9({groups:t},l8)}}(ax.customSchemes);for(let e=0;e<ax.tokenQueue.length;e++)ax.tokenQueue[e][1]({scanner:ax.scanner});ax.parser=function({groups:e}){let t=e.domain.concat([l$,lH,"AT",lV,lF,lW,lK,lJ,lG,"NUM",lY,lX,lQ,lZ,l4,"SYM",l5,l6]),n=[lB,l_,lq,"DOT",lU,lY,l0,l1,l3,lO,lT,lk,lS,lM,lC,lN,lA,lE,lD,lR,lI,lP,lL,lj,lz],r=[l$,lB,lH,lV,lF,lW,lK,lJ,lG,lk,lS,lY,lX,lQ,lZ,l0,l4,"SYM",l5,l6],i=ab(),o=lu(i,l5);ld(o,r,o),ld(o,e.domain,o);let s=ab(),l=ab(),a=ab();ld(i,e.domain,s),ld(i,e.scheme,l),ld(i,e.slashscheme,a),ld(s,r,o),ld(s,e.domain,s);let c=lu(s,"AT");lu(o,"AT",c),lu(l,"AT",c),lu(a,"AT",c);let d=lu(o,"DOT");ld(d,r,o),ld(d,e.domain,o);let h=ab();ld(c,e.domain,h),ld(h,e.domain,h);let p=lu(h,"DOT");ld(p,e.domain,h);let u=ab(af);ld(p,e.tld,u),ld(p,e.utld,u),lu(c,lb,u);let f=lu(h,lG);lu(f,lG,f),ld(f,e.domain,h),ld(u,e.domain,h),lu(u,"DOT",p),lu(u,lG,f),ld(lu(u,l_),e.numeric,af);let m=lu(s,lG),g=lu(s,"DOT");lu(m,lG,m),ld(m,e.domain,s),ld(g,r,o),ld(g,e.domain,s);let y=ab(ay);ld(g,e.tld,y),ld(g,e.utld,y),ld(y,e.domain,s),ld(y,r,o),lu(y,"DOT",g),lu(y,lG,m),lu(y,"AT",c);let b=lu(y,l_),w=ab(ay);ld(b,e.numeric,w);let v=ab(ay),x=ab();ld(v,t,v),ld(v,n,x),ld(x,t,v),ld(x,n,x),lu(y,l4,v),lu(w,l4,v);let k=lu(l,l_),S=lu(a,l_),C=lu(S,l4),M=lu(C,l4);ld(l,e.domain,s),lu(l,"DOT",g),lu(l,lG,m),ld(a,e.domain,s),lu(a,"DOT",g),lu(a,lG,m),ld(k,e.domain,v),lu(k,l4,v),lu(k,l0,v),ld(M,e.domain,v),ld(M,t,v),lu(M,l4,v);let N=[[lk,lS],[lC,lM],[lN,lA],[lO,lT],[lE,lD],[lR,lI],[lP,lL],[lj,lz]];for(let e=0;e<N.length;e++){let[r,i]=N[e],o=lu(v,r);lu(x,r,o),lu(o,i,v);let s=ab(ay);ld(o,t,s);let l=ab();ld(o,n),ld(s,t,s),ld(s,n,l),ld(l,t,s),ld(l,n,l),lu(s,i,v),lu(l,i,v)}return lu(i,lb,y),lu(i,"NL",ag),{start:i,tokens:l8}}(ax.scanner.tokens);for(let e=0;e<ax.pluginQueue.length;e++)ax.pluginQueue[e][1]({scanner:ax.scanner,parser:ax.parser});ax.initialized=!0}(),function(e,t,n){let r=n.length,i=0,o=[],s=[];for(;i<r;){let l=e,a=null,c=null,d=0,h=null,p=-1;for(;i<r&&!(a=l.go(n[i].t));)s.push(n[i++]);for(;i<r&&(c=a||l.go(n[i].t));)a=null,(l=c).accepts()?(p=0,h=l):p>=0&&p++,i++,d++;if(p<0)(i-=d)<r&&(s.push(n[i]),i++);else{s.length>0&&(o.push(aw(am,t,s)),s=[]),i-=p,d-=p;let e=h.t,r=n.slice(i-d,i);o.push(aw(e,t,r))}}return s.length>0&&o.push(aw(am,t,s)),o}(ax.parser.start,e,as(ax.scanner.start,e))}function aC(e,t=null,n=null){if(t&&"object"==typeof t){if(n)throw Error(`linkifyjs: Invalid link type ${t}; must be a string`);n=t,t=null}let r=new ad(n),i=aS(e),o=[];for(let e=0;e<i.length;e++){let n=i[e];n.isLink&&(!t||n.t===t)&&r.check(n)&&o.push(n.toFormattedObject(r))}return o}aS.scan=as;let aM="[\0- \xa0 ᠎ -\u2029 　]",aN=new RegExp(aM),aA=RegExp(`${aM}$`),aO=RegExp(aM,"g");function aT(e,t){let n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return t&&t.forEach(e=>{let t="string"==typeof e?e:e.scheme;t&&n.push(t)}),!e||e.replace(aO,"").match(RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}let aE=iW.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(e=>{if("string"==typeof e)return void ak(e);ak(e.scheme,e.optionalSlashes)})},onDestroy(){lc.groups={},ax.scanner=null,ax.parser=null,ax.tokenQueue=[],ax.pluginQueue=[],ax.customSchemes=[],ax.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(e,t)=>!!aT(e,t.protocols),validate:e=>!!e,shouldAutoLink:e=>!!e}),addAttributes(){return{href:{default:null,parseHTML:e=>e.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:e=>{let t=e.getAttribute("href");return!!t&&!!this.options.isAllowedUri(t,{defaultValidate:e=>!!aT(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&null}}]},renderHTML({HTMLAttributes:e}){return this.options.isAllowedUri(e.href,{defaultValidate:e=>!!aT(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",iN(this.options.HTMLAttributes,e),0]:["a",iN(this.options.HTMLAttributes,{...e,href:""}),0]},addCommands(){return{setLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!aT(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().setMark(this.name,e).setMeta("preventAutolink",!0).run()},toggleLink:e=>({chain:t})=>{let{href:n}=e;return!!this.options.isAllowedUri(n,{defaultValidate:e=>!!aT(e,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&t().toggleMark(this.name,e,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:e})=>e().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[o$({find:e=>{let t=[];if(e){let{protocols:n,defaultProtocol:r}=this.options,i=aC(e).filter(e=>e.isLink&&this.options.isAllowedUri(e.value,{defaultValidate:e=>!!aT(e,n),protocols:n,defaultProtocol:r}));i.length&&i.forEach(e=>t.push({text:e.value,data:{href:e.href},index:e.start}))}return t},type:this.type,getAttributes:e=>{var t;return{href:null==(t=e.data)?void 0:t.href}}})]},addProseMirrorPlugins(){var e,t,n;let r=[],{protocols:i,defaultProtocol:o}=this.options;return this.options.autolink&&r.push((e={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:e=>this.options.isAllowedUri(e,{defaultValidate:e=>!!aT(e,i),protocols:i,defaultProtocol:o}),shouldAutoLink:this.options.shouldAutoLink},new te({key:new tr("autolink"),appendTransaction:(t,n,r)=>{let i=t.some(e=>e.docChanged)&&!n.doc.eq(r.doc),o=t.some(e=>e.getMeta("preventAutolink"));if(!i||o)return;let{tr:s}=r;if((function(e){let{mapping:t,steps:n}=e,r=[];t.maps.forEach((e,i)=>{let o=[];if(e.ranges.length)e.forEach((e,t)=>{o.push({from:e,to:t})});else{let{from:e,to:t}=n[i];if(void 0===e||void 0===t)return;o.push({from:e,to:t})}o.forEach(({from:e,to:n})=>{let o=t.slice(i).map(e,-1),s=t.slice(i).map(n),l=t.invert().map(o,-1),a=t.invert().map(s);r.push({oldRange:{from:l,to:a},newRange:{from:o,to:s}})})});let i=function(e,t=JSON.stringify){let n={};return e.filter(e=>{let r=t(e);return!Object.prototype.hasOwnProperty.call(n,r)&&(n[r]=!0)})}(r);return 1===i.length?i:i.filter((e,t)=>!i.filter((e,n)=>n!==t).some(t=>e.oldRange.from>=t.oldRange.from&&e.oldRange.to<=t.oldRange.to&&e.newRange.from>=t.newRange.from&&e.newRange.to<=t.newRange.to))})(function(e,t){let n=new e_(e);return t.forEach(e=>{e.steps.forEach(e=>{n.step(e)})}),n}(n.doc,[...t])).forEach(({newRange:t})=>{let n,i,o=function(e,t,n){let r=[];return e.nodesBetween(t.from,t.to,(e,t)=>{n(e)&&r.push({node:e,pos:t})}),r}(r.doc,t,e=>e.isTextblock);if(o.length>1)n=o[0],i=r.doc.textBetween(n.pos,n.pos+n.node.nodeSize,void 0," ");else if(o.length){let e=r.doc.textBetween(t.from,t.to," "," ");if(!aA.test(e))return;n=o[0],i=r.doc.textBetween(n.pos,t.to,void 0," ")}if(n&&i){let t=i.split(aN).filter(Boolean);if(t.length<=0)return!1;let o=t[t.length-1],l=n.pos+i.lastIndexOf(o);if(!o)return!1;let a=aS(o).map(t=>t.toObject(e.defaultProtocol));if(!(1===a.length?a[0].isLink:3===a.length&&!!a[1].isLink&&["()","[]"].includes(a[0].value+a[2].value)))return!1;a.filter(e=>e.isLink).map(e=>({...e,from:l+e.start+1,to:l+e.end+1})).filter(e=>!r.schema.marks.code||!r.doc.rangeHasMark(e.from,e.to,r.schema.marks.code)).filter(t=>e.validate(t.value)).filter(t=>e.shouldAutoLink(t.value)).forEach(t=>{ou(t.from,t.to,r.doc).some(t=>t.mark.type===e.type)||s.addMark(t.from,t.to,e.type.create({href:t.href}))})}}),s.steps.length)return s}}))),!0===this.options.openOnClick&&r.push((t={type:this.type},new te({key:new tr("handleClickLink"),props:{handleClick:(e,n,r)=>{var i,o;if(0!==r.button||!e.editable)return!1;let s=r.target,l=[];for(;"DIV"!==s.nodeName;)l.push(s),s=s.parentNode;if(!l.find(e=>"A"===e.nodeName))return!1;let a=op(e.state,t.type.name),c=r.target,d=null!=(i=null==c?void 0:c.href)?i:a.href,h=null!=(o=null==c?void 0:c.target)?o:a.target;return!!c&&!!d&&(window.open(d,h),!0)}}}))),this.options.linkOnPaste&&r.push((n={editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type},new te({key:new tr("handlePasteLink"),props:{handlePaste:(e,t,r)=>{let{state:i}=e,{selection:o}=i,{empty:s}=o;if(s)return!1;let l="";r.content.forEach(e=>{l+=e.textContent});let a=aC(l,{defaultProtocol:n.defaultProtocol}).find(e=>e.isLink&&e.value===l);return!!l&&!!a&&n.editor.commands.setMark(n.type,{href:a.href})}}}))),r}}),aD=iG.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new te({key:new tr("placeholder"),props:{decorations:({doc:e,selection:t})=>{let n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:r}=t,i=[];if(!n)return null;let o=this.editor.isEmpty;return e.descendants((e,t)=>{let n=r>=t&&r<=t+e.nodeSize,s=!e.isLeaf&&oy(e);if((n||!this.options.showOnlyCurrent)&&s){let r=[this.options.emptyNodeClass];o&&r.push(this.options.emptyEditorClass);let s=ri.node(t,t+e.nodeSize,{class:r.join(" "),"data-placeholder":"function"==typeof this.options.placeholder?this.options.placeholder({editor:this.editor,node:e,pos:t,hasAnchor:n}):this.options.placeholder});i.push(s)}return this.options.includeChildren}),rl.create(e,i)}}})]}}),aR=iG.create({name:"characterCount",addOptions:()=>({limit:null,mode:"textSize",textCounter:e=>e.length,wordCounter:e=>e.split(" ").filter(e=>""!==e).length}),addStorage:()=>({characters:()=>0,words:()=>0}),onBeforeCreate(){this.storage.characters=e=>{let t=(null==e?void 0:e.node)||this.editor.state.doc;if("textSize"===((null==e?void 0:e.mode)||this.options.mode)){let e=t.textBetween(0,t.content.size,void 0," ");return this.options.textCounter(e)}return t.nodeSize},this.storage.words=e=>{let t=(null==e?void 0:e.node)||this.editor.state.doc,n=t.textBetween(0,t.content.size," "," ");return this.options.wordCounter(n)}},addProseMirrorPlugins(){let e=!1;return[new te({key:new tr("characterCount"),appendTransaction:(t,n,r)=>{if(e)return;let i=this.options.limit;if(null==i||0===i){e=!0;return}let o=this.storage.characters({node:r.doc});if(o>i){console.warn(`[CharacterCount] Initial content exceeded limit of ${i} characters. Content was automatically trimmed.`);let t=r.tr.deleteRange(0,o-i);return e=!0,t}e=!0},filterTransaction:(e,t)=>{let n=this.options.limit;if(!e.docChanged||0===n||null==n)return!0;let r=this.storage.characters({node:t.doc}),i=this.storage.characters({node:e.doc});if(i<=n||r>n&&i>n&&i<=r)return!0;if(r>n&&i>n&&i>r||!e.getMeta("paste"))return!1;let o=e.selection.$head.pos;return e.deleteRange(o-(i-n),o),!(this.storage.characters({node:e.doc})>n)}})]}});if("undefined"!=typeof WeakMap){let e=new WeakMap;s=t=>e.get(t),l=(t,n)=>(e.set(t,n),n)}else{let e=[],t=0;s=t=>{for(let n=0;n<e.length;n+=2)if(e[n]==t)return e[n+1]},l=(n,r)=>(10==t&&(t=0),e[t++]=n,e[t++]=r)}var aI=class{constructor(e,t,n,r){this.width=e,this.height=t,this.map=n,this.problems=r}findCell(e){for(let t=0;t<this.map.length;t++){let n=this.map[t];if(n!=e)continue;let r=t%this.width,i=t/this.width|0,o=r+1,s=i+1;for(let e=1;o<this.width&&this.map[t+e]==n;e++)o++;for(let e=1;s<this.height&&this.map[t+this.width*e]==n;e++)s++;return{left:r,top:i,right:o,bottom:s}}throw RangeError(`No cell with offset ${e} found`)}colCount(e){for(let t=0;t<this.map.length;t++)if(this.map[t]==e)return t%this.width;throw RangeError(`No cell with offset ${e} found`)}nextCell(e,t,n){let{left:r,right:i,top:o,bottom:s}=this.findCell(e);return"horiz"==t?(n<0?0==r:i==this.width)?null:this.map[o*this.width+(n<0?r-1:i)]:(n<0?0==o:s==this.height)?null:this.map[r+this.width*(n<0?o-1:s)]}rectBetween(e,t){let{left:n,right:r,top:i,bottom:o}=this.findCell(e),{left:s,right:l,top:a,bottom:c}=this.findCell(t);return{left:Math.min(n,s),top:Math.min(i,a),right:Math.max(r,l),bottom:Math.max(o,c)}}cellsInRect(e){let t=[],n={};for(let r=e.top;r<e.bottom;r++)for(let i=e.left;i<e.right;i++){let o=r*this.width+i,s=this.map[o];!n[s]&&(n[s]=!0,i==e.left&&i&&this.map[o-1]==s||r==e.top&&r&&this.map[o-this.width]==s||t.push(s))}return t}positionAt(e,t,n){for(let r=0,i=0;;r++){let o=i+n.child(r).nodeSize;if(r==e){let n=t+e*this.width,r=(e+1)*this.width;for(;n<r&&this.map[n]<i;)n++;return n==r?o-1:this.map[n]}i=o}}static get(e){return s(e)||l(e,function(e){if("table"!=e.type.spec.tableRole)throw RangeError("Not a table node: "+e.type.name);let t=function(e){let t=-1,n=!1;for(let r=0;r<e.childCount;r++){let i=e.child(r),o=0;if(n)for(let t=0;t<r;t++){let n=e.child(t);for(let e=0;e<n.childCount;e++){let i=n.child(e);t+i.attrs.rowspan>r&&(o+=i.attrs.colspan)}}for(let e=0;e<i.childCount;e++){let t=i.child(e);o+=t.attrs.colspan,t.attrs.rowspan>1&&(n=!0)}-1==t?t=o:t!=o&&(t=Math.max(t,o))}return t}(e),n=e.childCount,r=[],i=0,o=null,s=[];for(let e=0,i=t*n;e<i;e++)r[e]=0;for(let l=0,a=0;l<n;l++){let c=e.child(l);a++;for(let e=0;;e++){for(;i<r.length&&0!=r[i];)i++;if(e==c.childCount)break;let d=c.child(e),{colspan:h,rowspan:p,colwidth:u}=d.attrs;for(let e=0;e<p;e++){if(e+l>=n){(o||(o=[])).push({type:"overlong_rowspan",pos:a,n:p-e});break}let c=i+e*t;for(let e=0;e<h;e++){0==r[c+e]?r[c+e]=a:(o||(o=[])).push({type:"collision",row:l,pos:a,n:h-e});let n=u&&u[e];if(n){let r=(c+e)%t*2,i=s[r];null==i||i!=n&&1==s[r+1]?(s[r]=n,s[r+1]=1):i==n&&s[r+1]++}}}i+=h,a+=d.nodeSize}let d=(l+1)*t,h=0;for(;i<d;)0==r[i++]&&h++;h&&(o||(o=[])).push({type:"missing",row:l,n:h}),a++}(0===t||0===n)&&(o||(o=[])).push({type:"zero_sized"});let l=new aI(t,n,r,o),a=!1;for(let e=0;!a&&e<s.length;e+=2)null!=s[e]&&s[e+1]<n&&(a=!0);return a&&function(e,t,n){e.problems||(e.problems=[]);let r={};for(let i=0;i<e.map.length;i++){let o=e.map[i];if(r[o])continue;r[o]=!0;let s=n.nodeAt(o);if(!s)throw RangeError(`No cell with offset ${o} found`);let l=null,a=s.attrs;for(let n=0;n<a.colspan;n++){let r=t[2*((i+n)%e.width)];null==r||a.colwidth&&a.colwidth[n]==r||((l||(l=function(e){if(e.colwidth)return e.colwidth.slice();let t=[];for(let n=0;n<e.colspan;n++)t.push(0);return t}(a)))[n]=r)}l&&e.problems.unshift({type:"colwidth mismatch",pos:o,colwidth:l})}}(l,s,e),l}(e))}};function aP(e){let t=e.cached.tableNodeTypes;if(!t)for(let n in t=e.cached.tableNodeTypes={},e.nodes){let r=e.nodes[n],i=r.spec.tableRole;i&&(t[i]=r)}return t}var aL=new tr("selectingCells");function aj(e){for(let t=e.depth-1;t>0;t--)if("row"==e.node(t).type.spec.tableRole)return e.node(0).resolve(e.before(t+1));return null}function az(e){let t=e.selection.$head;for(let e=t.depth;e>0;e--)if("row"==t.node(e).type.spec.tableRole)return!0;return!1}function a$(e){let t=e.selection;if("$anchorCell"in t&&t.$anchorCell)return t.$anchorCell.pos>t.$headCell.pos?t.$anchorCell:t.$headCell;if("node"in t&&t.node&&"cell"==t.node.type.spec.tableRole)return t.$anchor;let n=aj(t.$head)||function(e){for(let t=e.nodeAfter,n=e.pos;t;t=t.firstChild,n++){let r=t.type.spec.tableRole;if("cell"==r||"header_cell"==r)return e.doc.resolve(n)}for(let t=e.nodeBefore,n=e.pos;t;t=t.lastChild,n--){let r=t.type.spec.tableRole;if("cell"==r||"header_cell"==r)return e.doc.resolve(n-t.nodeSize)}}(t.$head);if(n)return n;throw RangeError(`No cell found around position ${t.head}`)}function aB(e){return"row"==e.parent.type.spec.tableRole&&!!e.nodeAfter}function aH(e,t){return e.depth==t.depth&&e.pos>=t.start(-1)&&e.pos<=t.end(-1)}function aV(e,t,n){let r=e.node(-1),i=aI.get(r),o=e.start(-1),s=i.nextCell(e.pos-o,t,n);return null==s?null:e.node(0).resolve(o+s)}function aF(e,t,n=1){let r={...e,colspan:e.colspan-n};return r.colwidth&&(r.colwidth=r.colwidth.slice(),r.colwidth.splice(t,n),r.colwidth.some(e=>e>0)||(r.colwidth=null)),r}function aW(e,t,n=1){let r={...e,colspan:e.colspan+n};if(r.colwidth){r.colwidth=r.colwidth.slice();for(let e=0;e<n;e++)r.colwidth.splice(t,0,0)}return r}var a_=class e extends eK{constructor(e,t=e){let n=e.node(-1),r=aI.get(n),i=e.start(-1),o=r.rectBetween(e.pos-i,t.pos-i),s=e.node(0),l=r.cellsInRect(o).filter(e=>e!=t.pos-i);l.unshift(t.pos-i);let a=l.map(e=>{let t=n.nodeAt(e);if(!t)throw RangeError(`No cell with offset ${e} found`);let r=i+e+1;return new eJ(s.resolve(r),s.resolve(r+t.content.size))});super(a[0].$from,a[0].$to,a),this.$anchorCell=e,this.$headCell=t}map(t,n){let r=t.resolve(n.map(this.$anchorCell.pos)),i=t.resolve(n.map(this.$headCell.pos));if(aB(r)&&aB(i)&&aH(r,i)){let t=this.$anchorCell.node(-1)!=r.node(-1);return t&&this.isRowSelection()?e.rowSelection(r,i):t&&this.isColSelection()?e.colSelection(r,i):new e(r,i)}return eY.between(r,i)}content(){let e=this.$anchorCell.node(-1),t=aI.get(e),n=this.$anchorCell.start(-1),r=t.rectBetween(this.$anchorCell.pos-n,this.$headCell.pos-n),i={},o=[];for(let n=r.top;n<r.bottom;n++){let s=[];for(let o=n*t.width+r.left,l=r.left;l<r.right;l++,o++){let n=t.map[o];if(i[n])continue;i[n]=!0;let l=t.findCell(n),a=e.nodeAt(n);if(!a)throw RangeError(`No cell with offset ${n} found`);let c=r.left-l.left,d=l.right-r.right;if(c>0||d>0){let e=a.attrs;if(c>0&&(e=aF(e,0,c)),d>0&&(e=aF(e,e.colspan-d,d)),l.left<r.left){if(!(a=a.type.createAndFill(e)))throw RangeError(`Could not create cell with attrs ${JSON.stringify(e)}`)}else a=a.type.create(e,a.content)}if(l.top<r.top||l.bottom>r.bottom){let e={...a.attrs,rowspan:Math.min(l.bottom,r.bottom)-Math.max(l.top,r.top)};a=l.top<r.top?a.type.createAndFill(e):a.type.create(e,a.content)}s.push(a)}o.push(e.child(n).copy(p.from(s)))}let s=this.isColSelection()&&this.isRowSelection()?e:o;return new b(p.from(s),1,1)}replace(e,t=b.empty){let n=e.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:o,$to:s}=r[i],l=e.mapping.slice(n);e.replace(l.map(o.pos),l.map(s.pos),i?b.empty:t)}let i=eK.findFrom(e.doc.resolve(e.mapping.slice(n).map(this.to)),-1);i&&e.setSelection(i)}replaceWith(e,t){this.replace(e,new b(p.from(t),0,0))}forEachCell(e){let t=this.$anchorCell.node(-1),n=aI.get(t),r=this.$anchorCell.start(-1),i=n.cellsInRect(n.rectBetween(this.$anchorCell.pos-r,this.$headCell.pos-r));for(let n=0;n<i.length;n++)e(t.nodeAt(i[n]),r+i[n])}isColSelection(){let e=this.$anchorCell.index(-1),t=this.$headCell.index(-1);return!(Math.min(e,t)>0)&&Math.max(e+this.$anchorCell.nodeAfter.attrs.rowspan,t+this.$headCell.nodeAfter.attrs.rowspan)==this.$headCell.node(-1).childCount}static colSelection(t,n=t){let r=t.node(-1),i=aI.get(r),o=t.start(-1),s=i.findCell(t.pos-o),l=i.findCell(n.pos-o),a=t.node(0);return s.top<=l.top?(s.top>0&&(t=a.resolve(o+i.map[s.left])),l.bottom<i.height&&(n=a.resolve(o+i.map[i.width*(i.height-1)+l.right-1]))):(l.top>0&&(n=a.resolve(o+i.map[l.left])),s.bottom<i.height&&(t=a.resolve(o+i.map[i.width*(i.height-1)+s.right-1]))),new e(t,n)}isRowSelection(){let e=this.$anchorCell.node(-1),t=aI.get(e),n=this.$anchorCell.start(-1),r=t.colCount(this.$anchorCell.pos-n),i=t.colCount(this.$headCell.pos-n);return!(Math.min(r,i)>0)&&Math.max(r+this.$anchorCell.nodeAfter.attrs.colspan,i+this.$headCell.nodeAfter.attrs.colspan)==t.width}eq(t){return t instanceof e&&t.$anchorCell.pos==this.$anchorCell.pos&&t.$headCell.pos==this.$headCell.pos}static rowSelection(t,n=t){let r=t.node(-1),i=aI.get(r),o=t.start(-1),s=i.findCell(t.pos-o),l=i.findCell(n.pos-o),a=t.node(0);return s.left<=l.left?(s.left>0&&(t=a.resolve(o+i.map[s.top*i.width])),l.right<i.width&&(n=a.resolve(o+i.map[i.width*(l.top+1)-1]))):(l.left>0&&(n=a.resolve(o+i.map[l.top*i.width])),s.right<i.width&&(t=a.resolve(o+i.map[i.width*(s.top+1)-1]))),new e(t,n)}toJSON(){return{type:"cell",anchor:this.$anchorCell.pos,head:this.$headCell.pos}}static fromJSON(t,n){return new e(t.resolve(n.anchor),t.resolve(n.head))}static create(t,n,r=n){return new e(t.resolve(n),t.resolve(r))}getBookmark(){return new aq(this.$anchorCell.pos,this.$headCell.pos)}};a_.prototype.visible=!1,eK.jsonID("cell",a_);var aq=class e{constructor(e,t){this.anchor=e,this.head=t}map(t){return new e(t.map(this.anchor),t.map(this.head))}resolve(e){let t=e.resolve(this.anchor),n=e.resolve(this.head);return"row"==t.parent.type.spec.tableRole&&"row"==n.parent.type.spec.tableRole&&t.index()<t.parent.childCount&&n.index()<n.parent.childCount&&aH(t,n)?new a_(t,n):eK.near(n,1)}};function aK(e){if(!(e.selection instanceof a_))return null;let t=[];return e.selection.forEachCell((e,n)=>{t.push(ri.node(n,n+e.nodeSize,{class:"selectedCell"}))}),rl.create(e.doc,t)}var aJ=new tr("fix-tables");function aU(e,t){let n,r=(t,r)=>{"table"==t.type.spec.tableRole&&(n=function(e,t,n,r){let i,o,s=aI.get(t);if(!s.problems)return r;r||(r=e.tr);let l=[];for(let e=0;e<s.height;e++)l.push(0);for(let e=0;e<s.problems.length;e++){let i=s.problems[e];if("collision"==i.type){let e=t.nodeAt(i.pos);if(!e)continue;let o=e.attrs;for(let e=0;e<o.rowspan;e++)l[i.row+e]+=i.n;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,aF(o,o.colspan-i.n,i.n))}else if("missing"==i.type)l[i.row]+=i.n;else if("overlong_rowspan"==i.type){let e=t.nodeAt(i.pos);if(!e)continue;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,{...e.attrs,rowspan:e.attrs.rowspan-i.n})}else if("colwidth mismatch"==i.type){let e=t.nodeAt(i.pos);if(!e)continue;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,{...e.attrs,colwidth:i.colwidth})}else if("zero_sized"==i.type){let e=r.mapping.map(n);r.delete(e,e+t.nodeSize)}}for(let e=0;e<l.length;e++)l[e]&&(null==i&&(i=e),o=e);for(let a=0,c=n+1;a<s.height;a++){let n=t.child(a),s=c+n.nodeSize,d=l[a];if(d>0){let t="cell";n.firstChild&&(t=n.firstChild.type.spec.tableRole);let l=[];for(let n=0;n<d;n++){let n=aP(e.schema)[t].createAndFill();n&&l.push(n)}let h=(0==a||i==a-1)&&o==a?c+1:s-1;r.insert(r.mapping.map(h),l)}c=s}return r.setMeta(aJ,{fixTables:!0})}(e,t,r,n))};return t?t.doc!=e.doc&&function e(t,n,r,i){let o=t.childCount,s=n.childCount;n:for(let l=0,a=0;l<s;l++){let s=n.child(l);for(let e=a,n=Math.min(o,l+3);e<n;e++)if(t.child(e)==s){a=e+1,r+=s.nodeSize;continue n}i(s,r),a<o&&t.child(a).sameMarkup(s)?e(t.child(a),s,r+1,i):s.nodesBetween(0,s.content.size,i,r+1),r+=s.nodeSize}}(t.doc,e.doc,0,r):e.doc.descendants(r),n}function aG(e){let t=e.selection,n=a$(e),r=n.node(-1),i=n.start(-1),o=aI.get(r);return{...t instanceof a_?o.rectBetween(t.$anchorCell.pos-i,t.$headCell.pos-i):o.findCell(n.pos-i),tableStart:i,map:o,table:r}}function aY(e,{map:t,tableStart:n,table:r},i){let o=i>0?-1:0;(function(e,t,n){let r=aP(t.type.schema).header_cell;for(let i=0;i<e.height;i++)if(t.nodeAt(e.map[n+i*e.width]).type!=r)return!1;return!0})(t,r,i+o)&&(o=0==i||i==t.width?null:0);for(let s=0;s<t.height;s++){let l=s*t.width+i;if(i>0&&i<t.width&&t.map[l-1]==t.map[l]){let o=t.map[l],a=r.nodeAt(o);e.setNodeMarkup(e.mapping.map(n+o),null,aW(a.attrs,i-t.colCount(o))),s+=a.attrs.rowspan-1}else{let a=null==o?aP(r.type.schema).cell:r.nodeAt(t.map[l+o]).type,c=t.positionAt(s,i,r);e.insert(e.mapping.map(n+c),a.createAndFill())}}return e}function aX(e,{map:t,tableStart:n,table:r},i){var o;let s=n;for(let e=0;e<i;e++)s+=r.child(e).nodeSize;let l=[],a=i>0?-1:0;(function(e,t,n){var r;let i=aP(t.type.schema).header_cell;for(let o=0;o<e.width;o++)if((null==(r=t.nodeAt(e.map[o+n*e.width]))?void 0:r.type)!=i)return!1;return!0})(t,r,i+a)&&(a=0==i||i==t.height?null:0);for(let s=0,c=t.width*i;s<t.width;s++,c++)if(i>0&&i<t.height&&t.map[c]==t.map[c-t.width]){let i=t.map[c],o=r.nodeAt(i).attrs;e.setNodeMarkup(n+i,null,{...o,rowspan:o.rowspan+1}),s+=o.colspan-1}else{let e=null==a?aP(r.type.schema).cell:null==(o=r.nodeAt(t.map[c+a*t.width]))?void 0:o.type,n=null==e?void 0:e.createAndFill();n&&l.push(n)}return e.insert(s,aP(r.type.schema).row.create(null,l)),e}function aQ(e){let t=e.content;return 1==t.childCount&&t.child(0).isTextblock&&0==t.child(0).childCount}function aZ(e,t){let n=e.selection;if(!(n instanceof a_)||n.$anchorCell.pos==n.$headCell.pos)return!1;let r=aG(e),{map:i}=r;if(function({width:e,height:t,map:n},r){let i=r.top*e+r.left,o=i,s=(r.bottom-1)*e+r.left,l=i+(r.right-r.left-1);for(let t=r.top;t<r.bottom;t++){if(r.left>0&&n[o]==n[o-1]||r.right<e&&n[l]==n[l+1])return!0;o+=e,l+=e}for(let o=r.left;o<r.right;o++){if(r.top>0&&n[i]==n[i-e]||r.bottom<t&&n[s]==n[s+e])return!0;i++,s++}return!1}(i,r))return!1;if(t){let n,o,s=e.tr,l={},a=p.empty;for(let e=r.top;e<r.bottom;e++)for(let t=r.left;t<r.right;t++){let c=i.map[e*i.width+t],d=r.table.nodeAt(c);if(!l[c]&&d)if(l[c]=!0,null==n)n=c,o=d;else{aQ(d)||(a=a.append(d.content));let e=s.mapping.map(c+r.tableStart);s.delete(e,e+d.nodeSize)}}if(null==n||null==o)return!0;if(s.setNodeMarkup(n+r.tableStart,null,{...aW(o.attrs,o.attrs.colspan,r.right-r.left-o.attrs.colspan),rowspan:r.bottom-r.top}),a.size){let e=n+1+o.content.size,t=aQ(o)?n+1:e;s.replaceWith(t+r.tableStart,e+r.tableStart,a)}s.setSelection(new a_(s.doc.resolve(n+r.tableStart))),t(s)}return!0}function a0(e,t){var n;let r=aP(e.schema);return(n=({node:e})=>r[e.type.spec.tableRole],(e,t)=>{var r;let i,o,s=e.selection;if(s instanceof a_){if(s.$anchorCell.pos!=s.$headCell.pos)return!1;i=s.$anchorCell.nodeAfter,o=s.$anchorCell.pos}else{if(!(i=function(e){for(let t=e.depth;t>0;t--){let n=e.node(t).type.spec.tableRole;if("cell"===n||"header_cell"===n)return e.node(t)}return null}(s.$from)))return!1;o=null==(r=aj(s.$from))?void 0:r.pos}if(null==i||null==o||1==i.attrs.colspan&&1==i.attrs.rowspan)return!1;if(t){let r,l=i.attrs,a=[],c=l.colwidth;l.rowspan>1&&(l={...l,rowspan:1}),l.colspan>1&&(l={...l,colspan:1});let d=aG(e),h=e.tr;for(let e=0;e<d.right-d.left;e++)a.push(c?{...l,colwidth:c&&c[e]?[c[e]]:null}:l);for(let e=d.top;e<d.bottom;e++){let t=d.map.positionAt(e,d.left,d.table);e==d.top&&(t+=i.nodeSize);for(let o=d.left,s=0;o<d.right;o++,s++)(o!=d.left||e!=d.top)&&h.insert(r=h.mapping.map(t+d.tableStart,1),n({node:i,row:e,col:o}).createAndFill(a[s]))}h.setNodeMarkup(o,n({node:i,row:d.top,col:d.left}),a[0]),s instanceof a_&&h.setSelection(new a_(h.doc.resolve(s.$anchorCell.pos),r?h.doc.resolve(r):void 0)),t(h)}return!0})(e,t)}function a1(e,t,n){let r=t.map.cellsInRect({left:0,top:0,right:"row"==e?t.map.width:1,bottom:"column"==e?t.map.height:1});for(let e=0;e<r.length;e++){let i=t.table.nodeAt(r[e]);if(i&&i.type!==n.header_cell)return!1}return!0}function a2(e,t){if((t=t||{useDeprecatedLogic:!1}).useDeprecatedLogic)return function(t,n){if(!az(t))return!1;if(n){let r=aP(t.schema),i=aG(t),o=t.tr,s=i.map.cellsInRect("column"==e?{left:i.left,top:0,right:i.right,bottom:i.map.height}:"row"==e?{left:0,top:i.top,right:i.map.width,bottom:i.bottom}:i),l=s.map(e=>i.table.nodeAt(e));for(let e=0;e<s.length;e++)l[e].type==r.header_cell&&o.setNodeMarkup(i.tableStart+s[e],r.cell,l[e].attrs);if(0==o.steps.length)for(let e=0;e<s.length;e++)o.setNodeMarkup(i.tableStart+s[e],r.header_cell,l[e].attrs);n(o)}return!0};return function(t,n){if(!az(t))return!1;if(n){let r=aP(t.schema),i=aG(t),o=t.tr,s=a1("row",i,r),l=a1("column",i,r),a=+!!("column"===e?s:"row"===e&&l),c="column"==e?{left:0,top:a,right:1,bottom:i.map.height}:"row"==e?{left:a,top:0,right:i.map.width,bottom:1}:i,d="column"==e?l?r.cell:r.header_cell:"row"==e?s?r.cell:r.header_cell:r.cell;i.map.cellsInRect(c).forEach(e=>{let t=e+i.tableStart,n=o.doc.nodeAt(t);n&&o.setNodeMarkup(t,d,n.attrs)}),n(o)}return!0}}a2("row",{useDeprecatedLogic:!0}),a2("column",{useDeprecatedLogic:!0});var a3=a2("cell",{useDeprecatedLogic:!0});function a4(e){return function(t,n){if(!az(t))return!1;let r=function(e,t){if(t<0){let t=e.nodeBefore;if(t)return e.pos-t.nodeSize;for(let t=e.index(-1)-1,n=e.before();t>=0;t--){let r=e.node(-1).child(t),i=r.lastChild;if(i)return n-1-i.nodeSize;n-=r.nodeSize}}else{if(e.index()<e.parent.childCount-1)return e.pos+e.nodeAfter.nodeSize;let t=e.node(-1);for(let n=e.indexAfter(-1),r=e.after();n<t.childCount;n++){let e=t.child(n);if(e.childCount)return r+1;r+=e.nodeSize}}return null}(a$(t),e);if(null==r)return!1;if(n){let e=t.doc.resolve(r);n(t.tr.setSelection(eY.between(e,e.node(0).resolve(e.pos+e.nodeAfter.nodeSize))).scrollIntoView())}return!0}}function a5(e,t){let n=e.selection;if(!(n instanceof a_))return!1;if(t){let r=e.tr,i=aP(e.schema).cell.createAndFill().content;n.forEachCell((e,t)=>{e.content.eq(i)||r.replace(r.mapping.map(t+1),r.mapping.map(t+e.nodeSize-1),new b(i,0,0))}),r.docChanged&&t(r)}return!0}function a6(e,t){let n=e.createAndFill();return new e_(n).replace(0,n.content.size,t).doc}function a7(e,t,n,r,i,o,s,l){if(0==s||s==t.height)return!1;let a=!1;for(let c=i;c<o;c++){let i=s*t.width+c,o=t.map[i];if(t.map[i-t.width]==o){a=!0;let i=n.nodeAt(o),{top:d,left:h}=t.findCell(o);e.setNodeMarkup(e.mapping.slice(l).map(o+r),null,{...i.attrs,rowspan:s-d}),e.insert(e.mapping.slice(l).map(t.positionAt(s,h,n)),i.type.createAndFill({...i.attrs,rowspan:d+i.attrs.rowspan-s})),c+=i.attrs.colspan-1}}return a}function a8(e,t,n,r,i,o,s,l){if(0==s||s==t.width)return!1;let a=!1;for(let c=i;c<o;c++){let i=c*t.width+s,o=t.map[i];if(t.map[i-1]==o){a=!0;let i=n.nodeAt(o),d=t.colCount(o),h=e.mapping.slice(l).map(o+r);e.setNodeMarkup(h,null,aF(i.attrs,s-d,i.attrs.colspan-(s-d))),e.insert(h+i.nodeSize,i.type.createAndFill(aF(i.attrs,0,s-d))),c+=i.attrs.rowspan-1}}return a}function a9(e,t,n,r,i){let o=n?e.doc.nodeAt(n-1):e.doc;if(!o)throw Error("No table found");let s=aI.get(o),{top:l,left:a}=r,c=a+i.width,d=l+i.height,h=e.tr,u=0;function f(){if(!(o=n?h.doc.nodeAt(n-1):h.doc))throw Error("No table found");s=aI.get(o),u=h.mapping.maps.length}(function(e,t,n,r,i,o,s){let l,a,c=aP(e.doc.type.schema);if(i>t.width)for(let o=0,s=0;o<t.height;o++){let d,h=n.child(o);s+=h.nodeSize;let p=[];d=null==h.lastChild||h.lastChild.type==c.cell?l||(l=c.cell.createAndFill()):a||(a=c.header_cell.createAndFill());for(let e=t.width;e<i;e++)p.push(d);e.insert(e.mapping.slice(0).map(s-1+r),p)}if(o>t.height){let d=[];for(let e=0,r=(t.height-1)*t.width;e<Math.max(t.width,i);e++){let i=!(e>=t.width)&&n.nodeAt(t.map[r+e]).type==c.header_cell;d.push(i?a||(a=c.header_cell.createAndFill()):l||(l=c.cell.createAndFill()))}let h=c.row.create(null,p.from(d)),u=[];for(let e=t.height;e<o;e++)u.push(h);e.insert(e.mapping.slice(s).map(r+n.nodeSize-2),u)}return!!(l||a)})(h,s,o,n,c,d,0)&&f(),a7(h,s,o,n,a,c,l,u)&&f(),a7(h,s,o,n,a,c,d,u)&&f(),a8(h,s,o,n,l,d,a,u)&&f(),a8(h,s,o,n,l,d,c,u)&&f();for(let e=l;e<d;e++){let t=s.positionAt(e,a,o),r=s.positionAt(e,c,o);h.replace(h.mapping.slice(u).map(t+n),h.mapping.slice(u).map(r+n),new b(i.rows[e-l],0,0))}f(),h.setSelection(new a_(h.doc.resolve(n+s.positionAt(l,a,o)),h.doc.resolve(n+s.positionAt(d-1,c-1,o)))),t(h)}var ce=rq({ArrowLeft:cn("horiz",-1),ArrowRight:cn("horiz",1),ArrowUp:cn("vert",-1),ArrowDown:cn("vert",1),"Shift-ArrowLeft":cr("horiz",-1),"Shift-ArrowRight":cr("horiz",1),"Shift-ArrowUp":cr("vert",-1),"Shift-ArrowDown":cr("vert",1),Backspace:a5,"Mod-Backspace":a5,Delete:a5,"Mod-Delete":a5});function ct(e,t,n){return!n.eq(e.selection)&&(t&&t(e.tr.setSelection(n).scrollIntoView()),!0)}function cn(e,t){return(n,r,i)=>{if(!i)return!1;let o=n.selection;if(o instanceof a_)return ct(n,r,eK.near(o.$headCell,t));if("horiz"!=e&&!o.empty)return!1;let s=cl(i,e,t);if(null==s)return!1;if("horiz"==e)return ct(n,r,eK.near(n.doc.resolve(o.head+t),t));{let i,o=n.doc.resolve(s),l=aV(o,e,t);return i=l?eK.near(l,1):t<0?eK.near(n.doc.resolve(o.before(-1)),-1):eK.near(n.doc.resolve(o.after(-1)),1),ct(n,r,i)}}}function cr(e,t){return(n,r,i)=>{let o;if(!i)return!1;let s=n.selection;if(s instanceof a_)o=s;else{let r=cl(i,e,t);if(null==r)return!1;o=new a_(n.doc.resolve(r))}let l=aV(o.$headCell,e,t);return!!l&&ct(n,r,new a_(o.$anchorCell,l))}}function ci(e,t){let n=aj(e.state.doc.resolve(t));return!!n&&(e.dispatch(e.state.tr.setSelection(new a_(n))),!0)}function co(e,t,n){if(!az(e.state))return!1;let r=function(e){if(!e.size)return null;let{content:t,openStart:n,openEnd:r}=e;for(;1==t.childCount&&(n>0&&r>0||"table"==t.child(0).type.spec.tableRole);)n--,r--,t=t.child(0).content;let i=t.child(0),o=i.type.spec.tableRole,s=i.type.schema,l=[];if("row"==o)for(let e=0;e<t.childCount;e++){let i=t.child(e).content,o=e?0:Math.max(0,n-1),a=e<t.childCount-1?0:Math.max(0,r-1);(o||a)&&(i=a6(aP(s).row,new b(i,o,a)).content),l.push(i)}else{if("cell"!=o&&"header_cell"!=o)return null;l.push(n||r?a6(aP(s).row,new b(t,n,r)).content:t)}return function(e,t){let n=[];for(let e=0;e<t.length;e++){let r=t[e];for(let t=r.childCount-1;t>=0;t--){let{rowspan:i,colspan:o}=r.child(t).attrs;for(let t=e;t<e+i;t++)n[t]=(n[t]||0)+o}}let r=0;for(let e=0;e<n.length;e++)r=Math.max(r,n[e]);for(let i=0;i<n.length;i++)if(i>=t.length&&t.push(p.empty),n[i]<r){let o=aP(e).cell.createAndFill(),s=[];for(let e=n[i];e<r;e++)s.push(o);t[i]=t[i].append(p.from(s))}return{height:t.length,width:r,rows:t}}(s,l)}(n),i=e.state.selection;if(i instanceof a_){r||(r={width:1,height:1,rows:[p.from(a6(aP(e.state.schema).cell,n))]});let t=i.$anchorCell.node(-1),o=i.$anchorCell.start(-1),s=aI.get(t).rectBetween(i.$anchorCell.pos-o,i.$headCell.pos-o);return r=function({width:e,height:t,rows:n},r,i){if(e!=r){let t=[],i=[];for(let e=0;e<n.length;e++){let o=n[e],s=[];for(let n=t[e]||0,i=0;n<r;i++){let l=o.child(i%o.childCount);n+l.attrs.colspan>r&&(l=l.type.createChecked(aF(l.attrs,l.attrs.colspan,n+l.attrs.colspan-r),l.content)),s.push(l),n+=l.attrs.colspan;for(let n=1;n<l.attrs.rowspan;n++)t[e+n]=(t[e+n]||0)+l.attrs.colspan}i.push(p.from(s))}n=i,e=r}if(t!=i){let e=[];for(let r=0,o=0;r<i;r++,o++){let s=[],l=n[o%t];for(let e=0;e<l.childCount;e++){let t=l.child(e);r+t.attrs.rowspan>i&&(t=t.type.create({...t.attrs,rowspan:Math.max(1,i-t.attrs.rowspan)},t.content)),s.push(t)}e.push(p.from(s))}n=e,t=i}return{width:e,height:t,rows:n}}(r,s.right-s.left,s.bottom-s.top),a9(e.state,e.dispatch,o,s,r),!0}if(!r)return!1;{let t=a$(e.state),n=t.start(-1);return a9(e.state,e.dispatch,n,aI.get(t.node(-1)).findCell(t.pos-n),r),!0}}function cs(e,t){var n;let r;if(t.ctrlKey||t.metaKey)return;let i=ca(e,t.target);if(t.shiftKey&&e.state.selection instanceof a_)o(e.state.selection.$anchorCell,t),t.preventDefault();else if(t.shiftKey&&i&&null!=(r=aj(e.state.selection.$anchor))&&(null==(n=cc(e,t))?void 0:n.pos)!=r.pos)o(r,t),t.preventDefault();else if(!i)return;function o(t,n){let r=cc(e,n),i=null==aL.getState(e.state);if(!r||!aH(t,r))if(!i)return;else r=t;let o=new a_(t,r);if(i||!e.state.selection.eq(o)){let n=e.state.tr.setSelection(o);i&&n.setMeta(aL,t.pos),e.dispatch(n)}}function s(){e.root.removeEventListener("mouseup",s),e.root.removeEventListener("dragstart",s),e.root.removeEventListener("mousemove",l),null!=aL.getState(e.state)&&e.dispatch(e.state.tr.setMeta(aL,-1))}function l(n){let r,l=aL.getState(e.state);if(null!=l)r=e.state.doc.resolve(l);else if(ca(e,n.target)!=i&&!(r=cc(e,t)))return s();r&&o(r,n)}e.root.addEventListener("mouseup",s),e.root.addEventListener("dragstart",s),e.root.addEventListener("mousemove",l)}function cl(e,t,n){if(!(e.state.selection instanceof eY))return null;let{$head:r}=e.state.selection;for(let i=r.depth-1;i>=0;i--){let o=r.node(i);if((n<0?r.index(i):r.indexAfter(i))!=(n<0?0:o.childCount))break;if("cell"==o.type.spec.tableRole||"header_cell"==o.type.spec.tableRole){let o=r.before(i),s="vert"==t?n>0?"down":"up":n>0?"right":"left";return e.endOfTextblock(s)?o:null}}return null}function ca(e,t){for(;t&&t!=e.dom;t=t.parentNode)if("TD"==t.nodeName||"TH"==t.nodeName)return t;return null}function cc(e,t){let n=e.posAtCoords({left:t.clientX,top:t.clientY});return n&&n?aj(e.state.doc.resolve(n.pos)):null}var cd=class{constructor(e,t){this.node=e,this.defaultCellMinWidth=t,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.table.style.setProperty("--default-cell-min-width",`${t}px`),this.colgroup=this.table.appendChild(document.createElement("colgroup")),ch(e,this.colgroup,this.table,t),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(e){return e.type==this.node.type&&(this.node=e,ch(e,this.colgroup,this.table,this.defaultCellMinWidth),!0)}ignoreMutation(e){return"attributes"==e.type&&(e.target==this.table||this.colgroup.contains(e.target))}};function ch(e,t,n,r,i,o){var s;let l=0,a=!0,c=t.firstChild,d=e.firstChild;if(d){for(let e=0,n=0;e<d.childCount;e++){let{colspan:s,colwidth:h}=d.child(e).attrs;for(let e=0;e<s;e++,n++){let s=i==n?o:h&&h[e],d=s?s+"px":"";if(l+=s||r,s||(a=!1),c)c.style.width!=d&&(c.style.width=d),c=c.nextSibling;else{let e=document.createElement("col");e.style.width=d,t.appendChild(e)}}}for(;c;){let e=c.nextSibling;null==(s=c.parentNode)||s.removeChild(c),c=e}a?(n.style.width=l+"px",n.style.minWidth=""):(n.style.width="",n.style.minWidth=l+"px")}}var cp=new tr("tableColumnResizing"),cu=class e{constructor(e,t){this.activeHandle=e,this.dragging=t}apply(t){let n=t.getMeta(cp);if(n&&null!=n.setHandle)return new e(n.setHandle,!1);if(n&&void 0!==n.setDragging)return new e(this.activeHandle,n.setDragging);if(this.activeHandle>-1&&t.docChanged){let n=t.mapping.map(this.activeHandle,-1);return aB(t.doc.resolve(n))||(n=-1),new e(n,this.dragging)}return this}};function cf(e,t,n,r){let i=e.posAtCoords({left:t.clientX+("right"==n?-r:r),top:t.clientY});if(!i)return -1;let{pos:o}=i,s=aj(e.state.doc.resolve(o));if(!s)return -1;if("right"==n)return s.pos;let l=aI.get(s.node(-1)),a=s.start(-1),c=l.map.indexOf(s.pos-a);return c%l.width==0?-1:a+l.map[c-1]}function cm(e,t,n){let r=t.clientX-e.startX;return Math.max(n,e.startWidth+r)}function cg(e,t){e.dispatch(e.state.tr.setMeta(cp,{setHandle:t}))}function cy(e,t,n,r){let i=e.state.doc.resolve(t),o=i.node(-1),s=i.start(-1),l=aI.get(o).colCount(i.pos-s)+i.nodeAfter.attrs.colspan-1,a=e.domAtPos(i.start(-1)).node;for(;a&&"TABLE"!=a.nodeName;)a=a.parentNode;a&&ch(o,a.firstChild,a,r,l,n)}function cb(e,t){return t?["width",`${Math.max(t,e)}px`]:["min-width",`${e}px`]}function cw(e,t,n,r,i,o){var s;let l=0,a=!0,c=t.firstChild,d=e.firstChild;if(null!==d)for(let e=0,n=0;e<d.childCount;e+=1){let{colspan:s,colwidth:h}=d.child(e).attrs;for(let e=0;e<s;e+=1,n+=1){let s=i===n?o:h&&h[e],d=s?`${s}px`:"";if(l+=s||r,s||(a=!1),c){if(c.style.width!==d){let[e,t]=cb(r,s);c.style.setProperty(e,t)}c=c.nextSibling}else{let e=document.createElement("col"),[n,i]=cb(r,s);e.style.setProperty(n,i),t.appendChild(e)}}}for(;c;){let e=c.nextSibling;null==(s=c.parentNode)||s.removeChild(c),c=e}a?(n.style.width=`${l}px`,n.style.minWidth=""):(n.style.width="",n.style.minWidth=`${l}px`)}class cv{constructor(e,t){this.node=e,this.cellMinWidth=t,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.colgroup=this.table.appendChild(document.createElement("colgroup")),cw(e,this.colgroup,this.table,t),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(e){return e.type===this.node.type&&(this.node=e,cw(e,this.colgroup,this.table,this.cellMinWidth),!0)}ignoreMutation(e){return"attributes"===e.type&&(e.target===this.table||this.colgroup.contains(e.target))}}function cx(e,t){return t?e.createChecked(null,t):e.createAndFill()}let ck=({editor:e})=>{let{selection:t}=e.state;if(!(t instanceof a_))return!1;let n=0,r=od(t.ranges[0].$from,e=>"table"===e.type.name);return null==r||r.node.descendants(e=>{if("table"===e.type.name)return!1;["tableCell","tableHeader"].includes(e.type.name)&&(n+=1)}),n===t.ranges.length&&(e.commands.deleteTable(),!0)},cS=oj.create({name:"table",addOptions:()=>({HTMLAttributes:{},resizable:!1,handleWidth:5,cellMinWidth:25,View:cv,lastColumnResizable:!0,allowTableNodeSelection:!1}),content:"tableRow+",tableRole:"table",isolating:!0,group:"block",parseHTML:()=>[{tag:"table"}],renderHTML({node:e,HTMLAttributes:t}){let{colgroup:n,tableWidth:r,tableMinWidth:i}=function(e,t,n,r){let i=0,o=!0,s=[],l=e.firstChild;if(!l)return{};for(let e=0,n=0;e<l.childCount;e+=1){let{colspan:r,colwidth:a}=l.child(e).attrs;for(let e=0;e<r;e+=1,n+=1){let r=void 0===n?void 0:a&&a[e];i+=r||t,r||(o=!1);let[l,c]=cb(t,r);s.push(["col",{style:`${l}: ${c}`}])}}return{colgroup:["colgroup",{},...s],tableWidth:o?`${i}px`:"",tableMinWidth:o?"":`${i}px`}}(e,this.options.cellMinWidth);return["table",iN(this.options.HTMLAttributes,t,{style:r?`width: ${r}`:`min-width: ${i}`}),n,["tbody",0]]},addCommands:()=>({insertTable:({rows:e=3,cols:t=3,withHeaderRow:n=!0}={})=>({tr:r,dispatch:i,editor:o})=>{let s=function(e,t,n,r,i){let o=function(e){if(e.cached.tableNodeTypes)return e.cached.tableNodeTypes;let t={};return Object.keys(e.nodes).forEach(n=>{let r=e.nodes[n];r.spec.tableRole&&(t[r.spec.tableRole]=r)}),e.cached.tableNodeTypes=t,t}(e),s=[],l=[];for(let e=0;e<n;e+=1){let e=cx(o.cell,void 0);if(e&&l.push(e),r){let e=cx(o.header_cell,void 0);e&&s.push(e)}}let a=[];for(let e=0;e<t;e+=1)a.push(o.row.createChecked(null,r&&0===e?s:l));return o.table.createChecked(null,a)}(o.schema,e,t,n);if(i){let e=r.selection.from+1;r.replaceSelectionWith(s).scrollIntoView().setSelection(eY.near(r.doc.resolve(e)))}return!0},addColumnBefore:()=>({state:e,dispatch:t})=>(function(e,t){if(!az(e))return!1;if(t){let n=aG(e);t(aY(e.tr,n,n.left))}return!0})(e,t),addColumnAfter:()=>({state:e,dispatch:t})=>(function(e,t){if(!az(e))return!1;if(t){let n=aG(e);t(aY(e.tr,n,n.right))}return!0})(e,t),deleteColumn:()=>({state:e,dispatch:t})=>(function(e,t){if(!az(e))return!1;if(t){let n=aG(e),r=e.tr;if(0==n.left&&n.right==n.map.width)return!1;for(let e=n.right-1;!function(e,{map:t,table:n,tableStart:r},i){let o=e.mapping.maps.length;for(let s=0;s<t.height;){let l=s*t.width+i,a=t.map[l],c=n.nodeAt(a),d=c.attrs;if(i>0&&t.map[l-1]==a||i<t.width-1&&t.map[l+1]==a)e.setNodeMarkup(e.mapping.slice(o).map(r+a),null,aF(d,i-t.colCount(a)));else{let t=e.mapping.slice(o).map(r+a);e.delete(t,t+c.nodeSize)}s+=d.rowspan}}(r,n,e),e!=n.left;e--){let e=n.tableStart?r.doc.nodeAt(n.tableStart-1):r.doc;if(!e)throw RangeError("No table found");n.table=e,n.map=aI.get(e)}t(r)}return!0})(e,t),addRowBefore:()=>({state:e,dispatch:t})=>(function(e,t){if(!az(e))return!1;if(t){let n=aG(e);t(aX(e.tr,n,n.top))}return!0})(e,t),addRowAfter:()=>({state:e,dispatch:t})=>(function(e,t){if(!az(e))return!1;if(t){let n=aG(e);t(aX(e.tr,n,n.bottom))}return!0})(e,t),deleteRow:()=>({state:e,dispatch:t})=>(function(e,t){if(!az(e))return!1;if(t){let n=aG(e),r=e.tr;if(0==n.top&&n.bottom==n.map.height)return!1;for(let e=n.bottom-1;!function(e,{map:t,table:n,tableStart:r},i){let o=0;for(let e=0;e<i;e++)o+=n.child(e).nodeSize;let s=o+n.child(i).nodeSize,l=e.mapping.maps.length;e.delete(o+r,s+r);let a=new Set;for(let o=0,s=i*t.width;o<t.width;o++,s++){let c=t.map[s];if(!a.has(c)){if(a.add(c),i>0&&c==t.map[s-t.width]){let t=n.nodeAt(c).attrs;e.setNodeMarkup(e.mapping.slice(l).map(c+r),null,{...t,rowspan:t.rowspan-1}),o+=t.colspan-1}else if(i<t.height&&c==t.map[s+t.width]){let s=n.nodeAt(c),a=s.attrs,d=s.type.create({...a,rowspan:s.attrs.rowspan-1},s.content),h=t.positionAt(i+1,o,n);e.insert(e.mapping.slice(l).map(r+h),d),o+=a.colspan-1}}}}(r,n,e),e!=n.top;e--){let e=n.tableStart?r.doc.nodeAt(n.tableStart-1):r.doc;if(!e)throw RangeError("No table found");n.table=e,n.map=aI.get(n.table)}t(r)}return!0})(e,t),deleteTable:()=>({state:e,dispatch:t})=>(function(e,t){let n=e.selection.$anchor;for(let r=n.depth;r>0;r--)if("table"==n.node(r).type.spec.tableRole)return t&&t(e.tr.delete(n.before(r),n.after(r)).scrollIntoView()),!0;return!1})(e,t),mergeCells:()=>({state:e,dispatch:t})=>aZ(e,t),splitCell:()=>({state:e,dispatch:t})=>a0(e,t),toggleHeaderColumn:()=>({state:e,dispatch:t})=>a2("column")(e,t),toggleHeaderRow:()=>({state:e,dispatch:t})=>a2("row")(e,t),toggleHeaderCell:()=>({state:e,dispatch:t})=>a3(e,t),mergeOrSplit:()=>({state:e,dispatch:t})=>!!aZ(e,t)||a0(e,t),setCellAttribute:(e,t)=>({state:n,dispatch:r})=>(function(e,t){return function(n,r){if(!az(n))return!1;let i=a$(n);if(i.nodeAfter.attrs[e]===t)return!1;if(r){let o=n.tr;n.selection instanceof a_?n.selection.forEachCell((n,r)=>{n.attrs[e]!==t&&o.setNodeMarkup(r,null,{...n.attrs,[e]:t})}):o.setNodeMarkup(i.pos,null,{...i.nodeAfter.attrs,[e]:t}),r(o)}return!0}})(e,t)(n,r),goToNextCell:()=>({state:e,dispatch:t})=>a4(1)(e,t),goToPreviousCell:()=>({state:e,dispatch:t})=>a4(-1)(e,t),fixTables:()=>({state:e,dispatch:t})=>(t&&aU(e),!0),setCellSelection:e=>({tr:t,dispatch:n})=>{if(n){let n=a_.create(t.doc,e.anchorCell,e.headCell);t.setSelection(n)}return!0}}),addKeyboardShortcuts(){return{Tab:()=>!!this.editor.commands.goToNextCell()||!!this.editor.can().addRowAfter()&&this.editor.chain().addRowAfter().goToNextCell().run(),"Shift-Tab":()=>this.editor.commands.goToPreviousCell(),Backspace:ck,"Mod-Backspace":ck,Delete:ck,"Mod-Delete":ck}},addProseMirrorPlugins(){return[...this.options.resizable&&this.editor.isEditable?[function({handleWidth:e=5,cellMinWidth:t=25,defaultCellMinWidth:n=100,View:r=cd,lastColumnResizable:i=!0}={}){let o=new te({key:cp,state:{init(e,t){var i,s;let l=null==(s=null==(i=o.spec)?void 0:i.props)?void 0:s.nodeViews,a=aP(t.schema).table.name;return r&&l&&(l[a]=(e,t)=>new r(e,n,t)),new cu(-1,!1)},apply:(e,t)=>t.apply(e)},props:{attributes:e=>{let t=cp.getState(e);return t&&t.activeHandle>-1?{class:"resize-cursor"}:{}},handleDOMEvents:{mousemove:(t,n)=>{!function(e,t,n,r){if(!e.editable)return;let i=cp.getState(e.state);if(i&&!i.dragging){let o=function(e){for(;e&&"TD"!=e.nodeName&&"TH"!=e.nodeName;)e=e.classList&&e.classList.contains("ProseMirror")?null:e.parentNode;return e}(t.target),s=-1;if(o){let{left:r,right:i}=o.getBoundingClientRect();t.clientX-r<=n?s=cf(e,t,"left",n):i-t.clientX<=n&&(s=cf(e,t,"right",n))}if(s!=i.activeHandle){if(!r&&-1!==s){let t=e.state.doc.resolve(s),n=t.node(-1),r=aI.get(n),i=t.start(-1);if(r.colCount(t.pos-i)+t.nodeAfter.attrs.colspan-1==r.width-1)return}cg(e,s)}}}(t,n,e,i)},mouseleave:e=>{!function(e){if(!e.editable)return;let t=cp.getState(e.state);t&&t.activeHandle>-1&&!t.dragging&&cg(e,-1)}(e)},mousedown:(e,r)=>{!function(e,t,n,r){var i;if(!e.editable)return;let o=null!=(i=e.dom.ownerDocument.defaultView)?i:window,s=cp.getState(e.state);if(!s||-1==s.activeHandle||s.dragging)return;let l=e.state.doc.nodeAt(s.activeHandle),a=function(e,t,{colspan:n,colwidth:r}){let i=r&&r[r.length-1];if(i)return i;let o=e.domAtPos(t),s=o.node.childNodes[o.offset].offsetWidth,l=n;if(r)for(let e=0;e<n;e++)r[e]&&(s-=r[e],l--);return s/l}(e,s.activeHandle,l.attrs);function c(t){o.removeEventListener("mouseup",c),o.removeEventListener("mousemove",d);let r=cp.getState(e.state);(null==r?void 0:r.dragging)&&(function(e,t,n){let r=e.state.doc.resolve(t),i=r.node(-1),o=aI.get(i),s=r.start(-1),l=o.colCount(r.pos-s)+r.nodeAfter.attrs.colspan-1,a=e.state.tr;for(let e=0;e<o.height;e++){let t=e*o.width+l;if(e&&o.map[t]==o.map[t-o.width])continue;let r=o.map[t],c=i.nodeAt(r).attrs,d=1==c.colspan?0:l-o.colCount(r);if(c.colwidth&&c.colwidth[d]==n)continue;let h=c.colwidth?c.colwidth.slice():Array(c.colspan).fill(0);h[d]=n,a.setNodeMarkup(s+r,null,{...c,colwidth:h})}a.docChanged&&e.dispatch(a)}(e,r.activeHandle,cm(r.dragging,t,n)),e.dispatch(e.state.tr.setMeta(cp,{setDragging:null})))}function d(t){if(!t.which)return c(t);let i=cp.getState(e.state);if(i&&i.dragging){let o=cm(i.dragging,t,n);cy(e,i.activeHandle,o,r)}}e.dispatch(e.state.tr.setMeta(cp,{setDragging:{startX:t.clientX,startWidth:a}})),cy(e,s.activeHandle,a,r),o.addEventListener("mouseup",c),o.addEventListener("mousemove",d),t.preventDefault()}(e,r,t,n)}},decorations:e=>{let t=cp.getState(e);if(t&&t.activeHandle>-1)return function(e,t){var n;let r=[],i=e.doc.resolve(t),o=i.node(-1);if(!o)return rl.empty;let s=aI.get(o),l=i.start(-1),a=s.colCount(i.pos-l)+i.nodeAfter.attrs.colspan-1;for(let t=0;t<s.height;t++){let i=a+t*s.width;if((a==s.width-1||s.map[i]!=s.map[i+1])&&(0==t||s.map[i]!=s.map[i-s.width])){let t=s.map[i],a=l+t+o.nodeAt(t).nodeSize-1,c=document.createElement("div");c.className="column-resize-handle",(null==(n=cp.getState(e))?void 0:n.dragging)&&r.push(ri.node(l+t,l+t+o.nodeAt(t).nodeSize,{class:"column-resize-dragging"})),r.push(ri.widget(a,c))}}return rl.create(e.doc,r)}(e,t.activeHandle)},nodeViews:{}}});return o}({handleWidth:this.options.handleWidth,cellMinWidth:this.options.cellMinWidth,defaultCellMinWidth:this.options.cellMinWidth,View:this.options.View,lastColumnResizable:this.options.lastColumnResizable})]:[],function({allowTableNodeSelection:e=!1}={}){return new te({key:aL,state:{init:()=>null,apply(e,t){let n=e.getMeta(aL);if(null!=n)return -1==n?null:n;if(null==t||!e.docChanged)return t;let{deleted:r,pos:i}=e.mapping.mapResult(t);return r?null:i}},props:{decorations:aK,handleDOMEvents:{mousedown:cs},createSelectionBetween:e=>null!=aL.getState(e.state)?e.state.selection:null,handleTripleClick:ci,handleKeyDown:ce,handlePaste:co},appendTransaction:(t,n,r)=>(function(e,t,n){let r,i,o=(t||e).selection,s=(t||e).doc;if(o instanceof eQ&&(i=o.node.type.spec.tableRole)){if("cell"==i||"header_cell"==i)r=a_.create(s,o.from);else if("row"==i){let e=s.resolve(o.from+1);r=a_.rowSelection(e,e)}else if(!n){let e=aI.get(o.node),t=o.from+1,n=t+e.map[e.width*e.height-1];r=a_.create(s,t+1,n)}}else o instanceof eY&&function({$from:e,$to:t}){if(e.pos==t.pos||e.pos<t.pos-6)return!1;let n=e.pos,r=t.pos,i=e.depth;for(;i>=0&&!(e.after(i+1)<e.end(i));i--,n++);for(let e=t.depth;e>=0&&!(t.before(e+1)>t.start(e));e--,r--);return n==r&&/row|table/.test(e.node(i).type.spec.tableRole)}(o)?r=eY.create(s,o.from):o instanceof eY&&function({$from:e,$to:t}){let n,r;for(let t=e.depth;t>0;t--){let r=e.node(t);if("cell"===r.type.spec.tableRole||"header_cell"===r.type.spec.tableRole){n=r;break}}for(let e=t.depth;e>0;e--){let n=t.node(e);if("cell"===n.type.spec.tableRole||"header_cell"===n.type.spec.tableRole){r=n;break}}return n!==r&&0===t.parentOffset}(o)&&(r=eY.create(s,o.$from.start(),o.$from.end()));return r&&(t||(t=e.tr)).setSelection(r),t})(r,aU(r,n),e)})}({allowTableNodeSelection:this.options.allowTableNodeSelection})]},extendNodeSchema(e){let t={name:e.name,options:e.options,storage:e.storage};return{tableRole:iT(ik(e,"tableRole",t))}}}),cC=oj.create({name:"tableRow",addOptions:()=>({HTMLAttributes:{}}),content:"(tableCell | tableHeader)*",tableRole:"row",parseHTML:()=>[{tag:"tr"}],renderHTML({HTMLAttributes:e}){return["tr",iN(this.options.HTMLAttributes,e),0]}}),cM=oj.create({name:"tableHeader",addOptions:()=>({HTMLAttributes:{}}),content:"block+",addAttributes:()=>({colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:e=>{let t=e.getAttribute("colwidth");return t?t.split(",").map(e=>parseInt(e,10)):null}}}),tableRole:"header_cell",isolating:!0,parseHTML:()=>[{tag:"th"}],renderHTML({HTMLAttributes:e}){return["th",iN(this.options.HTMLAttributes,e),0]}}),cN=oj.create({name:"tableCell",addOptions:()=>({HTMLAttributes:{}}),content:"block+",addAttributes:()=>({colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:e=>{let t=e.getAttribute("colwidth");return t?t.split(",").map(e=>parseInt(e,10)):null}}}),tableRole:"cell",isolating:!0,parseHTML:()=>[{tag:"td"}],renderHTML({HTMLAttributes:e}){return["td",iN(this.options.HTMLAttributes,e),0]}});var cA=n(75324);let cO=(0,cA.A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]);var cT=n(11860),cE=n(11437),cD=n(13964);let cR=(0,cA.A)("Bold",[["path",{d:"M14 12a4 4 0 0 0 0-8H6v8",key:"v2sylx"}],["path",{d:"M15 20a4 4 0 0 0 0-8H6v8Z",key:"1ef5ya"}]]),cI=(0,cA.A)("Italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]]),cP=(0,cA.A)("Strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]]);var cL=n(80375);let cj=(0,cA.A)("Highlighter",[["path",{d:"m9 11-6 6v3h9l3-3",key:"1a3l36"}],["path",{d:"m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4",key:"14a9rk"}]]),cz=(0,cA.A)("Heading1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]]),c$=(0,cA.A)("Heading2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]]),cB=(0,cA.A)("Heading3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]]);var cH=n(25366);let cV=(0,cA.A)("ListOrdered",[["line",{x1:"10",x2:"21",y1:"6",y2:"6",key:"76qw6h"}],["line",{x1:"10",x2:"21",y1:"12",y2:"12",key:"16nom4"}],["line",{x1:"10",x2:"21",y1:"18",y2:"18",key:"u3jurt"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]]),cF=(0,cA.A)("Quote",[["path",{d:"M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z",key:"4rm80e"}],["path",{d:"M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z",key:"10za9r"}]]),cW=(0,cA.A)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]]),c_=(0,cA.A)("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]]),cq=(0,cA.A)("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]]);var cK=n(56085),cJ=n(25334),cU=n(88920),cG=n(97905);let cY=({isOpen:e,onClose:t,onSubmit:n,initialUrl:r="",initialTitle:i=""})=>{let[o,s]=(0,c.useState)(r),[l,d]=(0,c.useState)(i),[h,p]=(0,c.useState)(!1),[u,f]=(0,c.useState)(null);(0,c.useEffect)(()=>{e&&(s(r),d(i),f(null))},[e,r,i]);let m=async e=>{if(e.startsWith("http")){p(!0);try{await new Promise(e=>setTimeout(e,1e3));let t=new URL(e).hostname;f({title:l||`Page from ${t}`,description:`Content from ${t}`,favicon:`https://www.google.com/s2/favicons?domain=${t}&sz=32`})}catch(e){console.error("Failed to fetch link preview:",e)}finally{p(!1)}}},g=e=>{s(e),e&&e.startsWith("http")?m(e):f(null)};return(0,a.jsx)(cU.N,{children:e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(cG.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-50",onClick:t}),(0,a.jsx)(cG.P.div,{initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 border border-white/20 rounded-2xl shadow-2xl w-full max-w-md",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-white/10",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg",children:(0,a.jsx)(cO,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-white",children:"Add Link"})]}),(0,a.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-white hover:bg-white/10 rounded-lg transition-colors",children:(0,a.jsx)(cT.A,{className:"w-5 h-5"})})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),o&&(n(o,l),t())},className:"p-6 space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"URL"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(cE.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"url",value:o,onChange:e=>g(e.target.value),placeholder:"https://example.com",className:"w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",autoFocus:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Title (optional)"}),(0,a.jsx)("input",{type:"text",value:l,onChange:e=>d(e.target.value),placeholder:"Link title",className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"})]}),h&&(0,a.jsx)("div",{className:"p-4 bg-white/5 rounded-lg border border-white/10",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded animate-pulse"}),(0,a.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-white/20 rounded animate-pulse"}),(0,a.jsx)("div",{className:"h-3 bg-white/10 rounded w-2/3 animate-pulse"})]})]})}),u&&(0,a.jsx)(cG.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"p-4 bg-gradient-to-r from-blue-500/10 to-purple-600/10 rounded-lg border border-blue-500/20",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("img",{src:u.favicon,alt:"Favicon",className:"w-8 h-8 rounded",onError:e=>{e.currentTarget.src='data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32"><rect width="32" height="32" fill="%23374151"/><text x="16" y="20" text-anchor="middle" fill="white" font-size="14">\uD83C\uDF10</text></svg>'}}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"text-white font-medium truncate",children:u.title}),(0,a.jsx)("p",{className:"text-gray-400 text-sm truncate",children:u.description})]})]})}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:t,className:"flex-1 px-4 py-3 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors",children:"Cancel"}),(0,a.jsxs)(cG.P.button,{type:"submit",disabled:!o,whileHover:{scale:1.02},whileTap:{scale:.98},className:"flex-1 px-4 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed hover:from-blue-700 hover:to-purple-700 transition-all flex items-center justify-center space-x-2",children:[(0,a.jsx)(cD.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Add Link"})]})]})]})]})})]})})},cX=({onClick:e,isActive:t,disabled:n,children:r,title:i,gradient:o=!1})=>(0,a.jsxs)(cG.P.button,{onClick:e,disabled:n,title:i,whileHover:{scale:1.05},whileTap:{scale:.95},className:`p-2.5 rounded-lg transition-all duration-200 relative overflow-hidden ${t?o?"bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25":"bg-blue-600 text-white shadow-lg":"text-gray-300 hover:text-white hover:bg-white/10 hover:scale-105"} ${n?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:[r,t&&(0,a.jsx)(cG.P.div,{layoutId:"activeIndicator",className:"absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 -z-10 rounded-lg",initial:!1})]});function cQ({content:e,onChange:t,placeholder:n="Start writing...",className:r=""}){let[i,o]=(0,c.useState)(!1),[s,l]=(0,c.useState)(0),d=function(e={},t=[]){let n=(0,c.useRef)(e);n.current=e;let[r]=(0,c.useState)(()=>new o1(n)),i=oV.useSyncExternalStore(r.subscribe,r.getEditor,r.getServerSnapshot);return(0,c.useDebugValue)(i),(0,c.useEffect)(r.onRender(t)),!function(e){var t;let[n]=(0,c.useState)(()=>new oQ(e.editor)),r=oY.useSyncExternalStoreWithSelector(n.subscribe,n.getSnapshot,n.getServerSnapshot,e.selector,null!=(t=e.equalityFn)?t:oJ);oX(()=>n.watch(e.editor),[e.editor,n]),(0,c.useDebugValue)(r)}({editor:i,selector:({transactionNumber:t})=>!1===e.shouldRerenderOnTransaction?null:e.immediatelyRender&&0===t?0:t+1}),i}({extensions:[s5,s8.configure({multicolor:!0,HTMLAttributes:{class:"bg-gradient-to-r from-yellow-400/30 to-orange-500/30 px-1 rounded"}}),aE.configure({openOnClick:!1,HTMLAttributes:{class:"text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-500 hover:from-blue-300 hover:to-purple-400 underline decoration-blue-400/50 hover:decoration-blue-300/70 transition-all duration-200 cursor-pointer"}}),aD.configure({placeholder:n}),aR.configure({limit:1e4}),cS.configure({resizable:!0}),cC,cM,cN],content:e,onUpdate:({editor:e})=>{t(e.getHTML()),l(e.getText().split(/\s+/).filter(e=>e.length>0).length)},editorProps:{attributes:{class:"prose prose-invert max-w-none p-6 focus:outline-none min-h-[400px] text-gray-100"}}}),h=(0,c.useCallback)(()=>{d?.getAttributes("link").href,o(!0)},[d]),p=(0,c.useCallback)((e,t)=>{if(""===e)return void d?.chain().focus().extendMarkRange("link").unsetLink().run();d?.chain().focus().extendMarkRange("link").setLink({href:e}).run()},[d]),u=(0,c.useCallback)(()=>{d?.chain().focus().insertTable({rows:3,cols:3,withHeaderRow:!0}).run()},[d]);return d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:`glass-card ${r} bg-gradient-to-br from-slate-900/90 via-slate-800/90 to-slate-900/90 backdrop-blur-xl border border-white/20 shadow-2xl`,children:[(0,a.jsx)("div",{className:"border-b border-white/10 p-4 bg-gradient-to-r from-slate-800/50 to-slate-900/50",children:(0,a.jsxs)("div",{className:"flex flex-wrap items-center gap-2",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1 border-r border-white/10 pr-3 mr-3",children:[(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleBold().run(),isActive:d.isActive("bold"),title:"Bold (Ctrl+B)",gradient:!0,children:(0,a.jsx)(cR,{className:"w-4 h-4"})}),(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleItalic().run(),isActive:d.isActive("italic"),title:"Italic (Ctrl+I)",children:(0,a.jsx)(cI,{className:"w-4 h-4"})}),(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleStrike().run(),isActive:d.isActive("strike"),title:"Strikethrough",children:(0,a.jsx)(cP,{className:"w-4 h-4"})}),(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleCode().run(),isActive:d.isActive("code"),title:"Inline Code",children:(0,a.jsx)(cL.A,{className:"w-4 h-4"})}),(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleHighlight().run(),isActive:d.isActive("highlight"),title:"Highlight",gradient:!0,children:(0,a.jsx)(cj,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 border-r border-white/10 pr-3 mr-3",children:[(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleHeading({level:1}).run(),isActive:d.isActive("heading",{level:1}),title:"Heading 1",gradient:!0,children:(0,a.jsx)(cz,{className:"w-4 h-4"})}),(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleHeading({level:2}).run(),isActive:d.isActive("heading",{level:2}),title:"Heading 2",gradient:!0,children:(0,a.jsx)(c$,{className:"w-4 h-4"})}),(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleHeading({level:3}).run(),isActive:d.isActive("heading",{level:3}),title:"Heading 3",gradient:!0,children:(0,a.jsx)(cB,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 border-r border-white/10 pr-3 mr-3",children:[(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleBulletList().run(),isActive:d.isActive("bulletList"),title:"Bullet List",children:(0,a.jsx)(cH.A,{className:"w-4 h-4"})}),(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleOrderedList().run(),isActive:d.isActive("orderedList"),title:"Numbered List",children:(0,a.jsx)(cV,{className:"w-4 h-4"})}),(0,a.jsx)(cX,{onClick:()=>d.chain().focus().toggleBlockquote().run(),isActive:d.isActive("blockquote"),title:"Quote",children:(0,a.jsx)(cF,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 border-r border-white/10 pr-3 mr-3",children:[(0,a.jsx)(cX,{onClick:h,isActive:d.isActive("link"),title:"Add Link",gradient:!0,children:(0,a.jsx)(cO,{className:"w-4 h-4"})}),(0,a.jsx)(cX,{onClick:u,title:"Insert Table",children:(0,a.jsx)(cW,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[(0,a.jsx)(cX,{onClick:()=>d.chain().focus().undo().run(),disabled:!d.can().chain().focus().undo().run(),title:"Undo (Ctrl+Z)",children:(0,a.jsx)(c_,{className:"w-4 h-4"})}),(0,a.jsx)(cX,{onClick:()=>d.chain().focus().redo().run(),disabled:!d.can().chain().focus().redo().run(),title:"Redo (Ctrl+Y)",children:(0,a.jsx)(cq,{className:"w-4 h-4"})})]}),(0,a.jsx)("div",{className:"ml-auto",children:(0,a.jsxs)(cG.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:"px-4 py-2 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-lg font-medium shadow-lg shadow-purple-500/25 hover:shadow-purple-500/40 transition-all duration-200 flex items-center space-x-2",title:"AI Writing Assistant",children:[(0,a.jsx)(cK.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{className:"hidden sm:block",children:"AI Assist"})]})})]})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(oK,{editor:d}),(0,a.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-r from-slate-900/80 to-slate-800/80 backdrop-blur-sm border-t border-white/10",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4 text-gray-400",children:[(0,a.jsxs)("span",{className:"flex items-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,a.jsxs)("span",{children:[s," words"]})]}),(0,a.jsxs)("span",{children:[d.storage.characterCount.characters(),"/10,000 characters"]}),d.isActive("link")&&(0,a.jsxs)(cG.P.span,{initial:{opacity:0,x:-10},animate:{opacity:1,x:0},className:"flex items-center space-x-1 text-blue-400",children:[(0,a.jsx)(cJ.A,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Link selected"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-px h-4 bg-white/20"}),(0,a.jsx)("span",{className:"text-gray-400",children:"Ready"})]})]})})]})]}),(0,a.jsx)(cY,{isOpen:i,onClose:()=>o(!1),onSubmit:p,initialUrl:d?.getAttributes("link").href||""})]}):(0,a.jsx)("div",{className:"glass-card p-6",children:(0,a.jsxs)("div",{className:"animate-pulse",children:[(0,a.jsx)("div",{className:"h-4 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded w-1/4 mb-4"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("div",{className:"h-4 bg-white/20 rounded"}),(0,a.jsx)("div",{className:"h-4 bg-white/20 rounded w-5/6"}),(0,a.jsx)("div",{className:"h-4 bg-white/20 rounded w-4/6"})]})]})})}},80375:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(75324).A)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]])},84027:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(75324).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},87206:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>p,tree:()=>c});var r=n(65239),i=n(48088),o=n(88170),s=n.n(o),l=n(30893),a={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(a[e]=()=>l[e]);n.d(t,a);let c={children:["",{children:["editor",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,60714)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/editor/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(n.t.bind(n,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(n.t.bind(n,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/editor/page.tsx"],h={require:n,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/editor/page",pathname:"/editor",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[7719,5380,4101,3696],()=>n(87206));module.exports=r})();