(()=>{var e={};e.id=4662,e.ids=[4662],e.modules={1353:(e,t,s)=>{Promise.resolve().then(s.bind(s,74992))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7036:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15935:(e,t,s)=>{Promise.resolve().then(s.bind(s,84199))},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},16764:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},28947:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},33954:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>d,routeModule:()=>x,tree:()=>c});var r=s(65239),i=s(48088),a=s(88170),l=s.n(a),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c={children:["",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,74198)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/settings/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/settings/page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},37897:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},40083:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},45583:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51361:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},58869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},59497:(e,t,s)=>{Promise.resolve().then(s.bind(s,74198))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74198:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/settings/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/settings/page.tsx","default")},74808:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},74992:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>C});var r=s(60687),i=s(43210),a=s(97905),l=s(82136),n=s(16189),o=s(58869),c=s(84027),d=s(97051),h=s(98971),x=s(99891),m=s(37897),u=s(5336),p=s(93613),b=s(8819),g=s(40083),f=s(51361),v=s(92363),y=s(74808),j=s(25541),w=s(48730),N=s(28947),k=s(45583),A=s(85814),P=s.n(A);function C(){let{data:e,status:t}=(0,l.useSession)();(0,n.useRouter)();let[s,A]=(0,i.useState)(null),[C,S]=(0,i.useState)(!1),[M,z]=(0,i.useState)("idle"),[T,q]=(0,i.useState)(!0),[E,R]=(0,i.useState)("profile"),D=async()=>{try{let e=await fetch("/api/user/profile");if(e.ok){let t=await e.json();A(t)}}catch(e){console.error("Error fetching user profile:",e)}finally{q(!1)}},U=async()=>{if(s){S(!0),z("idle");try{let e=await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:s.firstName,lastName:s.lastName,bio:s.bio})}),t=await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s.settings)});e.ok&&t.ok?(z("success"),setTimeout(()=>z("idle"),3e3),await D()):z("error")}catch(e){console.error("Error saving settings:",e),z("error")}finally{S(!1)}}},_=(e,t)=>{A(s=>s?{...s,[e]:t}:null)},H=(e,t)=>{A(s=>s?{...s,settings:{...s.settings,[e]:t}}:null)},V=()=>s?.firstName&&s?.lastName?`${s.firstName} ${s.lastName}`:s?.name?s.name:s?.email?s.email.split("@")[0]:"User",$=async()=>{await (0,l.signOut)({callbackUrl:"/login"})},I=[{id:"profile",label:"Profile",icon:o.A,color:"from-blue-500 to-cyan-500"},{id:"preferences",label:"Preferences",icon:c.A,color:"from-emerald-500 to-teal-500"},{id:"notifications",label:"Notifications",icon:d.A,color:"from-yellow-500 to-orange-500"},{id:"appearance",label:"Appearance",icon:h.A,color:"from-purple-500 to-pink-500"},{id:"privacy",label:"Privacy",icon:x.A,color:"from-red-500 to-pink-500"}];return"loading"===t||T?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Loading your settings..."})]})}):"unauthenticated"===t?null:s?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900",children:[(0,r.jsx)("div",{className:"border-b border-white/10 bg-black/20 backdrop-blur-xl",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)(P(),{href:"/dashboard",children:(0,r.jsxs)(a.P.button,{whileHover:{scale:1.05},className:"flex items-center space-x-3 text-white hover:text-violet-400 transition-colors",children:[(0,r.jsx)(m.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"font-semibold",children:"Dashboard"})]})}),(0,r.jsx)("div",{className:"text-gray-600",children:"|"}),(0,r.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent",children:"Personal Settings"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:["success"===M&&(0,r.jsxs)(a.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"flex items-center text-emerald-400 text-sm bg-emerald-400/10 px-3 py-2 rounded-lg",children:[(0,r.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Changes saved!"]}),"error"===M&&(0,r.jsxs)(a.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"flex items-center text-red-400 text-sm bg-red-400/10 px-3 py-2 rounded-lg",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 mr-2"}),"Error saving"]}),(0,r.jsxs)(a.P.button,{whileHover:{scale:1.05},onClick:U,disabled:C,className:"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 text-white rounded-xl transition-all disabled:opacity-50 shadow-lg",children:[(0,r.jsx)(b.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:C?"Saving...":"Save Changes"})]}),(0,r.jsxs)(a.P.button,{whileHover:{scale:1.05},onClick:$,className:"flex items-center space-x-2 px-4 py-3 bg-red-600/20 hover:bg-red-600/30 border border-red-500/30 text-red-400 rounded-xl transition-all",children:[(0,r.jsx)(g.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Sign Out"})]})]})]})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)(a.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-6 sticky top-8",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsxs)("div",{className:"relative inline-block",children:[s.image?(0,r.jsx)("img",{src:s.image,alt:V(),className:"w-24 h-24 rounded-2xl object-cover border-4 border-white/20 mb-4 mx-auto"}):(0,r.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-4 mx-auto",children:(()=>{if(s?.firstName&&s?.lastName)return`${s.firstName[0]}${s.lastName[0]}`;if(s?.name){let e=s.name.split(" ");return e.length>1?`${e[0][0]}${e[e.length-1][0]}`:e[0][0]}return s?.email?s.email[0].toUpperCase():"U"})()}),(0,r.jsx)("button",{className:"absolute -bottom-1 -right-1 w-8 h-8 bg-violet-600 hover:bg-violet-700 rounded-full flex items-center justify-center text-white transition-colors",children:(0,r.jsx)(f.A,{className:"w-4 h-4"})})]}),(0,r.jsx)("h3",{className:"text-xl font-bold text-white mb-1",children:V()}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:s.email}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-1 mb-4",children:[(0,r.jsx)(v.A,{className:"w-4 h-4 text-yellow-400"}),(0,r.jsx)("span",{className:"text-yellow-400 text-sm font-medium",children:s.subscription?.plan==="free"?"Free Plan":s.subscription?.plan==="pro"?"Pro Member":"Member"})]})]}),(0,r.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-xl",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center",children:(0,r.jsx)(y.A,{className:"w-4 h-4 text-blue-400"})}),(0,r.jsx)("span",{className:"text-gray-300 text-sm",children:"Content Created"})]}),(0,r.jsx)("span",{className:"text-white font-semibold",children:s.stats?.totalContent||0})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-xl",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center",children:(0,r.jsx)(j.A,{className:"w-4 h-4 text-emerald-400"})}),(0,r.jsx)("span",{className:"text-gray-300 text-sm",children:"Quality Score"})]}),(0,r.jsx)("span",{className:"text-white font-semibold",children:"9.8/10"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-xl",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center",children:(0,r.jsx)(w.A,{className:"w-4 h-4 text-purple-400"})}),(0,r.jsx)("span",{className:"text-gray-300 text-sm",children:"Time Saved"})]}),(0,r.jsx)("span",{className:"text-white font-semibold",children:"48h"})]})]}),(0,r.jsx)("div",{className:"space-y-2",children:I.map(e=>(0,r.jsxs)(a.P.button,{whileHover:{scale:1.02},onClick:()=>R(e.id),className:`w-full flex items-center space-x-3 p-3 rounded-xl transition-all ${E===e.id?"bg-gradient-to-r from-violet-600/20 to-indigo-600/20 border border-violet-500/30 text-white":"text-gray-400 hover:text-white hover:bg-white/5"}`,children:[(0,r.jsx)("div",{className:`w-8 h-8 rounded-lg flex items-center justify-center ${E===e.id?`bg-gradient-to-r ${e.color}`:"bg-white/10"}`,children:(0,r.jsx)(e.icon,{className:"w-4 h-4"})}),(0,r.jsx)("span",{className:"font-medium",children:e.label})]},e.id))})]})}),(0,r.jsx)("div",{className:"lg:col-span-3",children:(0,r.jsxs)(a.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"space-y-6",children:["profile"===E&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center",children:(0,r.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Profile Information"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Manage your personal details and bio"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"First Name"}),(0,r.jsx)("input",{type:"text",value:s.firstName||"",onChange:e=>_("firstName",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-violet-500/50 focus:bg-white/10 transition-all"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Last Name"}),(0,r.jsx)("input",{type:"text",value:s.lastName||"",onChange:e=>_("lastName",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-violet-500/50 focus:bg-white/10 transition-all"})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Email Address"}),(0,r.jsx)("input",{type:"email",value:s.email||"",onChange:e=>_("email",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-violet-500/50 focus:bg-white/10 transition-all"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Bio"}),(0,r.jsx)("textarea",{value:s.bio||"",onChange:e=>_("bio",e.target.value),rows:4,placeholder:"Tell us about yourself...",className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-violet-500/50 focus:bg-white/10 transition-all resize-none"})]})]}),"preferences"===E&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center",children:(0,r.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Content Preferences"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Customize your content creation defaults"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Word Count"}),(0,r.jsx)("input",{type:"number",min:"100",max:"10000",value:s.settings?.defaultWordCount,onChange:e=>H("defaultWordCount",parseInt(e.target.value)),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Tone"}),(0,r.jsxs)("select",{value:s.settings?.defaultTone,onChange:e=>H("defaultTone",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all",children:[(0,r.jsx)("option",{value:"professional",children:"Professional"}),(0,r.jsx)("option",{value:"casual",children:"Casual"}),(0,r.jsx)("option",{value:"authoritative",children:"Authoritative"}),(0,r.jsx)("option",{value:"conversational",children:"Conversational"}),(0,r.jsx)("option",{value:"technical",children:"Technical"}),(0,r.jsx)("option",{value:"friendly",children:"Friendly"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("label",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-blue-500/20 rounded-xl flex items-center justify-center",children:(0,r.jsx)(N.A,{className:"w-5 h-5 text-blue-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-white font-medium",children:"Include Research by Default"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Automatically include research in content generation"})]})]}),(0,r.jsx)("input",{type:"checkbox",checked:s.settings?.includeResearchByDefault,onChange:e=>H("includeResearchByDefault",e.target.checked),className:"w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"})]}),(0,r.jsxs)("label",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"w-10 h-10 bg-emerald-500/20 rounded-xl flex items-center justify-center",children:(0,r.jsx)(k.A,{className:"w-5 h-5 text-emerald-400"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-white font-medium",children:"Auto-Save"}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:"Automatically save your work as you type"})]})]}),(0,r.jsx)("input",{type:"checkbox",checked:s.settings?.autoSaveEnabled,onChange:e=>H("autoSaveEnabled",e.target.checked),className:"w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"})]})]})]}),"notifications"===E&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center",children:(0,r.jsx)(d.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Notification Preferences"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Choose how you want to be notified"})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:[{key:"emailNotifications",title:"Email Notifications",desc:"Receive updates and alerts via email"},{key:"pushNotifications",title:"Push Notifications",desc:"Get instant browser notifications"},{key:"weeklyReports",title:"Weekly Reports",desc:"Summary of your content creation activity"},{key:"marketingEmails",title:"Marketing Emails",desc:"Product updates, tips, and special offers"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-white font-medium",children:e.title}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:e.desc})]}),(0,r.jsx)("input",{type:"checkbox",checked:s.settings?.[e.key],onChange:t=>H(e.key,t.target.checked),className:"w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"})]},e.key))})]}),"appearance"===E&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center",children:(0,r.jsx)(h.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Appearance & Theme"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Customize how the interface looks"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Theme"}),(0,r.jsxs)("select",{value:s.settings?.theme,onChange:e=>H("theme",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all",children:[(0,r.jsx)("option",{value:"dark",children:"Dark"}),(0,r.jsx)("option",{value:"light",children:"Light"}),(0,r.jsx)("option",{value:"auto",children:"Auto"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Accent Color"}),(0,r.jsxs)("select",{value:s.settings?.accentColor,onChange:e=>H("accentColor",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all",children:[(0,r.jsx)("option",{value:"blue",children:"Blue"}),(0,r.jsx)("option",{value:"purple",children:"Purple"}),(0,r.jsx)("option",{value:"green",children:"Green"}),(0,r.jsx)("option",{value:"red",children:"Red"}),(0,r.jsx)("option",{value:"orange",children:"Orange"})]})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:[{key:"animationsEnabled",title:"Enable Animations",desc:"Smooth transitions and motion effects"},{key:"compactMode",title:"Compact Mode",desc:"Reduce spacing for more content on screen"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-white font-medium",children:e.title}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:e.desc})]}),(0,r.jsx)("input",{type:"checkbox",checked:s.settings?.[e.key],onChange:t=>H(e.key,t.target.checked),className:"w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"})]},e.key))})]}),"privacy"===E&&(0,r.jsxs)("div",{className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Privacy & Security"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Control your data and privacy settings"})]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Profile Visibility"}),(0,r.jsxs)("select",{value:s.settings?.profileVisibility,onChange:e=>H("profileVisibility",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all",children:[(0,r.jsx)("option",{value:"private",children:"Private"}),(0,r.jsx)("option",{value:"team",children:"Team Only"}),(0,r.jsx)("option",{value:"public",children:"Public"})]})]}),(0,r.jsx)("div",{className:"space-y-4",children:[{key:"dataSharing",title:"Data Sharing",desc:"Share anonymized usage data to improve our services"},{key:"analyticsTracking",title:"Analytics Tracking",desc:"Help us improve the product with usage analytics"}].map(e=>(0,r.jsxs)("label",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-white font-medium",children:e.title}),(0,r.jsx)("p",{className:"text-gray-400 text-sm",children:e.desc})]}),(0,r.jsx)("input",{type:"checkbox",checked:s.settings?.[e.key],onChange:t=>H(e.key,t.target.checked),className:"w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"})]},e.key))})]})]},E)})]})})]}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-400",children:"Unable to load profile"}),(0,r.jsx)(P(),{href:"/login",children:(0,r.jsx)("button",{className:"px-4 py-2 bg-violet-600 text-white rounded-lg",children:"Sign In Again"})})]})})}},76183:(e,t,s)=>{Promise.resolve().then(s.bind(s,79025))},79025:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});var r=s(60687),i=s(82136);function a({children:e}){return(0,r.jsx)(i.SessionProvider,{children:e})}},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84199:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx","default")},92363:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},93613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>n});var r=s(37413),i=s(7339),a=s.n(i);s(61135);var l=s(84199);let n={title:"Invincible - AI Content Generation Platform",description:"The ultimate content writing SaaS platform powered by advanced AI technology"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",className:"dark",children:(0,r.jsx)("body",{className:`${a().className} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,suppressHydrationWarning:!0,children:(0,r.jsx)(l.default,{children:(0,r.jsx)("div",{className:"min-h-screen",children:e})})})})}},97051:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},98971:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},99891:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,5380,4101],()=>s(33954));module.exports=r})();