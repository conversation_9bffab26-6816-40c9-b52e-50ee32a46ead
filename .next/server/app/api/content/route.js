(()=>{var e={};e.id=6577,e.ids=[6577],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(16467),n=r(36344),a=r(31183);let o={adapter:(0,s.y)(a.z),providers:[(0,n.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"database"},callbacks:{session:async({session:e,user:t})=>(e?.user&&t&&(e.user.id=t.id),e),signIn:async({user:e,account:t,profile:r})=>!0},events:{async createUser({user:e}){try{await a.z.userSettings.findUnique({where:{userId:e.id}})?console.log(`ℹ️ User profile already exists for: ${e.email}`):(await a.z.user.update({where:{id:e.id},data:{firstName:e.name?.split(" ")[0]||"",lastName:e.name?.split(" ").slice(1).join(" ")||"",settings:{create:{}},subscription:{create:{plan:"free",status:"active"}},quotas:{create:[{quotaType:"blog_posts",totalLimit:5,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"emails",totalLimit:10,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"social_media",totalLimit:20,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"youtube_scripts",totalLimit:3,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"invincible_research",totalLimit:2,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}]}}}),console.log(`✅ Created complete user profile for: ${e.email}`))}catch(e){console.error("Error setting up user profile:",e)}}},pages:{signIn:"/auth/signin",error:"/auth/error"},debug:!1}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29748:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>x,serverHooks:()=>w,workAsyncStorage:()=>y,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>d,GET:()=>p,PUT:()=>g});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(35426),c=r(12909),l=r(31183);async function p(e){try{let t=await (0,u.getServerSession)(c.N);if(!t?.user?.id)return i.NextResponse.json({error:"Authentication required"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("type"),n=parseInt(r.get("limit")||"10"),a=parseInt(r.get("offset")||"0"),o={userId:t.user.id};s&&"all"!==s&&(o.type=s);let[p,d]=await Promise.all([l.z.content.findMany({where:o,orderBy:{createdAt:"desc"},take:n,skip:a,select:{id:!0,type:!0,title:!0,content:!0,wordCount:!0,tone:!0,status:!0,createdAt:!0,updatedAt:!0,metadata:!0}}),l.z.content.count({where:o})]),g=p.map(e=>({...e,metadata:e.metadata?JSON.parse(e.metadata):null,preview:e.content.substring(0,200)+(e.content.length>200?"...":"")}));return i.NextResponse.json({success:!0,content:g,pagination:{total:d,limit:n,offset:a,hasMore:a+n<d}})}catch(e){return console.error("Content fetch error:",e),i.NextResponse.json({error:"Failed to fetch content"},{status:500})}}async function d(e){try{let t=await (0,u.getServerSession)(c.N);if(!t?.user?.id)return i.NextResponse.json({error:"Authentication required"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("id");if(r.get("bulk"))try{let{contentIds:r}=await e.json();if(!Array.isArray(r)||0===r.length)return i.NextResponse.json({error:"Content IDs array is required for bulk delete"},{status:400});let s=await l.z.content.deleteMany({where:{id:{in:r},userId:t.user.id}});return i.NextResponse.json({success:!0,message:`${s.count} content items deleted successfully`,deletedCount:s.count})}catch(e){return i.NextResponse.json({error:"Invalid request body for bulk delete"},{status:400})}if(!s)return i.NextResponse.json({error:"Content ID is required"},{status:400});let n=await l.z.content.deleteMany({where:{id:s,userId:t.user.id}});if(0===n.count)return i.NextResponse.json({error:"Content not found or unauthorized"},{status:404});return i.NextResponse.json({success:!0,message:"Content deleted successfully"})}catch(e){return console.error("Content deletion error:",e),i.NextResponse.json({error:"Failed to delete content"},{status:500})}}async function g(e){try{let t=await (0,u.getServerSession)(c.N);if(!t?.user?.id)return i.NextResponse.json({error:"Authentication required"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("id"),n=r.get("action");if(!s)return i.NextResponse.json({error:"Content ID is required"},{status:400});if("favorite"===n){let{isFavorite:r}=await e.json(),n=await l.z.content.updateMany({where:{id:s,userId:t.user.id},data:{metadata:JSON.stringify({...JSON.parse("{}"),isFavorite:!!r,updatedAt:new Date().toISOString()})}});if(0===n.count)return i.NextResponse.json({error:"Content not found or unauthorized"},{status:404});return i.NextResponse.json({success:!0,message:r?"Content marked as favorite":"Content unfavorited"})}return i.NextResponse.json({error:"Invalid action"},{status:400})}catch(e){return console.error("Content update error:",e),i.NextResponse.json({error:"Failed to update content"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/content/route",pathname:"/api/content",filename:"route",bundlePath:"app/api/content/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/content/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:y,workUnitAsyncStorage:f,serverHooks:w}=x;function h(){return(0,o.patchFetch)({workAsyncStorage:y,workUnitAsyncStorage:f})}},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var s=r(96330);let n=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,4973],()=>r(29748));module.exports=s})();