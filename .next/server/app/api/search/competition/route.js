"use strict";(()=>{var e={};e.id=9994,e.ids=[9994],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3562:(e,t,n)=>{n.a(e,async(e,o)=>{try{n.r(t),n.d(t,{patchFetch:()=>c,routeModule:()=>u,serverHooks:()=>d,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var i=n(96559),r=n(48088),a=n(37719),s=n(49564),l=e([s]);s=(l.then?(await l)():l)[0];let u=new i.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/search/competition/route",pathname:"/api/search/competition",filename:"route",bundlePath:"app/api/search/competition/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/search/competition/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:d}=u;function c(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}o()}catch(e){o(e)}})},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},49564:(e,t,n)=>{n.a(e,async(e,o)=>{try{n.r(t),n.d(t,{POST:()=>l});var i=n(32190),r=n(99475),a=n(93356),s=e([r]);async function l(e){try{let{query:t}=await e.json();if(!t)return i.NextResponse.json({error:"Search query is required"},{status:400});console.log("\uD83D\uDD0D Searching for competition websites...");let n=new r.JU,o=new a.p,s=await o.extractKeywords(t);console.log(`🎯 Extracted keywords: ${s.trim()}`);let l=(await n.search(s.trim(),10)).items.map(e=>({title:e.title,url:e.link,snippet:e.snippet,displayLink:e.displayLink}));return console.log(`📊 Found ${l.length} competition websites`),i.NextResponse.json({success:!0,competitionSites:l,searchQuery:s.trim()})}catch(e){return console.error("Competition search error:",e),i.NextResponse.json({error:"Failed to search for competition websites"},{status:500})}}r=(s.then?(await s)():s)[0],o()}catch(e){o(e)}})},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},75263:e=>{e.exports=import("cheerio")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},93356:(e,t,n)=>{n.d(t,{p:()=>i});let o=new(n(37449)).ij("AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY");class i{constructor(e="gemini-2.5-flash-lite-preview-06-17"){this.model=o.getGenerativeModel({model:e})}updateModel(e){this.model=o.getGenerativeModel({model:e})}estimateTokenCount(e){return Math.ceil(e.length/4)}async generateContent(e,t={},n){let o=Date.now(),i=Math.random().toString(36).substr(2,9);console.log(`💎 Gemini Content Call Started`),console.log(`   📋 Call ID: ${i}`),console.log(`   🎬 Context: ${n||"General Content"}`),console.log(`   ⚙️ Model: ${this.model._model||"gemini-2.5-flash-lite-preview-06-17"}`),console.log(`   🌡️ Temperature: ${t.temperature||.7}`),console.log(`   📏 Max Tokens: ${t.maxOutputTokens||4e3}`),console.log(`   🎯 TopP: ${t.topP||.95}`),console.log(`   🔢 TopK: ${t.topK||40}`),console.log(`   📝 Prompt Length: ${e.length} chars`),t.thinkingConfig&&(console.log(`   🧠 Thinking Enabled:`),console.log(`      💭 Budget: ${t.thinkingConfig.thinkingBudget??"dynamic"}`),console.log(`      🔍 Include Thoughts: ${t.thinkingConfig.includeThoughts||!1}`));try{let r={temperature:t.temperature||.7,maxOutputTokens:t.maxOutputTokens||4e3,topP:t.topP||.95,topK:t.topK||40};t.thinkingConfig&&(r.thinkingConfig={},void 0!==t.thinkingConfig.thinkingBudget&&(r.thinkingConfig.thinkingBudget=t.thinkingConfig.thinkingBudget),void 0!==t.thinkingConfig.includeThoughts&&(r.thinkingConfig.includeThoughts=t.thinkingConfig.includeThoughts)),console.log(`   📤 Sending request to Gemini...`);let a=await this.model.generateContent({contents:[{role:"user",parts:[{text:e}]}],generationConfig:r}),s=await a.response,l=s.text(),c=[],u=0;if(t.thinkingConfig?.includeThoughts&&s.candidates?.[0]?.content?.parts)for(let e of s.candidates[0].content.parts)e.thought&&e.text&&c.push(e.text);s.usageMetadata?.thoughtsTokenCount&&(u=s.usageMetadata.thoughtsTokenCount);let g=this.estimateTokenCount(e),h=this.estimateTokenCount(l),d=Date.now()-o;console.log(`   ✅ Gemini Content Complete`),console.log(`   ⏱️ Duration: ${d}ms`),console.log(`   📊 Input Tokens: ${g} (estimated)`),console.log(`   📊 Output Tokens: ${h} (estimated)`),u>0&&(console.log(`   🧠 Thinking Tokens: ${u}`),console.log(`   💭 Thoughts Generated: ${c.length}`)),console.log(`   📄 Response Length: ${l.length} chars`);let p=4e-7*u;return console.log(`   💰 Estimated Cost: $${(1e-7*g+4e-7*h+p).toFixed(6)} (Flash-Lite 2025)`),n?.includes("YouTube")&&(console.log(`   🎬 YouTube Content Success - Call ${i}`),console.log(`   📺 Step: ${n}`)),{response:l,inputTokens:g,outputTokens:h,thoughtsTokenCount:u>0?u:void 0,thoughts:c.length>0?c:void 0}}catch(t){let e=Date.now()-o;throw console.error(`   ❌ Gemini Content Failed`),console.error(`   ⏱️ Failed after: ${e}ms`),console.error(`   📋 Call ID: ${i}`),console.error(`   🎬 Context: ${n||"General Content"}`),console.error(`   💥 Error:`,t),n?.includes("YouTube")&&(console.error(`   🎬 YouTube Content FAILED - Call ${i}`),console.error(`   📺 Failed Step: ${n}`)),Error(`Failed to generate content with Gemini: ${t}`)}}async generateContentWithThinking(e,t=-1,n=!0,o={},i){return this.generateContent(e,{...o,thinkingConfig:{thinkingBudget:t,includeThoughts:n}},i)}async generateContentWithMaxThinking(e,t={},n){return this.generateContentWithThinking(e,24576,!0,t,n)}async generateContentWithoutThinking(e,t={},n){return this.generateContent(e,{...t,thinkingConfig:{thinkingBudget:0,includeThoughts:!1}},n)}async generateBlogPost(e,t,n,o,i,r=!0){let a=`
You are a world-class professional content writer and subject matter expert. Create a comprehensive, engaging blog post about "${e}".

CONTENT REQUIREMENTS:
- Target word count: ${t} words
- Tone: ${n}
- Format: Professional markdown with proper headings, lists, and structure
- Include compelling hook and engaging introduction
- Use narrative storytelling and real-world examples
- Include strategic call-to-action at the end
- Write as a primary authoritative source
- Use confident, authoritative language (avoid hedging)

PROFESSIONAL WRITING STANDARDS:
- Start with an attention-grabbing hook (question, statistic, or bold statement)
- Create emotional connection with readers through storytelling
- Use scannable formatting with headings, subheadings, and bullet points
- Include actionable insights and practical advice
- Incorporate relevant statistics and data points
- Use active voice and strong verbs
- Create smooth transitions between sections
- End with a powerful conclusion and clear next steps

${i?.title?`Article Title: ${i.title}
`:""}
${i?.targetKeyword?`Target Keyword: ${i.targetKeyword} (use naturally throughout the content)
`:""}
${i?.targetAudience?`Target Audience: ${i.targetAudience} (tailor content for this audience)
`:""}
${i?.competitors?`Competitors to outperform: ${i.competitors} (create content that surpasses these sources)
`:""}

${o?`Research Data to incorporate:
${o}
`:""}

CONTENT STRUCTURE:
1. Compelling Hook (question, statistic, or bold statement)
2. Introduction with context and thesis
3. Main sections with clear headings and subheadings
4. Practical examples and case studies
5. Actionable takeaways and recommendations
6. Powerful conclusion with call-to-action

Create content that not only informs but also inspires action and provides exceptional value to readers. This should be the definitive resource on this topic.
`;return(await this.generateContent(a,{temperature:.7,maxOutputTokens:8e3,...r?{thinkingConfig:{thinkingBudget:-1,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async generateEmail(e,t,n,o,i=!1){let r=`
Create a professional email for the following:

Purpose: ${e}
Target Audience: ${t}
Tone: ${n}
Key Points to Include: ${o.join(", ")}

Requirements:
- Include compelling subject line
- Professional email structure (greeting, body, closing)
- Clear call-to-action
- Appropriate tone and language for the audience
- Concise but comprehensive

Format the response as:
Subject: [Subject Line]

[Email Body]
`;return(await this.generateContent(r,{temperature:.6,maxOutputTokens:1500,...i?{thinkingConfig:{thinkingBudget:512,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async generateTweet(e,t,n=!0,o=!1){let i=`
Create an engaging Twitter/X tweet about "${e}".

Style: ${t}
Include hashtags: ${n}

Requirements:
- Maximum 280 characters
- Engaging and shareable
- Include relevant emojis if appropriate
- ${n?"Include 2-3 relevant hashtags":"No hashtags"}
- Hook the reader's attention
- Encourage engagement (likes, retweets, replies)

Create a tweet that stands out in the feed and drives engagement.
`;return(await this.generateContent(i,{temperature:.8,maxOutputTokens:500,...o?{thinkingConfig:{thinkingBudget:256,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async extractKeywords(e,t=!1){let n=`
Extract the most important keywords from this topic for Google search: "${e}"

Requirements:
- If the topic is a single word or simple phrase, use it as the main keyword
- For complex topics, extract 3-5 key terms that best represent the topic
- Focus on the main concepts and important terms
- Use words that would be effective for Google search
- Return only the keywords separated by spaces, nothing else
- Do not include common words like "the", "and", "of", etc.
- Do not add words like "meaning", "definition", "example" unless they are part of the original topic
- Focus on specific, searchable terms from the original topic

Examples:
Topic: "magistral"
Keywords: magistral

Topic: "How to build a React application with TypeScript"
Keywords: React TypeScript application build development

Topic: "artificial intelligence in healthcare"
Keywords: artificial intelligence healthcare

Return only the keywords:
`;return(await this.generateContent(n,{temperature:.1,maxOutputTokens:50,...t?{thinkingConfig:{thinkingBudget:256,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async generateYouTubeScript(e,t,n,o,i=!0){let r=`
Create a YouTube video script about "${e}".

Video Duration: ${t}
Style: ${n}
Target Audience: ${o}

Requirements:
- Include compelling hook in first 15 seconds
- Clear structure with timestamps
- Engaging storytelling throughout
- Include call-to-action for likes, subscribes, comments
- Natural speaking rhythm and flow
- Include cues for visuals/graphics where appropriate
- End with strong conclusion and next video teaser

Format:
[HOOK - 0:00-0:15]
[INTRODUCTION - 0:15-0:45]
[MAIN CONTENT - Sections with timestamps]
[CONCLUSION & CTA - Final section]

Create a script that keeps viewers engaged throughout the entire video.
`;return(await this.generateContent(r,{temperature:.7,maxOutputTokens:5e3,...i?{thinkingConfig:{thinkingBudget:2048,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async extractKeywordsFromContent(e,t=!1){let n=`
Analyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:

Content:
${e.substring(0,3e3)}

Rules:
- Extract 8-12 high-value keywords and phrases
- Focus on terms that appear frequently and seem important
- Include both single keywords and 2-3 word phrases
- Prioritize terms that would be good for SEO targeting
- Separate keywords with commas
- Don't include common words like "the", "and", "or", etc.

Return only the keywords separated by commas:
`;return(await this.generateContent(n,{temperature:.2,maxOutputTokens:200,...t?{thinkingConfig:{thinkingBudget:1024,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}static calculateEnhancedInvincibleCost(e){let t=2e3+800*e.researchSources+6e3,n=Math.ceil(e.articleWordCount/.75),o=e.withThinking?Math.ceil(.2*n):0,i=1e-7*t,r=4e-7*n,a=4e-7*o;return{estimatedInputTokens:t,estimatedOutputTokens:n,estimatedThinkingTokens:o,totalCost:i+r+a,breakdown:{inputCost:i,outputCost:r,thinkingCost:a}}}}},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),o=t.X(0,[7719,580,7449,4612,6311],()=>n(3562));module.exports=o})();