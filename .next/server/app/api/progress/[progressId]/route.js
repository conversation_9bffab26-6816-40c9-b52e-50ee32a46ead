(()=>{var e={};e.id=4288,e.ids=[4288],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26734:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>u,serverHooks:()=>l,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};r.r(s),r.d(s,{GET:()=>p});var n=r(96559),o=r(48088),a=r(37719),i=r(67134);async function p(e,t){let{progressId:r}=await t.params;return new Response(new ReadableStream({start(t){(0,i.xU)(r,t);let s=new TextEncoder;t.enqueue(s.encode(`data: ${JSON.stringify({type:"connected",progressId:r})}

`)),e.signal.addEventListener("abort",()=>{(0,i.iR)(r),t.close()})}}),{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"}})}let u=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/progress/[progressId]/route",pathname:"/api/progress/[progressId]",filename:"route",bundlePath:"app/api/progress/[progressId]/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/progress/[progressId]/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:l}=u;function g(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67134:(e,t,r)=>{"use strict";r.d(t,{Rs:()=>i,iR:()=>o,xU:()=>n});let s=new Map;function n(e,t){s.set(e,t)}function o(e){s.delete(e)}function a(e,t){let r=s.get(e);if(r){let n=new TextEncoder;try{r.enqueue(n.encode(`data: ${JSON.stringify(t)}

`))}catch(t){s.delete(e)}}}function i(e){return{updateProgress:(t,r,s)=>{a(e,{progress:t,message:r,status:s||"processing",timestamp:new Date().toISOString()})},complete:t=>{a(e,{progress:100,message:t,status:"completed",timestamp:new Date().toISOString()}),o(e)},error:(t,r)=>{a(e,{progress:100,message:t,status:"error",error:r?.toString(),timestamp:new Date().toISOString()}),o(e)}}}},78335:()=>{},96487:()=>{},96559:(e,t,r)=>{"use strict";e.exports=r(44870)}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719],()=>r(26734));module.exports=s})();