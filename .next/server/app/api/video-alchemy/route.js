(()=>{var e={};e.id=941,e.ids=[941],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9956:e=>{"use strict";e.exports=require("undici")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37519:(e,t,n)=>{"use strict";n.r(t),n.d(t,{patchFetch:()=>A,routeModule:()=>E,serverHooks:()=>I,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>S});var r={};n.r(r),n.d(r,{POST:()=>y});var s=n(96559),i=n(48088),o=n(37719),a=n(32190),c=n(19854),l=n(12909),u=n(9865),d=n(37449),p=n(31183);let h=new d.ij(process.env.GOOGLE_API_KEY||"").getGenerativeModel({model:"gemini-2.5-flash-lite-preview-06-17"}),g={english:{greeting:"Hello readers!",transition:"Moving on to",conclusion:"In conclusion"},hindi:{greeting:"नमस्ते पाठकों!",transition:"आगे बढ़ते हुए",conclusion:"निष्कर्ष में"},french:{greeting:"Bonjour lecteurs!",transition:"En continuant avec",conclusion:"En conclusion"}},m={professional:"Write in a formal, authoritative tone suitable for business or academic contexts.",conversational:"Write in a friendly, engaging tone as if having a conversation with the reader.",casual:"Write in a relaxed, informal tone with a personal touch.",educational:"Write in an informative, clear tone focused on teaching and explaining concepts.",storytelling:"Write in a narrative style, using stories and examples to illustrate points.",technical:"Write in a detailed, precise tone with technical accuracy and depth."};async function f(e){try{let t=process.env.TAVILY_API_KEY;if(!t)return console.warn("Tavily API key not found, skipping search"),null;let n=await fetch("https://api.tavily.com/search",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({api_key:t,query:e,search_depth:"advanced",include_answer:!0,include_raw_content:!0,max_results:5})});if(!n.ok)return console.error("Tavily search failed:",n.statusText),null;return await n.json()}catch(e){return console.error("Tavily search error:",e),null}}async function w(e,t,n,r,s){console.log("\uD83E\uDDE0 GEMINI ANALYSIS: Starting intelligent video content analysis..."),console.log(`📊 Processing ${e.length} video transcript(s) for topic: "${t}"`);let i=`You are an expert content analyst using advanced reasoning to analyze video transcripts. Use step-by-step thinking to identify content gaps and generate intelligent research queries.

**ANALYSIS CONTEXT:**
Topic: "${t}"
${n?`User's Specific Focus: "${n}"`:""}
${r?`Custom Instructions: "${r}"`:""}
Language: ${s}

**VIDEO TRANSCRIPTS TO ANALYZE:**
${e.join("\n\n---\n\n")}

**THINKING PROCESS - ANALYZE STEP BY STEP:**

1. **CONTENT COMPREHENSION ANALYSIS:**
   - What are the main topics covered in these video transcripts?
   - What is the overall quality and depth of information provided?
   - Are there any conflicting or unclear statements?

2. **GAP IDENTIFICATION REASONING:**
   - What important information is missing that users would expect?
   - Which claims need verification or additional context?
   - What technical terms or concepts need better explanation?
   - Where are there logical jumps or insufficient explanations?

3. **USER REQUIREMENT ALIGNMENT:**
   - How well do the videos address the user's specific topic focus?
   - What additional information would make the content more valuable?
   - Are there user-specific requirements not covered by the videos?

4. **STRATEGIC RESEARCH QUERY GENERATION:**
   - What specific data, statistics, or facts would strengthen the content?
   - Which expert opinions or authoritative sources are needed?
   - What current information (2024-2025) would make the content more relevant?
   - Which comparison data or case studies would add value?

**RESEARCH QUERY REQUIREMENTS:**
- Generate 8-12 specific, actionable search queries
- Focus on filling identified gaps with concrete information
- Target authoritative sources and current data
- Include verification queries for claims made in videos
- Consider user's specific requirements and custom instructions

**THINKING OUTPUT FORMAT:**
First, show your step-by-step analysis and reasoning process.
Then provide the final queries in this JSON format:

{
  "analysis": {
    "mainTopics": ["topic1", "topic2", ...],
    "contentQuality": "assessment of video content quality",
    "identifiedGaps": ["gap1", "gap2", ...],
    "missingInformation": ["info1", "info2", ...],
    "userAlignmentScore": "1-10 rating of how well videos match user needs"
  },
  "queries": [
    "specific search query 1",
    "specific search query 2",
    ...
  ]
}

Use your advanced reasoning to create queries that will result in a superior article that provides more value than just watching the videos.`;try{console.log("\uD83D\uDD0D GEMINI ANALYSIS: Sending analysis request with thinking model...");let e=Date.now(),t=(await h.generateContent({contents:[{role:"user",parts:[{text:i}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:8e3}})).response.text(),n=Date.now()-e;console.log(`⏱️  GEMINI ANALYSIS: Completed in ${n}ms`),console.log("\uD83D\uDCCB GEMINI ANALYSIS: Processing response...");let r=t.match(/\{[\s\S]*\}/);if(r){let e=JSON.parse(r[0]);return console.log("✅ GEMINI ANALYSIS COMPLETE:"),console.log(`   📊 Main Topics: ${e.analysis?.mainTopics?.join(", ")||"Not specified"}`),console.log(`   📈 Content Quality: ${e.analysis?.contentQuality||"Not assessed"}`),console.log(`   🔍 Identified Gaps: ${e.analysis?.identifiedGaps?.length||0} gaps found`),console.log(`   📝 Missing Information: ${e.analysis?.missingInformation?.length||0} items identified`),console.log(`   🎯 User Alignment Score: ${e.analysis?.userAlignmentScore||"Not rated"}/10`),console.log(`   🔎 Generated Queries: ${e.queries?.length||0} intelligent research queries`),e.queries&&e.queries.length>0&&(console.log("\uD83D\uDCCB SAMPLE RESEARCH QUERIES:"),e.queries.slice(0,3).forEach((e,t)=>{console.log(`   ${t+1}. ${e}`)})),Array.isArray(e.queries)?e.queries.slice(0,12):[]}console.log("⚠️  GEMINI ANALYSIS: No JSON found in response, using fallback extraction...");let s=t.split("\n").filter(e=>e.includes('"')&&e.length>20).map(e=>e.replace(/[",\[\]]/g,"").trim()).filter(e=>e.length>10).slice(0,10);return console.log(`🔄 GEMINI ANALYSIS: Extracted ${s.length} queries using fallback method`),s}catch(n){console.error("❌ GEMINI ANALYSIS ERROR:",n),console.log("\uD83D\uDD04 GEMINI ANALYSIS: Using intelligent fallback queries...");let e=[`${t} latest statistics 2024 2025`,`${t} expert opinions authoritative sources`,`${t} case studies real examples`,`${t} benefits risks advantages disadvantages`,`${t} how to implement step by step`,`${t} common mistakes to avoid`,`${t} cost pricing comparison 2024`,`${t} best practices industry standards`,`${t} future trends predictions 2025`,`${t} user reviews testimonials feedback`];return console.log(`✅ GEMINI ANALYSIS: Generated ${e.length} fallback queries`),e}}async function y(e){try{let t=await (0,c.getServerSession)(l.N);if(!t?.user?.email)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{topic:n,videoLinks:r,tone:s,wordCount:i,customInstructions:o,language:d}=await e.json();if(!n||!r||0===r.length)return a.NextResponse.json({error:"Missing required fields"},{status:400});let y=new u.R,E=[],v=[],S=[];for(let e of(console.log("\uD83C\uDFA5 Step 1: Extracting captions from videos..."),r))try{let t=e.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/),n=t?t[1]:null;if(!n){S.push(e);continue}let r=await y.getVideoMetadata(n);r&&v.push(r.title);let s=await y.extractCaptions(n,"hindi"===d?"hi":"french"===d?"fr":"en");if(s&&s.length>0){let t=s.map(e=>e.text).join(" ");E.push(`Video: "${r?.title||e}"
Transcript: ${t}`)}else S.push(e)}catch(t){console.error(`Error processing video ${e}:`,t),S.push(e)}if(0===E.length)return a.NextResponse.json({error:"Failed to extract captions from any of the provided videos",failedVideos:S},{status:400});console.log(`✅ Extracted captions from ${E.length} videos`),console.log("\uD83D\uDD0D Step 2: Analyzing content and generating research queries...");let I=await w(E,n,n,o||"",d);console.log(`📋 Generated ${I.length} research queries`),console.log("\uD83C\uDF10 Step 3: Researching additional information...");let A=[];for(let e of I){console.log(`  🔎 Searching: ${e}`);let t=await f(e);t&&A.push({query:e,answer:t.answer||"",sources:t.results?.slice(0,3).map(e=>({title:e.title,content:e.content,url:e.url}))||[]})}console.log(`✅ Completed ${A.length} research searches`);let x=E.join("\n\n---\n\n"),T=A.length>0?"\n\n## Additional Research Data:\n"+A.map(e=>`**Query**: ${e.query}
**Answer**: ${e.answer}
**Sources**:
${e.sources.map(e=>`- ${e.title}: ${e.content.substring(0,200)}...`).join("\n")}`).join("\n\n"):"";g[d]||g.english;let N=m[s]||m.conversational,R=`You are an elite content writer with expertise in creating viral, SEO-optimized articles that rank #1 on Google and provide exceptional value to readers.

🎯 CRITICAL REQUIREMENTS:
1. **EXACT WORD COUNT**: Write precisely ${i} words (count internally, never show counts in output)
2. **LANGUAGE**: Write in ${"hindi"===d?"Hindi (हिंदी)":"french"===d?"French (Fran\xe7ais)":"English"}
3. **TONE**: ${N}

📊 CONTENT OPTIMIZATION FRAMEWORK:

### SEO Mastery (Search Engine Optimization):
- **Keyword Strategy**: Use "${n}" naturally 3-5 times per 1000 words
- **Semantic SEO**: Include related terms, synonyms, and LSI keywords
- **Title Optimization**: Create magnetic titles with power words and emotional triggers
- **Meta Description**: First 160 characters must compel clicks
- **Header Structure**: Use H2s every 150-200 words, H3s for sub-points
- **Internal Linking**: Reference related concepts naturally
- **Readability**: Flesch Reading Ease score 60-70 (8th-grade level)

### AEO Excellence (Answer Engine Optimization):
- **Direct Answers**: Answer key questions in the first 2-3 sentences of relevant sections
- **Featured Snippets**: Structure content for paragraph, list, and table snippets
- **FAQ Integration**: Include commonly asked questions with concise answers
- **Definition Boxes**: Define key terms clearly in standalone sentences
- **Voice Search**: Use conversational, question-based subheadings

### GEO Innovation (Generative Engine Optimization):
- **AI Comprehension**: Structure content with clear topic sentences
- **Semantic Clarity**: Use unambiguous language that AI can easily parse
- **Factual Accuracy**: Ensure all claims are verifiable and well-supported
- **Logical Flow**: Create content that follows a clear narrative arc
- **Entity Recognition**: Mention relevant people, places, and things clearly

### Human Writing Patterns:
- **Varied Sentence Structure**: Mix short (5-10 words) and long (20-25 words) sentences
- **Emotional Engagement**: Use storytelling, anecdotes, and relatable examples
- **Active Voice**: 80% active voice for energy and clarity
- **Transition Mastery**: Smooth flow between paragraphs with transition phrases
- **Conversational Elements**: Include rhetorical questions, direct address ("you")
- **Micro-Stories**: Add brief examples or case studies to illustrate points

### Content Architecture:
1. **Hook Opening** (10% of word count):
   - Start with a compelling statistic, question, or bold statement
   - Preview the value readers will gain
   - Create urgency or curiosity

2. **Main Content** (75% of word count):
   - Logical progression from basic to advanced concepts
   - Use the PAS formula (Problem-Agitate-Solution) where relevant
   - Include data, statistics, and expert insights
   - Add practical examples and actionable tips
   - Break complex ideas into digestible chunks
   - **CREATE DATA TABLES** when presenting comparative data, statistics, or multi-factor information
   - Format tables using proper markdown (|Column1|Column2| with header row and separator)

3. **Power Conclusion** (15% of word count):
   - Summarize key takeaways
   - Provide clear next steps
   - End with a thought-provoking statement or call-to-action

### Table Generation Requirements:
- Include at least 1-2 data tables when appropriate for the content
- Use tables for comparing options, features, statistics, or numerical data
- Format using proper markdown tables with headers
- Keep tables responsive (4-5 columns maximum)
- Include a brief description or insight above or below each table

### Engagement Optimization:
- **Bucket Brigades**: Use phrases like "Here's the deal:", "But wait:", "The truth is:"
- **Power Words**: Include emotional triggers (amazing, proven, essential, revolutionary)
- **Social Proof**: Reference studies, expert opinions, or common experiences
- **FOMO Creation**: Highlight what readers might miss or lose
- **Value Stacking**: Continuously emphasize benefits and outcomes

${o?`### Custom Requirements:
${o}`:""}

🚫 NEVER DO:
- Include word counts, placeholders, or meta-commentary
- Use generic headings like "Section 1", "Part A"
- Write walls of text (max 4 sentences per paragraph)
- Use jargon without explanation
- Make unsupported claims
- Forget the human element`,O=`Transform the following video transcripts and research data into an exceptional ${i}-word article about "${n}".

📹 VIDEO TRANSCRIPTS:
${x}

🔍 SUPPLEMENTARY RESEARCH:
${T}

📝 YOUR MISSION:
Create a comprehensive, engaging article that:
1. Synthesizes all video content into a cohesive narrative
2. Fills knowledge gaps with the research data provided
3. Adds value beyond what's in the videos
4. Maintains consistent tone and style throughout
5. Optimizes for both human readers and search engines
6. Includes data tables when presenting comparative information or statistics

🎯 WORD COUNT DISTRIBUTION:
- Introduction: ${Math.ceil(.1*i)} words (compelling hook + preview)
- Main Body: ${Math.ceil(.75*i)} words (core content + examples)  
- Conclusion: ${Math.ceil(.15*i)} words (summary + call-to-action)
- TOTAL: Exactly ${i} words

💡 CONTENT ENHANCEMENT TIPS:
- Use video quotes as social proof
- Transform video examples into written case studies
- Expand on concepts briefly mentioned in videos
- Add context and background information
- Include practical applications and actionable advice
- Create smooth transitions between video topics

Remember: You're not just transcribing videos - you're creating a superior article that provides more value than watching the videos themselves.

Start writing now, ensuring exactly ${i} words of exceptional content.`;console.log(`🚀 Step 4: Generating ${i}-word article with enhanced prompts...`);let $=(await h.generateContent({contents:[{role:"user",parts:[{text:R+"\n\n"+O}]}]})).response.text(),b=$.split(/\s+/).filter(e=>e.length>0).length,C=Math.ceil(.25*i),M=i-C,q=i+C;console.log(`✅ Generated article with ${b} words (target: ${i})`),console.log(`   Tolerance: \xb1${C} words (${M}-${q})`),console.log(`   Accuracy: ${Math.abs(b-i)} words off (${((1-Math.abs(b-i)/i)*100).toFixed(1)}% accurate)`);let U=Math.ceil(.15*i),k=Math.ceil(.7*i),P=Math.ceil(.15*i);if(b<M||b>q){console.log("Word count outside tolerance. Attempting retry...");let e=`🚨 CRITICAL WORD COUNT ERROR 🚨
      
The previous response was ${b} words but must be EXACTLY ${i} words.

🎯 MANDATORY: Write exactly ${i} words. Count every single word as you write.

${b<M?`❌ PREVIOUS ATTEMPT TOO SHORT: ${b} words (need ${i-b} more words)
✅ SOLUTION: Write MORE content - add examples, detailed explanations, case studies, or additional sections`:""}

${b>q?`❌ PREVIOUS ATTEMPT TOO LONG: ${b} words (need to remove ${b-i} words)  
✅ SOLUTION: Write LESS content - be more concise, remove unnecessary details, shorter paragraphs`:""}

${R}

🎯 WORD COUNT TARGET: ${i} WORDS EXACTLY

📊 REQUIRED STRUCTURE:
- INTRODUCTION: ${U} words 
- MAIN BODY: ${k} words
- CONCLUSION: ${P} words
- TOTAL: ${i} words

📝 SPECIFIC CORRECTIONS NEEDED:
${b<M?`- ADD ${i-b} MORE WORDS by expanding sections with:
  * More detailed explanations
  * Additional examples from the videos
  * Practical applications and tips
  * Case studies and real-world scenarios
  * Historical context or background
  * Step-by-step processes
  * Quotes and data from the transcripts`:`- REMOVE ${b-i} WORDS by:
  * Being more concise in explanations
  * Removing redundant information
  * Shorter paragraphs
  * Eliminating unnecessary details`}

⚠️ CRITICAL FORMATTING RULES:
- NO "(Word Count: X)" anywhere in output
- Use DESCRIPTIVE headings, not "Section 1", "Section 2"
- Keep paragraphs SHORT (2-4 sentences max)
- Use SIMPLE, easy-to-read language
- Include transition words for better flow
- Proper markdown: # for title, ## for descriptive sections, ### for subsections

${O}

🚨 FINAL WARNING: This is your last chance. Requirements:
- Write exactly ${i} words (count internally)
- Use DESCRIPTIVE headings (NOT "Section 1", "Section 2")
- SHORT paragraphs (2-4 sentences) with SIMPLE language
- NO "(Word Count: X)" anywhere in output
- Easy-to-read, SEO-optimized, publication-ready article`;try{let t=(await h.generateContent({contents:[{role:"user",parts:[{text:e}]}],generationConfig:{temperature:.7,topK:40,topP:.95}})).response.text(),o=t.split(/\s+/).filter(e=>e.length>0).length;if(console.log(`Retry word count: ${o}`),Math.abs(o-i)<Math.abs(b-i))return console.log("Using retry result as it has better word count"),a.NextResponse.json({success:!0,article:{id:"temp-"+Date.now(),title:n,content:t,metadata:{processedVideos:r.length-S.length,failedVideos:S,videoTitles:v,language:d,tone:s,wordCount:o,targetWordCount:i,wordCountStatus:"retry_improved"}}})}catch(e){console.error("Retry failed:",e)}}let L=await p.z.content.create({data:{title:n,content:$,type:"video_alchemy",userId:t.user.id,metadata:JSON.stringify({videoLinks:r,videoTitles:v,failedVideos:S,tone:s,wordCount:i,language:d,customInstructions:o||null,generatedAt:new Date().toISOString()}),wordCount:$.split(/\s+/).filter(e=>e.length>0).length,tone:s,language:"hindi"===d?"hi":"french"===d?"fr":"en"}}),W=$.split(/\s+/).filter(e=>e.length>0).length,G=Math.abs(W-i),D=G<=Math.ceil(.25*i)?"within_tolerance":"outside_tolerance";return a.NextResponse.json({success:!0,article:{id:L.id,title:n,content:$,metadata:{processedVideos:r.length-S.length,failedVideos:S,videoTitles:v,language:d,tone:s,wordCount:W,targetWordCount:i,wordCountAccuracy:G,wordCountStatus:D}}})}catch(e){return console.error("VideoAlchemy generation error:",e),a.NextResponse.json({error:e instanceof Error?e.message:"Failed to generate article"},{status:500})}}let E=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/video-alchemy/route",pathname:"/api/video-alchemy",filename:"route",bundlePath:"app/api/video-alchemy/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/video-alchemy/route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:v,workUnitAsyncStorage:S,serverHooks:I}=E;function A(){return(0,o.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:S})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79748:e=>{"use strict";e.exports=require("fs/promises")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},94175:e=>{"use strict";e.exports=require("stream/web")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[7719,580,4973,7449,4612,1201,5901],()=>n(37519));module.exports=r})();