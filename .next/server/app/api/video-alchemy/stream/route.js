"use strict";(()=>{var e={};e.id=3784,e.ids=[3784],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9956:e=>{e.exports=require("undici")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},21820:e=>{e.exports=require("os")},25854:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.r(t),n.d(t,{POST:()=>m});var s=n(32190),o=n(19854),a=n(12909),i=n(9865),l=n(37449),c=n(31183),u=n(99475),d=e([u]);u=(d.then?(await d)():d)[0];let y=new l.ij(process.env.GOOGLE_API_KEY||"").getGenerativeModel({model:"gemini-2.5-flash-lite-preview-06-17"}),w={english:{greeting:"Hello readers!",transition:"Moving on to",conclusion:"In conclusion"},hindi:{greeting:"नमस्ते पाठकों!",transition:"आगे बढ़ते हुए",conclusion:"निष्कर्ष में"},french:{greeting:"Bonjour lecteurs!",transition:"En continuant avec",conclusion:"En conclusion"}},f={professional:"Write in a formal, authoritative tone suitable for business or academic contexts.",conversational:"Write in a friendly, engaging tone as if having a conversation with the reader.",casual:"Write in a relaxed, informal tone with a personal touch.",educational:"Write in an informative, clear tone focused on teaching and explaining concepts.",storytelling:"Write in a narrative style, using stories and examples to illustrate points.",technical:"Write in a detailed, precise tone with technical accuracy and depth."},E=[{stage:"initialization",title:"The Journey Begins",description:"Our magical Video Alchemy laboratory is warming up...",animation:"laboratory-setup",duration:3e3},{stage:"video-processing",title:"Extracting Video Essence",description:"Magical extractors are capturing the essence from your videos...",animation:"video-extraction",duration:15e3},{stage:"content-analysis",title:"The AI Oracle Awakens",description:"Our AI oracle is analyzing your content and discovering hidden knowledge gaps...",animation:"ai-analysis",duration:1e4},{stage:"research-gathering",title:"Knowledge Hunters Dispatched",description:"Digital knowledge hunters are scouring the web for missing information...",animation:"research-hunt",duration:2e4},{stage:"article-generation",title:"The Grand Synthesis",description:"Master writers are weaving your perfect article with golden threads of wisdom...",animation:"article-creation",duration:25e3},{stage:"quality-assurance",title:"The Final Polish",description:"Quality guardians are adding the final touches to your masterpiece...",animation:"final-polish",duration:5e3},{stage:"completion",title:"The Magic is Complete!",description:"Your superior article is ready to captivate readers worldwide!",animation:"completion-celebration",duration:3e3}];async function g(e){try{console.log(`🔍 Searching with enhanced Tavily (fallback keys): ${e}`);let t=await (0,u.uG)(e,5);if(!t||0===t.length)return console.warn("No results found for query:",e),null;let n={answer:t[0]?.snippet||"Information found from search results",results:t.map(e=>({title:e.title,content:e.snippet||e.content,url:e.url}))};return console.log(`✅ Enhanced Tavily search successful: ${t.length} results`),n}catch(e){return console.error("Enhanced Tavily search error:",e),null}}async function p(e,t,n,r,s){console.log("\uD83E\uDDE0 GEMINI ANALYSIS (STREAM): Starting intelligent video content analysis..."),console.log(`📊 Processing ${e.length} video transcript(s) for topic: "${t}"`);let o=`You are an expert content analyst using advanced reasoning to analyze video transcripts. Use step-by-step thinking to identify content gaps and generate intelligent research queries.

**ANALYSIS CONTEXT:**
Topic: "${t}"
${n?`User's Specific Focus: "${n}"`:""}
${r?`Custom Instructions: "${r}"`:""}
Language: ${s}

**VIDEO TRANSCRIPTS TO ANALYZE:**
${e.join("\n\n---\n\n")}

**THINKING PROCESS - ANALYZE STEP BY STEP:**

1. **CONTENT COMPREHENSION ANALYSIS:**
   - What are the main topics covered in these video transcripts?
   - What is the overall quality and depth of information provided?
   - Are there any conflicting or unclear statements?

2. **GAP IDENTIFICATION REASONING:**
   - What important information is missing that users would expect?
   - Which claims need verification or additional context?
   - What technical terms or concepts need better explanation?
   - Where are there logical jumps or insufficient explanations?

3. **USER REQUIREMENT ALIGNMENT:**
   - How well do the videos address the user's specific topic focus?
   - What additional information would make the content more valuable?
   - Are there user-specific requirements not covered by the videos?

4. **STRATEGIC RESEARCH QUERY GENERATION:**
   - What specific data, statistics, or facts would strengthen the content?
   - Which expert opinions or authoritative sources are needed?
   - What current information (2024-2025) would make the content more relevant?
   - Which comparison data or case studies would add value?

**RESEARCH QUERY REQUIREMENTS:**
- Generate 8-12 specific, actionable search queries
- Focus on filling identified gaps with concrete information
- Target authoritative sources and current data
- Include verification queries for claims made in videos
- Consider user's specific requirements and custom instructions

**THINKING OUTPUT FORMAT:**
First, show your step-by-step analysis and reasoning process.
Then provide the final queries in this JSON format:

{
  "analysis": {
    "mainTopics": ["topic1", "topic2", ...],
    "contentQuality": "assessment of video content quality",
    "identifiedGaps": ["gap1", "gap2", ...],
    "missingInformation": ["info1", "info2", ...],
    "userAlignmentScore": "1-10 rating of how well videos match user needs"
  },
  "queries": [
    "specific search query 1",
    "specific search query 2",
    ...
  ]
}

Use your advanced reasoning to create queries that will result in a superior article that provides more value than just watching the videos.`;try{console.log("\uD83D\uDD0D GEMINI ANALYSIS (STREAM): Sending analysis request with thinking model...");let e=Date.now(),t=(await y.generateContent({contents:[{role:"user",parts:[{text:o}]}],generationConfig:{temperature:.7,topK:40,topP:.95,maxOutputTokens:8e3}})).response.text(),n=Date.now()-e;console.log(`⏱️  GEMINI ANALYSIS (STREAM): Completed in ${n}ms`),console.log("\uD83D\uDCCB GEMINI ANALYSIS (STREAM): Processing response...");let r=t.match(/\{[\s\S]*\}/);if(r){let e=JSON.parse(r[0]);return console.log("✅ GEMINI ANALYSIS (STREAM) COMPLETE:"),console.log(`   📊 Main Topics: ${e.analysis?.mainTopics?.join(", ")||"Not specified"}`),console.log(`   📈 Content Quality: ${e.analysis?.contentQuality||"Not assessed"}`),console.log(`   🔍 Identified Gaps: ${e.analysis?.identifiedGaps?.length||0} gaps found`),console.log(`   📝 Missing Information: ${e.analysis?.missingInformation?.length||0} items identified`),console.log(`   🎯 User Alignment Score: ${e.analysis?.userAlignmentScore||"Not rated"}/10`),console.log(`   🔎 Generated Queries: ${e.queries?.length||0} intelligent research queries`),e.queries&&e.queries.length>0&&(console.log("\uD83D\uDCCB SAMPLE RESEARCH QUERIES (STREAM):"),e.queries.slice(0,3).forEach((e,t)=>{console.log(`   ${t+1}. ${e}`)})),Array.isArray(e.queries)?e.queries.slice(0,12):[]}console.log("⚠️  GEMINI ANALYSIS (STREAM): No JSON found in response, using fallback extraction...");let s=t.split("\n").filter(e=>e.includes('"')&&e.length>20).map(e=>e.replace(/[",\[\]]/g,"").trim()).filter(e=>e.length>10).slice(0,10);return console.log(`🔄 GEMINI ANALYSIS (STREAM): Extracted ${s.length} queries using fallback method`),s}catch(n){console.error("❌ GEMINI ANALYSIS (STREAM) ERROR:",n),console.log("\uD83D\uDD04 GEMINI ANALYSIS (STREAM): Using intelligent fallback queries...");let e=[`${t} latest statistics 2024 2025`,`${t} expert opinions authoritative sources`,`${t} case studies real examples`,`${t} benefits risks advantages disadvantages`,`${t} how to implement step by step`,`${t} common mistakes to avoid`,`${t} cost pricing comparison 2024`,`${t} best practices industry standards`,`${t} future trends predictions 2025`,`${t} user reviews testimonials feedback`];return console.log(`✅ GEMINI ANALYSIS (STREAM): Generated ${e.length} fallback queries`),e}}function h(e,t){let n=`data: ${JSON.stringify(t)}

`;e.enqueue(new TextEncoder().encode(n))}async function m(e){let t=await (0,o.getServerSession)(a.N);if(!t?.user?.email)return s.NextResponse.json({error:"Unauthorized"},{status:401});let{topic:n,videoLinks:r,tone:l,wordCount:u,customInstructions:d,language:m}=await e.json();if(!n||!r||0===r.length)return s.NextResponse.json({error:"Missing required fields"},{status:400});let A=new ReadableStream({start(e){(async()=>{try{h(e,{stage:"initialization",progress:0,message:"Initializing Video Alchemy...",storyData:E[0]}),await new Promise(e=>setTimeout(e,2e3)),h(e,{stage:"video-processing",progress:10,message:"Extracting captions from videos...",storyData:E[1]});let s=new i.R,o=[],a=[],A=[];for(let t=0;t<r.length;t++){let n=r[t];h(e,{stage:"video-processing",progress:10+15*t/r.length,message:`Processing video ${t+1} of ${r.length}...`,storyData:E[1]});try{let e=n.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/),t=e?e[1]:null;if(!t){A.push(n);continue}let r=await s.getVideoMetadata(t);r&&a.push(r.title);let i=await s.extractCaptions(t,"hindi"===m?"hi":"french"===m?"fr":"en");if(i&&i.length>0){let e=i.map(e=>e.text).join(" ");o.push(`Video: "${r?.title||n}"
Transcript: ${e}`)}else A.push(n)}catch(e){console.error(`Error processing video ${n}:`,e),A.push(n)}}if(0===o.length){h(e,{stage:"error",progress:0,message:"Failed to extract captions from any videos",error:"No captions extracted"}),e.close();return}h(e,{stage:"content-analysis",progress:30,message:"AI oracle analyzing content for knowledge gaps...",storyData:E[2]}),console.log("\uD83E\uDDE0 STARTING GEMINI ANALYSIS PHASE...");let S=await p(o,n,n,d||"",m),v=`AI oracle discovered ${S.length} knowledge gaps to investigate`;console.log(`✅ GEMINI ANALYSIS COMPLETE: ${v}`),h(e,{stage:"content-analysis",progress:40,message:v,storyData:E[2],queries:S.length>0?S.slice(0,5):[],analysisDetails:{totalQueries:S.length,analysisComplete:!0,intelligentAnalysis:!0}}),h(e,{stage:"research-gathering",progress:45,message:"Dispatching knowledge hunters...",storyData:E[3]}),console.log(`🌐 STARTING RESEARCH PHASE: Processing ${S.length} intelligent queries...`);let I=[];for(let t=0;t<S.length;t++){let n=S[t],r=45+20*t/S.length;console.log(`🔍 RESEARCH QUERY ${t+1}/${S.length}: ${n}`),h(e,{stage:"research-gathering",progress:r,message:`Researching: ${n.substring(0,50)}...`,storyData:E[3],currentQuery:n,queryProgress:`${t+1}/${S.length}`});let s=await g(n);if(s){let e={query:n,answer:s.answer||"",sources:s.results?.slice(0,3).map(e=>({title:e.title,content:e.content,url:e.url}))||[]};I.push(e),console.log(`✅ RESEARCH SUCCESS: Query ${t+1} - Found ${e.sources.length} sources`)}else console.log(`⚠️  RESEARCH SKIPPED: Query ${t+1} - No results found`)}console.log(`✅ RESEARCH PHASE COMPLETE: Successfully researched ${I.length}/${S.length} queries`),h(e,{stage:"research-gathering",progress:65,message:`Research complete! Gathered ${I.length} knowledge sources`,storyData:E[3],researchSummary:{totalQueries:S.length,successfulQueries:I.length,totalSources:I.reduce((e,t)=>e+t.sources.length,0),researchComplete:!0}}),h(e,{stage:"article-generation",progress:70,message:"Master writers weaving your article...",storyData:E[4]}),console.log("\uD83D\uDE80 STARTING ARTICLE GENERATION PHASE..."),console.log(`📝 Combining ${o.length} video transcripts + ${I.length} research sources`);let T=o.join("\n\n---\n\n"),R=I.length>0?"\n\n## Additional Research Data:\n"+I.map(e=>`**Query**: ${e.query}
**Answer**: ${e.answer}
**Sources**:
${e.sources.map(e=>`- ${e.title}: ${e.content.substring(0,200)}...`).join("\n")}`).join("\n\n"):"";w[m]||w.english;let $=f[l]||f.conversational;console.log(`🎯 Generation Parameters: ${u} words, ${l} tone, ${m} language`),console.log(`📊 Content Sources: ${o.length} videos + ${I.length} research queries`);let x=`You are an elite content writer with expertise in creating viral, SEO-optimized articles that rank #1 on Google and provide exceptional value to readers.

🎯 CRITICAL REQUIREMENTS:
1. **EXACT WORD COUNT**: Write precisely ${u} words (count internally, never show counts in output)
2. **LANGUAGE**: Write in ${"hindi"===m?"Hindi (हिंदी)":"french"===m?"French (Fran\xe7ais)":"English"}
3. **TONE**: ${$}

📊 CONTENT OPTIMIZATION FRAMEWORK:

### SEO Mastery (Search Engine Optimization):
- **Keyword Strategy**: Use "${n}" naturally 3-5 times per 1000 words
- **Semantic SEO**: Include related terms, synonyms, and LSI keywords
- **Title Optimization**: Create magnetic titles with power words and emotional triggers
- **Meta Description**: First 160 characters must compel clicks
- **Header Structure**: Use H2s every 150-200 words, H3s for sub-points
- **Internal Linking**: Reference related concepts naturally
- **Readability**: Flesch Reading Ease score 60-70 (8th-grade level)

### AEO Excellence (Answer Engine Optimization):
- **Direct Answers**: Answer key questions in the first 2-3 sentences of relevant sections
- **Featured Snippets**: Structure content for paragraph, list, and table snippets
- **FAQ Integration**: Include commonly asked questions with concise answers
- **Definition Boxes**: Define key terms clearly in standalone sentences
- **Voice Search**: Use conversational, question-based subheadings

### GEO Innovation (Generative Engine Optimization):
- **AI Comprehension**: Structure content with clear topic sentences
- **Semantic Clarity**: Use unambiguous language that AI can easily parse
- **Factual Accuracy**: Ensure all claims are verifiable and well-supported
- **Logical Flow**: Create content that follows a clear narrative arc
- **Entity Recognition**: Mention relevant people, places, and things clearly

### Human Writing Patterns:
- **Varied Sentence Structure**: Mix short (5-10 words) and long (20-25 words) sentences
- **Emotional Engagement**: Use storytelling, anecdotes, and relatable examples
- **Active Voice**: 80% active voice for energy and clarity
- **Transition Mastery**: Smooth flow between paragraphs with transition phrases
- **Conversational Elements**: Include rhetorical questions, direct address ("you")
- **Micro-Stories**: Add brief examples or case studies to illustrate points

### Content Architecture:
1. **Hook Opening** (10% of word count):
   - Start with a compelling statistic, question, or bold statement
   - Preview the value readers will gain
   - Create urgency or curiosity

2. **Main Content** (75% of word count):
   - Logical progression from basic to advanced concepts
   - Use the PAS formula (Problem-Agitate-Solution) where relevant
   - Include data, statistics, and expert insights
   - Add practical examples and actionable tips
   - Break complex ideas into digestible chunks
   - **CREATE DATA TABLES** when presenting comparative data, statistics, or multi-factor information
   - Format tables using proper markdown (|Column1|Column2| with header row and separator)

3. **Power Conclusion** (15% of word count):
   - Summarize key takeaways
   - Provide clear next steps
   - End with a thought-provoking statement or call-to-action

### Table Generation Requirements:
- Include at least 1-2 data tables when appropriate for the content
- Use tables for comparing options, features, statistics, or numerical data
- Format using proper markdown tables with headers
- Keep tables responsive (4-5 columns maximum)
- Include a brief description or insight above or below each table

### Engagement Optimization:
- **Bucket Brigades**: Use phrases like "Here's the deal:", "But wait:", "The truth is:"
- **Power Words**: Include emotional triggers (amazing, proven, essential, revolutionary)
- **Social Proof**: Reference studies, expert opinions, or common experiences
- **FOMO Creation**: Highlight what readers might miss or lose
- **Value Stacking**: Continuously emphasize benefits and outcomes

${d?`### Custom Requirements:
${d}`:""}

🚫 NEVER DO:
- Include word counts, placeholders, or meta-commentary
- Use generic headings like "Section 1", "Part A"
- Write walls of text (max 4 sentences per paragraph)
- Use jargon without explanation
- Make unsupported claims
- Forget the human element`,N=`Transform the following video transcripts and research data into an exceptional ${u}-word article about "${n}".

📹 VIDEO TRANSCRIPTS:
${T}

🔍 SUPPLEMENTARY RESEARCH:
${R}

📝 YOUR MISSION:
Create a comprehensive, engaging article that:
1. Synthesizes all video content into a cohesive narrative
2. Fills knowledge gaps with the research data provided
3. Adds value beyond what's in the videos
4. Maintains consistent tone and style throughout
5. Optimizes for both human readers and search engines
6. Includes data tables when presenting comparative information or statistics

🎯 WORD COUNT DISTRIBUTION:
- Introduction: ${Math.ceil(.1*u)} words (compelling hook + preview)
- Main Body: ${Math.ceil(.75*u)} words (core content + examples)  
- Conclusion: ${Math.ceil(.15*u)} words (summary + call-to-action)
- TOTAL: Exactly ${u} words

💡 CONTENT ENHANCEMENT TIPS:
- Use video quotes as social proof
- Transform video examples into written case studies
- Expand on concepts briefly mentioned in videos
- Add context and background information
- Include practical applications and actionable advice
- Create smooth transitions between video topics

Remember: You're not just transcribing videos - you're creating a superior article that provides more value than watching the videos themselves.

Start writing now, ensuring exactly ${u} words of exceptional content.`;h(e,{stage:"article-generation",progress:85,message:"Crafting the perfect article...",storyData:E[4],generationDetails:{wordCount:u,tone:l,language:m,sourceCount:o.length+I.length}}),console.log("\uD83D\uDCDD GENERATING ARTICLE: Sending enhanced prompts to Gemini...");let C=Date.now(),b=(await y.generateContent({contents:[{role:"user",parts:[{text:x+"\n\n"+N}]}]})).response.text(),O=Date.now()-C;console.log(`✅ ARTICLE GENERATION COMPLETE in ${O}ms`),console.log(`📊 Generated content length: ${b.length} characters`),h(e,{stage:"quality-assurance",progress:95,message:"Quality guardians polishing your masterpiece...",storyData:E[5]});let q=b.split(/\s+/).filter(e=>e.length>0).length,M=Math.abs(q-u),P=(q/u*100).toFixed(1);console.log(`📝 QUALITY ASSURANCE: Word count analysis complete`),console.log(`   🎯 Target: ${u} words`),console.log(`   ✅ Actual: ${q} words`),console.log(`   📊 Accuracy: ${P}% (${M} words difference)`),await new Promise(e=>setTimeout(e,2e3));let k=await c.z.content.create({data:{title:n,content:b,type:"video_alchemy",userId:t.user.id,metadata:JSON.stringify({videoLinks:r,videoTitles:a,failedVideos:A,researchQueries:S,researchResults:I.length,tone:l,wordCount:u,language:m,customInstructions:d||null,generatedAt:new Date().toISOString(),analysisData:{videosProcessed:o.length,queriesGenerated:S.length,successfulResearch:I.length,totalSources:I.reduce((e,t)=>e+t.sources.length,0)}}),wordCount:q,tone:l,language:"hindi"===m?"hi":"french"===m?"fr":"en"}});console.log("\uD83D\uDCBE DATABASE SAVE COMPLETE:"),console.log(`   📝 Article ID: ${k.id}`),console.log(`   📊 Word Count: ${q} words`),console.log(`   🎯 Accuracy: ${P}% of target`),console.log(`   📱 Language: ${m}`),console.log(`   🎨 Tone: ${l}`);let G=`Your magical article is ready! ${q} words of pure value created from ${o.length} videos + ${I.length} research sources.`;console.log(`🎉 COMPLETION: ${G}`),h(e,{stage:"completion",progress:100,message:G,storyData:E[6],article:{id:k.id,title:n,content:b,metadata:{processedVideos:r.length-A.length,failedVideos:A,videoTitles:a,researchQueries:S,researchResults:I.length,language:m,tone:l,wordCount:q,targetWordCount:u,wordCountAccuracy:P,totalSources:I.reduce((e,t)=>e+t.sources.length,0),generationTime:O,analysisComplete:!0}},finalStats:{videosProcessed:o.length,totalQueries:S.length,successfulResearch:I.length,totalSources:I.reduce((e,t)=>e+t.sources.length,0),wordCount:q,wordCountAccuracy:P,generationTime:O,overallSuccess:!0}}),await new Promise(e=>setTimeout(e,2e3)),e.close()}catch(t){console.error("SSE Generation error:",t),h(e,{stage:"error",progress:0,message:"An error occurred during generation",error:t instanceof Error?t.message:"Unknown error"}),e.close()}})()}});return new Response(A,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}r()}catch(e){r(e)}})},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},60538:(e,t,n)=>{n.a(e,async(e,r)=>{try{n.r(t),n.d(t,{patchFetch:()=>c,routeModule:()=>u,serverHooks:()=>p,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>g});var s=n(96559),o=n(48088),a=n(37719),i=n(25854),l=e([i]);i=(l.then?(await l)():l)[0];let u=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/video-alchemy/stream/route",pathname:"/api/video-alchemy/stream",filename:"route",bundlePath:"app/api/video-alchemy/stream/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/video-alchemy/stream/route.ts",nextConfigOutput:"",userland:i}),{workAsyncStorage:d,workUnitAsyncStorage:g,serverHooks:p}=u;function c(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:g})}r()}catch(e){r(e)}})},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},75263:e=>{e.exports=import("cheerio")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79748:e=>{e.exports=require("fs/promises")},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},94175:e=>{e.exports=require("stream/web")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[7719,580,4973,7449,4612,1201,6311,5901],()=>n(60538));module.exports=r})();