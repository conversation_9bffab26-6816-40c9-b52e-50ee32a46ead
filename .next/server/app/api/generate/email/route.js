(()=>{var e={};e.id=2190,e.ids=[2190],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,n)=>{"use strict";n.d(t,{N:()=>i});var o=n(16467),a=n(36344),r=n(31183);let i={adapter:(0,o.y)(r.z),providers:[(0,a.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"database"},callbacks:{session:async({session:e,user:t})=>(e?.user&&t&&(e.user.id=t.id),e),signIn:async({user:e,account:t,profile:n})=>!0},events:{async createUser({user:e}){try{await r.z.userSettings.findUnique({where:{userId:e.id}})?console.log(`ℹ️ User profile already exists for: ${e.email}`):(await r.z.user.update({where:{id:e.id},data:{firstName:e.name?.split(" ")[0]||"",lastName:e.name?.split(" ").slice(1).join(" ")||"",settings:{create:{}},subscription:{create:{plan:"free",status:"active"}},quotas:{create:[{quotaType:"blog_posts",totalLimit:5,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"emails",totalLimit:10,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"social_media",totalLimit:20,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"youtube_scripts",totalLimit:3,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"invincible_research",totalLimit:2,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}]}}}),console.log(`✅ Created complete user profile for: ${e.email}`))}catch(e){console.error("Error setting up user profile:",e)}}},pages:{signIn:"/auth/signin",error:"/auth/error"},debug:!1}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,n)=>{"use strict";n.d(t,{z:()=>a});var o=n(96330);let a=globalThis.prisma??new o.PrismaClient({log:["query"]})},39923:(e,t,n)=>{"use strict";n.r(t),n.d(t,{patchFetch:()=>k,routeModule:()=>p,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>w});var o={};n.r(o),n.d(o,{POST:()=>h});var a=n(96559),r=n(48088),i=n(37719),s=n(32190),u=n(35426),l=n(12909),c=n(84299),g=n(31183),d=n(93356);async function h(e){try{let t=await (0,u.getServerSession)(l.N);if(!t?.user?.id)return s.NextResponse.json({error:"Authentication required"},{status:401});let n=await c.a.checkQuota(t.user.id,"emails");if(!n.hasQuota)return s.NextResponse.json({error:"Email quota exceeded",quota:{used:n.used,limit:n.limit,resetDate:n.resetDate}},{status:429});let{purpose:o,audience:a,tone:r,keyPoints:i}=await e.json();if(!o||!a)return s.NextResponse.json({error:"Purpose and audience are required"},{status:400});let h=new d.p;console.log("\uD83D\uDCE7 Generating email...");let p=await h.generateEmail(o,a,r||"professional",i||[]);console.log("✅ Email generated successfully"),await c.a.useQuota(t.user.id,"emails")||console.error("Failed to update quota after successful generation");try{await g.z.content.create({data:{userId:t.user.id,type:"email",title:`Email: ${o}`,content:p,tone:r||"professional",metadata:JSON.stringify({purpose:o,audience:a,keyPoints:i,generatedAt:new Date().toISOString()})}})}catch(e){console.error("Failed to save content to database:",e)}return s.NextResponse.json({success:!0,content:p,quota:{used:n.used+1,limit:n.limit,remaining:-1===n.limit?-1:n.limit-(n.used+1)}})}catch(e){return console.error("Email generation error:",e),s.NextResponse.json({error:"Failed to generate email"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:r.RouteKind.APP_ROUTE,page:"/api/generate/email/route",pathname:"/api/generate/email",filename:"route",bundlePath:"app/api/generate/email/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/generate/email/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:m,workUnitAsyncStorage:w,serverHooks:f}=p;function k(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:w})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84299:(e,t,n)=>{"use strict";n.d(t,{a:()=>r});var o=n(31183);let a={free:{blog_posts:5,emails:10,social_media:20,youtube_scripts:3,invincible_research:2},pro:{blog_posts:50,emails:100,social_media:200,youtube_scripts:25,invincible_research:20},enterprise:{blog_posts:-1,emails:-1,social_media:-1,youtube_scripts:-1,invincible_research:-1}};class r{static async checkQuota(e,t){try{let n=await o.z.user.findUnique({where:{id:e},include:{subscription:!0,quotas:{where:{quotaType:t}}}});if(!n)throw Error("User not found");let r=n.subscription?.plan||"free",i=n.quotas[0];if(!i){let n=a[r][t],i=await o.z.userQuota.create({data:{userId:e,quotaType:t,totalLimit:n,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}});return{hasQuota:-1===n||i.used<n,used:i.used,limit:n,resetDate:i.resetDate}}if(new Date>=i.resetDate){await this.resetQuota(e,t);let n=a[r][t];return{hasQuota:-1===n||0<n,used:0,limit:n,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}}let s=a[r][t];return{hasQuota:-1===s||i.used<s,used:i.used,limit:s,resetDate:i.resetDate}}catch(e){throw console.error("Error checking quota:",e),e}}static async useQuota(e,t){try{let n=await this.checkQuota(e,t);if(!n.hasQuota)return!1;return await o.z.userQuota.update({where:{userId_quotaType:{userId:e,quotaType:t}},data:{used:{increment:1}}}),await o.z.usageHistory.create({data:{userId:e,action:"content_generated",type:t,metadata:JSON.stringify({quotaUsed:n.used+1,quotaLimit:n.limit})}}),!0}catch(e){return console.error("Error using quota:",e),!1}}static async resetQuota(e,t){try{let n=new Date;n.setMonth(n.getMonth()+1,1),n.setHours(0,0,0,0),await o.z.userQuota.update({where:{userId_quotaType:{userId:e,quotaType:t}},data:{used:0,resetDate:n}})}catch(e){throw console.error("Error resetting quota:",e),e}}static async getAllQuotas(e){try{let t=await o.z.user.findUnique({where:{id:e},include:{subscription:!0,quotas:!0}});if(!t)throw Error("User not found");return t.subscription?.plan,await Promise.all(["blog_posts","emails","social_media","youtube_scripts","invincible_research"].map(async t=>{let n=await this.checkQuota(e,t);return{quotaType:t,used:n.used,limit:n.limit,resetDate:n.resetDate,percentage:-1===n.limit?0:n.used/n.limit*100}}))}catch(e){throw console.error("Error getting all quotas:",e),e}}static async upgradeUserPlan(e,t){try{for(let n of(await o.z.subscription.upsert({where:{userId:e},update:{plan:t,status:"active"},create:{userId:e,plan:t,status:"active"}}),["blog_posts","emails","social_media","youtube_scripts","invincible_research"])){let r=a[t][n];await o.z.userQuota.upsert({where:{userId_quotaType:{userId:e,quotaType:n}},update:{totalLimit:r},create:{userId:e,quotaType:n,totalLimit:r,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}})}}catch(e){throw console.error("Error upgrading user plan:",e),e}}}},93356:(e,t,n)=>{"use strict";n.d(t,{p:()=>a});let o=new(n(37449)).ij("AIzaSyCU1qb0b0XEM-B99XUDIRmCfKE3kunbKfY");class a{constructor(e="gemini-2.5-flash-lite-preview-06-17"){this.model=o.getGenerativeModel({model:e})}updateModel(e){this.model=o.getGenerativeModel({model:e})}estimateTokenCount(e){return Math.ceil(e.length/4)}async generateContent(e,t={},n){let o=Date.now(),a=Math.random().toString(36).substr(2,9);console.log(`💎 Gemini Content Call Started`),console.log(`   📋 Call ID: ${a}`),console.log(`   🎬 Context: ${n||"General Content"}`),console.log(`   ⚙️ Model: ${this.model._model||"gemini-2.5-flash-lite-preview-06-17"}`),console.log(`   🌡️ Temperature: ${t.temperature||.7}`),console.log(`   📏 Max Tokens: ${t.maxOutputTokens||4e3}`),console.log(`   🎯 TopP: ${t.topP||.95}`),console.log(`   🔢 TopK: ${t.topK||40}`),console.log(`   📝 Prompt Length: ${e.length} chars`),t.thinkingConfig&&(console.log(`   🧠 Thinking Enabled:`),console.log(`      💭 Budget: ${t.thinkingConfig.thinkingBudget??"dynamic"}`),console.log(`      🔍 Include Thoughts: ${t.thinkingConfig.includeThoughts||!1}`));try{let r={temperature:t.temperature||.7,maxOutputTokens:t.maxOutputTokens||4e3,topP:t.topP||.95,topK:t.topK||40};t.thinkingConfig&&(r.thinkingConfig={},void 0!==t.thinkingConfig.thinkingBudget&&(r.thinkingConfig.thinkingBudget=t.thinkingConfig.thinkingBudget),void 0!==t.thinkingConfig.includeThoughts&&(r.thinkingConfig.includeThoughts=t.thinkingConfig.includeThoughts)),console.log(`   📤 Sending request to Gemini...`);let i=await this.model.generateContent({contents:[{role:"user",parts:[{text:e}]}],generationConfig:r}),s=await i.response,u=s.text(),l=[],c=0;if(t.thinkingConfig?.includeThoughts&&s.candidates?.[0]?.content?.parts)for(let e of s.candidates[0].content.parts)e.thought&&e.text&&l.push(e.text);s.usageMetadata?.thoughtsTokenCount&&(c=s.usageMetadata.thoughtsTokenCount);let g=this.estimateTokenCount(e),d=this.estimateTokenCount(u),h=Date.now()-o;console.log(`   ✅ Gemini Content Complete`),console.log(`   ⏱️ Duration: ${h}ms`),console.log(`   📊 Input Tokens: ${g} (estimated)`),console.log(`   📊 Output Tokens: ${d} (estimated)`),c>0&&(console.log(`   🧠 Thinking Tokens: ${c}`),console.log(`   💭 Thoughts Generated: ${l.length}`)),console.log(`   📄 Response Length: ${u.length} chars`);let p=4e-7*c;return console.log(`   💰 Estimated Cost: $${(1e-7*g+4e-7*d+p).toFixed(6)} (Flash-Lite 2025)`),n?.includes("YouTube")&&(console.log(`   🎬 YouTube Content Success - Call ${a}`),console.log(`   📺 Step: ${n}`)),{response:u,inputTokens:g,outputTokens:d,thoughtsTokenCount:c>0?c:void 0,thoughts:l.length>0?l:void 0}}catch(t){let e=Date.now()-o;throw console.error(`   ❌ Gemini Content Failed`),console.error(`   ⏱️ Failed after: ${e}ms`),console.error(`   📋 Call ID: ${a}`),console.error(`   🎬 Context: ${n||"General Content"}`),console.error(`   💥 Error:`,t),n?.includes("YouTube")&&(console.error(`   🎬 YouTube Content FAILED - Call ${a}`),console.error(`   📺 Failed Step: ${n}`)),Error(`Failed to generate content with Gemini: ${t}`)}}async generateContentWithThinking(e,t=-1,n=!0,o={},a){return this.generateContent(e,{...o,thinkingConfig:{thinkingBudget:t,includeThoughts:n}},a)}async generateContentWithMaxThinking(e,t={},n){return this.generateContentWithThinking(e,24576,!0,t,n)}async generateContentWithoutThinking(e,t={},n){return this.generateContent(e,{...t,thinkingConfig:{thinkingBudget:0,includeThoughts:!1}},n)}async generateBlogPost(e,t,n,o,a,r=!0){let i=`
You are a world-class professional content writer and subject matter expert. Create a comprehensive, engaging blog post about "${e}".

CONTENT REQUIREMENTS:
- Target word count: ${t} words
- Tone: ${n}
- Format: Professional markdown with proper headings, lists, and structure
- Include compelling hook and engaging introduction
- Use narrative storytelling and real-world examples
- Include strategic call-to-action at the end
- Write as a primary authoritative source
- Use confident, authoritative language (avoid hedging)

PROFESSIONAL WRITING STANDARDS:
- Start with an attention-grabbing hook (question, statistic, or bold statement)
- Create emotional connection with readers through storytelling
- Use scannable formatting with headings, subheadings, and bullet points
- Include actionable insights and practical advice
- Incorporate relevant statistics and data points
- Use active voice and strong verbs
- Create smooth transitions between sections
- End with a powerful conclusion and clear next steps

${a?.title?`Article Title: ${a.title}
`:""}
${a?.targetKeyword?`Target Keyword: ${a.targetKeyword} (use naturally throughout the content)
`:""}
${a?.targetAudience?`Target Audience: ${a.targetAudience} (tailor content for this audience)
`:""}
${a?.competitors?`Competitors to outperform: ${a.competitors} (create content that surpasses these sources)
`:""}

${o?`Research Data to incorporate:
${o}
`:""}

CONTENT STRUCTURE:
1. Compelling Hook (question, statistic, or bold statement)
2. Introduction with context and thesis
3. Main sections with clear headings and subheadings
4. Practical examples and case studies
5. Actionable takeaways and recommendations
6. Powerful conclusion with call-to-action

Create content that not only informs but also inspires action and provides exceptional value to readers. This should be the definitive resource on this topic.
`;return(await this.generateContent(i,{temperature:.7,maxOutputTokens:8e3,...r?{thinkingConfig:{thinkingBudget:-1,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async generateEmail(e,t,n,o,a=!1){let r=`
Create a professional email for the following:

Purpose: ${e}
Target Audience: ${t}
Tone: ${n}
Key Points to Include: ${o.join(", ")}

Requirements:
- Include compelling subject line
- Professional email structure (greeting, body, closing)
- Clear call-to-action
- Appropriate tone and language for the audience
- Concise but comprehensive

Format the response as:
Subject: [Subject Line]

[Email Body]
`;return(await this.generateContent(r,{temperature:.6,maxOutputTokens:1500,...a?{thinkingConfig:{thinkingBudget:512,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async generateTweet(e,t,n=!0,o=!1){let a=`
Create an engaging Twitter/X tweet about "${e}".

Style: ${t}
Include hashtags: ${n}

Requirements:
- Maximum 280 characters
- Engaging and shareable
- Include relevant emojis if appropriate
- ${n?"Include 2-3 relevant hashtags":"No hashtags"}
- Hook the reader's attention
- Encourage engagement (likes, retweets, replies)

Create a tweet that stands out in the feed and drives engagement.
`;return(await this.generateContent(a,{temperature:.8,maxOutputTokens:500,...o?{thinkingConfig:{thinkingBudget:256,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async extractKeywords(e,t=!1){let n=`
Extract the most important keywords from this topic for Google search: "${e}"

Requirements:
- If the topic is a single word or simple phrase, use it as the main keyword
- For complex topics, extract 3-5 key terms that best represent the topic
- Focus on the main concepts and important terms
- Use words that would be effective for Google search
- Return only the keywords separated by spaces, nothing else
- Do not include common words like "the", "and", "of", etc.
- Do not add words like "meaning", "definition", "example" unless they are part of the original topic
- Focus on specific, searchable terms from the original topic

Examples:
Topic: "magistral"
Keywords: magistral

Topic: "How to build a React application with TypeScript"
Keywords: React TypeScript application build development

Topic: "artificial intelligence in healthcare"
Keywords: artificial intelligence healthcare

Return only the keywords:
`;return(await this.generateContent(n,{temperature:.1,maxOutputTokens:50,...t?{thinkingConfig:{thinkingBudget:256,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async generateYouTubeScript(e,t,n,o,a=!0){let r=`
Create a YouTube video script about "${e}".

Video Duration: ${t}
Style: ${n}
Target Audience: ${o}

Requirements:
- Include compelling hook in first 15 seconds
- Clear structure with timestamps
- Engaging storytelling throughout
- Include call-to-action for likes, subscribes, comments
- Natural speaking rhythm and flow
- Include cues for visuals/graphics where appropriate
- End with strong conclusion and next video teaser

Format:
[HOOK - 0:00-0:15]
[INTRODUCTION - 0:15-0:45]
[MAIN CONTENT - Sections with timestamps]
[CONCLUSION & CTA - Final section]

Create a script that keeps viewers engaged throughout the entire video.
`;return(await this.generateContent(r,{temperature:.7,maxOutputTokens:5e3,...a?{thinkingConfig:{thinkingBudget:2048,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}async extractKeywordsFromContent(e,t=!1){let n=`
Analyze this content and extract the most important SEO keywords and phrases that would be valuable for content optimization:

Content:
${e.substring(0,3e3)}

Rules:
- Extract 8-12 high-value keywords and phrases
- Focus on terms that appear frequently and seem important
- Include both single keywords and 2-3 word phrases
- Prioritize terms that would be good for SEO targeting
- Separate keywords with commas
- Don't include common words like "the", "and", "or", etc.

Return only the keywords separated by commas:
`;return(await this.generateContent(n,{temperature:.2,maxOutputTokens:200,...t?{thinkingConfig:{thinkingBudget:1024,includeThoughts:!1}}:{thinkingConfig:{thinkingBudget:0}}})).response}static calculateEnhancedInvincibleCost(e){let t=2e3+800*e.researchSources+6e3,n=Math.ceil(e.articleWordCount/.75),o=e.withThinking?Math.ceil(.2*n):0,a=1e-7*t,r=4e-7*n,i=4e-7*o;return{estimatedInputTokens:t,estimatedOutputTokens:n,estimatedThinkingTokens:o,totalCost:a+r+i,breakdown:{inputCost:a,outputCost:r,thinkingCost:i}}}}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),o=t.X(0,[7719,580,4973,7449],()=>n(39923));module.exports=o})();