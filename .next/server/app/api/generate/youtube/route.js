"use strict";(()=>{var e={};e.id=4229,e.ids=[3595,4229],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3595:(e,t,s)=>{s.d(t,{KnowledgeBaseOptimizer:()=>a});var r=s(80505);class a{static{this.globalCache=new Map}static getGlobalKnowledgeBase(e){let t=e.toLowerCase().replace(/[^a-z0-9]/g,"_");return this.globalCache.has(t)||this.globalCache.set(t,new r.I(t)),this.globalCache.get(t)}static findCachedAnalyses(e,t=5){console.log(`🔍 Searching global cache for analyses on "${e}"...`);let s=[],r=e.toLowerCase().split(" ");return this.globalCache.forEach((e,t)=>{e.getEntriesByType("competitive").forEach(e=>{let t=this.calculateRelevance(r,e);if(t>=.5){let r=e.url?.split("v=")[1]||"";s.push({videoId:r,title:e.title||"",analysis:e.content,timestamp:e.metadata?.timestamp||Date.now(),relevanceScore:t})}})}),s.sort((e,t)=>{let s=(e.relevanceScore||0)+1/(Date.now()-e.timestamp+1);return(t.relevanceScore||0)+1/(Date.now()-t.timestamp+1)-s}),console.log(`📊 Found ${s.length} relevant cached analyses`),s.slice(0,t)}static findCachedCaptions(e){console.log(`🔍 Checking global cache for captions of ${e.length} videos...`);let t=new Map;return this.globalCache.forEach(s=>{s.getEntriesByType("extracted_content").forEach(s=>{let r=s.url?.split("v=")[1];r&&e.includes(r)&&Date.now()-(s.metadata?.timestamp||0)<6048e5&&t.set(r,s.content)})}),console.log(`💾 Found ${t.size} cached captions`),t}static buildTopicProfile(e){console.log(`📈 Building topic profile for "${e}"...`);let t=e.toLowerCase().split(" "),s=new Map,r=new Map,a=[],o=[];if(this.globalCache.forEach(e=>{[...e.getEntriesByType("research"),...e.getEntriesByType("competitive"),...e.getEntriesByType("extracted_content")].forEach(e=>{this.calculateRelevance(t,e)>.3&&(e.metadata?.keywords?.forEach(e=>{s.set(e,(s.get(e)||0)+1)}),e.metadata?.gaps?.forEach(e=>{r.set(e,(r.get(e)||0)+1)}),e.metadata?.keyInsights&&a.push(...e.metadata.keyInsights),e.metadata?.statistics?.forEach(e=>{(e.includes("views")||e.includes("engagement"))&&o.push(e)}))})}),0===s.size)return console.log("\uD83D\uDCCA No historical data found for topic profile"),null;let i=Array.from(s.entries()).sort((e,t)=>t[1]-e[1]).map(([e])=>e).slice(0,10),n=Array.from(r.entries()).sort((e,t)=>t[1]-e[1]).map(([e])=>e).slice(0,10),c={topic:e,commonThemes:i,successfulPatterns:[...new Set(a)].slice(0,10),contentGaps:n,bestPractices:[...new Set(o)].slice(0,5),lastUpdated:Date.now()};return console.log(`📊 Topic profile created with ${c.commonThemes.length} themes, ${c.contentGaps.length} gaps`),c}static findSuccessfulPatterns(e,t){console.log(`🎯 Finding successful patterns for ${e} style, ${t} duration...`);let s=[];return this.globalCache.forEach(r=>{r.searchContent("Final Script").forEach(r=>{if(r.metadata?.keywords?.includes(e)||r.metadata?.keywords?.includes(t)){let e=r.content.match(/\[([\d:]+)\]\s*([^[]+)/g);if(e){let t=e.slice(0,3).map(e=>e.replace(/\[[\d:]+\]\s*/,""));s.push(...t)}}})}),console.log(`✨ Found ${s.length} successful patterns`),[...new Set(s)].slice(0,5)}static calculateRelevance(e,t){let s=0,r=(t.title||"").toLowerCase().split(" "),a=t.content.substring(0,500).toLowerCase();e.forEach(e=>{r.includes(e)&&(s+=.3),a.includes(e)&&(s+=.1),t.metadata?.keywords?.some(t=>t.toLowerCase().includes(e))&&(s+=.2)});let o=(Date.now()-(t.metadata?.timestamp||0))/864e5;return o<1?s+=.2:o<3?s+=.1:o<7&&(s+=.05),Math.min(s,1)}static getOptimizationRecommendations(e){console.log(`💡 Generating optimization recommendations for "${e}"...`);let t=[],s=this.findCachedAnalyses(e,3);s.length>0&&t.push(`🔄 Found ${s.length} cached competitor analyses - reuse to save API calls`);let r=this.buildTopicProfile(e);r&&(r.contentGaps.length>0&&t.push(`📊 Common content gaps: ${r.contentGaps.slice(0,3).join(", ")}`),r.bestPractices.length>0&&t.push(`✨ Best practice: ${r.bestPractices[0]}`),r.commonThemes.length>0&&t.push(`🎯 Focus on themes: ${r.commonThemes.slice(0,3).join(", ")}`));let a=this.findSuccessfulPatterns("educational","5-10 minutes");return a.length>0&&t.push(`💡 Successful hook: "${a[0].substring(0,50)}..."`),console.log(`💡 Generated ${t.length} recommendations`),t}static trackMetrics(e,t){this.getGlobalKnowledgeBase("_optimization_metrics").addEntry({type:"research",title:`Optimization Metrics: ${e}`,content:JSON.stringify(t,null,2),metadata:{source:"workflow_optimization",timestamp:Date.now(),statistics:[`${t.cachedAnalysesFound} cached analyses found`,`${t.cachedCaptionsFound} cached captions found`,`${t.apiCallsSaved} API calls saved`,`${t.timeSaved}s time saved`],keywords:["optimization","metrics","performance"]}}),console.log(`📊 Tracked optimization metrics: ${t.apiCallsSaved} API calls saved`)}static cleanupCache(e=2592e6){console.log("\uD83E\uDDF9 Cleaning up old cache entries...");let t=0;this.globalCache.forEach((s,r)=>{[...s.getEntriesByType("research"),...s.getEntriesByType("competitive"),...s.getEntriesByType("extracted_content"),...s.getEntriesByType("writing_style")].forEach(r=>{Date.now()-(r.metadata?.timestamp||0)>e&&(s.removeEntry(r.id),t++)}),0===s.getSize()&&this.globalCache.delete(r)}),console.log(`🧹 Removed ${t} old entries`)}static getGlobalCacheSize(){return this.globalCache.size}static clearGlobalCache(){console.log("\uD83E\uDDF9 Clearing entire global knowledge base cache...");let e=this.globalCache.size;this.globalCache.clear(),console.log(`🧹 Cleared ${e} global cache entries`)}constructor(){this.sessionCache=new Map,this.CACHE_DURATION=6048e5,this.RELEVANCE_THRESHOLD=.7}}},9956:e=>{e.exports=require("undici")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},19034:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>p,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>h});var a=s(96559),o=s(48088),i=s(37719),n=s(88377),c=e([n]);n=(c.then?(await c)():c)[0];let u=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/generate/youtube/route",pathname:"/api/generate/youtube",filename:"route",bundlePath:"app/api/generate/youtube/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/generate/youtube/route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:d,workUnitAsyncStorage:h,serverHooks:p}=u;function l(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:h})}r()}catch(e){r(e)}})},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67134:(e,t,s)=>{s.d(t,{Rs:()=>n,iR:()=>o,xU:()=>a});let r=new Map;function a(e,t){r.set(e,t)}function o(e){r.delete(e)}function i(e,t){let s=r.get(e);if(s){let a=new TextEncoder;try{s.enqueue(a.encode(`data: ${JSON.stringify(t)}

`))}catch(t){r.delete(e)}}}function n(e){return{updateProgress:(t,s,r)=>{i(e,{progress:t,message:s,status:r||"processing",timestamp:new Date().toISOString()})},complete:t=>{i(e,{progress:100,message:t,status:"completed",timestamp:new Date().toISOString()}),o(e)},error:(t,s)=>{i(e,{progress:100,message:t,status:"error",error:s?.toString(),timestamp:new Date().toISOString()}),o(e)}}}},74075:e=>{e.exports=require("zlib")},75263:e=>{e.exports=import("cheerio")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},79748:e=>{e.exports=require("fs/promises")},80505:(e,t,s)=>{s.d(t,{I:()=>r});class r{constructor(e){this.entries=new Map,this.topicId=e,this.createdAt=Date.now()}addEntry(e){let t=this.generateId(e.type),s={...e,id:t,metadata:{...e.metadata,timestamp:Date.now()}};return this.entries.set(t,s),console.log(`✅ Added ${e.type} entry to knowledge base: ${t}`),t}addBulkEntries(e){return e.map(e=>this.addEntry(e))}getEntriesByType(e){return Array.from(this.entries.values()).filter(t=>t.type===e)}getResearchData(){let e=[...this.getEntriesByType("research"),...this.getEntriesByType("extracted_content")],t=new Set,s=new Set,r=new Set;return e.forEach(e=>{e.query&&r.add(e.query),e.metadata?.keyInsights?.forEach(e=>t.add(e)),e.metadata?.statistics?.forEach(e=>s.add(e))}),{sources:e,totalSources:e.length,keyInsights:Array.from(t),statistics:Array.from(s),queries:Array.from(r)}}getCompetitiveAnalysis(){let e=this.getEntriesByType("competitive"),t=new Set,s=[];return e.forEach(e=>{if(e.metadata?.keywords?.forEach(e=>t.add(e)),e.content.includes("GAP:")){let t=e.content.match(/GAP:\s*([^|]+)/g);t?.forEach(e=>{s.push(e.replace("GAP:","").trim())})}}),{competitors:e,gaps:[],keywords:Array.from(t),strengths:[],opportunities:s}}getWritingStyleInsights(){let e=this.getEntriesByType("writing_style"),t=[],s=[],r=[],a=[];return e.forEach(e=>{if(e.metadata?.writingPatterns&&t.push(...e.metadata.writingPatterns),e.content.includes("TONE:")){let t=e.content.match(/TONE:\s*([^|]+)/g);t?.forEach(e=>{s.push(e.replace("TONE:","").trim())})}}),t.forEach(e=>{"structure"===e.category?r.push(e.pattern):"engagement"===e.category&&a.push(e.pattern)}),{patterns:t,toneAnalysis:s,structurePatterns:r,engagementTechniques:a}}searchContent(e){let t=e.toLowerCase();return Array.from(this.entries.values()).filter(e=>e.content.toLowerCase().includes(t)||e.title?.toLowerCase().includes(t)||e.metadata?.keyInsights?.some(e=>e.toLowerCase().includes(t)))}getKnowledgeSummary(){let e={};Array.from(this.entries.values()).forEach(t=>{e[t.type]=(e[t.type]||0)+1});let t=Math.max(...Array.from(this.entries.values()).map(e=>e.metadata?.timestamp||0));return{topicId:this.topicId,totalEntries:this.entries.size,entriesByType:e,researchSummary:this.getResearchData(),competitiveSummary:this.getCompetitiveAnalysis(),writingStyleSummary:this.getWritingStyleInsights(),createdAt:this.createdAt,lastUpdated:t}}export(){return JSON.stringify({topicId:this.topicId,createdAt:this.createdAt,entries:Array.from(this.entries.values())},null,2)}static import(e,t){let s=JSON.parse(e),a=new r(t);return s.entries.forEach(e=>{a.entries.set(e.id,e)}),a}clear(){this.entries.clear(),console.log("\uD83D\uDDD1️ Knowledge base cleared")}getEntry(e){return this.entries.get(e)}removeEntry(e){return this.entries.delete(e)}generateId(e){return`${e}_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}getSize(){return this.entries.size}}},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},84299:(e,t,s)=>{s.d(t,{a:()=>o});var r=s(31183);let a={free:{blog_posts:5,emails:10,social_media:20,youtube_scripts:3,invincible_research:2},pro:{blog_posts:50,emails:100,social_media:200,youtube_scripts:25,invincible_research:20},enterprise:{blog_posts:-1,emails:-1,social_media:-1,youtube_scripts:-1,invincible_research:-1}};class o{static async checkQuota(e,t){try{let s=await r.z.user.findUnique({where:{id:e},include:{subscription:!0,quotas:{where:{quotaType:t}}}});if(!s)throw Error("User not found");let o=s.subscription?.plan||"free",i=s.quotas[0];if(!i){let s=a[o][t],i=await r.z.userQuota.create({data:{userId:e,quotaType:t,totalLimit:s,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}});return{hasQuota:-1===s||i.used<s,used:i.used,limit:s,resetDate:i.resetDate}}if(new Date>=i.resetDate){await this.resetQuota(e,t);let s=a[o][t];return{hasQuota:-1===s||0<s,used:0,limit:s,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}}let n=a[o][t];return{hasQuota:-1===n||i.used<n,used:i.used,limit:n,resetDate:i.resetDate}}catch(e){throw console.error("Error checking quota:",e),e}}static async useQuota(e,t){try{let s=await this.checkQuota(e,t);if(!s.hasQuota)return!1;return await r.z.userQuota.update({where:{userId_quotaType:{userId:e,quotaType:t}},data:{used:{increment:1}}}),await r.z.usageHistory.create({data:{userId:e,action:"content_generated",type:t,metadata:JSON.stringify({quotaUsed:s.used+1,quotaLimit:s.limit})}}),!0}catch(e){return console.error("Error using quota:",e),!1}}static async resetQuota(e,t){try{let s=new Date;s.setMonth(s.getMonth()+1,1),s.setHours(0,0,0,0),await r.z.userQuota.update({where:{userId_quotaType:{userId:e,quotaType:t}},data:{used:0,resetDate:s}})}catch(e){throw console.error("Error resetting quota:",e),e}}static async getAllQuotas(e){try{let t=await r.z.user.findUnique({where:{id:e},include:{subscription:!0,quotas:!0}});if(!t)throw Error("User not found");return t.subscription?.plan,await Promise.all(["blog_posts","emails","social_media","youtube_scripts","invincible_research"].map(async t=>{let s=await this.checkQuota(e,t);return{quotaType:t,used:s.used,limit:s.limit,resetDate:s.resetDate,percentage:-1===s.limit?0:s.used/s.limit*100}}))}catch(e){throw console.error("Error getting all quotas:",e),e}}static async upgradeUserPlan(e,t){try{for(let s of(await r.z.subscription.upsert({where:{userId:e},update:{plan:t,status:"active"},create:{userId:e,plan:t,status:"active"}}),["blog_posts","emails","social_media","youtube_scripts","invincible_research"])){let o=a[t][s];await r.z.userQuota.upsert({where:{userId_quotaType:{userId:e,quotaType:s}},update:{totalLimit:o},create:{userId:e,quotaType:s,totalLimit:o,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}})}}catch(e){throw console.error("Error upgrading user plan:",e),e}}}},88377:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{POST:()=>y});var a=s(32190),o=s(19854),i=s(12909),n=s(9865),c=s(3595),l=s(67134),u=s(84299),d=s(31183),h=s(99475),p=s(93356),g=s(44630),m=e([h,g]);async function y(e){try{let t=await (0,o.getServerSession)(i.N);if(!t?.user?.id)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{title:s,brief:r,duration:m,style:y,targetAudience:w,videoUrls:f}=await e.json();if(!s||!r)return a.NextResponse.json({error:"Title and brief are required"},{status:400});let E=await u.a.checkQuota(t.user.id,"youtube_scripts");if(!E.hasQuota)return a.NextResponse.json({error:"Quota exceeded",quota:E},{status:429});let T=`youtube_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,S=(0,l.Rs)(T),C=new n.R,I=new p.p,v=new h.pB,A=s.toLowerCase().replace(/[^a-z0-9]/g,"_"),b=c.KnowledgeBaseOptimizer.getGlobalKnowledgeBase(A);console.log("\uD83C\uDFA5 Starting streamlined YouTube script generation..."),console.log(`📝 Topic: "${s}"`),console.log(`🎯 Target Duration: ${m||"5-10 minutes"}`),S.updateProgress(5,"Starting research and data collection..."),S.updateProgress(10,"Analyzing user description with Gemini to create targeted search queries..."),console.log(`🧠 Analyzing user description with Gemini: "${r}"`);let R=`You are an expert research assistant specializing in YouTube content creation. Analyze the user's description and create targeted search queries for comprehensive research.

🎯 **USER'S DESCRIPTION TO ANALYZE:**
"${r}"

📝 **VIDEO TITLE:** "${s}"
⏱️ **DURATION:** ${m||"5-10 minutes"}
🎨 **STYLE:** ${y||"educational"}
👥 **AUDIENCE:** ${w||"general audience"}

🔍 **TASK:** Create 8-10 specific search queries that will help find the most relevant and comprehensive research content for creating this YouTube script.

**ANALYSIS REQUIREMENTS:**
1. **Extract Key Topics:** Identify the main subjects, concepts, or themes from the user's description
2. **Identify Information Gaps:** What specific information, data, or examples would be needed?
3. **Consider Audience Needs:** What would the target audience want to know about this topic?
4. **Include Current Trends:** Add queries for recent developments, statistics, or trending aspects
5. **Cover Different Angles:** Include queries for different perspectives, use cases, or approaches

**SEARCH QUERY CATEGORIES TO INCLUDE:**
- **Definitional:** What is [topic]? [Topic] explained simply
- **Statistical:** [Topic] statistics 2024, [Topic] market data, [Topic] trends
- **How-to/Tutorial:** How to [specific action], [Topic] tutorial, [Topic] guide
- **Comparison:** [Topic] vs alternatives, best [topic] tools, [topic] comparison
- **Examples/Case Studies:** [Topic] examples, [Topic] case studies, [Topic] success stories
- **Problems/Solutions:** [Topic] challenges, [Topic] solutions, [Topic] common mistakes
- **Industry/Expert:** [Topic] expert opinions, [Topic] industry analysis, [Topic] research
- **Latest/Current:** [Topic] 2024 update, latest [topic] news, [topic] recent developments

**OUTPUT FORMAT:**
Provide exactly 8-10 search queries, one per line, formatted as:
1. [First search query]
2. [Second search query]
3. [Third search query]
...etc.

**EXAMPLE:**
If the user wants to create a video about "AI tools for content creators", the queries might be:
1. AI tools for content creators 2024
2. best AI writing tools content creation
3. AI video editing tools creators
4. AI content generation statistics 2024
5. AI tools content creators case studies
6. AI content creation workflow automation
7. AI tools content creators comparison review
8. future of AI content creation trends

Create search queries that will find the most comprehensive and relevant research content for this YouTube script.`,$=(await I.generateContent(R,{temperature:.7,maxOutputTokens:1e3})).response.split("\n").filter(e=>e.trim()&&/^\d+\./.test(e.trim())).map(e=>e.replace(/^\d+\.\s*/,"").trim()).filter(e=>e.length>0);console.log(`🎯 Generated ${$.length} targeted search queries:`),$.forEach((e,t)=>{console.log(`  ${t+1}. ${e}`)}),S.updateProgress(20,"Performing comprehensive web research with targeted queries...");let x=[];for(let e of $.slice(0,6))try{console.log(`🔍 Searching: "${e}"`);let t=await v.search(e,3);x.push(...t.items),await new Promise(e=>setTimeout(e,500))}catch(t){console.warn(`⚠️ Search failed for query: "${e}"`,t)}let k=x.filter((e,t,s)=>t===s.findIndex(t=>t.link===e.link));console.log(`📊 Found ${k.length} unique search results`),S.updateProgress(35,"Scraping content from research sources..."),await g.I.initialize();let N=k.slice(0,12).map(e=>e.link);console.log(`🌐 Scraping ${N.length} URLs for detailed content...`);let O=await g.I.scrapeMultipleUrls(N,{maxLength:5e3,onlyMainContent:!0,extractImages:!1,extractLinks:!0}),D=k.map(e=>{let t=O.find(t=>t.url===e.link);return{title:e.title,url:e.link,snippet:e.snippet,domain:new URL(e.link).hostname,scrapedContent:t?.content||"",scrapedSuccess:t?.success||!1,wordCount:t?.wordCount||0,keyInsights:t?.keyInsights||[],statistics:t?.statistics||[]}});D.forEach((e,t)=>{b.addEntry({type:"research",title:`Research: ${e.title}`,url:e.url,content:e.scrapedContent||e.snippet,query:s,metadata:{source:"enhanced_web_research",timestamp:Date.now(),wordCount:e.wordCount||e.snippet.split(" ").length,keyInsights:e.keyInsights.length>0?e.keyInsights:[e.snippet.substring(0,100)],keywords:[s.toLowerCase(),...$.slice(0,3)],statistics:e.statistics.length>0?e.statistics:[`Source ${t+1}`,`Domain: ${e.domain}`]}})}),console.log(`📚 Enhanced research complete: ${D.length} sources processed`),console.log(`📊 Successfully scraped: ${O.filter(e=>e.success).length}/${O.length} URLs`),await g.I.close(),S.updateProgress(35,"Finding YouTube videos and extracting captions...");let q=[],P=f?f.split("\n").map(e=>e.trim()).filter(e=>e&&(e.includes("youtube.com")||e.includes("youtu.be"))):[],U=[];for(let e of(P.length>0?(console.log(`📺 Using ${P.length} user-provided YouTube URLs`),U=P.map(e=>{let t=function(e){for(let t of[/(?:https?:\/\/)?(?:www\.)?youtube\.com\/watch\?v=([a-zA-Z0-9_-]{11})/,/(?:https?:\/\/)?(?:www\.)?youtu\.be\/([a-zA-Z0-9_-]{11})/,/(?:https?:\/\/)?(?:www\.)?youtube\.com\/embed\/([a-zA-Z0-9_-]{11})/,/(?:https?:\/\/)?(?:www\.)?youtube\.com\/v\/([a-zA-Z0-9_-]{11})/]){let s=e.match(t);if(s&&s[1])return s[1]}return null}(e);return t?{id:t,title:"User-provided video",channelTitle:"Unknown",viewCount:"Unknown",duration:"Unknown"}:null}).filter(Boolean),console.log(`📺 Successfully parsed ${U.length} video IDs from URLs`)):(console.log(`📺 Searching YouTube for: "${s}"`),U=(await C.searchVideos(s,5)).videos,console.log(`📺 Found ${U.length} relevant videos`)),U))try{console.log(`📝 Extracting captions: ${e.title}`);let t=await C.extractCaptions(e.id);if(t&&t.length>0){let r=t.map(e=>e.text).join(" "),a=`https://youtube.com/watch?v=${e.id}`;q.push({video:{id:e.id,title:e.title,channel:e.channelTitle,views:e.viewCount,duration:e.duration},captions:r,url:a,isTopicRelated:!0}),b.addEntry({type:"extracted_content",title:`Video: ${e.title}`,url:a,content:r,metadata:{source:"youtube_captions",timestamp:Date.now(),wordCount:r.split(" ").length,keyInsights:[`Channel: ${e.channelTitle}`,`Views: ${e.viewCount}`],keywords:[e.channelTitle,s.toLowerCase()],statistics:[`${e.viewCount} views`,`${t.length} caption segments`,`Duration: ${e.duration}`]}}),console.log(`✅ Saved captions: ${e.title}`)}}catch(t){console.warn(`⚠️ Failed to extract captions for ${e.id}:`,t)}console.log(`📊 Caption extraction complete: ${q.length} videos processed`),S.updateProgress(60,"Generating script with comprehensive analysis..."),console.log("\uD83E\uDDE0 Using Gemini for intelligent script generation...");let L=`You are a MASTER YouTube scriptwriter with expertise in viral content creation. Create an exceptional YouTube script using comprehensive research and caption analysis.

🎯 **VIDEO SPECIFICATIONS:**
Title: "${s}"
Target Duration: ${m||"5-10 minutes"}
Style: ${y||"educational"}
Target Audience: ${w||"general audience"}

🔥 **CRITICAL USER INSTRUCTIONS - FOLLOW EXACTLY:**
The user has provided specific instructions for how they want this script created. These are NOT just a description - they are YOUR DIRECT ORDERS for script creation:

"${r}"

⚠️ **MANDATORY:** You MUST follow these user instructions precisely. They override all other considerations. If the user specifies a tone, structure, points to cover, or any other requirements, you MUST implement them exactly as requested.

📊 **RESEARCH CONTENT TO USE:**
${D.map((e,t)=>`
**RESEARCH SOURCE ${t+1}:**
Title: ${e.title}
URL: ${e.url}
Content: ${e.scrapedContent||e.snippet}
Domain: ${e.domain}
Word Count: ${e.wordCount}
Key Insights: ${e.keyInsights.join(", ")}
Statistics: ${e.statistics.join(", ")}
`).join("\n")}

🎬 **YOUTUBE VIDEOS FOR STYLE ANALYSIS:**
${q.map((e,t)=>`
**VIDEO ${t+1}: ${e.video.title}**
Channel: ${e.video.channel}
Views: ${e.video.views}
Duration: ${e.video.duration}
URL: ${e.url}

CAPTIONS/TRANSCRIPT:
${e.captions.substring(0,2e3)}${e.captions.length>2e3?"...":""}
`).join("\n\n")}

🚀 **COMPREHENSIVE SCRIPT GENERATION INSTRUCTIONS:**

**PHASE 1: CAPTION ANALYSIS FOR SCRIPT STYLE**
From the YouTube captions provided, analyze and extract:

1. **Writing Style Patterns:**
   - How do these YouTubers start their videos?
   - What phrases and transitions do they use?
   - How do they structure their content flow?
   - What vocabulary and energy words do they prefer?
   - How do they engage with their audience?

2. **Engagement Techniques:**
   - How do they hook viewers in the first 15 seconds?
   - What questions do they ask to keep engagement?
   - How do they handle transitions between topics?
   - What call-to-action patterns do they use?

3. **Content Structure:**
   - How do they organize information?
   - When do they insert pauses or emphasis?
   - How do they build up to important points?
   - How do they conclude their videos?

4. **Authentic Voice Elements:**
   - What natural speech patterns do they use?
   - How do they express excitement or emphasis?
   - What personal touches make them relatable?
   - How do they handle complex information simply?

**PHASE 2: CONTENT RELEVANCE ANALYSIS**
For each video caption, determine:
- Is this video directly related to our topic "${s}"?
- If YES: Extract relevant facts, insights, and information to use
- If NO: Still learn from their script style but don't use their content facts

**PHASE 3: RESEARCH INTEGRATION**
From the web research sources:
- Extract key facts, statistics, and insights about "${s}"
- Identify authoritative information and data points
- Find compelling angles and perspectives
- Note any trending aspects or recent developments

**PHASE 4: SCRIPT CREATION**
Create a script that:

1. **🚨 PRIMARY DIRECTIVE - USER INSTRUCTIONS:**
   - The user's instructions "${r}" are your ABSOLUTE PRIORITY
   - Every aspect of the script must align with their specific requirements
   - If they specify tone, structure, points, or approach - implement EXACTLY as requested
   - Their instructions override all other considerations

2. **LEARNED STYLE APPLICATION:**
   - Write exactly like the analyzed YouTubers (unless user specifies otherwise)
   - Use their proven engagement patterns
   - Copy their natural speech rhythms and vocabulary
   - Apply their successful content structure techniques

3. **RESEARCH-BACKED CONTENT:**
   - Integrate factual information from web research
   - Use relevant insights from topic-related video captions
   - Include specific data points and statistics
   - Reference credible sources naturally

4. **OPTIMAL STRUCTURE (unless user specifies different structure):**
   - Hook: Powerful opening that stops scrolling (0:00-0:15)
   - Introduction: Context and promise (0:15-0:45)
   - Main Content: Well-organized sections with research
   - Conclusion: Strong wrap-up with call-to-action
   - Natural timestamps based on content flow

5. **TARGET SPECIFICATIONS:**
   - Word Count: ${m?.includes("3")?"450-480":m?.includes("5")?"750-800":m?.includes("10")?"1500-1600":m?.includes("15")?"2250-2400":"1200-1400"} words
   - Speaking Pace: 150-160 words per minute
   - Style: ${y||"Educational but engaging"}
   - Audience: ${w||"General audience with interest in the topic"}

🎯 **CRITICAL REQUIREMENTS:**

1. **🚨 USER COMPLIANCE:** The user's instructions "${r}" are ABSOLUTE LAW - follow them exactly
2. **AUTHENTICITY:** Sound exactly like a real YouTuber, not AI-generated content
3. **RESEARCH-DRIVEN:** Every major claim should be backed by the research provided
4. **ENGAGEMENT:** Include hooks, questions, and retention techniques throughout
5. **RELEVANCE:** Stay focused on the topic while using proven YouTube formats
6. **NATURAL FLOW:** Write conversationally with natural transitions
7. **VALUE-PACKED:** Provide genuine insights and actionable information

🔥 **REMEMBER:** If the user's instructions conflict with any other requirement, the USER'S INSTRUCTIONS WIN. They are the director, you are the scriptwriter executing their vision.

🔥 **ADVANCED ANALYSIS PARAMETERS:**

Analyze the captions for:
- **Sentence length patterns** (short vs long sentences)
- **Energy modulation** (when they get excited vs calm)
- **Personal story integration** (how they share experiences)
- **Data presentation** (how they share statistics naturally)
- **Audience connection** (how they make viewers feel included)
- **Controversy handling** (how they address opposing views)
- **Authority building** (how they establish credibility)
- **Entertainment balance** (how they keep it fun while informative)

📝 **OUTPUT FORMAT:**
Provide ONLY the complete script with natural timestamps. No explanations, analysis, or meta-commentary - just the pure, ready-to-use YouTube script that sounds authentic and engaging.

🎯 **FINAL SUCCESS CRITERIA:**
1. **USER SATISFACTION:** The script perfectly matches what the user requested in their instructions
2. **ENGAGEMENT:** Viewers won't scroll away in the first 15 seconds
3. **COMPLETION:** Viewers will watch until the end
4. **INTERACTION:** Viewers will comment with engagement
5. **SHARING:** Viewers will share with others
6. **SUBSCRIPTION:** Viewers will subscribe for more content

⚠️ **FINAL REMINDER:** The user's instructions "${r}" are your PRIMARY DIRECTIVE. Everything else is secondary. If they want a specific tone, structure, or approach - deliver EXACTLY that.

Create the script now using maximum analytical depth, creative excellence, and ABSOLUTE ADHERENCE to the user's specific instructions.`,_=await I.generateContentWithThinking(L,2048,!1,{temperature:.7,maxOutputTokens:8e3},"YouTube Script Generation");b.addEntry({type:"research",title:`Generated Script: ${s}`,content:_.response,metadata:{source:"gemini_generation",timestamp:Date.now(),wordCount:_.response.split(" ").length,keyInsights:[`YouTube script for ${s}`],keywords:[s,y||"educational",w||"general"],statistics:[`${m||"5-10 minutes"} duration`,"research-backed content"]}}),console.log("✅ Script generation complete");let Y=b.getKnowledgeSummary();console.log(`📚 Knowledge base: ${Y.totalEntries} entries`),await u.a.useQuota(t.user.id,"youtube_scripts")||console.error("Failed to update quota after successful generation");try{await d.z.content.create({data:{userId:t.user.id,type:"youtube_script",title:`YouTube: ${s}`,content:_.response,metadata:JSON.stringify({title:s,brief:r,duration:m,style:y,targetAudience:w,videosAnalyzed:q.length,researchSources:D.length,knowledgeBaseEntries:Y.totalEntries,generatedAt:new Date().toISOString(),workflow:"streamlined_research_v1",videoUrls:q.map(e=>e.url)})}})}catch(e){console.error("Failed to save content to database:",e)}S.complete("✅ YouTube script generated successfully!"),console.log("\uD83C\uDF89 Streamlined YouTube script generation complete");let z=_.response.split(/\s+/).filter(e=>e.length>0).length;return a.NextResponse.json({success:!0,content:_.response,wordCount:z,progressId:T,metadata:{videosAnalyzed:q.length,researchSources:D.length,workflow:"streamlined_research_v1",thinkingTokens:_.thoughtsTokenCount||0,inputTokens:_.inputTokens,outputTokens:_.outputTokens},research:{webSources:D.slice(0,5).map(e=>({title:e.title,source:e.domain,url:e.url})),youtubeVideos:q.map(e=>({title:e.video.title,channel:e.video.channel,views:e.video.views,url:e.url,captionLength:e.captions.length}))},quota:{used:E.used+1,limit:E.limit,remaining:-1===E.limit?-1:E.limit-(E.used+1)},knowledgeBase:{id:Y.topicId,totalEntries:Y.totalEntries,entriesByType:Y.entriesByType}})}catch(e){return console.error("YouTube script generation error:",e),a.NextResponse.json({error:"Failed to generate YouTube script",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}[h,g]=m.then?(await m)():m,r()}catch(e){r(e)}})},94175:e=>{e.exports=require("stream/web")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,580,4973,7449,4612,4940,1201,6311,2087,5901],()=>s(19034));module.exports=r})();