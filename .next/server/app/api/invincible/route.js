"use strict";(()=>{var e={};e.id=6869,e.ids=[6869],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},11997:e=>{e.exports=require("punycode")},12412:e=>{e.exports=require("assert")},12909:(e,t,r)=>{r.d(t,{N:()=>n});var s=r(16467),a=r(36344),i=r(31183);let n={adapter:(0,s.y)(i.z),providers:[(0,a.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"database"},callbacks:{session:async({session:e,user:t})=>(e?.user&&t&&(e.user.id=t.id),e),signIn:async({user:e,account:t,profile:r})=>!0},events:{async createUser({user:e}){try{await i.z.userSettings.findUnique({where:{userId:e.id}})?console.log(`ℹ️ User profile already exists for: ${e.email}`):(await i.z.user.update({where:{id:e.id},data:{firstName:e.name?.split(" ")[0]||"",lastName:e.name?.split(" ").slice(1).join(" ")||"",settings:{create:{}},subscription:{create:{plan:"free",status:"active"}},quotas:{create:[{quotaType:"blog_posts",totalLimit:5,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"emails",totalLimit:10,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"social_media",totalLimit:20,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"youtube_scripts",totalLimit:3,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"invincible_research",totalLimit:2,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}]}}}),console.log(`✅ Created complete user profile for: ${e.email}`))}catch(e){console.error("Error setting up user profile:",e)}}},pages:{signIn:"/auth/signin",error:"/auth/error"},debug:!1}},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},33873:e=>{e.exports=require("path")},37830:e=>{e.exports=require("node:stream/web")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67134:(e,t,r)=>{r.d(t,{Rs:()=>o,iR:()=>i,xU:()=>a});let s=new Map;function a(e,t){s.set(e,t)}function i(e){s.delete(e)}function n(e,t){let r=s.get(e);if(r){let a=new TextEncoder;try{r.enqueue(a.encode(`data: ${JSON.stringify(t)}

`))}catch(t){s.delete(e)}}}function o(e){return{updateProgress:(t,r,s)=>{n(e,{progress:t,message:r,status:s||"processing",timestamp:new Date().toISOString()})},complete:t=>{n(e,{progress:100,message:t,status:"completed",timestamp:new Date().toISOString()}),i(e)},error:(t,r)=>{n(e,{progress:100,message:t,status:"error",error:r?.toString(),timestamp:new Date().toISOString()}),i(e)}}}},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74075:e=>{e.exports=require("zlib")},75263:e=>{e.exports=import("cheerio")},78789:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{GET:()=>h,POST:()=>g,maxDuration:()=>m});var a=r(32190),i=r(35426),n=r(12909),o=r(84299),c=r(31183),u=r(54785),l=r(67134),p=e([u]);u=(p.then?(await p)():p)[0];let m=300,y=new Map,w=null;function d(){w&&(clearInterval(w),w=null,console.log("\uD83E\uDDF9 Session cleanup interval cleared")),y.clear()}async function g(e){try{let r,s=e.cookies.get("next-auth.session-token")?.value||e.cookies.get("__Secure-next-auth.session-token")?.value,p=function(e){if(!e)return null;let t=y.get(e);return t&&Date.now()<t.expires?t.session:(y.delete(e),null)}(s);if(p)console.log("⚡ Using daily cached session - zero database queries");else if(console.log("\uD83D\uDD10 Performing daily authentication check..."),(p=await (0,i.getServerSession)(n.N))&&s){var t;t=p,y.set(s,{session:t,expires:Date.now()+864e5}),console.log("✅ Session cached for 24 hours - no more auth queries today")}if(!p?.user?.id)return a.NextResponse.json({error:"Authentication required"},{status:401});console.log("\uD83D\uDCCA Checking quota...");let d=await o.a.checkQuota(p.user.id,"invincible_research");if(!d.hasQuota)return a.NextResponse.json({error:"Invincible research quota exceeded",quota:{used:d.used,limit:d.limit,resetDate:d.resetDate,userEmail:p.user.email,plan:"free"},message:`You've used ${d.used}/${d.limit} monthly Invincible research requests. ${d.resetDate?`Quota resets on ${new Date(d.resetDate).toLocaleDateString()}.`:""} Upgrade for unlimited access.`},{status:429});let g=await e.json();if(!g.topic||"string"!=typeof g.topic)return a.NextResponse.json({error:"Topic is required and must be a string"},{status:400});console.log("\uD83D\uDE80 Starting Invincible workflow for authenticated user:",p.user.email);let h={taskId:`invincible_${Date.now()}`,topic:g.topic,customInstructions:g.customInstructions,targetAudience:g.targetAudience,contentLength:g.contentLength||2e3,tone:g.tone,keywords:g.keywords,contentType:g.contentType||"article"},m=(0,l.Rs)(h.taskId);m.updateProgress(5,"Initializing Invincible V.1 Agent..."),console.log("\uD83D\uDCC8 Progress tracking initialized");let w=new u.X({searchDepth:g.searchDepth||7,competitorCount:g.competitorCount||5,deepSearchQueriesPerTopic:g.deepSearchQueriesPerTopic||7,maxContentLength:g.maxContentLength||15e3,temperature:g.temperature||.7,uniquenessLevel:g.uniquenessLevel||"high"}),v=5;try{r=setInterval(()=>{if(v<85){v+=15;try{m.updateProgress(v,"Processing...")}catch(e){console.log("⚠️ Progress update failed (non-critical):",e)}}},1e4),console.log("\uD83C\uDFAF Executing Invincible agent workflow...");let e=await w.execute(h);if(r&&(clearInterval(r),r=void 0),console.log("✅ Agent execution completed"),!e.success)return m.updateProgress(100,"Failed","error"),a.NextResponse.json({error:e.error||"Agent execution failed",logs:e.logs},{status:500});m.updateProgress(100,"Article generated successfully!","completed"),console.log("\uD83D\uDCCA Updating quota usage..."),await o.a.useQuota(p.user.id,"invincible_research")||console.error("Failed to update quota after successful generation");try{console.log("\uD83D\uDCBE Saving generated content..."),await c.z.content.create({data:{userId:p.user.id,type:"invincible_research",title:e.article?.title||g.topic,content:e.article?.content||"",wordCount:e.article?.content?.length||0,tone:g.tone||"professional",metadata:JSON.stringify({taskId:h.taskId,executionTime:e.executionTime,totalSources:e.researchData?.totalSources||0,competitorsAnalyzed:e.competitiveAnalysis?.length||0,keyInsights:e.researchData?.keyInsights?.length||0,statistics:e.researchData?.statistics?.length||0,researchQueries:e.researchData?.searchQueries||[],uniquenessScore:e.uniquenessScore||0,contentFingerprint:e.contentFingerprint||"",uniquenessLevel:g.uniquenessLevel||"high",generatedAt:new Date().toISOString()})}}),console.log("✅ Content saved to database")}catch(e){console.error("Failed to save content to database:",e)}let t={success:!0,article:e.article,metadata:{taskId:h.taskId,executionTime:e.executionTime,totalSources:e.researchData?.totalSources||0,competitorsAnalyzed:e.competitiveAnalysis?.length||0,keyInsights:e.researchData?.keyInsights?.length||0,statistics:e.researchData?.statistics?.length||0,uniquenessScore:e.uniquenessScore||0,contentFingerprint:e.contentFingerprint||"",uniquenessLevel:g.uniquenessLevel||"high"},research:{sources:e.researchData?.sources?.length||0,queries:e.researchData?.searchQueries||[],keyInsights:e.researchData?.keyInsights?.slice(0,10)||[]},competitive:{analyzed:Array.isArray(e.competitiveAnalysis)?e.competitiveAnalysis.length:+!!e.competitiveAnalysis,opportunities:Array.isArray(e.competitiveAnalysis)?e.competitiveAnalysis.flatMap(e=>e.opportunities||[]).slice(0,10):(e.competitiveAnalysis?.criticalGaps||[]).slice(0,10),gaps:Array.isArray(e.competitiveAnalysis)?e.competitiveAnalysis.flatMap(e=>e.weaknesses||[]).slice(0,10):(e.competitiveAnalysis?.depthDeficiencies||[]).slice(0,10)},logs:e.logs,quota:{used:d.used+1,limit:d.limit,remaining:-1===d.limit?-1:d.limit-(d.used+1)}};return console.log("\uD83C\uDF89 Workflow completed successfully"),a.NextResponse.json(t)}catch(e){throw r&&(clearInterval(r),r=void 0),e}}catch(e){return console.error("Invincible agent error:",e),a.NextResponse.json({error:e instanceof Error?e.message:"Internal server error",details:void 0},{status:500})}}async function h(e){return a.NextResponse.json({agent:"Invincible V.1 - Daily Auth Optimized",version:"1.0.2",status:"ready",optimizations:["24-hour session caching - one auth per day","Zero repeated database queries during workflows","Reduced progress update frequency","Optimized database operations","Enhanced error handling and logging"],capabilities:["Primary search and URL extraction","Article type analysis","Deep research with custom queries","Competitive analysis","Human writing style analysis","Superior content generation","Knowledge base management"],workflow:{steps:6,description:"RAG-based single agent system for superior content creation"}})}process.on("SIGTERM",d),process.on("SIGINT",d),process.on("exit",d),w||(w=setInterval(function(){let e=Date.now();for(let[t,r]of y.entries())e>=r.expires&&y.delete(t)},36e5),console.log("\uD83E\uDDF9 Session cleanup interval initialized")),s()}catch(e){s(e)}})},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},80246:(e,t,r)=>{r.a(e,async(e,s)=>{try{r.r(t),r.d(t,{patchFetch:()=>u,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>d});var a=r(96559),i=r(48088),n=r(37719),o=r(78789),c=e([o]);o=(c.then?(await c)():c)[0];let l=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/invincible/route",pathname:"/api/invincible",filename:"route",bundlePath:"app/api/invincible/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/invincible/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:p,workUnitAsyncStorage:d,serverHooks:g}=l;function u(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:d})}s()}catch(e){s(e)}})},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},84299:(e,t,r)=>{r.d(t,{a:()=>i});var s=r(31183);let a={free:{blog_posts:5,emails:10,social_media:20,youtube_scripts:3,invincible_research:2},pro:{blog_posts:50,emails:100,social_media:200,youtube_scripts:25,invincible_research:20},enterprise:{blog_posts:-1,emails:-1,social_media:-1,youtube_scripts:-1,invincible_research:-1}};class i{static async checkQuota(e,t){try{let r=await s.z.user.findUnique({where:{id:e},include:{subscription:!0,quotas:{where:{quotaType:t}}}});if(!r)throw Error("User not found");let i=r.subscription?.plan||"free",n=r.quotas[0];if(!n){let r=a[i][t],n=await s.z.userQuota.create({data:{userId:e,quotaType:t,totalLimit:r,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}});return{hasQuota:-1===r||n.used<r,used:n.used,limit:r,resetDate:n.resetDate}}if(new Date>=n.resetDate){await this.resetQuota(e,t);let r=a[i][t];return{hasQuota:-1===r||0<r,used:0,limit:r,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}}let o=a[i][t];return{hasQuota:-1===o||n.used<o,used:n.used,limit:o,resetDate:n.resetDate}}catch(e){throw console.error("Error checking quota:",e),e}}static async useQuota(e,t){try{let r=await this.checkQuota(e,t);if(!r.hasQuota)return!1;return await s.z.userQuota.update({where:{userId_quotaType:{userId:e,quotaType:t}},data:{used:{increment:1}}}),await s.z.usageHistory.create({data:{userId:e,action:"content_generated",type:t,metadata:JSON.stringify({quotaUsed:r.used+1,quotaLimit:r.limit})}}),!0}catch(e){return console.error("Error using quota:",e),!1}}static async resetQuota(e,t){try{let r=new Date;r.setMonth(r.getMonth()+1,1),r.setHours(0,0,0,0),await s.z.userQuota.update({where:{userId_quotaType:{userId:e,quotaType:t}},data:{used:0,resetDate:r}})}catch(e){throw console.error("Error resetting quota:",e),e}}static async getAllQuotas(e){try{let t=await s.z.user.findUnique({where:{id:e},include:{subscription:!0,quotas:!0}});if(!t)throw Error("User not found");return t.subscription?.plan,await Promise.all(["blog_posts","emails","social_media","youtube_scripts","invincible_research"].map(async t=>{let r=await this.checkQuota(e,t);return{quotaType:t,used:r.used,limit:r.limit,resetDate:r.resetDate,percentage:-1===r.limit?0:r.used/r.limit*100}}))}catch(e){throw console.error("Error getting all quotas:",e),e}}static async upgradeUserPlan(e,t){try{for(let r of(await s.z.subscription.upsert({where:{userId:e},update:{plan:t,status:"active"},create:{userId:e,plan:t,status:"active"}}),["blog_posts","emails","social_media","youtube_scripts","invincible_research"])){let i=a[t][r];await s.z.userQuota.upsert({where:{userId_quotaType:{userId:e,quotaType:r}},update:{totalLimit:i},create:{userId:e,quotaType:r,totalLimit:i,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}})}}catch(e){throw console.error("Error upgrading user plan:",e),e}}}},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,4973,7449,4612,4940,1630,6311,2087,4785],()=>r(80246));module.exports=s})();