"use strict";(()=>{var e={};e.id=7584,e.ids=[7584],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},12412:e=>{e.exports=require("assert")},16220:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{GET:()=>o,OPTIONS:()=>c});var n=s(32190),a=s(54785),i=e([a]);async function o(e){try{let{searchParams:t}=new URL(e.url),s=t.get("topic"),r=t.get("contentLength"),i=t.get("tone"),o=t.get("targetAudience"),c=t.get("customInstructions");if(!s)return n.NextResponse.json({error:"Topic is required"},{status:400});let l=new TextEncoder,p=new ReadableStream({start(e){let t=(t,s)=>{let r=`event: ${t}
data: ${JSON.stringify(s)}

`;e.enqueue(l.encode(r))};(async()=>{try{t("start",{message:"Initializing Invincible Agent...",timestamp:Date.now(),phase:"initialization"});let n=new a.X({searchDepth:7,competitorCount:10,deepSearchQueriesPerTopic:15,maxContentLength:r?parseInt(r):15e3,temperature:.7,uniquenessLevel:"high",enableNicheLearning:!0}),l=n.log.bind(n);n.log=e=>{l(e);let s=Date.now();if(e.includes("\uD83D\uDD0D STEP 1:"))t("phase",{phase:"primary_search",message:"Starting primary search and data extraction",step:1,total:4,timestamp:s});else if(e.includes("\uD83E\uDDE0 STEP 2:"))t("phase",{phase:"content_analysis",message:"Analyzing content with Gemini AI",step:2,total:4,timestamp:s});else if(e.includes("\uD83D\uDD0E STEP 3:"))t("phase",{phase:"comprehensive_research",message:"Performing comprehensive data scraping",step:3,total:4,timestamp:s});else if(e.includes("✍️ STEP 4:"))t("phase",{phase:"content_generation",message:"Generating superior article with full analysis",step:4,total:4,timestamp:s});else if(e.includes("\uD83D\uDD0D Starting search")){let r=e.match(/Starting search \d+\/\d+: "([^"]+)"/);r&&t("search_query",{query:r[1],message:`Searching: ${r[1]}`,timestamp:s})}else if(e.includes("\uD83D\uDCCA Found")&&e.includes("URLs")){let r=e.match(/Found (\d+) URLs/);r&&t("search_results",{urlCount:parseInt(r[1]),message:`Found ${r[1]} URLs for analysis`,timestamp:s})}else if(e.includes("✅ Scraped:")){let r=e.match(/Scraped: ([^)]+)/);r&&t("scraping",{domain:r[1],message:`Analyzed content from ${r[1]}`,timestamp:s})}else if(e.includes("\uD83D\uDCCB Detected")&&e.includes("article type"))e.match(/article type detected/)&&t("analysis",{type:"article_type",message:"Article type and structure analyzed",timestamp:s});else if(e.includes("✅ Content analysis complete"))t("analysis",{type:"content_complete",message:"Content analysis completed successfully",timestamp:s});else if(e.includes("✅ Competition analysis complete"))t("analysis",{type:"competition_complete",message:"Competition analysis completed",timestamp:s});else if(e.includes("✅ Human writing analysis complete"))t("analysis",{type:"human_writing_complete",message:"Human writing patterns analyzed",timestamp:s});else if(e.includes("\uD83D\uDD04 Fixing")&&e.includes("instances")){let r=e.match(/Fixing (\d+) instances of "([^"]+)"/);r&&t("humanization",{type:"date_fixing",count:parseInt(r[1]),target:r[2],message:`Fixed ${r[1]} instances of repetitive dates`,timestamp:s})}else if(e.includes("\uD83D\uDD04 Fixed")&&e.includes("AI jargon")){let r=e.match(/Fixed (\d+) instances of AI jargon/);r&&t("humanization",{type:"jargon_removal",count:parseInt(r[1]),message:`Removed ${r[1]} instances of AI jargon`,timestamp:s})}else if(e.includes("✅ Applied advanced AI detection bypass")){let r=e.match(/bypass techniques: (.+)$/);if(r){let e=r[1].split(", ");t("humanization",{type:"bypass_complete",improvements:e,message:"AI detection bypass techniques applied",timestamp:s})}}else if(e.includes("\uD83D\uDCCA Final word count:")){let r=e.match(/Final word count: (\d+)/);r&&t("progress",{wordCount:parseInt(r[1]),message:`Generated ${r[1]} words`,timestamp:s})}else if(e.includes("✅ Enhanced Invincible agent completed")){let r=e.match(/completed in (\d+)ms/);t("progress",{message:"Generation process completed!",executionTime:r?parseInt(r[1]):null,timestamp:s})}};let p={taskId:`stream_${Date.now()}`,topic:s,contentLength:r?parseInt(r):2e3,tone:i||"professional",targetAudience:o||"general audience",customInstructions:c||void 0};t("context",{message:"Starting content generation...",context:p,timestamp:Date.now()});let u=await n.execute(p);u.success&&u.article?t("success",{message:"Content generation completed successfully!",article:{title:u.article.title,content:u.article.content,metaDescription:u.article.metaDescription,wordCount:u.article.wordCount,seoScore:u.article.seoScore,readabilityScore:u.article.readabilityScore},stats:{executionTime:u.executionTime,uniquenessScore:u.uniquenessScore,totalSources:u.researchData?.totalSources||0},factCheckReport:u.factCheckReport,timestamp:Date.now()}):t("error",{message:"Content generation failed",error:u.error,timestamp:Date.now()}),t("complete",{message:"Stream completed",timestamp:Date.now()}),e.close()}catch(s){console.error("Streaming error:",s),t("error",{message:"An error occurred during generation",error:s instanceof Error?s.message:"Unknown error",timestamp:Date.now()}),e.close()}})()}});return new Response(p,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}catch(e){return console.error("SSE setup error:",e),n.NextResponse.json({error:"Failed to setup streaming response"},{status:500})}}async function c(){return new Response(null,{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET, OPTIONS","Access-Control-Allow-Headers":"Content-Type"}})}a=(i.then?(await i)():i)[0],r()}catch(e){r(e)}})},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},37830:e=>{e.exports=require("node:stream/web")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{e.exports=require("node:fs")},73566:e=>{e.exports=require("worker_threads")},74075:e=>{e.exports=require("zlib")},75263:e=>{e.exports=import("cheerio")},77936:(e,t,s)=>{s.a(e,async(e,r)=>{try{s.r(t),s.d(t,{patchFetch:()=>l,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>u,workUnitAsyncStorage:()=>d});var n=s(96559),a=s(48088),i=s(37719),o=s(16220),c=e([o]);o=(c.then?(await c)():c)[0];let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/invincible/stream/route",pathname:"/api/invincible/stream",filename:"route",bundlePath:"app/api/invincible/stream/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/invincible/stream/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:u,workUnitAsyncStorage:d,serverHooks:m}=p;function l(){return(0,i.patchFetch)({workAsyncStorage:u,workUnitAsyncStorage:d})}r()}catch(e){r(e)}})},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,580,7449,4612,4940,1630,6311,2087,4785],()=>s(77936));module.exports=r})();