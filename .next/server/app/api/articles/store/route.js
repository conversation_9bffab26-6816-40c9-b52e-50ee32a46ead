(()=>{var e={};e.id=2233,e.ids=[2233],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12269:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0})},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(16467),a=r(36344),i=r(31183);let n={adapter:(0,s.y)(i.z),providers:[(0,a.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"database"},callbacks:{session:async({session:e,user:t})=>(e?.user&&t&&(e.user.id=t.id),e),signIn:async({user:e,account:t,profile:r})=>!0},events:{async createUser({user:e}){try{await i.z.userSettings.findUnique({where:{userId:e.id}})?console.log(`ℹ️ User profile already exists for: ${e.email}`):(await i.z.user.update({where:{id:e.id},data:{firstName:e.name?.split(" ")[0]||"",lastName:e.name?.split(" ").slice(1).join(" ")||"",settings:{create:{}},subscription:{create:{plan:"free",status:"active"}},quotas:{create:[{quotaType:"blog_posts",totalLimit:5,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"emails",totalLimit:10,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"social_media",totalLimit:20,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"youtube_scripts",totalLimit:3,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"invincible_research",totalLimit:2,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}]}}}),console.log(`✅ Created complete user profile for: ${e.email}`))}catch(e){console.error("Error setting up user profile:",e)}}},pages:{signIn:"/auth/signin",error:"/auth/error"},debug:!1}},19082:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>y});var s={};r.r(s),r.d(s,{POST:()=>p});var a=r(96559),i=r(48088),n=r(37719),o=r(32190),u=r(19854),l=r(12909),c=r(31183);async function p(e){try{let t=await (0,u.getServerSession)(l.N);if(!t?.user?.email)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{title:r,content:s,type:a,metadata:i,tone:n,language:p}=await e.json();if(!r||!s||!a)return o.NextResponse.json({error:"Missing required fields: title, content, and type are required"},{status:400});let d=await c.z.user.findUnique({where:{email:t.user.email}});if(!d)return o.NextResponse.json({error:"User not found"},{status:404});let f=s.split(/\s+/).filter(e=>e.length>0).length,y=await c.z.content.create({data:{userId:d.id,title:r,content:s,type:a,metadata:i?JSON.stringify(i):null,wordCount:f,tone:n||"professional",language:p||"en",status:"published"}});return o.NextResponse.json({success:!0,article:{id:y.id,title:y.title,type:y.type,wordCount:y.wordCount,createdAt:y.createdAt},url:`/article-view/${y.id}`})}catch(e){return console.error("Error storing article:",e),o.NextResponse.json({error:"Failed to store article"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/articles/store/route",pathname:"/api/articles/store",filename:"route",bundlePath:"app/api/articles/store/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/articles/store/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:y,serverHooks:g}=d;function w(){return(0,n.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:y})}},19854:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var s={};Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i.default}});var a=r(12269);Object.keys(a).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===a[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}}))});var i=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=n(t);if(r&&r.has(e))return r.get(e);var s={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var i in e)if("default"!==i&&({}).hasOwnProperty.call(e,i)){var o=a?Object.getOwnPropertyDescriptor(e,i):null;o&&(o.get||o.set)?Object.defineProperty(s,i,o):s[i]=e[i]}return s.default=e,r&&r.set(e,s),s}(r(35426));function n(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(n=function(e){return e?r:t})(e)}Object.keys(i).forEach(function(e){!("default"===e||"__esModule"===e||Object.prototype.hasOwnProperty.call(s,e))&&(e in t&&t[e]===i[e]||Object.defineProperty(t,e,{enumerable:!0,get:function(){return i[e]}}))})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,4973],()=>r(19082));module.exports=s})();