(()=>{var e={};e.id=4967,e.ids=[4967],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(16467),a=r(36344),i=r(31183);let o={adapter:(0,s.y)(i.z),providers:[(0,a.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"database"},callbacks:{session:async({session:e,user:t})=>(e?.user&&t&&(e.user.id=t.id),e),signIn:async({user:e,account:t,profile:r})=>!0},events:{async createUser({user:e}){try{await i.z.userSettings.findUnique({where:{userId:e.id}})?console.log(`ℹ️ User profile already exists for: ${e.email}`):(await i.z.user.update({where:{id:e.id},data:{firstName:e.name?.split(" ")[0]||"",lastName:e.name?.split(" ").slice(1).join(" ")||"",settings:{create:{}},subscription:{create:{plan:"free",status:"active"}},quotas:{create:[{quotaType:"blog_posts",totalLimit:5,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"emails",totalLimit:10,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"social_media",totalLimit:20,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"youtube_scripts",totalLimit:3,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"invincible_research",totalLimit:2,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}]}}}),console.log(`✅ Created complete user profile for: ${e.email}`))}catch(e){console.error("Error setting up user profile:",e)}}},pages:{signIn:"/auth/signin",error:"/auth/error"},debug:!1}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60088:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>d,serverHooks:()=>g,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>l});var a=r(96559),i=r(48088),o=r(37719),n=r(32190),u=r(35426),c=r(12909),p=r(31183);async function l(e){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user?.id)return n.NextResponse.json({error:"Authentication required"},{status:401});let[t,r,s,a,i]=await Promise.all([p.z.content.count({where:{userId:e.user.id}}),p.z.content.groupBy({by:["type"],where:{userId:e.user.id},_count:{id:!0}}),p.z.content.count({where:{userId:e.user.id,createdAt:{gte:new Date(Date.now()-6048e5)}}}),p.z.usageHistory.count({where:{userId:e.user.id}}),p.z.userQuota.findMany({where:{userId:e.user.id},select:{quotaType:!0,totalLimit:!0,used:!0,resetDate:!0}})]),o=r.reduce((e,t)=>e+t._count.id*(({blog:30,email:15,youtube_script:45,invincible_research:120})[t.type]||30),0),l=Math.min(9.8,8.5+.1*t),d=r.reduce((e,t)=>(e[t.type]=t._count.id,e),{}),h=i.reduce((e,t)=>{let r=-1===t.totalLimit?0:t.used/t.totalLimit*100;return e[t.quotaType]={used:t.used,limit:t.totalLimit,percentage:Math.round(r),resetDate:t.resetDate},e},{});return n.NextResponse.json({success:!0,stats:{totalContent:t,recentContent:s,totalUsage:a,timeSavedHours:Math.round(o/60),qualityScore:Math.round(10*l)/10,contentBreakdown:d,quotaUtilization:h,trends:{contentGrowth:s>0?"+"+Math.round(s/Math.max(t-s,1)*100)+"%":"+0%",qualityImprovement:"+0.3",timeEfficiency:"+24%",toolsActive:Object.keys(d).length}}})}catch(e){return console.error("Stats fetch error:",e),n.NextResponse.json({error:"Failed to fetch statistics"},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/stats/route",pathname:"/api/stats",filename:"route",bundlePath:"app/api/stats/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/stats/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:g}=d;function y(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,4973],()=>r(60088));module.exports=s})();