(()=>{var e={};e.id=9822,e.ids=[9822],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(16467),a=r(36344),i=r(31183);let o={adapter:(0,s.y)(i.z),providers:[(0,a.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"database"},callbacks:{session:async({session:e,user:t})=>(e?.user&&t&&(e.user.id=t.id),e),signIn:async({user:e,account:t,profile:r})=>!0},events:{async createUser({user:e}){try{await i.z.userSettings.findUnique({where:{userId:e.id}})?console.log(`ℹ️ User profile already exists for: ${e.email}`):(await i.z.user.update({where:{id:e.id},data:{firstName:e.name?.split(" ")[0]||"",lastName:e.name?.split(" ").slice(1).join(" ")||"",settings:{create:{}},subscription:{create:{plan:"free",status:"active"}},quotas:{create:[{quotaType:"blog_posts",totalLimit:5,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"emails",totalLimit:10,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"social_media",totalLimit:20,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"youtube_scripts",totalLimit:3,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"invincible_research",totalLimit:2,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}]}}}),console.log(`✅ Created complete user profile for: ${e.email}`))}catch(e){console.error("Error setting up user profile:",e)}}},pages:{signIn:"/auth/signin",error:"/auth/error"},debug:!1}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60528:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>h,serverHooks:()=>m,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>p,POST:()=>d});var a=r(96559),i=r(48088),o=r(37719),u=r(32190),n=r(35426),c=r(12909),l=r(84299);async function p(e){try{let t=await (0,n.getServerSession)(c.N);if(!t?.user?.id)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("type");if(s){let e=await l.a.checkQuota(t.user.id,s);return u.NextResponse.json(e)}{let e=await l.a.getAllQuotas(t.user.id);return u.NextResponse.json(e)}}catch(e){return console.error("Error fetching quotas:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let t=await (0,n.getServerSession)(c.N);if(!t?.user?.id)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{quotaType:r}=await e.json();if(!r)return u.NextResponse.json({error:"Quota type is required"},{status:400});if(!await l.a.useQuota(t.user.id,r))return u.NextResponse.json({error:"Quota exceeded or insufficient quota"},{status:429});return u.NextResponse.json({success:!0})}catch(e){return console.error("Error using quota:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let h=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/quota/route",pathname:"/api/quota",filename:"route",bundlePath:"app/api/quota/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/quota/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:g,serverHooks:m}=h;function y(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:g})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},84299:(e,t,r)=>{"use strict";r.d(t,{a:()=>i});var s=r(31183);let a={free:{blog_posts:5,emails:10,social_media:20,youtube_scripts:3,invincible_research:2},pro:{blog_posts:50,emails:100,social_media:200,youtube_scripts:25,invincible_research:20},enterprise:{blog_posts:-1,emails:-1,social_media:-1,youtube_scripts:-1,invincible_research:-1}};class i{static async checkQuota(e,t){try{let r=await s.z.user.findUnique({where:{id:e},include:{subscription:!0,quotas:{where:{quotaType:t}}}});if(!r)throw Error("User not found");let i=r.subscription?.plan||"free",o=r.quotas[0];if(!o){let r=a[i][t],o=await s.z.userQuota.create({data:{userId:e,quotaType:t,totalLimit:r,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}});return{hasQuota:-1===r||o.used<r,used:o.used,limit:r,resetDate:o.resetDate}}if(new Date>=o.resetDate){await this.resetQuota(e,t);let r=a[i][t];return{hasQuota:-1===r||0<r,used:0,limit:r,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}}let u=a[i][t];return{hasQuota:-1===u||o.used<u,used:o.used,limit:u,resetDate:o.resetDate}}catch(e){throw console.error("Error checking quota:",e),e}}static async useQuota(e,t){try{let r=await this.checkQuota(e,t);if(!r.hasQuota)return!1;return await s.z.userQuota.update({where:{userId_quotaType:{userId:e,quotaType:t}},data:{used:{increment:1}}}),await s.z.usageHistory.create({data:{userId:e,action:"content_generated",type:t,metadata:JSON.stringify({quotaUsed:r.used+1,quotaLimit:r.limit})}}),!0}catch(e){return console.error("Error using quota:",e),!1}}static async resetQuota(e,t){try{let r=new Date;r.setMonth(r.getMonth()+1,1),r.setHours(0,0,0,0),await s.z.userQuota.update({where:{userId_quotaType:{userId:e,quotaType:t}},data:{used:0,resetDate:r}})}catch(e){throw console.error("Error resetting quota:",e),e}}static async getAllQuotas(e){try{let t=await s.z.user.findUnique({where:{id:e},include:{subscription:!0,quotas:!0}});if(!t)throw Error("User not found");return t.subscription?.plan,await Promise.all(["blog_posts","emails","social_media","youtube_scripts","invincible_research"].map(async t=>{let r=await this.checkQuota(e,t);return{quotaType:t,used:r.used,limit:r.limit,resetDate:r.resetDate,percentage:-1===r.limit?0:r.used/r.limit*100}}))}catch(e){throw console.error("Error getting all quotas:",e),e}}static async upgradeUserPlan(e,t){try{for(let r of(await s.z.subscription.upsert({where:{userId:e},update:{plan:t,status:"active"},create:{userId:e,plan:t,status:"active"}}),["blog_posts","emails","social_media","youtube_scripts","invincible_research"])){let i=a[t][r];await s.z.userQuota.upsert({where:{userId_quotaType:{userId:e,quotaType:r}},update:{totalLimit:i},create:{userId:e,quotaType:r,totalLimit:i,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}})}}catch(e){throw console.error("Error upgrading user plan:",e),e}}}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,4973],()=>r(60528));module.exports=s})();