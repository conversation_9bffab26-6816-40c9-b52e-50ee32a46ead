(()=>{var e={};e.id=397,e.ids=[397],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11477:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>g,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};r.r(s),r.d(s,{GET:()=>l,PUT:()=>d});var i=r(96559),a=r(48088),o=r(37719),n=r(32190),u=r(35426),p=r(12909),c=r(31183);async function l(){try{let e=await (0,u.getServerSession)(p.N);if(!e?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let t=await c.z.user.findUnique({where:{id:e.user.id},include:{settings:!0,subscription:!0,quotas:!0,_count:{select:{content:!0,usageHistory:!0}}}});if(!t)return n.NextResponse.json({error:"User not found"},{status:404});return n.NextResponse.json({id:t.id,name:t.name,email:t.email,image:t.image,firstName:t.firstName,lastName:t.lastName,bio:t.bio,createdAt:t.createdAt,settings:t.settings,subscription:t.subscription,quotas:t.quotas,stats:{totalContent:t._count.content,totalUsage:t._count.usageHistory}})}catch(e){return console.error("Error fetching user profile:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let t=await (0,u.getServerSession)(p.N);if(!t?.user?.id)return n.NextResponse.json({error:"Unauthorized"},{status:401});let{firstName:r,lastName:s,bio:i}=await e.json(),a=await c.z.user.update({where:{id:t.user.id},data:{firstName:r,lastName:s,bio:i,name:r&&s?`${r} ${s}`:void 0},include:{settings:!0,subscription:!0}});return n.NextResponse.json({id:a.id,name:a.name,email:a.email,image:a.image,firstName:a.firstName,lastName:a.lastName,bio:a.bio,settings:a.settings,subscription:a.subscription})}catch(e){return console.error("Error updating user profile:",e),n.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/user/profile/route",pathname:"/api/user/profile",filename:"route",bundlePath:"app/api/user/profile/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/user/profile/route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:f}=g;function w(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},12909:(e,t,r)=>{"use strict";r.d(t,{N:()=>o});var s=r(16467),i=r(36344),a=r(31183);let o={adapter:(0,s.y)(a.z),providers:[(0,i.A)({clientId:process.env.GOOGLE_CLIENT_ID,clientSecret:process.env.GOOGLE_CLIENT_SECRET,authorization:{params:{prompt:"consent",access_type:"offline",response_type:"code"}}})],session:{strategy:"database"},callbacks:{session:async({session:e,user:t})=>(e?.user&&t&&(e.user.id=t.id),e),signIn:async({user:e,account:t,profile:r})=>!0},events:{async createUser({user:e}){try{await a.z.userSettings.findUnique({where:{userId:e.id}})?console.log(`ℹ️ User profile already exists for: ${e.email}`):(await a.z.user.update({where:{id:e.id},data:{firstName:e.name?.split(" ")[0]||"",lastName:e.name?.split(" ").slice(1).join(" ")||"",settings:{create:{}},subscription:{create:{plan:"free",status:"active"}},quotas:{create:[{quotaType:"blog_posts",totalLimit:5,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"emails",totalLimit:10,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"social_media",totalLimit:20,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"youtube_scripts",totalLimit:3,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)},{quotaType:"invincible_research",totalLimit:2,resetDate:new Date(new Date().getFullYear(),new Date().getMonth()+1,1)}]}}}),console.log(`✅ Created complete user profile for: ${e.email}`))}catch(e){console.error("Error setting up user profile:",e)}}},pages:{signIn:"/auth/signin",error:"/auth/error"},debug:!1}},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31183:(e,t,r)=>{"use strict";r.d(t,{z:()=>i});var s=r(96330);let i=globalThis.prisma??new s.PrismaClient({log:["query"]})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,580,4973],()=>r(11477));module.exports=s})();