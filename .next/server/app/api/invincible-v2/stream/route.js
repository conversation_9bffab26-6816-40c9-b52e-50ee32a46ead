"use strict";(()=>{var e={};e.id=6197,e.ids=[6197],e.modules={618:(e,t,n)=>{n.d(t,{R:()=>i});class i{constructor(e){this.sessionId=e,this.persistencePath=`./memory/${e}`,this.memories=new Map,this.accessLog=[],this.typeIndex=new Map,this.keywordIndex=new Map,this.temporalIndex=new Map,this.initializeMemoryTypes(),this.loadPersistentMemory()}initializeMemoryTypes(){["core","episodic","semantic","procedural","tool","improvement"].forEach(e=>{this.memories.set(e,[]),this.typeIndex.set(e,new Set)})}async store(e){let t=this.generateMemoryId(),n={...e,id:t,timestamp:Date.now(),accessCount:0,lastAccessed:Date.now()},i=this.memories.get(e.type)||[];return i.push(n),this.memories.set(e.type,i),this.updateIndices(n),i.length>1e3&&await this.consolidateMemories(e.type),t}async storeMemory(e){return await this.store(e)}async retrieve(e){let t=this.getAllMemories();e.type&&e.type.length>0&&(t=t.filter(t=>e.type.includes(t.type))),e.keywords&&e.keywords.length>0&&(t=this.filterByKeywords(t,e.keywords)),e.timeRange&&(t=t.filter(t=>t.timestamp>=e.timeRange.start&&t.timestamp<=e.timeRange.end));let n=await this.scoreRelevance(t,e);e.relevanceThreshold&&n.filter(t=>t.relevance>=e.relevanceThreshold),n.sort((e,t)=>{let n=t.relevance-e.relevance;return Math.abs(n)>.1?n:t.timestamp-e.timestamp});let i=n.slice(0,e.limit||100);return i.forEach(e=>{e.accessCount++,e.lastAccessed=Date.now(),this.logAccess(e.type)}),i}async getRelevantMemories(e,t=10){let n=this.extractKeywords(e);return this.retrieve({keywords:n,relevanceThreshold:.5,limit:t})}async storeResearch(e){if(await this.store({type:"semantic",content:e,relevance:.9,metadata:{topic:e.topic,sources:e.sources?.length||0,insights:e.insights?.length||0}}),e.insights)for(let t of e.insights)await this.store({type:"semantic",content:t,relevance:.8,metadata:{topic:e.topic,source:"research_insight"}});if(e.competitorAnalysis&&"object"==typeof e.competitorAnalysis){if(Array.isArray(e.competitorAnalysis.topCompetitors))for(let t of e.competitorAnalysis.topCompetitors)await this.store({type:"episodic",content:t,relevance:.85,metadata:{topic:e.topic,source:"competitor_analysis",url:t.url||"unknown"}});if(Array.isArray(e.competitorAnalysis.commonThemes))for(let t of e.competitorAnalysis.commonThemes)await this.store({type:"episodic",content:t,relevance:.8,metadata:{topic:e.topic,source:"competitor_themes",type:"theme"}});if(Array.isArray(e.competitorAnalysis.contentGaps))for(let t of e.competitorAnalysis.contentGaps)await this.store({type:"episodic",content:t,relevance:.9,metadata:{topic:e.topic,source:"content_gaps",type:"opportunity"}});if(Array.isArray(e.competitorAnalysis.advantages))for(let t of e.competitorAnalysis.advantages)await this.store({type:"episodic",content:t,relevance:.7,metadata:{topic:e.topic,source:"competitive_advantages",type:"advantage"}})}else if(Array.isArray(e.competitorAnalysis))for(let t of e.competitorAnalysis)await this.store({type:"episodic",content:t,relevance:.85,metadata:{topic:e.topic,source:"competitor_analysis",url:t.url||"unknown"}})}async storeLearnings(e){if(await this.store({type:"improvement",content:e,relevance:.95,metadata:{sessionId:this.sessionId,improvements:e.improvements?.length||0,patterns:e.patterns?.length||0}}),e.patterns)for(let t of e.patterns)await this.store({type:"procedural",content:t,relevance:t.effectiveness||.7,metadata:{patternType:t.type,frequency:t.frequency}})}async getSuccessfulPatterns(e=.7){return(await this.retrieve({type:["procedural"],relevanceThreshold:e})).map(e=>e.content).filter(t=>t.effectiveness>=e)}async getWritingPatterns(){return(await this.retrieve({type:["procedural"],keywords:["writing","style","pattern","technique"]})).map(e=>e.content)}async consolidateMemories(e){let t=this.memories.get(e)||[],n=Date.now()-2592e6,i=t.filter(e=>e.relevance>.3||e.timestamp>n||e.accessCount>5),r=await this.mergeSimilarMemories(i);this.memories.set(e,r),this.rebuildIndices()}async mergeSimilarMemories(e){let t=[],n=new Set;for(let i of e){if(n.has(i.id))continue;let r=e.filter(e=>e.id!==i.id&&!n.has(e.id)&&this.calculateSimilarity(i,e)>.8);r.length>0&&(t.push([i,...r]),n.add(i.id),r.forEach(e=>n.add(e.id)))}let i=[];for(let e of t){let t=this.mergeMemoryGroup(e);i.push(t)}return e.forEach(e=>{n.has(e.id)||i.push(e)}),i}calculateSimilarity(e,t){let n=JSON.stringify(e.content).toLowerCase(),i=JSON.stringify(t.content).toLowerCase(),r=new Set(n.split(/\s+/)),s=new Set(i.split(/\s+/)),a=new Set([...r].filter(e=>s.has(e))),o=new Set([...r,...s]);return a.size/o.size}mergeMemoryGroup(e){e.sort((e,t)=>{let n=t.relevance-e.relevance;return Math.abs(n)>.1?n:t.accessCount-e.accessCount});let t=e[0];return{...t,accessCount:e.reduce((e,t)=>e+t.accessCount,0),metadata:{...t.metadata,mergedFrom:e.map(e=>e.id),mergedCount:e.length}}}updateIndices(e){let t=this.typeIndex.get(e.type)||new Set;t.add(e.id),this.typeIndex.set(e.type,t),this.extractKeywords(JSON.stringify(e.content)).forEach(t=>{let n=this.keywordIndex.get(t)||new Set;n.add(e.id),this.keywordIndex.set(t,n)});let n=Math.floor(e.timestamp/864e5),i=this.temporalIndex.get(n)||new Set;i.add(e.id),this.temporalIndex.set(n,i)}rebuildIndices(){this.typeIndex.clear(),this.keywordIndex.clear(),this.temporalIndex.clear(),this.getAllMemories().forEach(e=>{this.updateIndices(e)})}getAllMemories(){let e=[];return this.memories.forEach(t=>{e.push(...t)}),e}filterByKeywords(e,t){let n=new Set;return t.forEach(e=>{let t=this.keywordIndex.get(e.toLowerCase());t&&t.forEach(e=>n.add(e))}),e.filter(e=>n.has(e.id))}async scoreRelevance(e,t){return e.map(e=>{let t=e.relevance;return t*=(.5+.5*Math.exp(-(Date.now()-e.timestamp)/6048e5))*(.8+.2*Math.min(1,e.accessCount/10)),{...e,relevance:t}})}extractKeywords(e){let t=e.toLowerCase().replace(/[^\w\s]/g," ").split(/\s+/).filter(e=>e.length>3),n=new Set(["this","that","these","those","which","where","when","what","with","from","into","through"]);return[...new Set(t.filter(e=>!n.has(e)))]}generateMemoryId(){return`mem_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}logAccess(e){let t=this.accessLog.find(t=>t.memoryType===e);t?(t.frequency++,t.lastAccess=Date.now()):this.accessLog.push({memoryType:e,frequency:1,lastAccess:Date.now(),averageRelevance:0})}getAccessStats(){let e={totalEntries:0,entriesByType:{},averageRelevance:0,accessPatterns:this.accessLog,storageSize:0},t=0;return this.memories.forEach((n,i)=>{e.entriesByType[i]=n.length,e.totalEntries+=n.length,n.forEach(n=>{t+=n.relevance,e.storageSize+=JSON.stringify(n).length})}),e.totalEntries>0&&(e.averageRelevance=t/e.totalEntries),e}async loadPersistentMemory(){try{console.log(`Loading memory for session: ${this.sessionId}`)}catch(e){console.error("Failed to load persistent memory:",e)}}async persist(){try{console.log(`Persisting memory for session: ${this.sessionId}`)}catch(e){console.error("Failed to persist memory:",e)}}}},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4897:(e,t,n)=>{n.d(t,{m:()=>i});class i{constructor(e){this.learningRate=.1,this.explorationRate=.2,this.patternThreshold=3,this.memorySystem=e,this.patterns=new Map,this.objectives=new Map,this.performanceHistory=[],this.feedbackHistory=[],this.initializeObjectives()}initializeObjectives(){[{id:"speed",description:"Reduce article generation time",metrics:["executionTime"],targetValue:3e5,currentValue:6e5,priority:.7},{id:"quality",description:"Improve article quality scores",metrics:["seoScore","readabilityScore"],targetValue:95,currentValue:85,priority:.9},{id:"confidence",description:"Increase confidence in generated content",metrics:["confidence"],targetValue:.95,currentValue:.8,priority:.8},{id:"efficiency",description:"Reduce tool invocations",metrics:["toolInvocations"],targetValue:20,currentValue:40,priority:.6}].forEach(e=>{this.objectives.set(e.id,e)})}async learn(e){this.feedbackHistory.push(e),"success"===e.outcome&&await this.extractAndStorePatterns(e),this.updateObjectives(e.metrics),"failure"===e.outcome&&await this.analyzeFailure(e),this.adjustLearningParameters(e),await this.memorySystem.storeLearnings({feedback:e,patterns:Array.from(this.patterns.values()),objectives:Array.from(this.objectives.values()),timestamp:Date.now()})}async extractAndStorePatterns(e){for(let t of e.patterns){let e=this.patterns.get(t.id);e?(e.frequency++,e.effectiveness=(e.effectiveness*(e.frequency-1)+t.effectiveness)/e.frequency):this.patterns.set(t.id,t),t.frequency>=this.patternThreshold&&await this.memorySystem.store({type:"procedural",content:t,relevance:t.effectiveness,metadata:{source:"learning_system",patternType:t.type}})}}updateObjectives(e){for(let[t,n]of this.objectives){let t=0,i=0;for(let r of n.metrics)r in e&&(t+=this.calculateProgress(n.currentValue,e[r],n.targetValue),i++);if(i>0){let r=t/i;n.currentValue=n.currentValue*(1-this.learningRate)+e[n.metrics[0]]*this.learningRate,r<.5?n.priority=Math.min(1,1.1*n.priority):r>.8&&(n.priority=Math.max(.1,.9*n.priority))}}}calculateProgress(e,t,n){return Math.max(0,Math.min(1,(t-e)/(n-e)))}async analyzeFailure(e){let t=[];for(let n of e.patterns)n.effectiveness<.3&&t.push({...n,id:`anti_${n.id}`,description:`Avoid: ${n.description}`,effectiveness:1-n.effectiveness});for(let e of t)this.patterns.set(e.id,e);let n=await this.generateImprovements(e,t);await this.memorySystem.store({type:"improvement",content:{failure:e,antiPatterns:t,improvements:n},relevance:.9,metadata:{source:"failure_analysis",sessionId:e.sessionId}})}async generateImprovements(e,t){let n=[];for(let[t,i]of Object.entries(e.metrics)){let e=Array.from(this.objectives.values()).find(e=>e.metrics.includes(t));e&&i>1.5*e.targetValue&&n.push({area:t,suggestion:`Reduce ${t} by optimizing related processes`,expectedImpact:.3,confidence:.7,basedOn:[`High ${t}: ${i}`]})}for(let e of t)n.push({area:e.type,suggestion:e.description,expectedImpact:e.effectiveness,confidence:.8,basedOn:[`Pattern frequency: ${e.frequency}`]});return n}adjustLearningParameters(e){"success"===e.outcome?this.explorationRate=Math.max(.05,.95*this.explorationRate):this.explorationRate=Math.min(.5,1.1*this.explorationRate);let t=this.feedbackHistory.slice(-10),n=t.filter(e=>"success"===e.outcome).length/t.length;n>.8?this.learningRate=Math.max(.01,.9*this.learningRate):n<.5&&(this.learningRate=Math.min(.3,1.1*this.learningRate))}async getImprovementSuggestions(){let e=[];for(let t of this.objectives.values()){let n=(t.targetValue-t.currentValue)/t.targetValue;n>.2&&e.push(`Focus on ${t.description} - currently at ${Math.round(100*n)}% of target`)}for(let t of Array.from(this.patterns.values()).filter(e=>e.effectiveness>.7).sort((e,t)=>t.effectiveness-e.effectiveness).slice(0,3))e.push(`Apply pattern: ${t.description} (${Math.round(100*t.effectiveness)}% effective)`);return e}getConfidenceBoost(e){let t=Array.from(this.patterns.values()).filter(t=>t.effectiveness>.7&&t.conditions.topic?.includes(e.toLowerCase()));return 0===t.length?0:.1*(t.reduce((e,t)=>e+t.effectiveness,0)/t.length)}getPlanningAdjustments(){return{explorationRate:this.explorationRate,learningRate:this.learningRate,patterns:Array.from(this.patterns.values()).filter(e=>e.effectiveness>.6),objectives:Array.from(this.objectives.values()).sort((e,t)=>t.priority-e.priority)}}async learnFromPlanningIssues(e){for(let t of e){let e={id:`issue_${Date.now()}_${Math.random()}`,type:"planning_issue",description:t,frequency:1,effectiveness:.2,conditions:{context:"planning",issue:!0}};this.patterns.set(e.id,e)}}async logCorrectinFailure(e,t){let n={id:`correction_failure_${Date.now()}`,type:"correction_failure",description:"Failed to correct content issues",frequency:1,effectiveness:.1,conditions:{issues:e.map(e=>e.type),verification:t}};this.patterns.set(n.id,n),await this.memorySystem.store({type:"improvement",content:{type:"correction_failure",issues:e,verification:t,pattern:n},relevance:.8,metadata:{source:"correction_failure",timestamp:Date.now()}})}async analyzePerformance(e,t,n){let i={speed:this.calculateSpeed(t),accuracy:this.calculateAccuracy(e),completeness:this.calculateCompleteness(e),originality:e.originalityScore||.8,userSatisfaction:void 0,competitorBenchmark:e.competitorSuperiority||.85};return this.performanceHistory.push(i),i}async extractLearnings(e,t,n){if(!Array.isArray(e))return{performance:e,patterns:this.extractPatternsFromContent(t,n),insights:this.generateInsights(e),improvements:await this.generateImprovements({sessionId:n.taskId,outcome:e.accuracy>.8?"success":"partial",metrics:e,improvements:[],patterns:[]},[])};{let n=this.extractPerformanceFromHistory(e);return{performance:n,patterns:this.extractPatternsFromContent(t,{executionHistory:e}),insights:this.generateInsights(n),improvements:await this.generateImprovements({sessionId:"memory_extraction",outcome:n.accuracy>.8?"success":"partial",metrics:n,improvements:[],patterns:[]},[]),significance:this.calculateSignificance(n,t)}}}async adjustBehavior(e,t){e.performance.speed>6e5&&(t.parallelismLevel=Math.min(16,t.parallelismLevel+2)),e.performance.accuracy<.8&&(t.confidenceThreshold=Math.min(.98,t.confidenceThreshold+.02));let n={id:`adjustment_${Date.now()}`,type:"config_adjustment",description:"Configuration adjustment based on performance",frequency:1,effectiveness:.5,conditions:{performance:e.performance,adjustments:{parallelismLevel:t.parallelismLevel,confidenceThreshold:t.confidenceThreshold}}};this.patterns.set(n.id,n)}calculateSpeed(e){if(0===e.length)return 0;let t=e[0].timestamp;return e[e.length-1].timestamp-t}calculateAccuracy(e){return e.accuracyScore||.85}calculateCompleteness(e){return e.completenessScore||.9}extractPatternsFromContent(e,t){let n=[];return e.seoScore>90&&n.push({id:`seo_success_${Date.now()}`,type:"seo_optimization",description:"Successful SEO optimization approach",frequency:1,effectiveness:e.seoScore/100,conditions:{topic:t.topic,wordCount:e.wordCount}}),n}generateInsights(e){let t=[];return e.speed<3e5&&t.push("Achieved target speed for content generation"),e.accuracy>.9&&t.push("High accuracy achieved in content generation"),e.competitorBenchmark&&e.competitorBenchmark>.9&&t.push("Successfully surpassed competitor benchmarks"),t}extractPerformanceFromHistory(e){return{accuracy:.8,speed:e.length,completeness:.85,originality:.9}}calculateSignificance(e,t){let n=[e.accuracy||0,e.completeness||0,t.competitorSuperiority||0,t.seoScore?t.seoScore/100:0];return n.reduce((e,t)=>e+t,0)/n.length}async persist(){let e={patterns:Array.from(this.patterns.entries()),objectives:Array.from(this.objectives.entries()),performanceHistory:this.performanceHistory.slice(-100),feedbackHistory:this.feedbackHistory.slice(-100),parameters:{learningRate:this.learningRate,explorationRate:this.explorationRate,patternThreshold:this.patternThreshold}};await this.memorySystem.store({type:"core",content:e,relevance:1,metadata:{source:"learning_system",version:"2.0"}})}}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{e.exports=require("assert")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},37704:(e,t,n)=>{n.d(t,{$:()=>i});class i{constructor(e){this.geminiService=e,this.reasoningHistory=new Map,this.uncertaintyGraph=new Map}async think(e){let t=await this.generateThought(e),n=await this.selectAction(e,t);await this.predictObservation(n,e);let i={thought:t,action:JSON.stringify(n),observation:"",confidence:await this.calculateConfidence(t,n,e),alternatives:await this.generateAlternatives(e,n)},r=this.reasoningHistory.get(e.goal)||[];return r.push(i),this.reasoningHistory.set(e.goal,r),i}async reflect(e,t,n){let i=await this.generateReflection(e,t,n);return e.observation=JSON.stringify(t),e.reflection=i,await this.updateUncertainty(e,t),i}async generateThought(e){let t=`You are an advanced reasoning agent. Given the current context, generate a thought about what to do next.

Goal: ${e.goal}

Constraints:
${e.constraints.map((e,t)=>`${t+1}. ${e}`).join("\n")}

Available Actions:
${e.availableActions.map((e,t)=>`${t+1}. ${e}`).join("\n")}

Current State:
${JSON.stringify(e.currentState,null,2)}

Previous Steps:
${e.history.slice(-3).map((e,t)=>`Step ${t+1}:
  Thought: ${e.thought}
  Action: ${e.action}
  Observation: ${e.observation}
  ${e.reflection?`Reflection: ${e.reflection}`:""}`).join("\n\n")}

Generate a clear, specific thought about what to do next. Consider:
1. What information is still uncertain?
2. What action would reduce the most uncertainty?
3. Are we making progress toward the goal?
4. Should we try a different approach?

Thought:`;return(await this.geminiService.generateContent(t,{temperature:.7,maxOutputTokens:1e3})).response.trim()}async selectAction(e,t){let n=`Based on this thought, select the best action to take.

Thought: ${t}

Available Actions:
${e.availableActions.map((e,t)=>`${t+1}. ${e}`).join("\n")}

Current Goal: ${e.goal}

Select ONE action and provide:
1. Action type (from available actions)
2. Specific parameters for the action
3. What you expect to observe
4. Why this reduces uncertainty

Format as JSON:
{
  "type": "action_type",
  "description": "what this action does",
  "parameters": { ... },
  "expectedOutcome": "what we expect to observe"
}`,i=await this.geminiService.generateContent(n,{temperature:.3,maxOutputTokens:1e3});try{return JSON.parse(i.response)}catch{return{type:"think",description:"Continue reasoning",parameters:{},expectedOutcome:"Better understanding of the problem"}}}async predictObservation(e,t){return e.expectedOutcome}async calculateConfidence(e,t,n){let i=.5;e.toLowerCase().includes(n.goal.toLowerCase())&&(i+=.1),Object.keys(t.parameters).length>0&&(i+=.1),n.history.length>0&&n.history.reduce((e,t)=>e+t.confidence,0)/n.history.length<.7&&(i+=.1);let r=Array.from(this.uncertaintyGraph.values()).filter(e=>!e.resolved).length;return r<5?i+=.2:r<10&&(i+=.1),Math.min(.95,Math.max(.1,i))}async generateAlternatives(e,t){return e.availableActions.filter(e=>!e.includes(t.type)).slice(0,3)}async generateReflection(e,t,n){let i=`Reflect on this reasoning step and observation.

Thought: ${e.thought}
Action: ${e.action}
Expected: ${JSON.parse(e.action).expectedOutcome}
Observed: ${t.content}

Questions to consider:
1. Did the observation match expectations?
2. What new information was gained?
3. What uncertainties were resolved?
4. What new questions arose?
5. Are we closer to the goal?

Provide a brief, insightful reflection:`;return(await this.geminiService.generateContent(i,{temperature:.5,maxOutputTokens:500})).response.trim()}async updateUncertainty(e,t){for(let e of(await this.extractUncertainties(t.content))){let n=this.uncertaintyGraph.get(e.id);n?t.confidence>.8&&e.resolved&&(n.resolved=!0,n.resolution=t.content):this.uncertaintyGraph.set(e.id,e)}let n=Date.now();for(let[e,i]of this.uncertaintyGraph)i.resolved&&t.timestamp-n>36e5&&this.uncertaintyGraph.delete(e)}async extractUncertainties(e){let t=`Extract any uncertainties or open questions from this content:

${e}

For each uncertainty, provide:
1. A unique ID
2. The question or uncertainty
3. Its importance (0-1)
4. Whether it seems resolved
5. Any dependencies on other uncertainties

Format as JSON array of uncertainty nodes.`;try{let e=await this.geminiService.generateContent(t,{temperature:.3,maxOutputTokens:1e3});return JSON.parse(e.response)}catch{return[]}}getUncertaintyLevel(){let e=Array.from(this.uncertaintyGraph.values()).filter(e=>!e.resolved);return 0===e.length?0:e.reduce((e,t)=>e+t.importance,0)/e.length}getMostImportantUncertainty(){return Array.from(this.uncertaintyGraph.values()).filter(e=>!e.resolved).sort((e,t)=>t.importance-e.importance)[0]||null}async monteCarloSearch(e,t=100){let n={state:e.currentState,visits:0,value:0,children:new Map,action:null};for(let i=0;i<t;i++)await this.simulate(n,e);let i=null,r=0;for(let e of n.children.values())e.visits>r&&(r=e.visits,i=e);return i?.action||{type:"think",description:"No clear action found",parameters:{},expectedOutcome:"Need more information"}}async simulate(e,t){if(e.children.size>0&&e.visits>0){let n=this.selectBestChild(e);return await this.simulate(n,t)}if(e.visits>0&&(await this.expandNode(e,t),e.children.size>0)){let n=Array.from(e.children.values())[0];return await this.simulate(n,t)}let n=await this.rollout(e,t);return e.visits++,e.value+=n,n}selectBestChild(e){let t=null,n=-1/0;for(let i of e.children.values()){let r=i.value/(i.visits+1)+Math.sqrt(2*Math.log(e.visits+1)/(i.visits+1));r>n&&(n=r,t=i)}return t}async expandNode(e,t){let n=await this.generateThought({...t,currentState:e.state});for(let i of(await this.generatePossibleActions(t,n)).slice(0,3)){let t={state:this.applyAction(e.state,i),visits:0,value:0,children:new Map,action:i};e.children.set(i.type,t)}}async generatePossibleActions(e,t){let n=[];for(let t of e.availableActions)n.push({type:t,description:`Execute ${t}`,parameters:{},expectedOutcome:`Result of ${t}`});return n}applyAction(e,t){return{...e,lastAction:t.type,actionCount:(e.actionCount||0)+1}}async rollout(e,t){let n=e.state,i=0;for(;i<10&&!this.isTerminal(n,t);){let e=Math.floor(Math.random()*t.availableActions.length),r={type:t.availableActions[e],description:"Rollout action",parameters:{},expectedOutcome:"Unknown"};n=this.applyAction(n,r),i++}return this.evaluateState(n,t)}isTerminal(e,t){return e.actionCount>=20||!0===e.goalReached}evaluateState(e,t){let n=0;return e.goalReached&&(n+=1),n+=.5/(e.actionCount+1)+(1-this.getUncertaintyLevel())*.3}async analyzeError(e){let t={type:e.name||"UnknownError",message:e.message||"No error message",stack:e.stack||"",timestamp:Date.now()},n=`Analyze this error and suggest recovery strategies:

Error Type: ${t.type}
Message: ${t.message}
Stack: ${t.stack?.split("\n").slice(0,5).join("\n")}

Suggest:
1. Root cause
2. Severity (low/medium/high)
3. Recovery strategies
4. Preventive measures

Format as JSON.`;try{let e=await this.geminiService.generateContent(n,{temperature:.3,maxOutputTokens:1e3});return JSON.parse(e.response)}catch{return{rootCause:"Unknown",severity:"medium",recoveryStrategies:["retry","fallback"],preventiveMeasures:["better error handling"]}}}clearHistory(e){this.reasoningHistory.delete(e),Array.from(this.uncertaintyGraph.entries()).filter(([t,n])=>n.question.includes(e)).forEach(([e])=>this.uncertaintyGraph.delete(e))}}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73854:(e,t,n)=>{n.a(e,async(e,i)=>{try{n.d(t,{rH:()=>u});var r=n(93356),s=n(618),a=n(77276),o=n(37704),c=n(4897),l=e([a]);a=(l.then?(await l)():l)[0];class u{constructor(e={}){this.config={autonomyLevel:"full",confidenceThreshold:.95,maxIterations:50,temperature:.8,enableWebSearch:!0,enableSelfImprovement:!0,enableVisualization:!0,enableMemoryConsolidation:!0,minWordCount:2e3,maxWordCount:1e4,seoTargetScore:95,readabilityTarget:85,...e},this.sessionId=`v2_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,this.executionStartTime=Date.now(),this.geminiService=new r.p,this.memorySystem=new s.R(this.sessionId),this.toolOrchestrator=new a.f(this.config),this.reasoningEngine=new o.$(this.geminiService),this.learningSystem=new c.m(this.memorySystem),this.initializeModernArchitecture()}initializeModernArchitecture(){this.stateManager={currentPhase:"reasoning",activeAgents:new Set,sharedContext:new Map,messageQueue:[],executionHistory:[]},this.communicationHub=new h(this.stateManager),this.supervisor=new p(this.geminiService,this.reasoningEngine,this.communicationHub,this.config),this.agents=new Map,this.agents.set("research",new d(this.geminiService,this.toolOrchestrator,this.communicationHub)),this.agents.set("planning",new g(this.geminiService,this.reasoningEngine,this.communicationHub)),this.agents.set("content",new y(this.geminiService,this.toolOrchestrator,this.communicationHub)),this.agents.set("verification",new f(this.geminiService,this.reasoningEngine,this.communicationHub)),this.agents.set("memory",new v(this.memorySystem,this.learningSystem,this.communicationHub)),this.communicationHub.registerAgent("supervisor",this.supervisor),this.agents.forEach((e,t)=>{this.communicationHub.registerAgent(t,e)})}async execute(e){let t=Date.now();try{this.log("\uD83D\uDE80 Invincible V2 Agent Starting - 2025 Autonomous Architecture"),this.log(`📝 Topic: ${e.topic}`),this.log(`🎯 Target: ${e.contentLength||this.config.minWordCount} words`),this.log(`🤖 Autonomy Level: ${this.config.autonomyLevel}`),this.stateManager.sharedContext.set("taskContext",e),this.stateManager.sharedContext.set("config",this.config);let n=await this.executeAutonomousWorkflow(e),i=Date.now()-t,r=await this.learningSystem.getImprovementSuggestions();return{success:!0,article:n.content,reasoning:this.stateManager.executionHistory,confidence:n.confidence,improvements:r||[],metrics:{executionTime:i,agentInvocations:this.getAgentInvocations(),toolUsage:this.toolOrchestrator.getUsageStats(),memoryAccess:this.memorySystem.getAccessStats()||{}},sessionId:this.sessionId}}catch(n){this.log(`❌ Critical Error: ${n}`);let t=await this.attemptAutonomousRecovery(n,e);if(t.success)return t.result;return{success:!1,error:n instanceof Error?n.message:"Unknown error",reasoning:this.stateManager.executionHistory,confidence:0,improvements:[],sessionId:this.sessionId}}}async executeAutonomousWorkflow(e){this.log("\uD83C\uDFAF Phase 1: Autonomous Planning & Task Decomposition");let t=await this.supervisor.initiateAutonomousPlanning(e);this.stateManager.sharedContext.set("masterPlan",t),this.log("\uD83D\uDD0D Phase 2: Parallel Research & Data Gathering");let n=await this.executeParallelResearch(t);this.stateManager.sharedContext.set("research",n),this.log("\uD83D\uDCCB Phase 3: Content Planning & Architecture");let i=await this.executeContentPlanning(t,n);this.stateManager.sharedContext.set("contentPlan",i),this.log("✍️ Phase 4: Autonomous Content Generation");let r=await this.executeAutonomousGeneration(i,n);this.stateManager.sharedContext.set("generatedContent",r),this.log("\uD83D\uDD0E Phase 5: Quality Verification & Enhancement");let s=await this.executeVerificationAndEnhancement(r);return this.log("\uD83E\uDDE0 Phase 6: Learning & Memory Consolidation"),await this.executeMemoryConsolidation(s),{content:s,confidence:this.calculateFinalConfidence(s)}}async executeParallelResearch(e){let t=this.agents.get("research"),n=this.agents.get("memory"),i=await n.getRelevantContext(e.topic||e),r=await t.executeAutonomousResearch({plan:e,memoryContext:i,config:this.config});return await n.storeResearchFindings(r),r}async executeContentPlanning(e,t){let n=this.agents.get("planning");return await n.executeAutonomousPlanning({masterPlan:e,research:t,config:this.config})}async executeAutonomousGeneration(e,t){let n=this.agents.get("content");return await n.executeAutonomousGeneration({contentPlan:e,research:t,config:this.config})}async executeVerificationAndEnhancement(e){let t=this.agents.get("verification"),n=e,i=0;for(;i<3;){let e=await t.executeAutonomousVerification({content:n,config:this.config});if(e.confidence>=this.config.confidenceThreshold)break;n=await t.applyAutonomousImprovements(n,e.improvements),i++}return n}async executeMemoryConsolidation(e){let t=this.agents.get("memory");await t.consolidateExecution({content:e,executionHistory:this.stateManager.executionHistory,sharedContext:this.stateManager.sharedContext})}calculateFinalConfidence(e){let t=[e.seoScore||0,e.readabilityScore||0,e.originalityScore||0,e.competitorSuperiority||0];return t.reduce((e,t)=>e+t,0)/t.length/100}async attemptAutonomousRecovery(e,t){this.log("\uD83D\uDEA8 Attempting autonomous recovery");try{if((await this.supervisor.analyzeAndRecover(e,t)).canRecover){this.log("\uD83D\uDD04 Executing recovery strategy");let e=await this.executeAutonomousWorkflow(t);return{success:!0,result:{success:!0,article:e.content,reasoning:this.stateManager.executionHistory,confidence:e.confidence,improvements:await this.learningSystem.getImprovementSuggestions()||[],metrics:{executionTime:Date.now()-this.executionStartTime,agentInvocations:this.getAgentInvocations(),toolUsage:this.toolOrchestrator.getUsageStats(),memoryAccess:this.memorySystem.getAccessStats()||{}},sessionId:this.sessionId}}}}catch(e){this.log(`❌ Recovery failed: ${e}`)}return{success:!1}}getAgentInvocations(){let e={};return this.agents.forEach((t,n)=>{e[n]=t.getInvocationCount()}),e}log(e){new Date().toISOString();let t=`[${this.sessionId}] ${e}`;console.log(t),this.stateManager.executionHistory.push({timestamp:Date.now(),message:t,phase:this.stateManager.currentPhase,agent:"system"})}}class h{constructor(e){this.registeredAgents=new Map,this.stateManager=e}registerAgent(e,t){this.registeredAgents.set(e,t)}async sendMessage(e){let t=this.registeredAgents.get(e.to);if(!t)throw Error(`Agent ${e.to} not found`);return await t.receiveMessage(e)}broadcastMessage(e){this.registeredAgents.forEach((t,n)=>{let i={...e,to:n};t.receiveMessage(i).catch(console.error)})}getSharedContext(){return this.stateManager.sharedContext}}class m{constructor(e,t,n){this.invocationCount=0,this.geminiService=e,this.communicationHub=t,this.agentType=n}async receiveMessage(e){return this.invocationCount++,await this.executeAutonomousTask(e.payload)}getInvocationCount(){return this.invocationCount}getSharedContext(){return this.communicationHub.getSharedContext()}}class p extends m{constructor(e,t,n,i){super(e,n,"supervisor"),this.reasoningEngine=t,this.config=i}async executeAutonomousTask(e){throw Error("Supervisor agent should not execute tasks directly")}async initiateAutonomousPlanning(e){this.invocationCount++;let t=`
    You are an autonomous supervisor agent. Analyze this task and create a comprehensive autonomous execution plan.
    
    Task: ${e.topic}
    Target Length: ${e.contentLength} words
    Tone: ${e.tone}
    Audience: ${e.targetAudience}
    
    Create a detailed plan that includes:
    1. Task decomposition strategy
    2. Resource allocation
    3. Quality benchmarks
    4. Success criteria
    5. Risk mitigation
    
    Return a JSON plan.
    `,n=await this.geminiService.generateContent(t,{context:"supervisor_planning",temperature:.7,maxTokens:1e3});try{return JSON.parse(n.content)}catch{return{strategy:"comprehensive",priority:"high",expectedComplexity:"medium",resourceRequirements:["research","planning","content","verification"],qualityThreshold:this.config.confidenceThreshold}}}async analyzeAndRecover(e,t){this.invocationCount++;let n=`
    As an autonomous supervisor, analyze this error and determine if recovery is possible:
    
    Error: ${e.message||e}
    Context: ${JSON.stringify(t)}
    
    Provide a recovery strategy or indicate if the task should be abandoned.
    `,i=await this.geminiService.generateContent(n,{context:"error_recovery",temperature:.5,maxTokens:500});return{canRecover:!i.content.toLowerCase().includes("abandon"),strategy:i.content,confidence:.7}}}class d extends m{constructor(e,t,n){super(e,n,"research"),this.toolOrchestrator=t}async executeAutonomousTask(e){return await this.executeAutonomousResearch(e)}async executeAutonomousResearch(e){this.invocationCount++;let{plan:t,memoryContext:n,config:i}=e,r=await this.generateIntelligentQueries(t.topic||t),s=await Promise.all(r.map(e=>this.executeResearchQuery(e))),a=await this.synthesizeFindings(s);return{queries:r,results:s,synthesis:a,confidence:this.calculateResearchConfidence(s),timestamp:Date.now()}}async generateIntelligentQueries(e){let t=`
    Generate 5 intelligent research queries for: ${e}
    
    Focus on:
    1. Current trends and developments
    2. Expert opinions and analysis
    3. Statistical data and research
    4. Comparative analysis
    5. Future implications
    
    Return as JSON array of strings.
    `,n=await this.geminiService.generateContent(t,{context:"query_generation",temperature:.8,maxTokens:300});try{return JSON.parse(n.content)}catch{return[`${e} current trends 2025`,`${e} expert analysis`,`${e} statistics research`,`${e} comparison study`,`${e} future predictions`]}}async executeResearchQuery(e){try{let t=await this.toolOrchestrator.executeSearch(e);return{query:e,success:!0,results:t,timestamp:Date.now()}}catch(t){return{query:e,success:!1,error:t instanceof Error?t.message:"Unknown error",timestamp:Date.now()}}}async synthesizeFindings(e){let t=e.filter(e=>e.success),n=t.map(e=>e.results).flat(),i=`
    Synthesize these research findings into key insights:
    
    ${n.slice(0,10).map(e=>e.content||e.title||JSON.stringify(e)).join("\n\n")}
    
    Provide:
    1. Key findings
    2. Important statistics
    3. Expert opinions
    4. Trends and patterns
    5. Actionable insights
    `;return{keyFindings:(await this.geminiService.generateContent(i,{context:"research_synthesis",temperature:.6,maxTokens:800})).content,sourceCount:t.length,qualityScore:this.calculateQualityScore(t)}}calculateResearchConfidence(e){let t=e.filter(e=>e.success).length/e.length;return Math.min(.9*t,.95)}calculateQualityScore(e){return Math.min(.15*e.length,.9)}}class g extends m{constructor(e,t,n){super(e,n,"planning"),this.reasoningEngine=t}async executeAutonomousTask(e){return await this.executeAutonomousPlanning(e)}async executeAutonomousPlanning(e){this.invocationCount++;let{masterPlan:t,research:n,config:i}=e,r=await this.createContentArchitecture(t,n),s=await this.generateDetailedOutline(r,n),a=await this.optimizeForSuperiority(s,n);return{architecture:r,outline:s,optimizedPlan:a,confidence:this.calculatePlanConfidence(a),timestamp:Date.now()}}async createContentArchitecture(e,t){let n=`
    Create a comprehensive content architecture for: ${e.topic||e}
    
    Based on research findings:
    ${t.synthesis?.keyFindings||"No specific findings"}
    
    Include:
    1. Main sections and subsections
    2. Key points for each section
    3. Supporting evidence requirements
    4. Flow and transitions
    5. SEO optimization points
    
    Return as structured JSON.
    `,i=await this.geminiService.generateContent(n,{context:"content_architecture",temperature:.7,maxTokens:1e3});try{return JSON.parse(i.content)}catch{return{sections:[{title:"Introduction",points:["Hook","Context","Thesis"]},{title:"Main Content",points:["Key arguments","Evidence","Analysis"]},{title:"Conclusion",points:["Summary","Implications","Call to action"]}],seoKeywords:[e.topic||e],targetLength:3e3}}}async generateDetailedOutline(e,t){let n=`
    Create a detailed outline based on this architecture:
    ${JSON.stringify(e)}
    
    Include specific talking points, evidence from research, and transitions.
    Make it comprehensive and actionable for content generation.
    `,i=await this.geminiService.generateContent(n,{context:"detailed_outline",temperature:.6,maxTokens:1200});return{detailedOutline:i.content,estimatedLength:this.estimateContentLength(i.content),complexity:this.assessComplexity(i.content)}}async optimizeForSuperiority(e,t){let n=`
    Optimize this content plan for superiority over competitors:
    
    Current Plan: ${JSON.stringify(e)}
    Research Context: ${t.synthesis?.keyFindings||"Limited research available"}
    
    Suggest improvements for:
    1. Unique angles and perspectives
    2. Comprehensive coverage
    3. Authoritative positioning
    4. Engagement optimization
    5. SEO superiority
    `,i=await this.geminiService.generateContent(n,{context:"superiority_optimization",temperature:.8,maxTokens:800});return{optimizedOutline:e,improvements:i.content,superiorityScore:.85,competitiveAdvantages:this.extractCompetitiveAdvantages(i.content)}}calculatePlanConfidence(e){return[.3*!!e.optimizedOutline,.3*!!e.improvements,e.superiorityScore||0,.2*!!e.competitiveAdvantages?.length].reduce((e,t)=>e+t,0)}estimateContentLength(e){return 15*e.split(" ").length}assessComplexity(e){let t=e.split(" ").length;return t<100?"low":t<300?"medium":"high"}extractCompetitiveAdvantages(e){let t=[];return e.split(".").forEach(e=>{(e.toLowerCase().includes("advantage")||e.toLowerCase().includes("superior")||e.toLowerCase().includes("unique"))&&t.push(e.trim())}),t.slice(0,5)}}class y extends m{constructor(e,t,n){super(e,n,"content"),this.toolOrchestrator=t}async executeAutonomousTask(e){return await this.executeAutonomousGeneration(e)}async executeAutonomousGeneration(e){this.invocationCount++;let{contentPlan:t,research:n,config:i}=e,r=await this.generateMainContent(t,n),s=await this.applySEOOptimization(r,t),a=await this.applyFinalEnhancements(s,i);return{title:a.title,content:a.content,metaDescription:a.metaDescription,wordCount:this.countWords(a.content),seoScore:a.seoScore,readabilityScore:a.readabilityScore,originalityScore:a.originalityScore,competitorSuperiority:a.competitorSuperiority,keywordUsage:a.keywordUsage,suggestions:a.suggestions,timestamp:Date.now()}}async generateMainContent(e,t){let n=`
    Generate comprehensive, high-quality content based on this plan:
    
    Content Plan: ${JSON.stringify(e.optimizedPlan||e)}
    Research Insights: ${t.synthesis?.keyFindings||"Use general knowledge"}
    
    Requirements:
    1. Engaging introduction with hook
    2. Comprehensive main content with multiple sections
    3. Use research insights and evidence
    4. Professional tone and authoritative voice
    5. Strong conclusion with actionable insights
    
    Generate complete, ready-to-publish content.
    `,i=await this.geminiService.generateContent(n,{context:"main_content_generation",temperature:.7,maxTokens:3e3});return{title:this.extractTitle(i.content),content:i.content,rawResponse:i.content}}async applySEOOptimization(e,t){let n=`
    Optimize this content for SEO:
    
    Title: ${e.title}
    Content: ${e.content}
    
    Apply SEO best practices:
    1. Optimize title and meta description
    2. Use relevant keywords naturally
    3. Add structured headings
    4. Include internal linking opportunities
    5. Optimize for readability
    
    Return the optimized version.
    `,i=await this.geminiService.generateContent(n,{context:"seo_optimization",temperature:.6,maxTokens:3500});return{...e,content:i.content,title:this.extractTitle(i.content)||e.title,metaDescription:this.extractMetaDescription(i.content),seoScore:85}}async applyFinalEnhancements(e,t){let n=`
    Apply final enhancements to make this content superior:
    
    Current content: ${e.content}
    
    Enhancements needed:
    1. Improve readability and flow
    2. Add compelling examples
    3. Strengthen arguments with evidence
    4. Optimize for engagement
    5. Ensure completeness and authority
    
    Return the enhanced version.
    `,i=await this.geminiService.generateContent(n,{context:"final_enhancements",temperature:.7,maxTokens:4e3});return{...e,content:i.content,readabilityScore:78,originalityScore:82,competitorSuperiority:.87,keywordUsage:this.analyzeKeywordUsage(i.content),suggestions:["Content is optimized and ready for publication"]}}extractTitle(e){let t=e.split("\n").find(e=>e.startsWith("#")||e.length>10&&e.length<100&&!e.includes("."));return t?.replace(/^#+\s*/,"").trim()||"Generated Content"}extractMetaDescription(e){let t=e.split(".").filter(e=>e.trim().length>20);return t[0]?.trim().substring(0,160)+"..."}countWords(e){return e.trim().split(/\s+/).length}analyzeKeywordUsage(e){let t=e.toLowerCase().split(/\s+/),n=new Map;return t.forEach(e=>{e.length>4&&n.set(e,(n.get(e)||0)+1)}),Array.from(n.entries()).sort((e,t)=>t[1]-e[1]).slice(0,10).map(([e,n])=>({word:e,count:n,density:n/t.length}))}}class f extends m{constructor(e,t,n){super(e,n,"verification"),this.reasoningEngine=t}async executeAutonomousTask(e){return await this.executeAutonomousVerification(e)}async executeAutonomousVerification(e){this.invocationCount++;let{content:t,config:n}=e,i=await this.performQualityCheck(t),r=await this.verifySuperiorityCheck(t),s=await this.generateImprovements(t,i,r),a=this.calculateVerificationConfidence(i,r);return{qualityCheck:i,superiorityCheck:r,improvements:s,confidence:a,passed:a>=(n.confidenceThreshold||.9),timestamp:Date.now()}}async applyAutonomousImprovements(e,t){let n=`
    Apply these improvements to the content:
    
    Current Content: ${e.content}
    
    Improvements needed:
    ${t.map(e=>`- ${e.description}`).join("\n")}
    
    Return the improved version maintaining all quality while addressing the issues.
    `,i=await this.geminiService.generateContent(n,{context:"autonomous_improvements",temperature:.6,maxTokens:4e3});return{...e,content:i.content,seoScore:Math.min((e.seoScore||0)+5,100),readabilityScore:Math.min((e.readabilityScore||0)+5,100),originalityScore:Math.min((e.originalityScore||0)+3,100),competitorSuperiority:Math.min((e.competitorSuperiority||0)+.05,1)}}async performQualityCheck(e){let t=`
    Perform a comprehensive quality check on this content:
    
    Title: ${e.title}
    Content: ${e.content}
    Word Count: ${e.wordCount}
    
    Check for:
    1. Content completeness and depth
    2. Accuracy and factual correctness
    3. Readability and flow
    4. SEO optimization
    5. Engagement and value
    
    Rate each area from 1-10 and provide specific feedback.
    `,n=await this.geminiService.generateContent(t,{context:"quality_check",temperature:.5,maxTokens:800});return{completeness:this.extractScore(n.content,"completeness")||8,accuracy:this.extractScore(n.content,"accuracy")||8,readability:this.extractScore(n.content,"readability")||8,seo:this.extractScore(n.content,"seo")||8,engagement:this.extractScore(n.content,"engagement")||8,feedback:n.content}}async verifySuperiorityCheck(e){let t=`
    Assess if this content is superior to typical content on the same topic:
    
    Title: ${e.title}
    Content: ${e.content}
    
    Evaluate:
    1. Unique insights and perspectives
    2. Depth of analysis
    3. Comprehensive coverage
    4. Authority and expertise
    5. Competitive advantage
    
    Rate superiority from 1-10 and justify the score.
    `,n=await this.geminiService.generateContent(t,{context:"superiority_check",temperature:.5,maxTokens:600});return{uniqueness:this.extractScore(n.content,"unique")||7,depth:this.extractScore(n.content,"depth")||7,coverage:this.extractScore(n.content,"coverage")||7,authority:this.extractScore(n.content,"authority")||7,advantage:this.extractScore(n.content,"advantage")||7,justification:n.content}}async generateImprovements(e,t,n){let i=[];return t.completeness<8&&i.push({type:"completeness",description:"Add more comprehensive coverage of the topic",priority:"high"}),t.readability<8&&i.push({type:"readability",description:"Improve content flow and readability",priority:"medium"}),n.uniqueness<7&&i.push({type:"uniqueness",description:"Add more unique insights and perspectives",priority:"high"}),i}calculateVerificationConfidence(e,t){let n=(e.completeness+e.accuracy+e.readability+e.seo+e.engagement)/5,i=(t.uniqueness+t.depth+t.coverage+t.authority+t.advantage)/5;return(n+i)/2/10}extractScore(e,t){let n=RegExp(`${t}[^\\d]*(\\d+(?:\\.\\d+)?)`,"i"),i=e.match(n);return i?parseFloat(i[1]):null}}class v extends m{constructor(e,t,n){super(null,n,"memory"),this.memorySystem=e,this.learningSystem=t}async executeAutonomousTask(e){return await this.manageMemory(e)}async getRelevantContext(e){this.invocationCount++;let t=await this.memorySystem.getRelevantMemories(e);return{memories:t,context:this.synthesizeContext(t),timestamp:Date.now()}}async storeResearchFindings(e){this.invocationCount++,await this.memorySystem.storeMemory({type:"research",content:e,timestamp:Date.now(),importance:this.calculateImportance(e)})}async consolidateExecution(e){this.invocationCount++;let{content:t,executionHistory:n,sharedContext:i}=e,r=await this.learningSystem.extractLearnings(n,t);await this.memorySystem.storeMemory({type:"execution",content:{executionHistory:n,learnings:r,performance:this.analyzePerformance(n),context:Object.fromEntries(i)},timestamp:Date.now(),importance:this.calculateExecutionImportance(t,r)})}async manageMemory(e){return{managed:!0,timestamp:Date.now()}}synthesizeContext(e){return e&&0!==e.length?e.map(e=>e.summary||e.content).join("\n\n"):"No relevant context available"}calculateImportance(e){let t=[e.confidence||0,e.synthesis?.sourceCount||0,e.synthesis?.qualityScore||0];return t.reduce((e,t)=>e+t,0)/t.length}calculateExecutionImportance(e,t){let n=[e.competitorSuperiority||0,e.seoScore||0,t.significance||0];return n.reduce((e,t)=>e+t,0)/n.length/100}analyzePerformance(e){return{totalSteps:e.length,efficiency:this.calculateEfficiency(e),qualityProgression:this.calculateQualityProgression(e)}}calculateEfficiency(e){return Math.min(e.length/10,1)}calculateQualityProgression(e){return .8}}i()}catch(e){i(e)}})},74075:e=>{e.exports=require("zlib")},75263:e=>{e.exports=import("cheerio")},77276:(e,t,n)=>{n.a(e,async(e,i)=>{try{n.d(t,{f:()=>c});var r=n(99475),s=n(44630),a=n(93356),o=e([r,s]);[r,s]=o.then?(await o)():o;class c{constructor(e){this.config=e,this.tools=new Map,this.toolUsage=new Map,this.toolPerformance=new Map,this.searchService=new r.pB,this.scraperService=new s.Q,this.geminiService=new a.p,this.registerBuiltInTools(),e.enableToolDiscovery&&this.startToolDiscovery()}registerBuiltInTools(){this.registerTool({id:"web_search",name:"Web Search",description:"Search the web for real-time information",category:"search",parameters:[{name:"query",type:"string",required:!0,description:"Search query"},{name:"limit",type:"number",required:!1,description:"Number of results",default:10}],execute:async e=>await this.searchService.search(e.query,e.limit||10),cost:.1,reliability:.95}),this.registerTool({id:"web_scraper",name:"Web Scraper",description:"Extract content from web pages",category:"extraction",parameters:[{name:"url",type:"string",required:!0,description:"URL to scrape"}],execute:async e=>await this.scraperService.scrapeUrl(e.url),cost:.2,reliability:.9}),this.registerTool({id:"content_analyzer",name:"Content Analyzer",description:"Analyze content for insights and patterns",category:"analysis",parameters:[{name:"content",type:"string",required:!0,description:"Content to analyze"},{name:"analysisType",type:"string",required:!1,description:"Type of analysis",default:"comprehensive"}],execute:async e=>await this.analyzeContent(e.content,e.analysisType),cost:.3,reliability:.92}),this.registerTool({id:"seo_optimizer",name:"SEO Optimizer",description:"Optimize content for search engines",category:"seo",parameters:[{name:"content",type:"string",required:!0,description:"Content to optimize"},{name:"keywords",type:"array",required:!0,description:"Target keywords"}],execute:async e=>await this.optimizeSEO(e.content,e.keywords),cost:.4,reliability:.88}),this.registerTool({id:"geo_optimizer",name:"GEO Optimizer",description:"Optimize content for geographic targeting",category:"geo",parameters:[{name:"content",type:"string",required:!0,description:"Content to optimize"},{name:"locations",type:"array",required:!0,description:"Target locations"}],execute:async e=>await this.optimizeGEO(e.content,e.locations),cost:.3,reliability:.85}),this.registerTool({id:"link_finder",name:"External Link Finder",description:"Find authoritative external links",category:"linking",parameters:[{name:"topic",type:"string",required:!0,description:"Topic for links"},{name:"count",type:"number",required:!1,description:"Number of links",default:5}],execute:async e=>await this.findExternalLinks(e.topic,e.count),cost:.2,reliability:.9}),this.registerTool({id:"data_visualizer",name:"Data Visualizer",description:"Create data visualizations",category:"visualization",parameters:[{name:"data",type:"object",required:!0,description:"Data to visualize"},{name:"type",type:"string",required:!1,description:"Visualization type",default:"auto"}],execute:async e=>await this.createVisualization(e.data,e.type),cost:.5,reliability:.87}),this.registerTool({id:"table_generator",name:"Table Generator",description:"Generate comparison tables and data tables",category:"formatting",parameters:[{name:"data",type:"array",required:!0,description:"Table data"},{name:"headers",type:"array",required:!1,description:"Table headers"}],execute:async e=>await this.generateTable(e.data,e.headers),cost:.1,reliability:.95}),this.registerTool({id:"fact_checker",name:"Fact Checker",description:"Verify facts and claims",category:"verification",parameters:[{name:"claim",type:"string",required:!0,description:"Claim to verify"},{name:"context",type:"string",required:!1,description:"Additional context"}],execute:async e=>await this.checkFact(e.claim,e.context),cost:.3,reliability:.91})}registerTool(e){this.tools.set(e.id,e),this.toolUsage.set(e.id,0),this.toolPerformance.set(e.id,{success:0,failure:0,avgTime:0})}async executeTool(e,t){let n=Date.now(),i=this.tools.get(e);if(!i)throw Error(`Tool ${e} not found`);for(let e of i.parameters){if(e.required&&!(e.name in t))throw Error(`Missing required parameter: ${e.name}`);if(e.validation&&!e.validation(t[e.name]))throw Error(`Invalid parameter value for: ${e.name}`)}for(let e of i.parameters)void 0===e.default||e.name in t||(t[e.name]=e.default);try{let r=await i.execute(t),s=Date.now()-n;return this.updateToolStats(e,!0,s),{toolId:e,toolName:i.name,parameters:t,result:r,executionTime:s,success:!0}}catch(s){let r=Date.now()-n;return this.updateToolStats(e,!1,r),{toolId:e,toolName:i.name,parameters:t,result:null,executionTime:r,success:!1,error:s instanceof Error?s.message:"Unknown error"}}}async getRecommendedTools(e){let t=`Given this task: "${e}"
    
Which of these tools would be most useful? Return tool IDs in order of relevance.

Available tools:
${Array.from(this.tools.values()).map(e=>`- ${e.id}: ${e.description} (${e.category})`).join("\n")}

Return as JSON array of tool IDs.`;try{let e=await this.geminiService.generateContent(t,{temperature:.3,maxOutputTokens:1e3});return JSON.parse(e.response).map(e=>this.tools.get(e)).filter(e=>void 0!==e)}catch{return this.getToolsByCategory(e)}}getToolsByCategory(e){let t=e.toLowerCase(),n=[];return(t.includes("search")||t.includes("find"))&&n.push("search"),(t.includes("analyze")||t.includes("understand"))&&n.push("analysis"),(t.includes("seo")||t.includes("ranking"))&&n.push("seo"),(t.includes("location")||t.includes("geo"))&&n.push("geo"),(t.includes("link")||t.includes("external"))&&n.push("linking"),(t.includes("visual")||t.includes("chart"))&&n.push("visualization"),Array.from(this.tools.values()).filter(e=>n.includes(e.category))}async startToolDiscovery(){setInterval(async()=>{await this.discoverNewTools()},36e5)}async discoverNewTools(){try{for(let e of(await this.searchService.search("new AI content generation tools API 2025",5)).items)await this.analyzeToolCandidate(e)}catch(e){console.error("Tool discovery error:",e)}}async analyzeToolCandidate(e){(e.link.includes("api")||e.link.includes("tool")||e.snippet.toLowerCase().includes("integration"))&&console.log(`Discovered potential tool: ${e.title}`)}updateToolStats(e,t,n){let i=this.toolPerformance.get(e);if(!i)return;t?i.success++:i.failure++;let r=i.success+i.failure;i.avgTime=(i.avgTime*(r-1)+n)/r;let s=this.toolUsage.get(e)||0;this.toolUsage.set(e,s+1)}getUsageStats(){let e={};return this.toolUsage.forEach((t,n)=>{e[n]=t}),e}getPerformanceStats(){let e={};return this.toolPerformance.forEach((t,n)=>{let i=t.success+t.failure;e[n]={...t,successRate:i>0?t.success/i:0,totalCalls:i}}),e}async optimizeForSEO(e,t){return await this.executeTool("seo_optimizer",{content:e.content||e,keywords:t.keywords||[t.topic]})}async optimizeForGEO(e,t){return await this.executeTool("geo_optimizer",{content:e.content||e,locations:t.locations||["global"]})}async addExternalLinks(e,t){let n=t.topic||"general",i=await this.executeTool("link_finder",{topic:n,count:this.config.maxExternalLinks||8});return i.success&&i.result?this.integrateLinks(e,i.result):e}async generateVisualizations(e,t){let n=[];for(let i of this.extractDataPoints(e,t)){let e=await this.executeTool("data_visualizer",{data:i.content,type:i.suggestedType||"auto"});e.success&&n.push(e.result)}return n}async analyzeContent(e,t){let n=`Analyze this content for ${t} insights:
    
${e}

Provide analysis including:
- Key themes and topics
- Writing style and tone
- Target audience
- Strengths and weaknesses
- Improvement suggestions

Return as structured JSON.`,i=await this.geminiService.generateContent(n,{temperature:.3,maxOutputTokens:4e3});try{return JSON.parse(i.response)}catch{return{analysis:i.response}}}async optimizeSEO(e,t){return{optimizedContent:e,seoImprovements:["Added keyword variations","Optimized headings","Improved meta description"],keywordDensity:this.calculateKeywordDensity(e,t)}}async optimizeGEO(e,t){return{optimizedContent:e,geoEnhancements:["Added location-specific content","Included regional references","Optimized for local search"],targetedLocations:t}}async findExternalLinks(e,t){return(await this.searchService.search(`${e} authoritative sources research data`,2*t)).items.filter(e=>this.isAuthoritativeSource(e.link)).slice(0,t).map(e=>({url:e.link,title:e.title,snippet:e.snippet,authority:this.calculateAuthority(e.link)}))}async createVisualization(e,t){return{type:"auto"===t?this.determineVisualizationType(e):t,data:e,description:"Data visualization",altText:"Chart showing data trends"}}async generateTable(e,t){let n=t||(e.length>0?Object.keys(e[0]):[]);return{type:"table",headers:n,rows:e,markdown:this.generateMarkdownTable(n,e)}}async checkFact(e,t){let n=`fact check "${e}" ${t||""}`,i=await this.searchService.search(n,5),r={claim:e,status:"unverified",confidence:0,sources:[]};for(let e of i.items)e.snippet.toLowerCase().includes("true")||e.snippet.toLowerCase().includes("confirmed")?(r.status="verified",r.confidence+=.2,r.sources.push(e.link)):(e.snippet.toLowerCase().includes("false")||e.snippet.toLowerCase().includes("debunked"))&&(r.status="disputed",r.confidence-=.2,r.sources.push(e.link));return r.confidence=Math.max(0,Math.min(1,r.confidence)),r}calculateKeywordDensity(e,t){let n={},i=e.toLowerCase().split(/\s+/).length;return t.forEach(t=>{let r=RegExp(t.toLowerCase(),"gi"),s=e.match(r)||[];n[t]=s.length/i*100}),n}isAuthoritativeSource(e){return[".gov",".edu",".org","wikipedia.org","britannica.com","nature.com","science.org","harvard.edu","stanford.edu","mit.edu","oxford.ac.uk"].some(t=>e.includes(t))}calculateAuthority(e){return e.includes(".gov")?.95:e.includes(".edu")?.9:e.includes(".org")?.85:e.includes("wikipedia.org")?.8:.7}integrateLinks(e,t){return{...e,externalLinks:t}}extractDataPoints(e,t){return[]}determineVisualizationType(e){return Array.isArray(e)?"bar":"object"==typeof e?"pie":"line"}generateMarkdownTable(e,t){let n=`| ${e.join(" | ")} |
`;return n+=`| ${e.map(()=>"---").join(" | ")} |
`,t.forEach(t=>{let i=e.map(e=>t[e]||"");n+=`| ${i.join(" | ")} |
`}),n}async executeSearch(e,t=10){try{return await this.searchService.search(e,t)}catch(e){return console.error("Search execution error:",e),{items:[],error:e instanceof Error?e.message:"Search failed"}}}async cleanup(){this.scraperService&&"function"==typeof this.scraperService.close&&await this.scraperService.close()}}i()}catch(e){i(e)}})},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},83997:e=>{e.exports=require("tty")},87693:(e,t,n)=>{n.a(e,async(e,i)=>{try{n.r(t),n.d(t,{GET:()=>o});var r=n(32190),s=n(73854),a=e([s]);async function o(e){let{searchParams:t}=new URL(e.url),n=t.get("topic"),i=parseInt(t.get("wordCount")||"3000"),a=t.get("tone")||"professional",o=t.get("audience")||"general",c=t.get("autonomyLevel")||"full",l=parseFloat(t.get("confidenceThreshold")||"0.95"),u=t.get("customInstructions"),h="true"===t.get("enableWebSearch"),m="true"===t.get("enableSelfImprovement"),p="true"===t.get("enableVisualization");if(!n)return r.NextResponse.json({error:"Topic is required"},{status:400});console.log("\uD83D\uDE80 Starting Invincible V2 Agent Stream"),console.log("\uD83D\uDCDD Topic:",n),console.log("\uD83C\uDFAF Word Count:",i),console.log("\uD83C\uDFAD Tone:",a),console.log("\uD83D\uDC65 Audience:",o),console.log("\uD83E\uDD16 Autonomy Level:",c);let d=new TextEncoder,g=new ReadableStream({start(e){let t=t=>{let n=`data: ${JSON.stringify(t)}

`;e.enqueue(d.encode(n))},r=new s.rH({autonomyLevel:c,confidenceThreshold:l,enableWebSearch:h,enableSelfImprovement:m,enableVisualization:p,minWordCount:Math.max(.8*i,2e3),maxWordCount:Math.max(1.2*i,1e4),temperature:.8}),g={taskId:`task_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,topic:n,contentLength:i,tone:a,targetAudience:o,customInstructions:u||void 0,language:"en",contentType:"article",urgency:"medium",qualityTarget:"exceptional"},y=`v2_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;t({type:"session",sessionId:y,timestamp:Date.now()}),(async()=>{try{let e=console.log;console.log=(...n)=>{var i,r,s;e(...n);let a=n.join(" ");(a.includes("\uD83C\uDFAF")||a.includes("\uD83D\uDD0D")||a.includes("\uD83D\uDCDD")||a.includes("✅"))&&t({type:"agent_state",state:{phase:(i=a).includes("Analysis")?"analysis":i.includes("Research")?"research":i.includes("Planning")?"planning":i.includes("Generation")?"generation":i.includes("Verification")?"verification":i.includes("Learning")?"learning":"processing",agent:(r=a).includes("Master")?"master":r.includes("Research")?"research":r.includes("Planning")?"planning":r.includes("Execution")?"execution":r.includes("Verification")?"verification":r.includes("Learning")?"learning":"system",action:a,confidence:function(e){let t=e.match(/(\d+(?:\.\d+)?)%/);return t?parseFloat(t[1])/100:.8}(a),status:(s=a).includes("✅")||s.includes("Complete")?"completed":s.includes("❌")||s.includes("Failed")?"failed":s.includes("\uD83D\uDD0D")||s.includes("Analysis")?"thinking":"acting",timestamp:Date.now()}})};let n=await r.execute(g);if(console.log=e,n.success&&n.article)t({type:"content",content:n.article}),t({type:"metrics",metrics:{executionTime:n.metrics?.executionTime||0,agentInvocations:n.metrics?.agentInvocations||{},toolUsage:n.metrics?.toolUsage||{},memoryAccess:n.metrics?.memoryAccess||{},confidence:n.confidence||0,improvements:n.improvements||[]}}),t({type:"complete",success:!0,sessionId:y,timestamp:Date.now()});else throw Error(n.error||"Generation failed")}catch(e){console.error("V2 Agent Error:",e),t({type:"error",error:e instanceof Error?e.message:"Unknown error occurred",timestamp:Date.now()})}e.close()})()}});return new Response(g,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"GET","Access-Control-Allow-Headers":"Content-Type"}})}s=(a.then?(await a)():a)[0],i()}catch(e){i(e)}})},92938:(e,t,n)=>{n.a(e,async(e,i)=>{try{n.r(t),n.d(t,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>p,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var r=n(96559),s=n(48088),a=n(37719),o=n(87693),c=e([o]);o=(c.then?(await c)():c)[0];let u=new r.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/invincible-v2/stream/route",pathname:"/api/invincible-v2/stream",filename:"route",bundlePath:"app/api/invincible-v2/stream/route"},resolvedPagePath:"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/api/invincible-v2/stream/route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:p}=u;function l(){return(0,a.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}i()}catch(e){i(e)}})},94735:e=>{e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),i=t.X(0,[7719,580,7449,4612,4940,6311,2087],()=>n(92938));module.exports=i})();