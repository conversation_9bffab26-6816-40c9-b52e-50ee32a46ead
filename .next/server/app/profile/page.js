(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7036:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},10022:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15935:(e,t,s)=>{Promise.resolve().then(s.bind(s,84199))},16189:(e,t,s)=>{"use strict";var r=s(65773);s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}}),s.o(r,"useSearchParams")&&s.d(t,{useSearchParams:function(){return r.useSearchParams}})},16764:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25541:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37897:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},40228:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},45583:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},48730:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},51361:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},58559:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},61135:()=>{},62060:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),i=s(88170),l=s.n(i),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,75758)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/profile/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/profile/page.tsx"],h={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73489:(e,t,s)=>{Promise.resolve().then(s.bind(s,75758))},74808:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},75758:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/profile/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/profile/page.tsx","default")},76183:(e,t,s)=>{Promise.resolve().then(s.bind(s,79025))},79025:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var r=s(60687),a=s(82136);function i({children:e}){return(0,r.jsx)(a.SessionProvider,{children:e})}},84027:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84199:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx","default")},86561:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},87032:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(60687),a=s(43210),i=s(82136),l=s(16189),n=s(97905),o=s(85814),d=s.n(o),c=s(37897),h=s(84027),m=s(51361),x=s(92363),p=s(40228),u=s(74808),b=s(25541),v=s(86561),f=s(48730),g=s(58559),y=s(45583),j=s(10022);function w(){let{data:e,status:t}=(0,i.useSession)();(0,l.useRouter)();let[s,o]=(0,a.useState)(null),[w,N]=(0,a.useState)(!0);if("loading"===t||w)return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Loading your profile..."})]})});if("unauthenticated"===t)return null;if(!s)return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("p",{className:"text-gray-400",children:"Unable to load profile"}),(0,r.jsx)(d(),{href:"/dashboard",children:(0,r.jsx)("button",{className:"px-4 py-2 bg-violet-600 text-white rounded-lg",children:"Back to Dashboard"})})]})});let k=()=>s?.firstName&&s?.lastName?`${s.firstName} ${s.lastName}`:s?.name?s.name:s?.email?s.email.split("@")[0]:"User";return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900",children:[(0,r.jsx)("div",{className:"border-b border-white/10 bg-black/20 backdrop-blur-xl",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,r.jsx)(d(),{href:"/dashboard",children:(0,r.jsxs)(n.P.button,{whileHover:{scale:1.05},className:"flex items-center space-x-3 text-white hover:text-violet-400 transition-colors",children:[(0,r.jsx)(c.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{className:"font-semibold",children:"Dashboard"})]})}),(0,r.jsx)("div",{className:"text-gray-600",children:"|"}),(0,r.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent",children:"My Profile"})]}),(0,r.jsx)(d(),{href:"/settings",children:(0,r.jsxs)(n.P.button,{whileHover:{scale:1.05},className:"flex items-center space-x-2 px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-xl transition-colors",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Edit Profile"})]})})]})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8 text-center",children:[(0,r.jsxs)("div",{className:"relative inline-block mb-6",children:[s.image?(0,r.jsx)("img",{src:s.image,alt:k(),className:"w-32 h-32 rounded-3xl object-cover border-4 border-white/20"}):(0,r.jsx)("div",{className:"w-32 h-32 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-3xl flex items-center justify-center text-white text-4xl font-bold",children:(()=>{if(s?.firstName&&s?.lastName)return`${s.firstName[0]}${s.lastName[0]}`;if(s?.name){let e=s.name.split(" ");return e.length>1?`${e[0][0]}${e[e.length-1][0]}`:e[0][0]}return s?.email?s.email[0].toUpperCase():"U"})()}),(0,r.jsx)("button",{className:"absolute -bottom-2 -right-2 w-10 h-10 bg-violet-600 hover:bg-violet-700 rounded-full flex items-center justify-center text-white transition-colors",children:(0,r.jsx)(m.A,{className:"w-5 h-5"})})]}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:k()}),(0,r.jsx)("p",{className:"text-gray-400 mb-4",children:s.email}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-6",children:[(0,r.jsx)(x.A,{className:`w-5 h-5 ${s.subscription?.plan==="free"?"text-gray-400":"text-yellow-400"}`}),(0,r.jsx)("span",{className:`font-medium ${s.subscription?.plan==="free"?"text-gray-400":"text-yellow-400"}`,children:s.subscription?.plan==="free"?"Free Plan":s.subscription?.plan==="pro"?"Pro Member":"Member"})]}),s.bio&&(0,r.jsx)("p",{className:"text-gray-300 text-sm leading-relaxed mb-6",children:s.bio}),(0,r.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-gray-400 text-sm",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:["Joined ",new Date(s.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]})]})}),(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-br from-blue-500/20 to-blue-600/10 backdrop-blur-xl border border-blue-500/20 rounded-2xl p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)(u.A,{className:"w-8 h-8 text-blue-400"}),(0,r.jsx)(b.A,{className:"w-5 h-5 text-emerald-400"})]}),(0,r.jsx)("p",{className:"text-3xl font-bold text-white mb-1",children:s.stats?.totalContent||0}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Content Created"})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 backdrop-blur-xl border border-emerald-500/20 rounded-2xl p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)(v.A,{className:"w-8 h-8 text-emerald-400"}),(0,r.jsx)("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(e=>(0,r.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-400"},e))})]}),(0,r.jsx)("p",{className:"text-3xl font-bold text-white mb-1",children:"9.8/10"}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Quality Score"})]}),(0,r.jsxs)("div",{className:"bg-gradient-to-br from-purple-500/20 to-purple-600/10 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)(f.A,{className:"w-8 h-8 text-purple-400"}),(0,r.jsx)(g.A,{className:"w-5 h-5 text-blue-400"})]}),(0,r.jsx)("p",{className:"text-3xl font-bold text-white mb-1",children:"48h"}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Time Saved"})]})]}),(0,r.jsxs)(n.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-white mb-6 flex items-center",children:[(0,r.jsx)(y.A,{className:"w-6 h-6 mr-3 text-violet-400"}),"Quick Actions"]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsx)(d(),{href:"/settings",children:(0,r.jsxs)(n.P.button,{whileHover:{scale:1.02},className:"w-full flex items-center space-x-3 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left",children:[(0,r.jsx)(h.A,{className:"w-5 h-5 text-violet-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-white",children:"Account Settings"}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Manage preferences"})]})]})}),(0,r.jsx)(d(),{href:"/dashboard",children:(0,r.jsxs)(n.P.button,{whileHover:{scale:1.02},className:"w-full flex items-center space-x-3 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left",children:[(0,r.jsx)(c.A,{className:"w-5 h-5 text-blue-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-white",children:"Dashboard"}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Back to main hub"})]})]})}),(0,r.jsx)(d(),{href:"/invincible",children:(0,r.jsxs)(n.P.button,{whileHover:{scale:1.02},className:"w-full flex items-center space-x-3 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left",children:[(0,r.jsx)(x.A,{className:"w-5 h-5 text-yellow-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-white",children:"Invincible V.1"}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Superior content"})]})]})}),(0,r.jsx)(d(),{href:"/generate/blog",children:(0,r.jsxs)(n.P.button,{whileHover:{scale:1.02},className:"w-full flex items-center space-x-3 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left",children:[(0,r.jsx)(j.A,{className:"w-5 h-5 text-pink-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-white",children:"Create Content"}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Start writing"})]})]})})]})]})]})]})})]})}},89169:(e,t,s)=>{Promise.resolve().then(s.bind(s,87032))},92363:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>n});var r=s(37413),a=s(7339),i=s.n(a);s(61135);var l=s(84199);let n={title:"Invincible - AI Content Generation Platform",description:"The ultimate content writing SaaS platform powered by advanced AI technology"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",className:"dark",children:(0,r.jsx)("body",{className:`${i().className} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,suppressHydrationWarning:!0,children:(0,r.jsx)(l.default,{children:(0,r.jsx)("div",{className:"min-h-screen",children:e})})})})}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,5380,4101],()=>s(62060));module.exports=r})();