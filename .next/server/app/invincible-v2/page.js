/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/invincible-v2/page";
exports.ids = ["app/invincible-v2/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finvincible-v2%2Fpage&page=%2Finvincible-v2%2Fpage&appPaths=%2Finvincible-v2%2Fpage&pagePath=private-next-app-dir%2Finvincible-v2%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finvincible-v2%2Fpage&page=%2Finvincible-v2%2Fpage&appPaths=%2Finvincible-v2%2Fpage&pagePath=private-next-app-dir%2Finvincible-v2%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/invincible-v2/page.tsx */ \"(rsc)/./src/app/invincible-v2/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'invincible-v2',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page4, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module0, \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\"],\n'not-found': [module1, \"next/dist/client/components/not-found-error\"],\n'forbidden': [module2, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module3, \"next/dist/client/components/unauthorized-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/invincible-v2/page\",\n        pathname: \"/invincible-v2\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finvincible-v2%2Fpage&page=%2Finvincible-v2%2Fpage&appPaths=%2Finvincible-v2%2Fpage&pagePath=private-next-app-dir%2Finvincible-v2%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(rsc)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible-v2%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible-v2%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/invincible-v2/page.tsx */ \"(rsc)/./src/app/invincible-v2/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGaW52aW5jaWJsZS12MiUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBZ0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvYXBwL2ludmluY2libGUtdjIvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible-v2%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"150d915160a8\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWF5dXNobWlzaHJhL0Rlc2t0b3Avb2xkIGludmluY2libGUgd2l0aCBkZWVwcmVzZWFyY2gvc3JjL2FwcC9nbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjE1MGQ5MTUxNjBhOFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/invincible-v2/page.tsx":
/*!****************************************!*\
  !*** ./src/app/invincible-v2/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src/app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SessionProvider */ \"(rsc)/./src/components/SessionProvider.tsx\");\n\n\n\n\nconst metadata = {\n    title: 'Invincible - AI Content Generation Platform',\n    description: 'The ultimate content writing SaaS platform powered by advanced AI technology'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"dark\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className)} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SessionProvider__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-screen\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n            lineNumber: 20,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx\",\n        lineNumber: 19,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/SessionProvider.tsx */ \"(ssr)/./src/components/SessionProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmZvbnQlMkZnb29nbGUlMkZ0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMnNyYyUyRmFwcCUyRmxheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhYXl1c2htaXNocmElMkZEZXNrdG9wJTJGb2xkJTIwaW52aW5jaWJsZSUyMHdpdGglMjBkZWVwcmVzZWFyY2glMkZzcmMlMkZjb21wb25lbnRzJTJGU2Vzc2lvblByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9MQUFpSyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%2Fapp%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fcomponents%2FSessionProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible-v2%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible-v2%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/invincible-v2/page.tsx */ \"(ssr)/./src/app/invincible-v2/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWF5dXNobWlzaHJhJTJGRGVza3RvcCUyRm9sZCUyMGludmluY2libGUlMjB3aXRoJTIwZGVlcHJlc2VhcmNoJTJGc3JjJTJGYXBwJTJGaW52aW5jaWJsZS12MiUyRnBhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSw0S0FBZ0kiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hYXl1c2htaXNocmEvRGVza3RvcC9vbGQgaW52aW5jaWJsZSB3aXRoIGRlZXByZXNlYXJjaC9zcmMvYXBwL2ludmluY2libGUtdjIvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp%2Finvincible-v2%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/invincible-v2/page.tsx":
/*!****************************************!*\
  !*** ./src/app/invincible-v2/page.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ InvincibleV2Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bar-chart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,ArrowLeft,BarChart,Brain,ChevronRight,Crown,Database,Lightbulb,Network,Plus,Search,Shield,TrendingUp,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _components_InvincibleV2StreamingUI__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/InvincibleV2StreamingUI */ \"(ssr)/./src/components/InvincibleV2StreamingUI.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\nfunction InvincibleV2Page() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_4__.useSession)();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        topic: '',\n        customInstructions: '',\n        targetAudience: 'general audience',\n        contentLength: 3000,\n        tone: 'professional',\n        keywords: [],\n        autonomyLevel: 'full',\n        confidenceThreshold: 0.95,\n        enableWebSearch: true,\n        enableSelfImprovement: true,\n        enableVisualization: true,\n        enableMemoryConsolidation: true\n    });\n    const [keywordInput, setKeywordInput] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showStreamingUI, setShowStreamingUI] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingResult, setStreamingResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Redirect to login if not authenticated\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvincibleV2Page.useEffect\": ()=>{\n            if (status === 'unauthenticated') {\n                window.location.href = '/login';\n            }\n        }\n    }[\"InvincibleV2Page.useEffect\"], [\n        status\n    ]);\n    // Loading state\n    if (status === 'loading') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-black flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin w-16 h-16 border-4 border-purple-400 border-t-transparent rounded-full mx-auto\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"absolute inset-0 m-auto w-6 h-6 text-purple-400\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-400\",\n                        children: \"Initializing Autonomous Agent...\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this);\n    }\n    // Don't render if not authenticated\n    if (status === 'unauthenticated') {\n        return null;\n    }\n    const handleStartStreaming = ()=>{\n        if (!config.topic.trim()) {\n            setError('Please enter a topic for autonomous generation');\n            return;\n        }\n        setError('');\n        setShowStreamingUI(true);\n        setIsStreaming(true);\n    };\n    const handleStreamingComplete = async (result)=>{\n        setStreamingResult(result);\n        setIsStreaming(false);\n        setIsSaving(true);\n        try {\n            // Save the article to the database\n            const response = await fetch('/api/articles/store', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    title: result.article?.title || `Autonomous Article: ${config.topic}`,\n                    content: result.article?.content || result.content || '',\n                    type: 'invincible-v2',\n                    metadata: {\n                        topic: config.topic,\n                        tone: config.tone,\n                        targetAudience: config.targetAudience,\n                        contentLength: config.contentLength,\n                        customInstructions: config.customInstructions,\n                        keywords: config.keywords,\n                        autonomyLevel: config.autonomyLevel,\n                        confidenceThreshold: config.confidenceThreshold,\n                        executionTime: result.metrics?.executionTime,\n                        agentInvocations: result.metrics?.agentInvocations,\n                        toolUsage: result.metrics?.toolUsage,\n                        confidence: result.confidence,\n                        improvements: result.improvements,\n                        seoScore: result.article?.seoScore,\n                        readabilityScore: result.article?.readabilityScore,\n                        originalityScore: result.article?.originalityScore,\n                        competitorSuperiority: result.article?.competitorSuperiority,\n                        generatedAt: new Date().toISOString(),\n                        version: '2.0'\n                    },\n                    tone: config.tone,\n                    language: 'en'\n                })\n            });\n            if (!response.ok) {\n                const errorText = await response.text();\n                throw new Error(`Failed to save article: ${errorText}`);\n            }\n            const saveResult = await response.json();\n            if (saveResult.success) {\n                // Small delay to show saving completion\n                await new Promise((resolve)=>setTimeout(resolve, 1500));\n                // Redirect to the article view page with the saved article ID\n                router.push(`/article-view/${saveResult.article.id}`);\n            } else {\n                throw new Error(saveResult.error || 'Failed to save article');\n            }\n        } catch (error) {\n            console.error('Error saving article:', error);\n            setIsSaving(false);\n            setError(`Article generated successfully but failed to save: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`);\n        }\n    };\n    const handleStreamingError = (error)=>{\n        setError(error);\n        setIsStreaming(false);\n    };\n    const addKeyword = ()=>{\n        if (keywordInput.trim() && !config.keywords?.includes(keywordInput.trim())) {\n            setConfig({\n                ...config,\n                keywords: [\n                    ...config.keywords || [],\n                    keywordInput.trim()\n                ]\n            });\n            setKeywordInput('');\n        }\n    };\n    const removeKeyword = (keyword)=>{\n        setConfig({\n            ...config,\n            keywords: config.keywords?.filter((k)=>k !== keyword) || []\n        });\n    };\n    const resetToConfiguration = ()=>{\n        setShowStreamingUI(false);\n        setIsStreaming(false);\n        setStreamingResult(null);\n        setError('');\n        setIsSaving(false);\n    };\n    // If showing streaming UI, render that component\n    if (showStreamingUI) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_InvincibleV2StreamingUI__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n            config: config,\n            onComplete: handleStreamingComplete,\n            onError: handleStreamingError,\n            onReset: resetToConfiguration,\n            isSaving: isSaving\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n            lineNumber: 219,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-purple-950/20 via-black to-indigo-950/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.1,\n                                1\n                            ],\n                            opacity: [\n                                0.3,\n                                0.5,\n                                0.3\n                            ]\n                        },\n                        transition: {\n                            duration: 8,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-1/4 left-1/6 w-96 h-96 bg-purple-600/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            scale: [\n                                1.1,\n                                1,\n                                1.1\n                            ],\n                            opacity: [\n                                0.4,\n                                0.6,\n                                0.4\n                            ]\n                        },\n                        transition: {\n                            duration: 10,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute bottom-1/3 right-1/6 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                        lineNumber: 248,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                        animate: {\n                            scale: [\n                                1,\n                                1.2,\n                                1\n                            ],\n                            opacity: [\n                                0.2,\n                                0.4,\n                                0.2\n                            ]\n                        },\n                        transition: {\n                            duration: 12,\n                            repeat: Infinity,\n                            ease: \"easeInOut\"\n                        },\n                        className: \"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-pink-600/5 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                        lineNumber: 260,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                lineNumber: 232,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.header, {\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"border-b border-white/10 backdrop-blur-xl bg-black/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                href: \"/dashboard\",\n                                                className: \"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"w-5 h-5 group-hover:-translate-x-1 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Dashboard\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 290,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-px h-6 bg-white/20\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 292,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl blur-lg opacity-60\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative bg-black rounded-xl p-2.5 border border-white/20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                    className: \"w-6 h-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                    lineNumber: 297,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 294,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-2xl font-bold bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent\",\n                                                                children: \"Invincible V2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Autonomous Content Generation Agent\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 300,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 293,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2 text-sm text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-400 rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Agent System Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                lineNumber: 283,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                            lineNumber: 282,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.section, {\n                        initial: {\n                            opacity: 0,\n                            y: 20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"max-w-7xl mx-auto px-6 py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center space-y-6 mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-2 bg-purple-900/30 px-4 py-2 rounded-full border border-purple-500/30\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-4 h-4 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-purple-300 text-sm font-medium\",\n                                                children: \"Next-Generation Autonomous System\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl md:text-6xl font-bold text-white leading-tight\",\n                                        children: [\n                                            \"Create \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-gradient-to-r from-purple-400 via-pink-400 to-indigo-400 bg-clip-text text-transparent\",\n                                                children: \"Superior\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 22\n                                            }, this),\n                                            \" Content\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed\",\n                                        children: [\n                                            \"Deploy a fully autonomous multi-agent system that researches, plans, generates, and verifies content with \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-purple-400 font-semibold\",\n                                                children: \"guaranteed superiority\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 337,\n                                                columnNumber: 20\n                                            }, this),\n                                            \" over competition.\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                        lineNumber: 335,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mt-8\",\n                                        children: [\n                                            {\n                                                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                label: 'Autonomous',\n                                                desc: 'Self-directed reasoning',\n                                                color: 'purple'\n                                            },\n                                            {\n                                                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                                label: 'Multi-Agent',\n                                                desc: '6 specialized agents',\n                                                color: 'blue'\n                                            },\n                                            {\n                                                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                                label: 'Self-Improving',\n                                                desc: 'Learns from experience',\n                                                color: 'green'\n                                            },\n                                            {\n                                                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                                label: 'Guaranteed',\n                                                desc: '95%+ confidence',\n                                                color: 'orange'\n                                            }\n                                        ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                initial: {\n                                                    opacity: 0,\n                                                    y: 20\n                                                },\n                                                animate: {\n                                                    opacity: 1,\n                                                    y: 0\n                                                },\n                                                transition: {\n                                                    delay: index * 0.1\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border rounded-xl p-4 hover:border-white/20 transition-all duration-300\", feature.color === 'purple' && \"border-purple-500/20 hover:border-purple-400/40\", feature.color === 'blue' && \"border-blue-500/20 hover:border-blue-400/40\", feature.color === 'green' && \"border-green-500/20 hover:border-green-400/40\", feature.color === 'orange' && \"border-orange-500/20 hover:border-orange-400/40\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-8 h-8 mb-2\", feature.color === 'purple' && \"text-purple-400\", feature.color === 'blue' && \"text-blue-400\", feature.color === 'green' && \"text-green-400\", feature.color === 'orange' && \"text-orange-400\")\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 361,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-white text-sm\",\n                                                        children: feature.label\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-gray-400 mt-1\",\n                                                        children: feature.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 348,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                initial: {\n                                    opacity: 0,\n                                    y: 30\n                                },\n                                animate: {\n                                    opacity: 1,\n                                    y: 0\n                                },\n                                transition: {\n                                    delay: 0.2\n                                },\n                                className: \"max-w-4xl mx-auto\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-br from-gray-900/80 via-gray-800/60 to-gray-900/80 backdrop-blur-xl border border-white/10 rounded-2xl p-8 shadow-2xl\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block text-lg font-semibold text-white\",\n                                                        children: \"Research Topic\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: config.topic,\n                                                                onChange: (e)=>setConfig({\n                                                                        ...config,\n                                                                        topic: e.target.value\n                                                                    }),\n                                                                placeholder: \"Enter any topic for autonomous content generation...\",\n                                                                className: \"w-full bg-black/50 border border-white/20 rounded-xl px-4 py-4 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all text-lg\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-medium text-gray-300\",\n                                                                        children: \"Target Audience\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 406,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: config.targetAudience,\n                                                                        onChange: (e)=>setConfig({\n                                                                                ...config,\n                                                                                targetAudience: e.target.value\n                                                                            }),\n                                                                        className: \"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"general audience\",\n                                                                                children: \"General Audience\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 412,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"professionals\",\n                                                                                children: \"Industry Professionals\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 413,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"experts\",\n                                                                                children: \"Subject Matter Experts\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 414,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"beginners\",\n                                                                                children: \"Beginners\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 415,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"students\",\n                                                                                children: \"Students & Academics\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 416,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"business leaders\",\n                                                                                children: \"Business Leaders\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 417,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 407,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 405,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-medium text-gray-300\",\n                                                                        children: \"Content Tone\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: config.tone,\n                                                                        onChange: (e)=>setConfig({\n                                                                                ...config,\n                                                                                tone: e.target.value\n                                                                            }),\n                                                                        className: \"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"professional\",\n                                                                                children: \"Professional\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"conversational\",\n                                                                                children: \"Conversational\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 429,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"academic\",\n                                                                                children: \"Academic\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 430,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"casual\",\n                                                                                children: \"Casual\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"authoritative\",\n                                                                                children: \"Authoritative\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"friendly\",\n                                                                                children: \"Friendly\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 433,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 423,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-medium text-gray-300\",\n                                                                        children: \"Content Length\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 438,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: config.contentLength,\n                                                                        onChange: (e)=>setConfig({\n                                                                                ...config,\n                                                                                contentLength: Number(e.target.value)\n                                                                            }),\n                                                                        className: \"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 1500,\n                                                                                children: \"Short Article (1,500 words)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 444,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 3000,\n                                                                                children: \"Medium Article (3,000 words)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 445,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 5000,\n                                                                                children: \"Long Article (5,000 words)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 446,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 7500,\n                                                                                children: \"Comprehensive (7,500 words)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 447,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: 10000,\n                                                                                children: \"Ultimate Guide (10,000 words)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 448,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 439,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-medium text-gray-300\",\n                                                                        children: \"Autonomy Level\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 456,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                        value: config.autonomyLevel,\n                                                                        onChange: (e)=>setConfig({\n                                                                                ...config,\n                                                                                autonomyLevel: e.target.value\n                                                                            }),\n                                                                        className: \"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"full\",\n                                                                                children: \"Full Autonomy (Recommended)\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 462,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"guided\",\n                                                                                children: \"Guided Generation\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 463,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                value: \"supervised\",\n                                                                                children: \"Supervised Mode\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 464,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 457,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 455,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-medium text-gray-300\",\n                                                                        children: [\n                                                                            \"Confidence Threshold: \",\n                                                                            Math.round((config.confidenceThreshold || 0.95) * 100),\n                                                                            \"%\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                        type: \"range\",\n                                                                        min: \"0.8\",\n                                                                        max: \"0.99\",\n                                                                        step: \"0.01\",\n                                                                        value: config.confidenceThreshold,\n                                                                        onChange: (e)=>setConfig({\n                                                                                ...config,\n                                                                                confidenceThreshold: Number(e.target.value)\n                                                                            }),\n                                                                        className: \"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-purple\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"text-xs text-gray-400\",\n                                                                        children: \"Higher values ensure better quality but may take longer\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 468,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                        className: \"block font-medium text-gray-300\",\n                                                                        children: \"Agent Capabilities\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 488,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-3\",\n                                                                        children: [\n                                                                            {\n                                                                                key: 'enableWebSearch',\n                                                                                label: 'Real-time Web Research',\n                                                                                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n                                                                            },\n                                                                            {\n                                                                                key: 'enableSelfImprovement',\n                                                                                label: 'Self-Improvement Learning',\n                                                                                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n                                                                            },\n                                                                            {\n                                                                                key: 'enableVisualization',\n                                                                                label: 'Data Visualizations',\n                                                                                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"]\n                                                                            },\n                                                                            {\n                                                                                key: 'enableMemoryConsolidation',\n                                                                                label: 'Memory Consolidation',\n                                                                                icon: _barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"]\n                                                                            }\n                                                                        ].map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"flex items-center space-x-3 cursor-pointer group\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"checkbox\",\n                                                                                        checked: config[feature.key],\n                                                                                        onChange: (e)=>setConfig({\n                                                                                                ...config,\n                                                                                                [feature.key]: e.target.checked\n                                                                                            }),\n                                                                                        className: \"w-4 h-4 text-purple-600 bg-black border-gray-600 rounded focus:ring-purple-500\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                        lineNumber: 497,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                                        className: \"w-4 h-4 text-gray-400 group-hover:text-purple-400 transition-colors\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                        lineNumber: 503,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-gray-300 group-hover:text-white transition-colors\",\n                                                                                        children: feature.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                        lineNumber: 504,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, feature.key, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                                lineNumber: 496,\n                                                                                columnNumber: 27\n                                                                            }, this))\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 489,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block font-medium text-gray-300\",\n                                                        children: \"Focus Keywords (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 514,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"text\",\n                                                                value: keywordInput,\n                                                                onChange: (e)=>setKeywordInput(e.target.value),\n                                                                onKeyPress: (e)=>e.key === 'Enter' && addKeyword(),\n                                                                placeholder: \"Add focus keywords...\",\n                                                                className: \"flex-1 bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-all\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: addKeyword,\n                                                                className: \"bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors flex items-center space-x-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-4 h-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                    lineNumber: 528,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 524,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 515,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    config.keywords && config.keywords.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-2 mt-3\",\n                                                        children: config.keywords.map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"bg-purple-900/50 text-purple-300 px-3 py-1 rounded-full text-sm border border-purple-500/30 flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: keyword\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 539,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"w-3 h-3 cursor-pointer hover:text-white transition-colors\",\n                                                                        onClick: ()=>removeKeyword(keyword)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                        lineNumber: 540,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                                lineNumber: 535,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 533,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                        className: \"block font-medium text-gray-300\",\n                                                        children: \"Custom Instructions (Optional)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 552,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                        value: config.customInstructions,\n                                                        onChange: (e)=>setConfig({\n                                                                ...config,\n                                                                customInstructions: e.target.value\n                                                            }),\n                                                        placeholder: \"Any specific requirements, style preferences, or focus areas...\",\n                                                        rows: 4,\n                                                        className: \"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-all resize-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_20__.AnimatePresence, {\n                                                children: error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.div, {\n                                                    initial: {\n                                                        opacity: 0,\n                                                        height: 0\n                                                    },\n                                                    animate: {\n                                                        opacity: 1,\n                                                        height: 'auto'\n                                                    },\n                                                    exit: {\n                                                        opacity: 0,\n                                                        height: 0\n                                                    },\n                                                    className: \"bg-red-900/50 border border-red-500/50 text-red-300 px-4 py-3 rounded-lg flex items-center space-x-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"w-5 h-5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                            lineNumber: 571,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: error\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                            lineNumber: 572,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                    lineNumber: 565,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 563,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_8__.motion.button, {\n                                                onClick: handleStartStreaming,\n                                                disabled: !config.topic.trim() || isStreaming,\n                                                whileHover: {\n                                                    scale: 1.02\n                                                },\n                                                whileTap: {\n                                                    scale: 0.98\n                                                },\n                                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 flex items-center justify-center space-x-3 text-lg shadow-lg shadow-purple-500/25\", (!config.topic.trim() || isStreaming) && \"opacity-50 cursor-not-allowed\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"w-6 h-6\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Deploy Autonomous Agent System\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_ArrowLeft_BarChart_Brain_ChevronRight_Crown_Database_Lightbulb_Network_Plus_Search_Shield_TrendingUp_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center text-sm text-gray-400\",\n                                                children: \"The autonomous agent will research, plan, generate, and verify your content with guaranteed superiority\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                                lineNumber: 593,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                        lineNumber: 320,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n                lineNumber: 275,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\",\n        lineNumber: 230,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/invincible-v2/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/InvincibleV2StreamingUI.tsx":
/*!****************************************************!*\
  !*** ./src/components/InvincibleV2StreamingUI.tsx ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,ArrowLeft,Brain,CheckCircle,Cpu,Crown,FileText,Globe,Lightbulb,Loader2,Search,Shield,Target,Terminal,TrendingUp!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nconst InvincibleV2StreamingUI = ({ config, onComplete, onError, onReset, isSaving })=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPhase, setCurrentPhase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [agentStates, setAgentStates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [activeAgents, setActiveAgents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [searchQueries, setSearchQueries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [researchSources, setResearchSources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [agentMetrics, setAgentMetrics] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [connectionStatus, setConnectionStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('connecting');\n    const [confidenceScore, setConfidenceScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [improvements, setImprovements] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const eventsContainerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Auto-start streaming when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"InvincibleV2StreamingUI.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"InvincibleV2StreamingUI.useEffect.timer\": ()=>{\n                    startStreaming();\n                }\n            }[\"InvincibleV2StreamingUI.useEffect.timer\"], 1000);\n            return ({\n                \"InvincibleV2StreamingUI.useEffect\": ()=>{\n                    clearTimeout(timer);\n                    if (eventSourceRef.current) {\n                        eventSourceRef.current.close();\n                    }\n                }\n            })[\"InvincibleV2StreamingUI.useEffect\"];\n        }\n    }[\"InvincibleV2StreamingUI.useEffect\"], []);\n    const startStreaming = ()=>{\n        if (isStreaming) return;\n        setIsStreaming(true);\n        setProgress(0);\n        setEvents([]);\n        setAgentStates([]);\n        setActiveAgents([]);\n        setSearchQueries([]);\n        setResearchSources([]);\n        setAgentMetrics({});\n        setFinalResult(null);\n        setError('');\n        setConnectionStatus('connecting');\n        setConfidenceScore(0);\n        setImprovements([]);\n        // Build URL with query parameters for EventSource GET request\n        const params = new URLSearchParams({\n            topic: config.topic,\n            contentLength: (config.contentLength || 3000).toString(),\n            tone: config.tone || 'professional',\n            targetAudience: config.targetAudience || 'general audience',\n            customInstructions: config.customInstructions || '',\n            autonomyLevel: config.autonomyLevel || 'full',\n            confidenceThreshold: (config.confidenceThreshold || 0.95).toString(),\n            enableWebSearch: config.enableWebSearch ? 'true' : 'false',\n            enableSelfImprovement: config.enableSelfImprovement ? 'true' : 'false',\n            enableVisualization: config.enableVisualization ? 'true' : 'false',\n            enableMemoryConsolidation: config.enableMemoryConsolidation ? 'true' : 'false'\n        });\n        const eventSource = new EventSource(`/api/invincible-v2/stream?${params.toString()}`);\n        eventSourceRef.current = eventSource;\n        // Safe JSON parsing helper\n        const safeParseJSON = (rawData)=>{\n            try {\n                if (!rawData || rawData === 'undefined' || rawData === 'null') {\n                    console.warn('Received invalid SSE data:', rawData);\n                    return null;\n                }\n                return JSON.parse(rawData);\n            } catch (error) {\n                console.error('Failed to parse SSE data:', rawData, error);\n                return null;\n            }\n        };\n        // Connection established\n        eventSource.onopen = ()=>{\n            setConnectionStatus('connected');\n            addEvent('connection', {\n                message: 'Multi-Agent System Connected',\n                timestamp: Date.now()\n            });\n        };\n        // Handle different event types\n        eventSource.addEventListener('session', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data) {\n                addEvent('session', data);\n            }\n        });\n        eventSource.addEventListener('agent_state', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data && data.state) {\n                setAgentStates((prev)=>[\n                        ...prev,\n                        data.state\n                    ]);\n                setCurrentPhase(data.state.phase);\n                // Update active agents\n                setActiveAgents((prev)=>{\n                    const newActive = [\n                        ...prev\n                    ];\n                    if (!newActive.includes(data.state.agent)) {\n                        newActive.push(data.state.agent);\n                    }\n                    return newActive;\n                });\n                addEvent('agent_state', data);\n            }\n        });\n        eventSource.addEventListener('search_query', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data && data.query) {\n                setSearchQueries((prev)=>[\n                        ...prev,\n                        data.query\n                    ]);\n                addEvent('search_query', data);\n            }\n        });\n        eventSource.addEventListener('research_source', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data && data.source) {\n                setResearchSources((prev)=>[\n                        ...prev,\n                        data.source\n                    ]);\n                addEvent('research_source', data);\n            }\n        });\n        eventSource.addEventListener('agent_metrics', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data) {\n                setAgentMetrics(data);\n                addEvent('agent_metrics', data);\n            }\n        });\n        eventSource.addEventListener('confidence', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data && typeof data.confidence === 'number') {\n                setConfidenceScore(data.confidence);\n                addEvent('confidence', data);\n            }\n        });\n        eventSource.addEventListener('improvement', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data && data.improvement) {\n                setImprovements((prev)=>[\n                        ...prev,\n                        data.improvement\n                    ]);\n                addEvent('improvement', data);\n            }\n        });\n        eventSource.addEventListener('progress', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data && typeof data.progress === 'number') {\n                setProgress(data.progress);\n                addEvent('progress', data);\n            }\n        });\n        eventSource.addEventListener('content', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data) {\n                setFinalResult(data);\n                addEvent('content', data);\n            }\n        });\n        eventSource.addEventListener('complete', (e)=>{\n            const data = safeParseJSON(e.data);\n            if (data) {\n                setFinalResult(data);\n                setProgress(100);\n                setConnectionStatus('complete');\n                setIsStreaming(false);\n                addEvent('complete', data);\n                onComplete?.(data);\n            }\n            eventSource.close();\n        });\n        eventSource.addEventListener('error', (e)=>{\n            const data = safeParseJSON(e.data);\n            const errorMessage = data?.error || 'Unknown error occurred';\n            setError(errorMessage);\n            setConnectionStatus('error');\n            setIsStreaming(false);\n            addEvent('error', data);\n            onError?.(errorMessage);\n            eventSource.close();\n        });\n        eventSource.onerror = (error)=>{\n            console.error('SSE Connection Error:', error);\n            setError('Connection lost. Please try again.');\n            setConnectionStatus('error');\n            setIsStreaming(false);\n            onError?.('Connection lost. Please try again.');\n            eventSource.close();\n        };\n    };\n    const addEvent = (type, data)=>{\n        const event = {\n            type,\n            data,\n            timestamp: Date.now()\n        };\n        setEvents((prev)=>[\n                ...prev,\n                event\n            ]);\n        // Auto-scroll to bottom\n        setTimeout(()=>{\n            if (eventsContainerRef.current) {\n                eventsContainerRef.current.scrollTop = eventsContainerRef.current.scrollHeight;\n            }\n        }, 100);\n    };\n    const getPhaseInfo = (phase)=>{\n        const phases = {\n            'initializing': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                label: 'Initializing',\n                color: 'text-blue-400',\n                description: 'Starting multi-agent system...'\n            },\n            'analysis': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                label: 'Master Analysis',\n                color: 'text-purple-400',\n                description: 'Understanding requirements and complexity...'\n            },\n            'research': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                label: 'Research Phase',\n                color: 'text-green-400',\n                description: 'Deep web research and competitive analysis...'\n            },\n            'planning': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                label: 'Strategic Planning',\n                color: 'text-orange-400',\n                description: 'Creating content strategy and structure...'\n            },\n            'generation': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                label: 'Content Generation',\n                color: 'text-pink-400',\n                description: 'Writing superior content...'\n            },\n            'verification': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                label: 'Verification',\n                color: 'text-red-400',\n                description: 'Quality assurance and fact-checking...'\n            },\n            'learning': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                label: 'Learning Phase',\n                color: 'text-indigo-400',\n                description: 'Self-improvement and optimization...'\n            }\n        };\n        return phases[phase] || {\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            label: 'Processing',\n            color: 'text-gray-400',\n            description: 'Agent processing...'\n        };\n    };\n    const getAgentInfo = (agent)=>{\n        const agents = {\n            'master': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                color: 'text-purple-400',\n                name: 'Master Agent'\n            },\n            'research': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                color: 'text-blue-400',\n                name: 'Research Agent'\n            },\n            'planning': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                color: 'text-green-400',\n                name: 'Planning Agent'\n            },\n            'execution': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                color: 'text-orange-400',\n                name: 'Execution Agent'\n            },\n            'verification': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                color: 'text-red-400',\n                name: 'Verification Agent'\n            },\n            'learning': {\n                icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: 'text-indigo-400',\n                name: 'Learning Agent'\n            }\n        };\n        return agents[agent] || {\n            icon: _barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: 'text-gray-400',\n            name: 'System Agent'\n        };\n    };\n    const getConnectionStatusColor = ()=>{\n        switch(connectionStatus){\n            case 'connected':\n                return 'text-green-400';\n            case 'error':\n                return 'text-red-400';\n            case 'complete':\n                return 'text-blue-400';\n            default:\n                return 'text-yellow-400';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-black\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 z-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 bg-gradient-to-br from-purple-950/20 via-black to-indigo-950/20\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        animate: {\n                            opacity: [\n                                0.3,\n                                0.6,\n                                0.3\n                            ]\n                        },\n                        transition: {\n                            duration: 4,\n                            repeat: Infinity\n                        },\n                        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-600/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                        animate: {\n                            opacity: [\n                                0.4,\n                                0.7,\n                                0.4\n                            ]\n                        },\n                        transition: {\n                            duration: 6,\n                            repeat: Infinity\n                        },\n                        className: \"absolute bottom-1/3 right-1/4 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.header, {\n                        initial: {\n                            opacity: 0,\n                            y: -20\n                        },\n                        animate: {\n                            opacity: 1,\n                            y: 0\n                        },\n                        className: \"border-b border-white/10 backdrop-blur-xl bg-black/50\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: onReset,\n                                                className: \"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors group\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                        className: \"w-5 h-5 group-hover:-translate-x-1 transition-transform\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Back to Configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                lineNumber: 414,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-px h-6 bg-white/20\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl blur-lg opacity-60\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                lineNumber: 424,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative bg-black rounded-xl p-2.5 border border-white/20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                    className: \"w-6 h-6 text-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 426,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                                className: \"text-2xl font-bold bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent\",\n                                                                children: \"Invincible V2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Autonomous Generation in Progress\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                lineNumber: 433,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                lineNumber: 422,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `flex items-center space-x-2 text-sm ${getConnectionStatusColor()}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-current rounded-full animate-pulse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: connectionStatus === 'connected' ? 'Agent System Active' : connectionStatus === 'complete' ? 'Generation Complete' : connectionStatus === 'error' ? 'Connection Error' : 'Connecting...'\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                lineNumber: 439,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            confidenceScore > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-purple-400\",\n                                                children: [\n                                                    \"Confidence: \",\n                                                    Math.round(confidenceScore * 100),\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                        lineNumber: 438,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                            lineNumber: 411,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-7xl mx-auto px-6 py-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-1 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            className: \"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Current Phase\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                currentPhase ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-3\",\n                                                            children: [\n                                                                /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(getPhaseInfo(currentPhase).icon, {\n                                                                    className: `w-8 h-8 ${getPhaseInfo(currentPhase).color}`\n                                                                }),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-semibold text-white\",\n                                                                            children: getPhaseInfo(currentPhase).label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 474,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-sm text-gray-400\",\n                                                                            children: getPhaseInfo(currentPhase).description\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 475,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between text-sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-gray-400\",\n                                                                            children: \"Progress\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-white\",\n                                                                            children: [\n                                                                                Math.round(progress),\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 481,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-full bg-gray-700 rounded-full h-2\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                                                        className: \"bg-gradient-to-r from-purple-500 to-indigo-500 h-2 rounded-full\",\n                                                                        initial: {\n                                                                            width: 0\n                                                                        },\n                                                                        animate: {\n                                                                            width: `${progress}%`\n                                                                        },\n                                                                        transition: {\n                                                                            duration: 0.5\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                        lineNumber: 484,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 483,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-gray-400 text-center py-4\",\n                                                    children: \"Waiting for agent initialization...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 494,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                            lineNumber: 461,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.1\n                                            },\n                                            className: \"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Agent Network\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 507,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        'master',\n                                                        'research',\n                                                        'planning',\n                                                        'execution',\n                                                        'verification',\n                                                        'learning'\n                                                    ].map((agent)=>{\n                                                        const agentInfo = getAgentInfo(agent);\n                                                        const isActive = activeAgents.includes(agent);\n                                                        const latestState = agentStates.filter((s)=>s.agent === agent).slice(-1)[0];\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: `flex items-center justify-between p-3 rounded-lg border transition-all ${isActive ? 'border-purple-500/50 bg-purple-900/20' : 'border-gray-700 bg-gray-800/30'}`,\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createElement(agentInfo.icon, {\n                                                                            className: `w-5 h-5 ${isActive ? agentInfo.color : 'text-gray-500'}`\n                                                                        }),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: `font-medium ${isActive ? 'text-white' : 'text-gray-500'}`,\n                                                                            children: agentInfo.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 522,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 518,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2\",\n                                                                    children: [\n                                                                        latestState && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: [\n                                                                                Math.round(latestState.confidence * 100),\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 528,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: `w-2 h-2 rounded-full ${isActive ? 'bg-green-400 animate-pulse' : 'bg-gray-600'}`\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 532,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, agent, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 515,\n                                                            columnNumber: 23\n                                                        }, undefined);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 508,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                            lineNumber: 501,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        (searchQueries.length > 0 || researchSources.length > 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.2\n                                            },\n                                            className: \"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Research Progress\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        searchQueries.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-blue-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 555,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-300\",\n                                                                            children: \"Search Queries\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 556,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-blue-400\",\n                                                                            children: [\n                                                                                \"(\",\n                                                                                searchQueries.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 557,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-h-24 overflow-y-auto space-y-1\",\n                                                                    children: searchQueries.slice(-3).map((query, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded\",\n                                                                            children: [\n                                                                                '\"',\n                                                                                query,\n                                                                                '\"'\n                                                                            ]\n                                                                        }, index, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 29\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 559,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        researchSources.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                            className: \"w-4 h-4 text-green-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 572,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-300\",\n                                                                            children: \"Sources Found\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 573,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs text-green-400\",\n                                                                            children: [\n                                                                                \"(\",\n                                                                                researchSources.length,\n                                                                                \")\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 574,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 571,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"max-h-24 overflow-y-auto space-y-1\",\n                                                                    children: researchSources.slice(-3).map((source, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded\",\n                                                                            children: source\n                                                                        }, index, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 578,\n                                                                            columnNumber: 29\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 576,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 570,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                            lineNumber: 544,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                    lineNumber: 459,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:col-span-2 space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            transition: {\n                                                delay: 0.3\n                                            },\n                                            className: \"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Live Agent Activity\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 599,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    ref: eventsContainerRef,\n                                                    className: \"h-80 overflow-y-auto space-y-2 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_15__.AnimatePresence, {\n                                                        children: events.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-gray-400 text-center py-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-8 h-8 mx-auto mb-2 text-gray-600\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 607,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    children: \"Waiting for agent events...\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 608,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 606,\n                                                            columnNumber: 23\n                                                        }, undefined) : events.map((event, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                                                initial: {\n                                                                    opacity: 0,\n                                                                    x: -20\n                                                                },\n                                                                animate: {\n                                                                    opacity: 1,\n                                                                    x: 0\n                                                                },\n                                                                className: \"flex items-start space-x-3 p-3 rounded-lg bg-gray-800/50 border border-gray-700/50\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                        lineNumber: 618,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex-1 min-w-0\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-2 mb-1\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-purple-400 font-medium\",\n                                                                                        children: event.type.toUpperCase()\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                                        lineNumber: 621,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-xs text-gray-500\",\n                                                                                        children: new Date(event.timestamp).toLocaleTimeString()\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                                        lineNumber: 624,\n                                                                                        columnNumber: 31\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                                lineNumber: 620,\n                                                                                columnNumber: 29\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"text-sm text-gray-300\",\n                                                                                children: event.type === 'agent_state' && event.data.state ? `${event.data.state.agent}: ${event.data.state.action}` : event.type === 'search_query' ? `Searching: \"${event.data.query}\"` : event.type === 'research_source' ? `Found source: ${event.data.source}` : event.type === 'connection' ? event.data.message : JSON.stringify(event.data).substring(0, 100) + '...'\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                                lineNumber: 628,\n                                                                                columnNumber: 29\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                        lineNumber: 619,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, index, true, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                lineNumber: 612,\n                                                                columnNumber: 25\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 604,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 600,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                            lineNumber: 593,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        improvements.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            className: \"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-white mb-4\",\n                                                    children: \"Self-Improvement Insights\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 656,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-2\",\n                                                    children: improvements.map((improvement, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-start space-x-3 p-3 rounded-lg bg-green-900/20 border border-green-500/30\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-4 h-4 text-green-400 mt-0.5 flex-shrink-0\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 660,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-green-300\",\n                                                                    children: improvement\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 661,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, index, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 659,\n                                                            columnNumber: 23\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 657,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        finalResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            className: \"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-semibold text-white\",\n                                                            children: \"Generation Complete\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-5 h-5 text-green-400\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 678,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-green-400 font-medium\",\n                                                                    children: \"Success\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 679,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 677,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 675,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                finalResult.article && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gray-800/50 rounded-lg p-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-semibold text-white mb-2\",\n                                                                    children: finalResult.article.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 686,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-300 text-sm\",\n                                                                    children: finalResult.article.metaDescription\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 685,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-2xl font-bold text-purple-400\",\n                                                                            children: finalResult.article.wordCount || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 692,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Words\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 695,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-2xl font-bold text-green-400\",\n                                                                            children: finalResult.article.seoScore || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 698,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"SEO Score\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 701,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 697,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-2xl font-bold text-blue-400\",\n                                                                            children: [\n                                                                                Math.round((finalResult.confidence || 0) * 100),\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 704,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Confidence\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 703,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-center\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-2xl font-bold text-orange-400\",\n                                                                            children: finalResult.article.readabilityScore || 0\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 710,\n                                                                            columnNumber: 27\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-400\",\n                                                                            children: \"Readability\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                            lineNumber: 713,\n                                                                            columnNumber: 27\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                    lineNumber: 684,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                            lineNumber: 670,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            className: \"bg-red-900/50 border border-red-500/50 rounded-2xl p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-6 h-6 text-red-400 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-red-300\",\n                                                                children: \"Generation Error\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                lineNumber: 731,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-red-200\",\n                                                                children: error\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                lineNumber: 732,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 730,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                            lineNumber: 723,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        isSaving && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                            initial: {\n                                                opacity: 0,\n                                                y: 20\n                                            },\n                                            animate: {\n                                                opacity: 1,\n                                                y: 0\n                                            },\n                                            className: \"bg-blue-900/50 border border-blue-500/50 rounded-2xl p-6\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_ArrowLeft_Brain_CheckCircle_Cpu_Crown_FileText_Globe_Lightbulb_Loader2_Search_Shield_Target_Terminal_TrendingUp_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-blue-400 animate-spin flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-semibold text-blue-300\",\n                                                                children: \"Saving Article\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                lineNumber: 748,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-blue-200\",\n                                                                children: \"Storing your autonomous creation...\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                            lineNumber: 740,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                                    lineNumber: 591,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n                lineNumber: 404,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/InvincibleV2StreamingUI.tsx\",\n        lineNumber: 387,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (InvincibleV2StreamingUI);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/InvincibleV2StreamingUI.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/SessionProvider.tsx":
/*!********************************************!*\
  !*** ./src/components/SessionProvider.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SessionProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction SessionProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9TZXNzaW9uUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU0RTtBQU83RCxTQUFTQSxnQkFBZ0IsRUFBRUUsUUFBUSxFQUF3QjtJQUN4RSxxQkFDRSw4REFBQ0QsNERBQXVCQTtrQkFDckJDOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsiL1VzZXJzL2FheXVzaG1pc2hyYS9EZXNrdG9wL29sZCBpbnZpbmNpYmxlIHdpdGggZGVlcHJlc2VhcmNoL3NyYy9jb21wb25lbnRzL1Nlc3Npb25Qcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciBhcyBOZXh0QXV0aFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCdcbmltcG9ydCB7IFJlYWN0Tm9kZSB9IGZyb20gJ3JlYWN0J1xuXG5pbnRlcmZhY2UgU2Vzc2lvblByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFNlc3Npb25Qcm92aWRlcih7IGNoaWxkcmVuIH06IFNlc3Npb25Qcm92aWRlclByb3BzKSB7XG4gIHJldHVybiAoXG4gICAgPE5leHRBdXRoU2Vzc2lvblByb3ZpZGVyPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTmV4dEF1dGhTZXNzaW9uUHJvdmlkZXI+XG4gIClcbn0gIl0sIm5hbWVzIjpbIlNlc3Npb25Qcm92aWRlciIsIk5leHRBdXRoU2Vzc2lvblByb3ZpZGVyIiwiY2hpbGRyZW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/SessionProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   safeDecodeURIComponent: () => (/* binding */ safeDecodeURIComponent),\n/* harmony export */   safeEncodeURIComponent: () => (/* binding */ safeEncodeURIComponent)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n/**\n * Safely decode URI component with fallback handling\n * @param encodedString - The URI encoded string to decode\n * @param fallback - Optional fallback value if decoding fails\n * @returns Decoded string or fallback/original string if decoding fails\n */ function safeDecodeURIComponent(encodedString, fallback) {\n    if (!encodedString) {\n        return fallback || '';\n    }\n    try {\n        return decodeURIComponent(encodedString);\n    } catch (error) {\n        console.warn('URI decode failed, using fallback:', error);\n        return fallback || encodedString;\n    }\n}\n/**\n * Safely encode URI component with error handling\n * @param str - The string to encode\n * @returns Encoded string or empty string if encoding fails\n */ function safeEncodeURIComponent(str) {\n    if (!str) return '';\n    try {\n        return encodeURIComponent(str);\n    } catch (error) {\n        console.warn('URI encode failed:', error);\n        return '';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/@babel","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/lucide-react","vendor-chunks/motion-utils","vendor-chunks/tailwind-merge","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Finvincible-v2%2Fpage&page=%2Finvincible-v2%2Fpage&appPaths=%2Finvincible-v2%2Fpage&pagePath=private-next-app-dir%2Finvincible-v2%2Fpage.tsx&appDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Faayushmishra%2FDesktop%2Fold%20invincible%20with%20deepresearch&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();