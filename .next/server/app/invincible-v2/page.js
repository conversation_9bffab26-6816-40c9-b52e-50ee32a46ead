(()=>{var e={};e.id=9941,e.ids=[9941],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,s)=>{"use strict";s.d(t,{T:()=>i,cn:()=>n});var r=s(49384),a=s(82348);function n(...e){return(0,a.QP)((0,r.$)(e))}function i(e,t){if(!e)return t||"";try{return decodeURIComponent(e)}catch(s){return console.warn("URI decode failed, using fallback:",s),t||e}}},7036:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},14952:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},15935:(e,t,s)=>{Promise.resolve().then(s.bind(s,84199))},16764:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34626:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>m,tree:()=>c});var r=s(65239),a=s(48088),n=s(88170),i=s.n(n),l=s(30893),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c={children:["",{children:["invincible-v2",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,37515)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/invincible-v2/page",pathname:"/invincible-v2",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},37515:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/invincible-v2/page.tsx","default")},41862:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44304:(e,t,s)=>{Promise.resolve().then(s.bind(s,65008))},50928:(e,t,s)=>{Promise.resolve().then(s.bind(s,37515))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65008:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>q});var r=s(60687),a=s(43210),n=s.n(a),i=s(16189),l=s(97905),o=s(88920),c=s(85814),d=s.n(c),x=s(82136),m=s(78200),u=s(28559),p=s(92363),h=s(75324);let g=(0,h.A)("Network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]]);var b=s(25541),v=s(99891);let y=(0,h.A)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);var f=s(99270),j=s(68184),N=s(61611),w=s(96474),k=s(11860),A=s(93613),S=s(14952),P=s(4780),C=s(10510),L=s(28947),E=s(10022),I=s(58559),M=s(11437),T=s(24366),_=s(5336),z=s(41862);let R=({config:e,onComplete:t,onError:s,onReset:c,isSaving:d})=>{(0,i.useRouter)();let[x,h]=(0,a.useState)(!1),[g,j]=(0,a.useState)(""),[N,w]=(0,a.useState)(0),[k,S]=(0,a.useState)([]),[P,R]=(0,a.useState)([]),[q,$]=(0,a.useState)([]),[D,G]=(0,a.useState)([]),[U,O]=(0,a.useState)([]),[V,W]=(0,a.useState)({}),[F,H]=(0,a.useState)(null),[B,J]=(0,a.useState)(""),[K,Q]=(0,a.useState)("connecting"),[X,Y]=(0,a.useState)(0),[Z,ee]=(0,a.useState)([]),et=(0,a.useRef)(null),es=(0,a.useRef)(null);(0,a.useEffect)(()=>{let e=setTimeout(()=>{er()},1e3);return()=>{clearTimeout(e),es.current&&es.current.close()}},[]);let er=()=>{if(x)return;h(!0),w(0),S([]),R([]),$([]),G([]),O([]),W({}),H(null),J(""),Q("connecting"),Y(0),ee([]);let r=new URLSearchParams({topic:e.topic,contentLength:(e.contentLength||3e3).toString(),tone:e.tone||"professional",targetAudience:e.targetAudience||"general audience",customInstructions:e.customInstructions||"",autonomyLevel:e.autonomyLevel||"full",confidenceThreshold:(e.confidenceThreshold||.95).toString(),enableWebSearch:e.enableWebSearch?"true":"false",enableSelfImprovement:e.enableSelfImprovement?"true":"false",enableVisualization:e.enableVisualization?"true":"false",enableMemoryConsolidation:e.enableMemoryConsolidation?"true":"false"}),a=new EventSource(`/api/invincible-v2/stream?${r.toString()}`);es.current=a;let n=e=>{try{if(!e||"undefined"===e||"null"===e)return console.warn("Received invalid SSE data:",e),null;return JSON.parse(e)}catch(t){return console.error("Failed to parse SSE data:",e,t),null}};a.onopen=()=>{Q("connected"),ea("connection",{message:"Multi-Agent System Connected",timestamp:Date.now()})},a.addEventListener("session",e=>{let t=n(e.data);t&&ea("session",t)}),a.addEventListener("agent_state",e=>{let t=n(e.data);t&&t.state&&(R(e=>[...e,t.state]),j(t.state.phase),$(e=>{let s=[...e];return s.includes(t.state.agent)||s.push(t.state.agent),s}),ea("agent_state",t))}),a.addEventListener("search_query",e=>{let t=n(e.data);t&&t.query&&(G(e=>[...e,t.query]),ea("search_query",t))}),a.addEventListener("research_source",e=>{let t=n(e.data);t&&t.source&&(O(e=>[...e,t.source]),ea("research_source",t))}),a.addEventListener("agent_metrics",e=>{let t=n(e.data);t&&(W(t),ea("agent_metrics",t))}),a.addEventListener("confidence",e=>{let t=n(e.data);t&&"number"==typeof t.confidence&&(Y(t.confidence),ea("confidence",t))}),a.addEventListener("improvement",e=>{let t=n(e.data);t&&t.improvement&&(ee(e=>[...e,t.improvement]),ea("improvement",t))}),a.addEventListener("progress",e=>{let t=n(e.data);t&&"number"==typeof t.progress&&(w(t.progress),ea("progress",t))}),a.addEventListener("content",e=>{let t=n(e.data);t&&(H(t),ea("content",t))}),a.addEventListener("complete",e=>{let s=n(e.data);s&&(H(s),w(100),Q("complete"),h(!1),ea("complete",s),t?.(s)),a.close()}),a.addEventListener("error",e=>{let t=n(e.data),r=t?.error||"Unknown error occurred";J(r),Q("error"),h(!1),ea("error",t),s?.(r),a.close()}),a.onerror=e=>{console.error("SSE Connection Error:",e),J("Connection lost. Please try again."),Q("error"),h(!1),s?.("Connection lost. Please try again."),a.close()}},ea=(e,t)=>{let s={type:e,data:t,timestamp:Date.now()};S(e=>[...e,s]),setTimeout(()=>{et.current&&(et.current.scrollTop=et.current.scrollHeight)},100)},en=e=>({initializing:{icon:C.A,label:"Initializing",color:"text-blue-400",description:"Starting multi-agent system..."},analysis:{icon:m.A,label:"Master Analysis",color:"text-purple-400",description:"Understanding requirements and complexity..."},research:{icon:f.A,label:"Research Phase",color:"text-green-400",description:"Deep web research and competitive analysis..."},planning:{icon:L.A,label:"Strategic Planning",color:"text-orange-400",description:"Creating content strategy and structure..."},generation:{icon:E.A,label:"Content Generation",color:"text-pink-400",description:"Writing superior content..."},verification:{icon:v.A,label:"Verification",color:"text-red-400",description:"Quality assurance and fact-checking..."},learning:{icon:b.A,label:"Learning Phase",color:"text-indigo-400",description:"Self-improvement and optimization..."}})[e]||{icon:I.A,label:"Processing",color:"text-gray-400",description:"Agent processing..."},ei=e=>({master:{icon:p.A,color:"text-purple-400",name:"Master Agent"},research:{icon:f.A,color:"text-blue-400",name:"Research Agent"},planning:{icon:L.A,color:"text-green-400",name:"Planning Agent"},execution:{icon:E.A,color:"text-orange-400",name:"Execution Agent"},verification:{icon:v.A,color:"text-red-400",name:"Verification Agent"},learning:{icon:b.A,color:"text-indigo-400",name:"Learning Agent"}})[e]||{icon:I.A,color:"text-gray-400",name:"System Agent"};return(0,r.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,r.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-950/20 via-black to-indigo-950/20"}),(0,r.jsx)(l.P.div,{animate:{opacity:[.3,.6,.3]},transition:{duration:4,repeat:1/0},className:"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-600/10 rounded-full blur-3xl"}),(0,r.jsx)(l.P.div,{animate:{opacity:[.4,.7,.4]},transition:{duration:6,repeat:1/0},className:"absolute bottom-1/3 right-1/4 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl"})]}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)(l.P.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"border-b border-white/10 backdrop-blur-xl bg-black/50",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("button",{onClick:c,className:"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors group",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 group-hover:-translate-x-1 transition-transform"}),(0,r.jsx)("span",{children:"Back to Configuration"})]}),(0,r.jsx)("div",{className:"w-px h-6 bg-white/20"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl blur-lg opacity-60"}),(0,r.jsx)("div",{className:"relative bg-black rounded-xl p-2.5 border border-white/20",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-white"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent",children:"Invincible V2"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Autonomous Generation in Progress"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:`flex items-center space-x-2 text-sm ${(()=>{switch(K){case"connected":return"text-green-400";case"error":return"text-red-400";case"complete":return"text-blue-400";default:return"text-yellow-400"}})()}`,children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-current rounded-full animate-pulse"}),(0,r.jsx)("span",{children:"connected"===K?"Agent System Active":"complete"===K?"Generation Complete":"error"===K?"Connection Error":"Connecting..."})]}),X>0&&(0,r.jsxs)("div",{className:"text-sm text-purple-400",children:["Confidence: ",Math.round(100*X),"%"]})]})]})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Current Phase"}),g?(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[n().createElement(en(g).icon,{className:`w-8 h-8 ${en(g).color}`}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-semibold text-white",children:en(g).label}),(0,r.jsx)("div",{className:"text-sm text-gray-400",children:en(g).description})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Progress"}),(0,r.jsxs)("span",{className:"text-white",children:[Math.round(N),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,r.jsx)(l.P.div,{className:"bg-gradient-to-r from-purple-500 to-indigo-500 h-2 rounded-full",initial:{width:0},animate:{width:`${N}%`},transition:{duration:.5}})})]})]}):(0,r.jsx)("div",{className:"text-gray-400 text-center py-4",children:"Waiting for agent initialization..."})]}),(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Agent Network"}),(0,r.jsx)("div",{className:"space-y-3",children:["master","research","planning","execution","verification","learning"].map(e=>{let t=ei(e),s=q.includes(e),a=P.filter(t=>t.agent===e).slice(-1)[0];return(0,r.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg border transition-all ${s?"border-purple-500/50 bg-purple-900/20":"border-gray-700 bg-gray-800/30"}`,children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[n().createElement(t.icon,{className:`w-5 h-5 ${s?t.color:"text-gray-500"}`}),(0,r.jsx)("span",{className:`font-medium ${s?"text-white":"text-gray-500"}`,children:t.name})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[a&&(0,r.jsxs)("span",{className:"text-xs text-gray-400",children:[Math.round(100*a.confidence),"%"]}),(0,r.jsx)("div",{className:`w-2 h-2 rounded-full ${s?"bg-green-400 animate-pulse":"bg-gray-600"}`})]})]},e)})})]}),(D.length>0||U.length>0)&&(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Research Progress"}),(0,r.jsxs)("div",{className:"space-y-4",children:[D.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(f.A,{className:"w-4 h-4 text-blue-400"}),(0,r.jsx)("span",{className:"text-sm text-gray-300",children:"Search Queries"}),(0,r.jsxs)("span",{className:"text-xs text-blue-400",children:["(",D.length,")"]})]}),(0,r.jsx)("div",{className:"max-h-24 overflow-y-auto space-y-1",children:D.slice(-3).map((e,t)=>(0,r.jsxs)("div",{className:"text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded",children:['"',e,'"']},t))})]}),U.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(M.A,{className:"w-4 h-4 text-green-400"}),(0,r.jsx)("span",{className:"text-sm text-gray-300",children:"Sources Found"}),(0,r.jsxs)("span",{className:"text-xs text-green-400",children:["(",U.length,")"]})]}),(0,r.jsx)("div",{className:"max-h-24 overflow-y-auto space-y-1",children:U.slice(-3).map((e,t)=>(0,r.jsx)("div",{className:"text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded",children:e},t))})]})]})]})]}),(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Live Agent Activity"}),(0,r.jsx)("div",{ref:et,className:"h-80 overflow-y-auto space-y-2 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800",children:(0,r.jsx)(o.N,{children:0===k.length?(0,r.jsxs)("div",{className:"text-gray-400 text-center py-8",children:[(0,r.jsx)(T.A,{className:"w-8 h-8 mx-auto mb-2 text-gray-600"}),(0,r.jsx)("p",{children:"Waiting for agent events..."})]}):k.map((e,t)=>(0,r.jsxs)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-start space-x-3 p-3 rounded-lg bg-gray-800/50 border border-gray-700/50",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,r.jsx)("span",{className:"text-xs text-purple-400 font-medium",children:e.type.toUpperCase()}),(0,r.jsx)("span",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleTimeString()})]}),(0,r.jsx)("div",{className:"text-sm text-gray-300",children:"agent_state"===e.type&&e.data.state?`${e.data.state.agent}: ${e.data.state.action}`:"search_query"===e.type?`Searching: "${e.data.query}"`:"research_source"===e.type?`Found source: ${e.data.source}`:"connection"===e.type?e.data.message:JSON.stringify(e.data).substring(0,100)+"..."})]})]},t))})})]}),Z.length>0&&(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Self-Improvement Insights"}),(0,r.jsx)("div",{className:"space-y-2",children:Z.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg bg-green-900/20 border border-green-500/30",children:[(0,r.jsx)(y,{className:"w-4 h-4 text-green-400 mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{className:"text-sm text-green-300",children:e})]},t))})]}),F&&(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Generation Complete"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(_.A,{className:"w-5 h-5 text-green-400"}),(0,r.jsx)("span",{className:"text-green-400 font-medium",children:"Success"})]})]}),F.article&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-semibold text-white mb-2",children:F.article.title}),(0,r.jsx)("p",{className:"text-gray-300 text-sm",children:F.article.metaDescription})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:F.article.wordCount||0}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Words"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-400",children:F.article.seoScore||0}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"SEO Score"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-blue-400",children:[Math.round(100*(F.confidence||0)),"%"]}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Confidence"})]}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-orange-400",children:F.article.readabilityScore||0}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Readability"})]})]})]})]}),B&&(0,r.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-red-900/50 border border-red-500/50 rounded-2xl p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(A.A,{className:"w-6 h-6 text-red-400 flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-red-300",children:"Generation Error"}),(0,r.jsx)("p",{className:"text-red-200",children:B})]})]})}),d&&(0,r.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-blue-900/50 border border-blue-500/50 rounded-2xl p-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(z.A,{className:"w-6 h-6 text-blue-400 animate-spin flex-shrink-0"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-blue-300",children:"Saving Article"}),(0,r.jsx)("p",{className:"text-blue-200",children:"Storing your autonomous creation..."})]})]})})]})]})})]})]})};function q(){let e=(0,i.useRouter)(),{data:t,status:s}=(0,x.useSession)(),[n,c]=(0,a.useState)({topic:"",customInstructions:"",targetAudience:"general audience",contentLength:3e3,tone:"professional",keywords:[],autonomyLevel:"full",confidenceThreshold:.95,enableWebSearch:!0,enableSelfImprovement:!0,enableVisualization:!0,enableMemoryConsolidation:!0}),[h,C]=(0,a.useState)(""),[L,E]=(0,a.useState)(!1),[I,M]=(0,a.useState)(!1),[T,_]=(0,a.useState)(null),[z,q]=(0,a.useState)(""),[$,D]=(0,a.useState)(!1);if("loading"===s)return(0,r.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"animate-spin w-16 h-16 border-4 border-purple-400 border-t-transparent rounded-full mx-auto"}),(0,r.jsx)(m.A,{className:"absolute inset-0 m-auto w-6 h-6 text-purple-400"})]}),(0,r.jsx)("p",{className:"text-gray-400",children:"Initializing Autonomous Agent..."})]})});if("unauthenticated"===s)return null;let G=async t=>{_(t),E(!1),D(!0);try{let s=await fetch("/api/articles/store",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:t.article?.title||`Autonomous Article: ${n.topic}`,content:t.article?.content||t.content||"",type:"invincible-v2",metadata:{topic:n.topic,tone:n.tone,targetAudience:n.targetAudience,contentLength:n.contentLength,customInstructions:n.customInstructions,keywords:n.keywords,autonomyLevel:n.autonomyLevel,confidenceThreshold:n.confidenceThreshold,executionTime:t.metrics?.executionTime,agentInvocations:t.metrics?.agentInvocations,toolUsage:t.metrics?.toolUsage,confidence:t.confidence,improvements:t.improvements,seoScore:t.article?.seoScore,readabilityScore:t.article?.readabilityScore,originalityScore:t.article?.originalityScore,competitorSuperiority:t.article?.competitorSuperiority,generatedAt:new Date().toISOString(),version:"2.0"},tone:n.tone,language:"en"})});if(!s.ok){let e=await s.text();throw Error(`Failed to save article: ${e}`)}let r=await s.json();if(r.success)await new Promise(e=>setTimeout(e,1500)),e.push(`/article-view/${r.article.id}`);else throw Error(r.error||"Failed to save article")}catch(e){console.error("Error saving article:",e),D(!1),q(`Article generated successfully but failed to save: ${e instanceof Error?e.message:"Unknown error"}. Please try again.`)}},U=()=>{h.trim()&&!n.keywords?.includes(h.trim())&&(c({...n,keywords:[...n.keywords||[],h.trim()]}),C(""))},O=e=>{c({...n,keywords:n.keywords?.filter(t=>t!==e)||[]})};return I?(0,r.jsx)(R,{config:n,onComplete:G,onError:e=>{q(e),E(!1)},onReset:()=>{M(!1),E(!1),_(null),q(""),D(!1)},isSaving:$}):(0,r.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,r.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-950/20 via-black to-indigo-950/20"}),(0,r.jsx)(l.P.div,{animate:{scale:[1,1.1,1],opacity:[.3,.5,.3]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},className:"absolute top-1/4 left-1/6 w-96 h-96 bg-purple-600/10 rounded-full blur-3xl"}),(0,r.jsx)(l.P.div,{animate:{scale:[1.1,1,1.1],opacity:[.4,.6,.4]},transition:{duration:10,repeat:1/0,ease:"easeInOut"},className:"absolute bottom-1/3 right-1/6 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl"}),(0,r.jsx)(l.P.div,{animate:{scale:[1,1.2,1],opacity:[.2,.4,.2]},transition:{duration:12,repeat:1/0,ease:"easeInOut"},className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-pink-600/5 rounded-full blur-3xl"})]}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)(l.P.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"border-b border-white/10 backdrop-blur-xl bg-black/50",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)(d(),{href:"/dashboard",className:"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors group",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 group-hover:-translate-x-1 transition-transform"}),(0,r.jsx)("span",{children:"Dashboard"})]}),(0,r.jsx)("div",{className:"w-px h-6 bg-white/20"}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl blur-lg opacity-60"}),(0,r.jsx)("div",{className:"relative bg-black rounded-xl p-2.5 border border-white/20",children:(0,r.jsx)(m.A,{className:"w-6 h-6 text-white"})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent",children:"Invincible V2"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Autonomous Content Generation Agent"})]})]})]}),(0,r.jsx)("div",{className:"flex items-center space-x-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,r.jsx)("span",{children:"Agent System Online"})]})})]})})}),(0,r.jsxs)(l.P.section,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-7xl mx-auto px-6 py-12",children:[(0,r.jsxs)("div",{className:"text-center space-y-6 mb-12",children:[(0,r.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-purple-900/30 px-4 py-2 rounded-full border border-purple-500/30",children:[(0,r.jsx)(p.A,{className:"w-4 h-4 text-purple-400"}),(0,r.jsx)("span",{className:"text-purple-300 text-sm font-medium",children:"Next-Generation Autonomous System"})]}),(0,r.jsxs)("h2",{className:"text-4xl md:text-6xl font-bold text-white leading-tight",children:["Create ",(0,r.jsx)("span",{className:"bg-gradient-to-r from-purple-400 via-pink-400 to-indigo-400 bg-clip-text text-transparent",children:"Superior"})," Content"]}),(0,r.jsxs)("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed",children:["Deploy a fully autonomous multi-agent system that researches, plans, generates, and verifies content with ",(0,r.jsx)("span",{className:"text-purple-400 font-semibold",children:"guaranteed superiority"})," over competition."]}),(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mt-8",children:[{icon:m.A,label:"Autonomous",desc:"Self-directed reasoning",color:"purple"},{icon:g,label:"Multi-Agent",desc:"6 specialized agents",color:"blue"},{icon:b.A,label:"Self-Improving",desc:"Learns from experience",color:"green"},{icon:v.A,label:"Guaranteed",desc:"95%+ confidence",color:"orange"}].map((e,t)=>(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:(0,P.cn)("bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border rounded-xl p-4 hover:border-white/20 transition-all duration-300","purple"===e.color&&"border-purple-500/20 hover:border-purple-400/40","blue"===e.color&&"border-blue-500/20 hover:border-blue-400/40","green"===e.color&&"border-green-500/20 hover:border-green-400/40","orange"===e.color&&"border-orange-500/20 hover:border-orange-400/40"),children:[(0,r.jsx)(e.icon,{className:(0,P.cn)("w-8 h-8 mb-2","purple"===e.color&&"text-purple-400","blue"===e.color&&"text-blue-400","green"===e.color&&"text-green-400","orange"===e.color&&"text-orange-400")}),(0,r.jsx)("h3",{className:"font-semibold text-white text-sm",children:e.label}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:e.desc})]},t))})]}),(0,r.jsx)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.2},className:"max-w-4xl mx-auto",children:(0,r.jsx)("div",{className:"bg-gradient-to-br from-gray-900/80 via-gray-800/60 to-gray-900/80 backdrop-blur-xl border border-white/10 rounded-2xl p-8 shadow-2xl",children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("label",{className:"block text-lg font-semibold text-white",children:"Research Topic"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",value:n.topic,onChange:e=>c({...n,topic:e.target.value}),placeholder:"Enter any topic for autonomous content generation...",className:"w-full bg-black/50 border border-white/20 rounded-xl px-4 py-4 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all text-lg"}),(0,r.jsx)(y,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("label",{className:"block font-medium text-gray-300",children:"Target Audience"}),(0,r.jsxs)("select",{value:n.targetAudience,onChange:e=>c({...n,targetAudience:e.target.value}),className:"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all",children:[(0,r.jsx)("option",{value:"general audience",children:"General Audience"}),(0,r.jsx)("option",{value:"professionals",children:"Industry Professionals"}),(0,r.jsx)("option",{value:"experts",children:"Subject Matter Experts"}),(0,r.jsx)("option",{value:"beginners",children:"Beginners"}),(0,r.jsx)("option",{value:"students",children:"Students & Academics"}),(0,r.jsx)("option",{value:"business leaders",children:"Business Leaders"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("label",{className:"block font-medium text-gray-300",children:"Content Tone"}),(0,r.jsxs)("select",{value:n.tone,onChange:e=>c({...n,tone:e.target.value}),className:"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all",children:[(0,r.jsx)("option",{value:"professional",children:"Professional"}),(0,r.jsx)("option",{value:"conversational",children:"Conversational"}),(0,r.jsx)("option",{value:"academic",children:"Academic"}),(0,r.jsx)("option",{value:"casual",children:"Casual"}),(0,r.jsx)("option",{value:"authoritative",children:"Authoritative"}),(0,r.jsx)("option",{value:"friendly",children:"Friendly"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("label",{className:"block font-medium text-gray-300",children:"Content Length"}),(0,r.jsxs)("select",{value:n.contentLength,onChange:e=>c({...n,contentLength:Number(e.target.value)}),className:"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all",children:[(0,r.jsx)("option",{value:1500,children:"Short Article (1,500 words)"}),(0,r.jsx)("option",{value:3e3,children:"Medium Article (3,000 words)"}),(0,r.jsx)("option",{value:5e3,children:"Long Article (5,000 words)"}),(0,r.jsx)("option",{value:7500,children:"Comprehensive (7,500 words)"}),(0,r.jsx)("option",{value:1e4,children:"Ultimate Guide (10,000 words)"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("label",{className:"block font-medium text-gray-300",children:"Autonomy Level"}),(0,r.jsxs)("select",{value:n.autonomyLevel,onChange:e=>c({...n,autonomyLevel:e.target.value}),className:"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all",children:[(0,r.jsx)("option",{value:"full",children:"Full Autonomy (Recommended)"}),(0,r.jsx)("option",{value:"guided",children:"Guided Generation"}),(0,r.jsx)("option",{value:"supervised",children:"Supervised Mode"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("label",{className:"block font-medium text-gray-300",children:["Confidence Threshold: ",Math.round(100*(n.confidenceThreshold||.95)),"%"]}),(0,r.jsx)("input",{type:"range",min:"0.8",max:"0.99",step:"0.01",value:n.confidenceThreshold,onChange:e=>c({...n,confidenceThreshold:Number(e.target.value)}),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-purple"}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:"Higher values ensure better quality but may take longer"})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("label",{className:"block font-medium text-gray-300",children:"Agent Capabilities"}),(0,r.jsx)("div",{className:"space-y-3",children:[{key:"enableWebSearch",label:"Real-time Web Research",icon:f.A},{key:"enableSelfImprovement",label:"Self-Improvement Learning",icon:b.A},{key:"enableVisualization",label:"Data Visualizations",icon:j.A},{key:"enableMemoryConsolidation",label:"Memory Consolidation",icon:N.A}].map(e=>(0,r.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer group",children:[(0,r.jsx)("input",{type:"checkbox",checked:n[e.key],onChange:t=>c({...n,[e.key]:t.target.checked}),className:"w-4 h-4 text-purple-600 bg-black border-gray-600 rounded focus:ring-purple-500"}),(0,r.jsx)(e.icon,{className:"w-4 h-4 text-gray-400 group-hover:text-purple-400 transition-colors"}),(0,r.jsx)("span",{className:"text-gray-300 group-hover:text-white transition-colors",children:e.label})]},e.key))})]})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("label",{className:"block font-medium text-gray-300",children:"Focus Keywords (Optional)"}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("input",{type:"text",value:h,onChange:e=>C(e.target.value),onKeyPress:e=>"Enter"===e.key&&U(),placeholder:"Add focus keywords...",className:"flex-1 bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-all"}),(0,r.jsx)("button",{onClick:U,className:"bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors flex items-center space-x-2",children:(0,r.jsx)(w.A,{className:"w-4 h-4"})})]}),n.keywords&&n.keywords.length>0&&(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-3",children:n.keywords.map((e,t)=>(0,r.jsxs)("span",{className:"bg-purple-900/50 text-purple-300 px-3 py-1 rounded-full text-sm border border-purple-500/30 flex items-center space-x-2",children:[(0,r.jsx)("span",{children:e}),(0,r.jsx)(k.A,{className:"w-3 h-3 cursor-pointer hover:text-white transition-colors",onClick:()=>O(e)})]},t))})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("label",{className:"block font-medium text-gray-300",children:"Custom Instructions (Optional)"}),(0,r.jsx)("textarea",{value:n.customInstructions,onChange:e=>c({...n,customInstructions:e.target.value}),placeholder:"Any specific requirements, style preferences, or focus areas...",rows:4,className:"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-all resize-none"})]}),(0,r.jsx)(o.N,{children:z&&(0,r.jsxs)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-red-900/50 border border-red-500/50 text-red-300 px-4 py-3 rounded-lg flex items-center space-x-2",children:[(0,r.jsx)(A.A,{className:"w-5 h-5 flex-shrink-0"}),(0,r.jsx)("span",{children:z})]})}),(0,r.jsxs)(l.P.button,{onClick:()=>{if(!n.topic.trim())return void q("Please enter a topic for autonomous generation");q(""),M(!0),E(!0)},disabled:!n.topic.trim()||L,whileHover:{scale:1.02},whileTap:{scale:.98},className:(0,P.cn)("w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 flex items-center justify-center space-x-3 text-lg shadow-lg shadow-purple-500/25",(!n.topic.trim()||L)&&"opacity-50 cursor-not-allowed"),children:[(0,r.jsx)(m.A,{className:"w-6 h-6"}),(0,r.jsx)("span",{children:"Deploy Autonomous Agent System"}),(0,r.jsx)(S.A,{className:"w-5 h-5"})]}),(0,r.jsx)("div",{className:"text-center text-sm text-gray-400",children:"The autonomous agent will research, plan, generate, and verify your content with guaranteed superiority"})]})})})]})]})]})}},76183:(e,t,s)=>{Promise.resolve().then(s.bind(s,79025))},79025:(e,t,s)=>{"use strict";s.d(t,{default:()=>n});var r=s(60687),a=s(82136);function n({children:e}){return(0,r.jsx)(a.SessionProvider,{children:e})}},84199:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx","default")},94431:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>l});var r=s(37413),a=s(7339),n=s.n(a);s(61135);var i=s(84199);let l={title:"Invincible - AI Content Generation Platform",description:"The ultimate content writing SaaS platform powered by advanced AI technology"};function o({children:e}){return(0,r.jsx)("html",{lang:"en",className:"dark",children:(0,r.jsx)("body",{className:`${n().className} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,suppressHydrationWarning:!0,children:(0,r.jsx)(i.default,{children:(0,r.jsx)("div",{className:"min-h-screen",children:e})})})})}},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(75324).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7719,5380,4101,7985,6863],()=>s(34626));module.exports=r})();