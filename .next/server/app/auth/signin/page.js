(()=>{var e={};e.id=4680,e.ids=[4680],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7036:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15047:(e,r,t)=>{Promise.resolve().then(t.bind(t,87578))},15935:(e,r,t)=>{Promise.resolve().then(t.bind(t,84199))},16764:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},43476:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l});var s=t(60687),n=t(82136),i=t(16189),a=t(43210),o=t(4355);function l(){let e=(0,i.useRouter)(),[r,t]=(0,a.useState)(!1),l=async()=>{t(!0);try{let r=await (0,n.signIn)("google",{callbackUrl:"/dashboard",redirect:!1});r?.ok?e.push("/dashboard"):console.error("Sign in failed:",r?.error)}catch(e){console.error("Sign in error:",e)}finally{t(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-white mb-2",children:"Invincible AI"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Your intelligent content creation platform"})]}),(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-xl border border-gray-800 rounded-2xl p-8 shadow-2xl",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Welcome Back"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Sign in to access your AI-powered workspace"})]}),(0,s.jsxs)("button",{onClick:l,disabled:r,className:"w-full flex items-center justify-center gap-3 bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-4 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)(o.F4b,{className:"w-5 h-5"}),r?"Signing in...":"Continue with Google"]}),(0,s.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-800",children:[(0,s.jsx)("p",{className:"text-sm text-gray-400 text-center mb-4",children:"What you'll get:"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm text-gray-300",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-violet-500 rounded-full"}),(0,s.jsx)("span",{children:"AI-powered content generation"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-violet-500 rounded-full"}),(0,s.jsx)("span",{children:"Smart quota management"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-violet-500 rounded-full"}),(0,s.jsx)("span",{children:"Advanced research capabilities"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-violet-500 rounded-full"}),(0,s.jsx)("span",{children:"Multi-format content creation"})]})]})]})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 text-center mt-6",children:["By signing in, you agree to our"," ",(0,s.jsx)("a",{href:"#",className:"text-violet-400 hover:text-violet-300",children:"Terms of Service"})," ","and"," ",(0,s.jsx)("a",{href:"#",className:"text-violet-400 hover:text-violet-300",children:"Privacy Policy"})]})]})})}},59432:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>h,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=t(65239),n=t(48088),i=t(88170),a=t.n(i),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,87578)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/auth/signin/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/auth/signin/page.tsx"],h={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},76183:(e,r,t)=>{Promise.resolve().then(t.bind(t,79025))},79025:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var s=t(60687),n=t(82136);function i({children:e}){return(0,s.jsx)(n.SessionProvider,{children:e})}},84199:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/components/SessionProvider.tsx","default")},87578:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/auth/signin/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/auth/signin/page.tsx","default")},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>o});var s=t(37413),n=t(7339),i=t.n(n);t(61135);var a=t(84199);let o={title:"Invincible - AI Content Generation Platform",description:"The ultimate content writing SaaS platform powered by advanced AI technology"};function l({children:e}){return(0,s.jsx)("html",{lang:"en",className:"dark",children:(0,s.jsx)("body",{className:`${i().className} bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 min-h-screen`,suppressHydrationWarning:!0,children:(0,s.jsx)(a.default,{children:(0,s.jsx)("div",{className:"min-h-screen",children:e})})})})}},96895:(e,r,t)=>{Promise.resolve().then(t.bind(t,43476))}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7719,5380,6279],()=>t(59432));module.exports=s})();