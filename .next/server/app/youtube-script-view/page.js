(()=>{var e={};e.id=8986,e.ids=[8986],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,t,r)=>{"use strict";r.d(t,{T:()=>a,cn:()=>i});var s=r(49384),n=r(82348);function i(...e){return(0,n.QP)((0,s.$)(e))}function a(e,t){if(!e)return t||"";try{return decodeURIComponent(e)}catch(r){return console.warn("URI decode failed, using fallback:",r),t||e}}},10219:(e,t,r)=>{Promise.resolve().then(r.bind(r,32902))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15795:(e,t,r)=>{Promise.resolve().then(r.bind(r,66572))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32902:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(60687),n=r(43210),i=r(82136),a=r(16189),o=r(97905),d=r(10022),l=r(28559),c=r(85814),u=r.n(c);r(4780);var p=r(42347);function h(){let{data:e,status:t}=(0,i.useSession)();(0,a.useRouter)();let[r,c]=(0,n.useState)(""),[h,x]=(0,n.useState)("YouTube Script"),[m,f]=(0,n.useState)(!0);if("loading"===t)return(0,s.jsx)("div",{className:"min-h-screen bg-[#0f0f0f] flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-red-400 border-t-transparent rounded-full mx-auto"}),(0,s.jsx)("p",{className:"text-[#aaa]",children:"Loading YouTube script..."})]})});if("unauthenticated"===t)return null;if(m)return(0,s.jsx)("div",{className:"min-h-screen bg-[#0f0f0f] flex items-center justify-center",children:(0,s.jsxs)("div",{className:"relative z-10 text-center",children:[(0,s.jsx)(o.P.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-16 h-16 border-4 border-red-600 border-t-transparent rounded-full mx-auto mb-4"}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-white mb-2",children:"Loading YouTube Script"}),(0,s.jsx)("p",{className:"text-[#aaa]",children:"Preparing your viral content..."})]})});if(!r)return(0,s.jsx)("div",{className:"min-h-screen bg-[#0f0f0f] flex items-center justify-center",children:(0,s.jsxs)("div",{className:"relative z-10 text-center",children:[(0,s.jsx)("div",{className:"p-3 bg-gradient-to-r from-red-600 to-red-700 rounded-2xl mb-6 w-fit mx-auto",children:(0,s.jsx)(d.A,{className:"w-8 h-8 text-white"})}),(0,s.jsx)("h2",{className:"text-xl font-semibold text-white mb-2",children:"No YouTube Script Found"}),(0,s.jsx)("p",{className:"text-[#aaa] mb-6",children:"No YouTube script found in your session."}),(0,s.jsxs)(u(),{href:"/content",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl hover:scale-105 transition-transform",children:[(0,s.jsx)(l.A,{className:"w-5 h-5 mr-2"}),"Back to Content Library"]})]})});let b=r.split(/\s+/).filter(e=>e.length>0).length;return(0,s.jsx)(p.A,{title:h,content:r,wordCount:b})}},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66572:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/youtube-script-view/page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/youtube-script-view/page.tsx","default")},69932:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(65239),n=r(48088),i=r(88170),a=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["youtube-script-view",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66572)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/youtube-script-view/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"/Users/<USER>/Desktop/old invincible with deepresearch/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["/Users/<USER>/Desktop/old invincible with deepresearch/src/app/youtube-script-view/page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/youtube-script-view/page",pathname:"/youtube-script-view",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7719,5380,4101,7985,2403],()=>r(69932));module.exports=s})();