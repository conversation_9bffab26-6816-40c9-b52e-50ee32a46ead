(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6949],{6478:(e,t,a)=>{Promise.resolve().then(a.bind(a,41051))},24357:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(90163).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},35169:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(90163).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},41051:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var r=a(95155),s=a(12115),l=a(1978),i=a(35169),n=a(57434),o=a(53311),d=a(47924),c=a(71539),h=a(24357),x=a(91788),u=a(6874),p=a.n(u),m=a(12108),b=a(35695),w=a(59434);function g(){let{data:e,status:t}=(0,m.useSession)(),a=(0,b.useRouter)(),[u,g]=(0,s.useState)({topic:"",title:"",wordCount:1e3,tone:"professional",includeResearch:!0,targetKeyword:"",competitors:"",targetAudience:""}),[j,y]=(0,s.useState)(!1),[f,v]=(0,s.useState)(""),[k,N]=(0,s.useState)(!1);if((0,s.useEffect)(()=>{"unauthenticated"===t&&a.push("/login")},[t,a]),"loading"===t)return(0,r.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-pink-400 border-t-transparent rounded-full mx-auto"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Loading..."})]})});if("unauthenticated"===t)return null;let A=async e=>{e.preventDefault(),y(!0);try{let e=await fetch("/api/generate/blog",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(u)}),t=await e.json();t.success?v(t.content):alert("Error: "+t.error)}catch(e){alert("Failed to generate blog post")}finally{y(!1)}};return(0,r.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,r.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-pink-900/20 via-black to-rose-900/20"}),(0,r.jsx)(l.P.div,{animate:{x:[0,100,0],y:[0,-100,0]},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-pink-500/10 rounded-full blur-[100px]"}),(0,r.jsx)(l.P.div,{animate:{x:[0,-100,0],y:[0,100,0]},transition:{duration:15,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-rose-500/10 rounded-full blur-[120px]"})]}),(0,r.jsxs)("div",{className:"relative z-10",children:[(0,r.jsx)("div",{className:"border-b border-white/10 bg-black/60 backdrop-blur-xl",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,r.jsx)("div",{className:"flex items-center justify-between",children:(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(p(),{href:"/dashboard",children:(0,r.jsx)(l.P.button,{whileHover:{scale:1.05},className:"p-2 rounded-xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all",children:(0,r.jsx)(i.A,{className:"w-5 h-5 text-white"})})}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-3 rounded-2xl bg-gradient-to-br from-pink-600 to-rose-600",children:(0,r.jsx)(n.A,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white",children:"Blog Writer"}),(0,r.jsx)("p",{className:"text-gray-400",children:"SEO-Optimized Content Generation"})]})]})]})})})}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsx)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"space-y-6",children:(0,r.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,r.jsxs)("h2",{className:"text-xl font-semibold text-white mb-6 flex items-center",children:[(0,r.jsx)(o.A,{className:"w-5 h-5 mr-2 text-pink-400"}),"Content Configuration"]}),(0,r.jsxs)("form",{onSubmit:A,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Blog Topic *"}),(0,r.jsx)("input",{type:"text",value:u.topic,onChange:e=>g({...u,topic:e.target.value}),placeholder:"Enter your blog topic...",required:!0,className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Title (Optional)"}),(0,r.jsx)("input",{type:"text",value:u.title,onChange:e=>g({...u,title:e.target.value}),placeholder:"Leave blank for AI-generated title",className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Word Count"}),(0,r.jsxs)("select",{value:u.wordCount,onChange:e=>g({...u,wordCount:parseInt(e.target.value)}),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-pink-500/50 transition-all",children:[(0,r.jsx)("option",{value:500,children:"500 words"}),(0,r.jsx)("option",{value:1e3,children:"1,000 words"}),(0,r.jsx)("option",{value:1500,children:"1,500 words"}),(0,r.jsx)("option",{value:2e3,children:"2,000 words"}),(0,r.jsx)("option",{value:3e3,children:"3,000 words"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Tone"}),(0,r.jsxs)("select",{value:u.tone,onChange:e=>g({...u,tone:e.target.value}),className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white focus:bg-white/20 focus:border-pink-500/50 transition-all",children:[(0,r.jsx)("option",{value:"professional",children:"Professional"}),(0,r.jsx)("option",{value:"casual",children:"Casual"}),(0,r.jsx)("option",{value:"conversational",children:"Conversational"}),(0,r.jsx)("option",{value:"authoritative",children:"Authoritative"}),(0,r.jsx)("option",{value:"friendly",children:"Friendly"}),(0,r.jsx)("option",{value:"academic",children:"Academic"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl border border-white/10",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(d.A,{className:"w-5 h-5 text-pink-400"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-white",children:"Include Research"}),(0,r.jsx)("p",{className:"text-sm text-gray-400",children:"Enhance content with web research"})]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>g({...u,includeResearch:!u.includeResearch}),className:(0,w.cn)("relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none",u.includeResearch?"bg-pink-600":"bg-gray-600"),children:(0,r.jsx)("span",{className:(0,w.cn)("pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out",u.includeResearch?"translate-x-5":"translate-x-0")})})]}),(0,r.jsxs)("button",{type:"button",onClick:()=>N(!k),className:"w-full text-left text-pink-400 hover:text-pink-300 font-medium transition-colors",children:[k?"Hide":"Show"," Advanced Options"]}),k&&(0,r.jsxs)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Target Keyword"}),(0,r.jsx)("input",{type:"text",value:u.targetKeyword,onChange:e=>g({...u,targetKeyword:e.target.value}),placeholder:"Primary SEO keyword",className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Competitors"}),(0,r.jsx)("input",{type:"text",value:u.competitors,onChange:e=>g({...u,competitors:e.target.value}),placeholder:"Competitor URLs or names",className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Target Audience"}),(0,r.jsx)("input",{type:"text",value:u.targetAudience,onChange:e=>g({...u,targetAudience:e.target.value}),placeholder:"Who is this content for?",className:"w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-gray-400 focus:bg-white/20 focus:border-pink-500/50 transition-all"})]})]}),(0,r.jsx)(l.P.button,{type:"submit",disabled:j||!u.topic,whileHover:{scale:1.02},whileTap:{scale:.98},className:"w-full py-4 bg-gradient-to-r from-pink-600 to-rose-600 text-white font-semibold rounded-xl hover:from-pink-700 hover:to-rose-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2",children:j?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-b-2 border-white"}),(0,r.jsx)("span",{children:"Generating Blog Post..."})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(c.A,{className:"w-5 h-5"}),(0,r.jsx)("span",{children:"Generate Blog Post"})]})})]})]})}),(0,r.jsx)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:(0,r.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,r.jsx)("h2",{className:"text-xl font-semibold text-white",children:"Generated Content"}),f&&(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)("button",{onClick:()=>{navigator.clipboard.writeText(f)},className:"p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors",children:(0,r.jsx)(h.A,{className:"w-4 h-4 text-white"})}),(0,r.jsx)("button",{onClick:()=>{let e=new Blob([f],{type:"text/markdown"}),t=URL.createObjectURL(e),a=document.createElement("a");a.href=t,a.download="".concat(u.topic.replace(/\s+/g,"-").toLowerCase(),".md"),a.click()},className:"p-2 bg-white/10 hover:bg-white/20 border border-white/20 rounded-lg transition-colors",children:(0,r.jsx)(x.A,{className:"w-4 h-4 text-white"})})]})]}),f?(0,r.jsx)("div",{className:"bg-black/40 rounded-xl p-4 border border-white/10 max-h-[600px] overflow-y-auto",children:(0,r.jsx)("pre",{className:"text-gray-300 whitespace-pre-wrap font-mono text-sm leading-relaxed",children:f})}):(0,r.jsxs)("div",{className:"text-center py-12 text-gray-400",children:[(0,r.jsx)(n.A,{className:"w-16 h-16 mx-auto mb-4 opacity-50"}),(0,r.jsx)("p",{children:"Your generated blog post will appear here..."})]})]})})]})})]})]})}},47924:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(90163).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},53311:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(90163).A)("Sparkles",[["path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z",key:"17u4zn"}],["path",{d:"M5 3v4",key:"bklmnn"}],["path",{d:"M19 17v4",key:"iiml17"}],["path",{d:"M3 5h4",key:"nem4j1"}],["path",{d:"M17 19h4",key:"lbex7p"}]])},57434:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(90163).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},59434:(e,t,a)=>{"use strict";a.d(t,{T:()=>i,cn:()=>l});var r=a(52596),s=a(39688);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function i(e,t){if(!e)return t||"";try{return decodeURIComponent(e)}catch(a){return console.warn("URI decode failed, using fallback:",a),t||e}}},71539:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(90163).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},91788:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(90163).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[2108,9061,6635,8441,1684,7358],()=>t(6478)),_N_E=e.O()}]);