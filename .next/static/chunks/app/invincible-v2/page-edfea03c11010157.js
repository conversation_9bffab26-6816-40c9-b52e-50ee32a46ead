(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9941],{13052:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(90163).A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(90163).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(90163).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},59434:(e,t,a)=>{"use strict";a.d(t,{T:()=>l,cn:()=>n});var s=a(52596),r=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function l(e,t){if(!e)return t||"";try{return decodeURIComponent(e)}catch(a){return console.warn("URI decode failed, using fallback:",a),t||e}}},62511:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>_});var s=a(95155),r=a(12115),n=a(35695),l=a(1978),i=a(60760),o=a(6874),c=a.n(o),d=a(12108),x=a(49376),u=a(35169),m=a(17951),p=a(90163);let h=(0,p.A)("Network",[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]]);var g=a(33109),b=a(75525);let y=(0,p.A)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);var v=a(47924),j=a(58320),f=a(54213),N=a(84616),w=a(54416),A=a(85339),k=a(13052),S=a(59434),C=a(73314),P=a(16785),L=a(57434),E=a(79397),I=a(34869),M=a(41684),T=a(40646),z=a(51154);let R=e=>{let{config:t,onComplete:a,onError:o,onReset:c,isSaving:d}=e;(0,n.useRouter)();let[p,h]=(0,r.useState)(!1),[j,f]=(0,r.useState)(""),[N,w]=(0,r.useState)(0),[k,S]=(0,r.useState)([]),[R,_]=(0,r.useState)([]),[O,G]=(0,r.useState)([]),[q,D]=(0,r.useState)([]),[U,V]=(0,r.useState)([]),[W,F]=(0,r.useState)({}),[B,H]=(0,r.useState)(null),[J,Q]=(0,r.useState)(""),[K,X]=(0,r.useState)("connecting"),[$,Y]=(0,r.useState)(0),[Z,ee]=(0,r.useState)([]),et=(0,r.useRef)(null),ea=(0,r.useRef)(null);(0,r.useEffect)(()=>{let e=setTimeout(()=>{es()},1e3);return()=>{clearTimeout(e),ea.current&&ea.current.close()}},[]);let es=()=>{if(p)return;h(!0),w(0),S([]),_([]),G([]),D([]),V([]),F({}),H(null),Q(""),X("connecting"),Y(0),ee([]);let e=new URLSearchParams({topic:t.topic,contentLength:(t.contentLength||3e3).toString(),tone:t.tone||"professional",targetAudience:t.targetAudience||"general audience",customInstructions:t.customInstructions||"",autonomyLevel:t.autonomyLevel||"full",confidenceThreshold:(t.confidenceThreshold||.95).toString(),enableWebSearch:t.enableWebSearch?"true":"false",enableSelfImprovement:t.enableSelfImprovement?"true":"false",enableVisualization:t.enableVisualization?"true":"false",enableMemoryConsolidation:t.enableMemoryConsolidation?"true":"false"}),s=new EventSource("/api/invincible-v2/stream?".concat(e.toString()));ea.current=s;let r=e=>{try{if(!e||"undefined"===e||"null"===e)return console.warn("Received invalid SSE data:",e),null;return JSON.parse(e)}catch(t){return console.error("Failed to parse SSE data:",e,t),null}};s.onopen=()=>{X("connected"),er("connection",{message:"Multi-Agent System Connected",timestamp:Date.now()})},s.addEventListener("session",e=>{let t=r(e.data);t&&er("session",t)}),s.addEventListener("agent_state",e=>{let t=r(e.data);t&&t.state&&(_(e=>[...e,t.state]),f(t.state.phase),G(e=>{let a=[...e];return a.includes(t.state.agent)||a.push(t.state.agent),a}),er("agent_state",t))}),s.addEventListener("search_query",e=>{let t=r(e.data);t&&t.query&&(D(e=>[...e,t.query]),er("search_query",t))}),s.addEventListener("research_source",e=>{let t=r(e.data);t&&t.source&&(V(e=>[...e,t.source]),er("research_source",t))}),s.addEventListener("agent_metrics",e=>{let t=r(e.data);t&&(F(t),er("agent_metrics",t))}),s.addEventListener("confidence",e=>{let t=r(e.data);t&&"number"==typeof t.confidence&&(Y(t.confidence),er("confidence",t))}),s.addEventListener("improvement",e=>{let t=r(e.data);t&&t.improvement&&(ee(e=>[...e,t.improvement]),er("improvement",t))}),s.addEventListener("progress",e=>{let t=r(e.data);t&&"number"==typeof t.progress&&(w(t.progress),er("progress",t))}),s.addEventListener("content",e=>{let t=r(e.data);t&&(H(t),er("content",t))}),s.addEventListener("complete",e=>{let t=r(e.data);t&&(H(t),w(100),X("complete"),h(!1),er("complete",t),null==a||a(t)),s.close()}),s.addEventListener("error",e=>{let t=r(e.data),a=(null==t?void 0:t.error)||"Unknown error occurred";Q(a),X("error"),h(!1),er("error",t),null==o||o(a),s.close()}),s.onerror=e=>{console.error("SSE Connection Error:",e),Q("Connection lost. Please try again."),X("error"),h(!1),null==o||o("Connection lost. Please try again."),s.close()}},er=(e,t)=>{let a={type:e,data:t,timestamp:Date.now()};S(e=>[...e,a]),setTimeout(()=>{et.current&&(et.current.scrollTop=et.current.scrollHeight)},100)},en=e=>({initializing:{icon:C.A,label:"Initializing",color:"text-blue-400",description:"Starting multi-agent system..."},analysis:{icon:x.A,label:"Master Analysis",color:"text-purple-400",description:"Understanding requirements and complexity..."},research:{icon:v.A,label:"Research Phase",color:"text-green-400",description:"Deep web research and competitive analysis..."},planning:{icon:P.A,label:"Strategic Planning",color:"text-orange-400",description:"Creating content strategy and structure..."},generation:{icon:L.A,label:"Content Generation",color:"text-pink-400",description:"Writing superior content..."},verification:{icon:b.A,label:"Verification",color:"text-red-400",description:"Quality assurance and fact-checking..."},learning:{icon:g.A,label:"Learning Phase",color:"text-indigo-400",description:"Self-improvement and optimization..."}})[e]||{icon:E.A,label:"Processing",color:"text-gray-400",description:"Agent processing..."},el=e=>({master:{icon:m.A,color:"text-purple-400",name:"Master Agent"},research:{icon:v.A,color:"text-blue-400",name:"Research Agent"},planning:{icon:P.A,color:"text-green-400",name:"Planning Agent"},execution:{icon:L.A,color:"text-orange-400",name:"Execution Agent"},verification:{icon:b.A,color:"text-red-400",name:"Verification Agent"},learning:{icon:g.A,color:"text-indigo-400",name:"Learning Agent"}})[e]||{icon:E.A,color:"text-gray-400",name:"System Agent"};return(0,s.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,s.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-950/20 via-black to-indigo-950/20"}),(0,s.jsx)(l.P.div,{animate:{opacity:[.3,.6,.3]},transition:{duration:4,repeat:1/0},className:"absolute top-1/4 left-1/4 w-96 h-96 bg-purple-600/10 rounded-full blur-3xl"}),(0,s.jsx)(l.P.div,{animate:{opacity:[.4,.7,.4]},transition:{duration:6,repeat:1/0},className:"absolute bottom-1/3 right-1/4 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl"})]}),(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsx)(l.P.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"border-b border-white/10 backdrop-blur-xl bg-black/50",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("button",{onClick:c,className:"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors group",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 group-hover:-translate-x-1 transition-transform"}),(0,s.jsx)("span",{children:"Back to Configuration"})]}),(0,s.jsx)("div",{className:"w-px h-6 bg-white/20"}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl blur-lg opacity-60"}),(0,s.jsx)("div",{className:"relative bg-black rounded-xl p-2.5 border border-white/20",children:(0,s.jsx)(x.A,{className:"w-6 h-6 text-white"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent",children:"Invincible V2"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Autonomous Generation in Progress"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm ".concat((()=>{switch(K){case"connected":return"text-green-400";case"error":return"text-red-400";case"complete":return"text-blue-400";default:return"text-yellow-400"}})()),children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-current rounded-full animate-pulse"}),(0,s.jsx)("span",{children:"connected"===K?"Agent System Active":"complete"===K?"Generation Complete":"error"===K?"Connection Error":"Connecting..."})]}),$>0&&(0,s.jsxs)("div",{className:"text-sm text-purple-400",children:["Confidence: ",Math.round(100*$),"%"]})]})]})})}),(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-1 space-y-6",children:[(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Current Phase"}),j?(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[r.createElement(en(j).icon,{className:"w-8 h-8 ".concat(en(j).color)}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"font-semibold text-white",children:en(j).label}),(0,s.jsx)("div",{className:"text-sm text-gray-400",children:en(j).description})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-gray-400",children:"Progress"}),(0,s.jsxs)("span",{className:"text-white",children:[Math.round(N),"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,s.jsx)(l.P.div,{className:"bg-gradient-to-r from-purple-500 to-indigo-500 h-2 rounded-full",initial:{width:0},animate:{width:"".concat(N,"%")},transition:{duration:.5}})})]})]}):(0,s.jsx)("div",{className:"text-gray-400 text-center py-4",children:"Waiting for agent initialization..."})]}),(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Agent Network"}),(0,s.jsx)("div",{className:"space-y-3",children:["master","research","planning","execution","verification","learning"].map(e=>{let t=el(e),a=O.includes(e),n=R.filter(t=>t.agent===e).slice(-1)[0];return(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border transition-all ".concat(a?"border-purple-500/50 bg-purple-900/20":"border-gray-700 bg-gray-800/30"),children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[r.createElement(t.icon,{className:"w-5 h-5 ".concat(a?t.color:"text-gray-500")}),(0,s.jsx)("span",{className:"font-medium ".concat(a?"text-white":"text-gray-500"),children:t.name})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[n&&(0,s.jsxs)("span",{className:"text-xs text-gray-400",children:[Math.round(100*n.confidence),"%"]}),(0,s.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(a?"bg-green-400 animate-pulse":"bg-gray-600")})]})]},e)})})]}),(q.length>0||U.length>0)&&(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Research Progress"}),(0,s.jsxs)("div",{className:"space-y-4",children:[q.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsx)(v.A,{className:"w-4 h-4 text-blue-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"Search Queries"}),(0,s.jsxs)("span",{className:"text-xs text-blue-400",children:["(",q.length,")"]})]}),(0,s.jsx)("div",{className:"max-h-24 overflow-y-auto space-y-1",children:q.slice(-3).map((e,t)=>(0,s.jsxs)("div",{className:"text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded",children:['"',e,'"']},t))})]}),U.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,s.jsx)(I.A,{className:"w-4 h-4 text-green-400"}),(0,s.jsx)("span",{className:"text-sm text-gray-300",children:"Sources Found"}),(0,s.jsxs)("span",{className:"text-xs text-green-400",children:["(",U.length,")"]})]}),(0,s.jsx)("div",{className:"max-h-24 overflow-y-auto space-y-1",children:U.slice(-3).map((e,t)=>(0,s.jsx)("div",{className:"text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded",children:e},t))})]})]})]})]}),(0,s.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Live Agent Activity"}),(0,s.jsx)("div",{ref:et,className:"h-80 overflow-y-auto space-y-2 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800",children:(0,s.jsx)(i.N,{children:0===k.length?(0,s.jsxs)("div",{className:"text-gray-400 text-center py-8",children:[(0,s.jsx)(M.A,{className:"w-8 h-8 mx-auto mb-2 text-gray-600"}),(0,s.jsx)("p",{children:"Waiting for agent events..."})]}):k.map((e,t)=>(0,s.jsxs)(l.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-start space-x-3 p-3 rounded-lg bg-gray-800/50 border border-gray-700/50",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,s.jsx)("span",{className:"text-xs text-purple-400 font-medium",children:e.type.toUpperCase()}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:new Date(e.timestamp).toLocaleTimeString()})]}),(0,s.jsx)("div",{className:"text-sm text-gray-300",children:"agent_state"===e.type&&e.data.state?"".concat(e.data.state.agent,": ").concat(e.data.state.action):"search_query"===e.type?'Searching: "'.concat(e.data.query,'"'):"research_source"===e.type?"Found source: ".concat(e.data.source):"connection"===e.type?e.data.message:JSON.stringify(e.data).substring(0,100)+"..."})]})]},t))})})]}),Z.length>0&&(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Self-Improvement Insights"}),(0,s.jsx)("div",{className:"space-y-2",children:Z.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg bg-green-900/20 border border-green-500/30",children:[(0,s.jsx)(y,{className:"w-4 h-4 text-green-400 mt-0.5 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-sm text-green-300",children:e})]},t))})]}),B&&(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-white",children:"Generation Complete"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(T.A,{className:"w-5 h-5 text-green-400"}),(0,s.jsx)("span",{className:"text-green-400 font-medium",children:"Success"})]})]}),B.article&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"bg-gray-800/50 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"font-semibold text-white mb-2",children:B.article.title}),(0,s.jsx)("p",{className:"text-gray-300 text-sm",children:B.article.metaDescription})]}),(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-purple-400",children:B.article.wordCount||0}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:"Words"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-400",children:B.article.seoScore||0}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:"SEO Score"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"text-2xl font-bold text-blue-400",children:[Math.round(100*(B.confidence||0)),"%"]}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:"Confidence"})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-orange-400",children:B.article.readabilityScore||0}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:"Readability"})]})]})]})]}),J&&(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-red-900/50 border border-red-500/50 rounded-2xl p-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(A.A,{className:"w-6 h-6 text-red-400 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-red-300",children:"Generation Error"}),(0,s.jsx)("p",{className:"text-red-200",children:J})]})]})}),d&&(0,s.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-blue-900/50 border border-blue-500/50 rounded-2xl p-6",children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(z.A,{className:"w-6 h-6 text-blue-400 animate-spin flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-blue-300",children:"Saving Article"}),(0,s.jsx)("p",{className:"text-blue-200",children:"Storing your autonomous creation..."})]})]})})]})]})})]})]})};function _(){let e=(0,n.useRouter)(),{data:t,status:a}=(0,d.useSession)(),[o,p]=(0,r.useState)({topic:"",customInstructions:"",targetAudience:"general audience",contentLength:3e3,tone:"professional",keywords:[],autonomyLevel:"full",confidenceThreshold:.95,enableWebSearch:!0,enableSelfImprovement:!0,enableVisualization:!0,enableMemoryConsolidation:!0}),[C,P]=(0,r.useState)(""),[L,E]=(0,r.useState)(!1),[I,M]=(0,r.useState)(!1),[T,z]=(0,r.useState)(null),[_,O]=(0,r.useState)(""),[G,q]=(0,r.useState)(!1);if((0,r.useEffect)(()=>{"unauthenticated"===a&&(window.location.href="/login")},[a]),"loading"===a)return(0,s.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"animate-spin w-16 h-16 border-4 border-purple-400 border-t-transparent rounded-full mx-auto"}),(0,s.jsx)(x.A,{className:"absolute inset-0 m-auto w-6 h-6 text-purple-400"})]}),(0,s.jsx)("p",{className:"text-gray-400",children:"Initializing Autonomous Agent..."})]})});if("unauthenticated"===a)return null;let D=async t=>{z(t),E(!1),q(!0);try{var a,s,r,n,l,i,c,d,x;let u=await fetch("/api/articles/store",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:(null==(a=t.article)?void 0:a.title)||"Autonomous Article: ".concat(o.topic),content:(null==(s=t.article)?void 0:s.content)||t.content||"",type:"invincible-v2",metadata:{topic:o.topic,tone:o.tone,targetAudience:o.targetAudience,contentLength:o.contentLength,customInstructions:o.customInstructions,keywords:o.keywords,autonomyLevel:o.autonomyLevel,confidenceThreshold:o.confidenceThreshold,executionTime:null==(r=t.metrics)?void 0:r.executionTime,agentInvocations:null==(n=t.metrics)?void 0:n.agentInvocations,toolUsage:null==(l=t.metrics)?void 0:l.toolUsage,confidence:t.confidence,improvements:t.improvements,seoScore:null==(i=t.article)?void 0:i.seoScore,readabilityScore:null==(c=t.article)?void 0:c.readabilityScore,originalityScore:null==(d=t.article)?void 0:d.originalityScore,competitorSuperiority:null==(x=t.article)?void 0:x.competitorSuperiority,generatedAt:new Date().toISOString(),version:"2.0"},tone:o.tone,language:"en"})});if(!u.ok){let e=await u.text();throw Error("Failed to save article: ".concat(e))}let m=await u.json();if(m.success)await new Promise(e=>setTimeout(e,1500)),e.push("/article-view/".concat(m.article.id));else throw Error(m.error||"Failed to save article")}catch(e){console.error("Error saving article:",e),q(!1),O("Article generated successfully but failed to save: ".concat(e instanceof Error?e.message:"Unknown error",". Please try again."))}},U=()=>{var e;!C.trim()||(null==(e=o.keywords)?void 0:e.includes(C.trim()))||(p({...o,keywords:[...o.keywords||[],C.trim()]}),P(""))},V=e=>{var t;p({...o,keywords:(null==(t=o.keywords)?void 0:t.filter(t=>t!==e))||[]})};return I?(0,s.jsx)(R,{config:o,onComplete:D,onError:e=>{O(e),E(!1)},onReset:()=>{M(!1),E(!1),z(null),O(""),q(!1)},isSaving:G}):(0,s.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,s.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-950/20 via-black to-indigo-950/20"}),(0,s.jsx)(l.P.div,{animate:{scale:[1,1.1,1],opacity:[.3,.5,.3]},transition:{duration:8,repeat:1/0,ease:"easeInOut"},className:"absolute top-1/4 left-1/6 w-96 h-96 bg-purple-600/10 rounded-full blur-3xl"}),(0,s.jsx)(l.P.div,{animate:{scale:[1.1,1,1.1],opacity:[.4,.6,.4]},transition:{duration:10,repeat:1/0,ease:"easeInOut"},className:"absolute bottom-1/3 right-1/6 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl"}),(0,s.jsx)(l.P.div,{animate:{scale:[1,1.2,1],opacity:[.2,.4,.2]},transition:{duration:12,repeat:1/0,ease:"easeInOut"},className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-pink-600/5 rounded-full blur-3xl"})]}),(0,s.jsxs)("div",{className:"relative z-10",children:[(0,s.jsx)(l.P.header,{initial:{opacity:0,y:-20},animate:{opacity:1,y:0},className:"border-b border-white/10 backdrop-blur-xl bg-black/50",children:(0,s.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)(c(),{href:"/dashboard",className:"flex items-center space-x-2 text-gray-400 hover:text-white transition-colors group",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 group-hover:-translate-x-1 transition-transform"}),(0,s.jsx)("span",{children:"Dashboard"})]}),(0,s.jsx)("div",{className:"w-px h-6 bg-white/20"}),(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl blur-lg opacity-60"}),(0,s.jsx)("div",{className:"relative bg-black rounded-xl p-2.5 border border-white/20",children:(0,s.jsx)(x.A,{className:"w-6 h-6 text-white"})})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent",children:"Invincible V2"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Autonomous Content Generation Agent"})]})]})]}),(0,s.jsx)("div",{className:"flex items-center space-x-3",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,s.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse"}),(0,s.jsx)("span",{children:"Agent System Online"})]})})]})})}),(0,s.jsxs)(l.P.section,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"max-w-7xl mx-auto px-6 py-12",children:[(0,s.jsxs)("div",{className:"text-center space-y-6 mb-12",children:[(0,s.jsxs)("div",{className:"inline-flex items-center space-x-2 bg-purple-900/30 px-4 py-2 rounded-full border border-purple-500/30",children:[(0,s.jsx)(m.A,{className:"w-4 h-4 text-purple-400"}),(0,s.jsx)("span",{className:"text-purple-300 text-sm font-medium",children:"Next-Generation Autonomous System"})]}),(0,s.jsxs)("h2",{className:"text-4xl md:text-6xl font-bold text-white leading-tight",children:["Create ",(0,s.jsx)("span",{className:"bg-gradient-to-r from-purple-400 via-pink-400 to-indigo-400 bg-clip-text text-transparent",children:"Superior"})," Content"]}),(0,s.jsxs)("p",{className:"text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed",children:["Deploy a fully autonomous multi-agent system that researches, plans, generates, and verifies content with ",(0,s.jsx)("span",{className:"text-purple-400 font-semibold",children:"guaranteed superiority"})," over competition."]}),(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mt-8",children:[{icon:x.A,label:"Autonomous",desc:"Self-directed reasoning",color:"purple"},{icon:h,label:"Multi-Agent",desc:"6 specialized agents",color:"blue"},{icon:g.A,label:"Self-Improving",desc:"Learns from experience",color:"green"},{icon:b.A,label:"Guaranteed",desc:"95%+ confidence",color:"orange"}].map((e,t)=>(0,s.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:(0,S.cn)("bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border rounded-xl p-4 hover:border-white/20 transition-all duration-300","purple"===e.color&&"border-purple-500/20 hover:border-purple-400/40","blue"===e.color&&"border-blue-500/20 hover:border-blue-400/40","green"===e.color&&"border-green-500/20 hover:border-green-400/40","orange"===e.color&&"border-orange-500/20 hover:border-orange-400/40"),children:[(0,s.jsx)(e.icon,{className:(0,S.cn)("w-8 h-8 mb-2","purple"===e.color&&"text-purple-400","blue"===e.color&&"text-blue-400","green"===e.color&&"text-green-400","orange"===e.color&&"text-orange-400")}),(0,s.jsx)("h3",{className:"font-semibold text-white text-sm",children:e.label}),(0,s.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:e.desc})]},t))})]}),(0,s.jsx)(l.P.div,{initial:{opacity:0,y:30},animate:{opacity:1,y:0},transition:{delay:.2},className:"max-w-4xl mx-auto",children:(0,s.jsx)("div",{className:"bg-gradient-to-br from-gray-900/80 via-gray-800/60 to-gray-900/80 backdrop-blur-xl border border-white/10 rounded-2xl p-8 shadow-2xl",children:(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block text-lg font-semibold text-white",children:"Research Topic"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",value:o.topic,onChange:e=>p({...o,topic:e.target.value}),placeholder:"Enter any topic for autonomous content generation...",className:"w-full bg-black/50 border border-white/20 rounded-xl px-4 py-4 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all text-lg"}),(0,s.jsx)(y,{className:"absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block font-medium text-gray-300",children:"Target Audience"}),(0,s.jsxs)("select",{value:o.targetAudience,onChange:e=>p({...o,targetAudience:e.target.value}),className:"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all",children:[(0,s.jsx)("option",{value:"general audience",children:"General Audience"}),(0,s.jsx)("option",{value:"professionals",children:"Industry Professionals"}),(0,s.jsx)("option",{value:"experts",children:"Subject Matter Experts"}),(0,s.jsx)("option",{value:"beginners",children:"Beginners"}),(0,s.jsx)("option",{value:"students",children:"Students & Academics"}),(0,s.jsx)("option",{value:"business leaders",children:"Business Leaders"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block font-medium text-gray-300",children:"Content Tone"}),(0,s.jsxs)("select",{value:o.tone,onChange:e=>p({...o,tone:e.target.value}),className:"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all",children:[(0,s.jsx)("option",{value:"professional",children:"Professional"}),(0,s.jsx)("option",{value:"conversational",children:"Conversational"}),(0,s.jsx)("option",{value:"academic",children:"Academic"}),(0,s.jsx)("option",{value:"casual",children:"Casual"}),(0,s.jsx)("option",{value:"authoritative",children:"Authoritative"}),(0,s.jsx)("option",{value:"friendly",children:"Friendly"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block font-medium text-gray-300",children:"Content Length"}),(0,s.jsxs)("select",{value:o.contentLength,onChange:e=>p({...o,contentLength:Number(e.target.value)}),className:"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all",children:[(0,s.jsx)("option",{value:1500,children:"Short Article (1,500 words)"}),(0,s.jsx)("option",{value:3e3,children:"Medium Article (3,000 words)"}),(0,s.jsx)("option",{value:5e3,children:"Long Article (5,000 words)"}),(0,s.jsx)("option",{value:7500,children:"Comprehensive (7,500 words)"}),(0,s.jsx)("option",{value:1e4,children:"Ultimate Guide (10,000 words)"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block font-medium text-gray-300",children:"Autonomy Level"}),(0,s.jsxs)("select",{value:o.autonomyLevel,onChange:e=>p({...o,autonomyLevel:e.target.value}),className:"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all",children:[(0,s.jsx)("option",{value:"full",children:"Full Autonomy (Recommended)"}),(0,s.jsx)("option",{value:"guided",children:"Guided Generation"}),(0,s.jsx)("option",{value:"supervised",children:"Supervised Mode"})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("label",{className:"block font-medium text-gray-300",children:["Confidence Threshold: ",Math.round(100*(o.confidenceThreshold||.95)),"%"]}),(0,s.jsx)("input",{type:"range",min:"0.8",max:"0.99",step:"0.01",value:o.confidenceThreshold,onChange:e=>p({...o,confidenceThreshold:Number(e.target.value)}),className:"w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-purple"}),(0,s.jsx)("div",{className:"text-xs text-gray-400",children:"Higher values ensure better quality but may take longer"})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block font-medium text-gray-300",children:"Agent Capabilities"}),(0,s.jsx)("div",{className:"space-y-3",children:[{key:"enableWebSearch",label:"Real-time Web Research",icon:v.A},{key:"enableSelfImprovement",label:"Self-Improvement Learning",icon:g.A},{key:"enableVisualization",label:"Data Visualizations",icon:j.A},{key:"enableMemoryConsolidation",label:"Memory Consolidation",icon:f.A}].map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer group",children:[(0,s.jsx)("input",{type:"checkbox",checked:o[e.key],onChange:t=>p({...o,[e.key]:t.target.checked}),className:"w-4 h-4 text-purple-600 bg-black border-gray-600 rounded focus:ring-purple-500"}),(0,s.jsx)(e.icon,{className:"w-4 h-4 text-gray-400 group-hover:text-purple-400 transition-colors"}),(0,s.jsx)("span",{className:"text-gray-300 group-hover:text-white transition-colors",children:e.label})]},e.key))})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block font-medium text-gray-300",children:"Focus Keywords (Optional)"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)("input",{type:"text",value:C,onChange:e=>P(e.target.value),onKeyPress:e=>"Enter"===e.key&&U(),placeholder:"Add focus keywords...",className:"flex-1 bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-all"}),(0,s.jsx)("button",{onClick:U,className:"bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors flex items-center space-x-2",children:(0,s.jsx)(N.A,{className:"w-4 h-4"})})]}),o.keywords&&o.keywords.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mt-3",children:o.keywords.map((e,t)=>(0,s.jsxs)("span",{className:"bg-purple-900/50 text-purple-300 px-3 py-1 rounded-full text-sm border border-purple-500/30 flex items-center space-x-2",children:[(0,s.jsx)("span",{children:e}),(0,s.jsx)(w.A,{className:"w-3 h-3 cursor-pointer hover:text-white transition-colors",onClick:()=>V(e)})]},t))})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("label",{className:"block font-medium text-gray-300",children:"Custom Instructions (Optional)"}),(0,s.jsx)("textarea",{value:o.customInstructions,onChange:e=>p({...o,customInstructions:e.target.value}),placeholder:"Any specific requirements, style preferences, or focus areas...",rows:4,className:"w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-all resize-none"})]}),(0,s.jsx)(i.N,{children:_&&(0,s.jsxs)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-red-900/50 border border-red-500/50 text-red-300 px-4 py-3 rounded-lg flex items-center space-x-2",children:[(0,s.jsx)(A.A,{className:"w-5 h-5 flex-shrink-0"}),(0,s.jsx)("span",{children:_})]})}),(0,s.jsxs)(l.P.button,{onClick:()=>{if(!o.topic.trim())return void O("Please enter a topic for autonomous generation");O(""),M(!0),E(!0)},disabled:!o.topic.trim()||L,whileHover:{scale:1.02},whileTap:{scale:.98},className:(0,S.cn)("w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 flex items-center justify-center space-x-3 text-lg shadow-lg shadow-purple-500/25",(!o.topic.trim()||L)&&"opacity-50 cursor-not-allowed"),children:[(0,s.jsx)(x.A,{className:"w-6 h-6"}),(0,s.jsx)("span",{children:"Deploy Autonomous Agent System"}),(0,s.jsx)(k.A,{className:"w-5 h-5"})]}),(0,s.jsx)("div",{className:"text-center text-sm text-gray-400",children:"The autonomous agent will research, plan, generate, and verify your content with guaranteed superiority"})]})})})]})]})]})}},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(90163).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},94504:(e,t,a)=>{Promise.resolve().then(a.bind(a,62511))}},e=>{var t=t=>e(e.s=t);e.O(0,[2108,9061,6635,1045,8441,1684,7358],()=>t(94504)),_N_E=e.O()}]);