(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8986],{24700:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var n=r(95155),s=r(12115),a=r(12108),i=r(35695),c=r(1978),l=r(57434),o=r(35169),u=r(6874),d=r.n(u),f=r(59434),m=r(35184);function h(){let{data:e,status:t}=(0,a.useSession)(),r=(0,i.useRouter)(),[u,h]=(0,s.useState)(""),[x,b]=(0,s.useState)("YouTube Script"),[p,g]=(0,s.useState)(!0);if((0,s.useEffect)(()=>{"unauthenticated"===t&&r.push("/login")},[t,r]),(0,s.useEffect)(()=>{let e=new URLSearchParams(window.location.search),t=e.get("script"),n=e.get("title");if(t){let e=(0,f.T)(t),r=(0,f.T)(n||"","YouTube Script");h(e),b(r),g(!1);return}let s=localStorage.getItem("youtubeScript"),a=localStorage.getItem("youtubeScriptTitle");s?(h(s),b(a||"YouTube Script")):setTimeout(()=>{r.push("/content")},3e3),g(!1)},[r]),"loading"===t)return(0,n.jsx)("div",{className:"min-h-screen bg-[#0f0f0f] flex items-center justify-center",children:(0,n.jsxs)("div",{className:"text-center space-y-4",children:[(0,n.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-red-400 border-t-transparent rounded-full mx-auto"}),(0,n.jsx)("p",{className:"text-[#aaa]",children:"Loading YouTube script..."})]})});if("unauthenticated"===t)return null;if(p)return(0,n.jsx)("div",{className:"min-h-screen bg-[#0f0f0f] flex items-center justify-center",children:(0,n.jsxs)("div",{className:"relative z-10 text-center",children:[(0,n.jsx)(c.P.div,{animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},className:"w-16 h-16 border-4 border-red-600 border-t-transparent rounded-full mx-auto mb-4"}),(0,n.jsx)("h2",{className:"text-xl font-semibold text-white mb-2",children:"Loading YouTube Script"}),(0,n.jsx)("p",{className:"text-[#aaa]",children:"Preparing your viral content..."})]})});if(!u)return(0,n.jsx)("div",{className:"min-h-screen bg-[#0f0f0f] flex items-center justify-center",children:(0,n.jsxs)("div",{className:"relative z-10 text-center",children:[(0,n.jsx)("div",{className:"p-3 bg-gradient-to-r from-red-600 to-red-700 rounded-2xl mb-6 w-fit mx-auto",children:(0,n.jsx)(l.A,{className:"w-8 h-8 text-white"})}),(0,n.jsx)("h2",{className:"text-xl font-semibold text-white mb-2",children:"No YouTube Script Found"}),(0,n.jsx)("p",{className:"text-[#aaa] mb-6",children:"No YouTube script found in your session."}),(0,n.jsxs)(d(),{href:"/content",className:"inline-flex items-center px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl hover:scale-105 transition-transform",children:[(0,n.jsx)(o.A,{className:"w-5 h-5 mr-2"}),"Back to Content Library"]})]})});let N=u.split(/\s+/).filter(e=>e.length>0).length;return(0,n.jsx)(m.A,{title:x,content:u,wordCount:N})}},59434:(e,t,r)=>{"use strict";r.d(t,{T:()=>i,cn:()=>a});var n=r(52596),s=r(39688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}function i(e,t){if(!e)return t||"";try{return decodeURIComponent(e)}catch(r){return console.warn("URI decode failed, using fallback:",r),t||e}}},87157:(e,t,r)=>{Promise.resolve().then(r.bind(r,24700))}},e=>{var t=t=>e(e.s=t);e.O(0,[2108,9061,6635,5184,8441,1684,7358],()=>t(87157)),_N_E=e.O()}]);