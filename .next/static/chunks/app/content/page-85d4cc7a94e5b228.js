(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7800],{5594:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>k});var r=o(95155),i=o(12115),l=o(1978),a=o(60760),s=o(57434),n=o(28883),c=o(9803),d=o(17951),h=o(24357),x=o(91788),m=o(62525),b=o(14186),p=o(1482),g=o(54416),u=o(47924),w=o(15968),v=o(54653),y=o(53904),j=o(6874),f=o.n(j),N=o(12108);let C={blog:{icon:s.A,label:"Blog Post",color:"from-pink-500 to-rose-500",bgColor:"bg-pink-500/10",borderColor:"border-pink-500/20"},email:{icon:n.A,label:"Email",color:"from-emerald-500 to-teal-500",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/20"},youtube_script:{icon:c.A,label:"YouTube Script",color:"from-red-500 to-orange-500",bgColor:"bg-red-500/10",borderColor:"border-red-500/20"},invincible_research:{icon:d.A,label:"Invincible V.1",color:"from-violet-700 to-indigo-700",bgColor:"bg-violet-700/10",borderColor:"border-violet-700/20"}};function k(){let{data:e}=(0,N.useSession)(),[t,o]=(0,i.useState)([]),[n,c]=(0,i.useState)(!0),[d,j]=(0,i.useState)("all"),[k,S]=(0,i.useState)(""),[A,T]=(0,i.useState)("newest"),[U,P]=(0,i.useState)("grid"),[R,I]=(0,i.useState)({total:0,limit:12,offset:0,hasMore:!1}),[L,_]=(0,i.useState)(!1),[E,D]=(0,i.useState)(!1),[F,O]=(0,i.useState)(!1),[H,M]=(0,i.useState)(""),[B,V]=(0,i.useState)(!1);(0,i.useEffect)(()=>{if(!e)return;console.log("\uD83D\uDD0D Processing URL parameters...");let t=new URLSearchParams(window.location.search).get("type");if(console.log("\uD83D\uDCC4 URL type parameter:",t),t&&"all"!==t){console.log("✅ Setting selectedType to:",t),j(t),D(!0);let e=C[t];e&&(M(e.label),O(!0),console.log("\uD83D\uDCE2 Showing notification for:",e.label),setTimeout(()=>{O(!1)},4e3),setTimeout(()=>{let e=document.querySelector('[data-filter="'.concat(t,'"]'));e&&e.scrollIntoView({behavior:"smooth",block:"center"})},500))}V(!0),console.log("\uD83D\uDE80 Initialization complete, selectedType:",t||"all")},[e]),(0,i.useEffect)(()=>{e&&B&&z()},[d,e,B]);let z=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{0===e&&_(!0);let r=new URLSearchParams({limit:R.limit.toString(),offset:e.toString()});"all"!==d&&(r.append("type",d),console.log("Fetching content with type filter:",d));let i="/api/content?".concat(r);console.log("Fetching from URL:",i);let l=await fetch(i);if(l.ok){let e=await l.json();console.log("API response:",e),t?o(t=>[...t,...e.content||[]]):o(e.content||[]),I(e.pagination||{total:0,limit:12,offset:0,hasMore:!1})}else console.error("API response error:",l.status,l.statusText)}catch(e){console.error("Error fetching content:",e)}finally{c(!1),_(!1)}},Y=e=>{j(e),o([]),I(e=>({...e,offset:0}))},q=async e=>{try{(await fetch("/api/content?id=".concat(e),{method:"DELETE"})).ok&&(o(t=>t.filter(t=>t.id!==e)),I(e=>({...e,total:e.total-1})))}catch(e){console.error("Error deleting content:",e)}},J=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("Error copying content:",e)}},W=e=>{let t=document.createElement("a"),o=new Blob([e.content],{type:"text/plain"});t.href=URL.createObjectURL(o),t.download="".concat(e.title.replace(/[^a-z0-9]/gi,"_"),".txt"),document.body.appendChild(t),t.click(),document.body.removeChild(t)},Z=async e=>{try{if("youtube_script"===e.type)return"/youtube-script-view?script=".concat(encodeURIComponent(e.content),"&title=").concat(encodeURIComponent(e.title));if(e.id&&(await fetch("/api/articles/".concat(e.id))).ok)return"/article-view/".concat(e.id);let t=await fetch("/api/articles/store",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:e.title,content:e.content,type:e.type,metadata:e.metadata})}),o=await t.json();if(t.ok&&o.success&&o.url)return o.url;return console.warn("Failed to store article, using fallback URL"),"/article-view?article=".concat(encodeURIComponent(e.content),"&title=").concat(encodeURIComponent(e.title),"&type=").concat(encodeURIComponent(e.type))}catch(t){return console.error("Error generating article URL:",t),"/article-view?article=".concat(encodeURIComponent(e.content),"&title=").concat(encodeURIComponent(e.title),"&type=").concat(encodeURIComponent(e.type))}},G=async(e,t)=>{t.preventDefault();try{let t=await Z(e);window.location.href=t}catch(t){console.error("Error handling view click:",t),"youtube_script"===e.type?window.location.href="/youtube-script-view?script=".concat(encodeURIComponent(e.content),"&title=").concat(encodeURIComponent(e.title)):window.location.href="/article-view?article=".concat(encodeURIComponent(e.content),"&title=").concat(encodeURIComponent(e.title),"&type=").concat(encodeURIComponent(e.type))}},K=t.filter(e=>""===k||e.title.toLowerCase().includes(k.toLowerCase())||e.content.toLowerCase().includes(k.toLowerCase())).sort((e,t)=>{switch(A){case"newest":return new Date(t.createdAt).getTime()-new Date(e.createdAt).getTime();case"oldest":return new Date(e.createdAt).getTime()-new Date(t.createdAt).getTime();case"title":return e.title.localeCompare(t.title);case"wordCount":return(t.wordCount||0)-(e.wordCount||0);default:return 0}}),Q=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),X=e=>C[e]||C.blog,$=e=>{let{item:t,index:o}=e,i=X(t.type),a=i.icon;return(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*o},className:"group relative backdrop-blur-xl border rounded-xl p-6 transition-all duration-300 ".concat("invincible_research"===t.type?"bg-gradient-to-br from-violet-900/20 to-indigo-900/20 border-violet-500/30 hover:border-violet-400/50 shadow-2xl":"bg-white/5 ".concat(i.borderColor," hover:bg-white/10 hover:border-white/30")),children:["invincible_research"===t.type&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-xl"}),(0,r.jsxs)("div",{className:"relative space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsx)("div",{className:"p-3 rounded-xl border ".concat("invincible_research"===t.type?"bg-violet-700/30 border-violet-600/50 backdrop-blur-sm":"".concat(i.bgColor," ").concat(i.borderColor)),children:(0,r.jsx)(a,{className:"w-6 h-6 text-white"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,r.jsx)(l.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:e=>{e.stopPropagation(),J(t.content)},className:"p-2 text-gray-400 hover:text-white rounded-lg hover:bg-white/10 transition-colors",title:"Copy content",children:(0,r.jsx)(h.A,{className:"w-4 h-4"})}),(0,r.jsx)(l.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:e=>{e.stopPropagation(),W(t)},className:"p-2 text-gray-400 hover:text-white rounded-lg hover:bg-white/10 transition-colors",title:"Download",children:(0,r.jsx)(x.A,{className:"w-4 h-4"})}),(0,r.jsx)(l.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:e=>{e.stopPropagation(),q(t.id)},className:"p-2 text-gray-400 hover:text-red-400 rounded-lg hover:bg-red-500/10 transition-colors",title:"Delete",children:(0,r.jsx)(m.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-white font-semibold text-lg mb-2 line-clamp-2",children:t.title}),(0,r.jsx)("p",{className:"text-gray-400 text-sm line-clamp-3 leading-relaxed",children:t.preview})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-white/10",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 text-xs text-gray-500",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,r.jsx)(b.A,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:Q(t.createdAt)})]}),t.wordCount&&(0,r.jsxs)("span",{children:[t.wordCount.toLocaleString()," words"]})]}),(0,r.jsx)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:e=>G(t,e),className:"px-3 py-1.5 bg-violet-600 hover:bg-violet-700 text-white text-sm rounded-lg transition-colors",children:"View"})]})]})]})};return e?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-violet-900",children:(0,r.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,r.jsx)(a.N,{children:F&&(0,r.jsx)(l.P.div,{initial:{opacity:0,y:-50,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:-50,scale:.95},className:"mb-6 bg-gradient-to-r from-violet-600/20 to-indigo-600/20 backdrop-blur-xl border border-violet-500/30 rounded-xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"p-2 bg-violet-600/30 rounded-lg",children:(0,r.jsx)(p.A,{className:"w-5 h-5 text-violet-300"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("p",{className:"text-white font-medium",children:["Filtering by ",H]}),(0,r.jsx)("p",{className:"text-violet-200 text-sm",children:"Showing content from your dashboard selection"})]})]}),(0,r.jsx)("button",{onClick:()=>O(!1),className:"p-1 text-violet-300 hover:text-white transition-colors",children:(0,r.jsx)(g.A,{className:"w-4 h-4"})})]})})}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"My Content Library"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Access and manage all your generated content"})]}),(0,r.jsxs)("div",{className:"mb-8 space-y-4",children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"}),(0,r.jsx)("input",{type:"text",placeholder:"Search content...",value:k,onChange:e=>S(e.target.value),className:"w-full pl-10 pr-4 py-3 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-violet-500/50 transition-colors"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:()=>D(!E),className:"px-4 py-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-gray-400 hover:text-white transition-colors flex items-center space-x-2",children:[(0,r.jsx)(p.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Filters"})]}),(0,r.jsx)("button",{onClick:()=>P("grid"===U?"list":"grid"),className:"p-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-gray-400 hover:text-white transition-colors",children:"grid"===U?(0,r.jsx)(w.A,{className:"w-4 h-4"}):(0,r.jsx)(v.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>z(),disabled:L,className:"p-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-gray-400 hover:text-white transition-colors disabled:opacity-50",children:(0,r.jsx)(y.A,{className:"w-4 h-4 ".concat(L?"animate-spin":"")})})]})]}),(0,r.jsx)(a.N,{children:E&&(0,r.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-6",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Content Type"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsxs)("button",{onClick:()=>Y("all"),className:"px-3 py-2 text-sm rounded-lg transition-colors ".concat("all"===d?"bg-violet-600 text-white":"bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white"),children:["All (",R.total,")"]}),Object.entries(C).map(e=>{let[t,o]=e;return(0,r.jsxs)("button",{"data-filter":t,onClick:()=>Y(t),className:"px-3 py-2 text-sm rounded-lg transition-colors flex items-center space-x-1 ".concat(d===t?"bg-violet-600 text-white":"bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white"),children:[(0,r.jsx)(o.icon,{className:"w-3 h-3"}),(0,r.jsx)("span",{children:o.label})]},t)})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Sort By"}),(0,r.jsxs)("select",{value:A,onChange:e=>T(e.target.value),className:"w-full px-3 py-2 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:border-violet-500/50",children:[(0,r.jsx)("option",{value:"newest",children:"Newest First"}),(0,r.jsx)("option",{value:"oldest",children:"Oldest First"}),(0,r.jsx)("option",{value:"title",children:"Title A-Z"}),(0,r.jsx)("option",{value:"wordCount",children:"Word Count (High to Low)"})]})]})]})})})]}),(0,r.jsxs)("div",{className:"mb-6 grid grid-cols-1 sm:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-white",children:R.total}),(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Total Content"})]}),(0,r.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-violet-400",children:t.filter(e=>"invincible_research"===e.type).length}),(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Invincible V.1"})]}),(0,r.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-pink-400",children:t.filter(e=>"blog"===e.type).length}),(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"Blog Posts"})]}),(0,r.jsxs)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-xl p-4",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-400",children:t.filter(e=>"youtube_script"===e.type).length}),(0,r.jsx)("div",{className:"text-sm text-gray-400",children:"YouTube Scripts"})]})]}),n?(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:[1,2,3,4,5,6].map(e=>(0,r.jsx)("div",{className:"animate-pulse",children:(0,r.jsx)("div",{className:"h-64 bg-white/5 rounded-xl"})},e))}):0===K.length?(0,r.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center py-20",children:[(0,r.jsx)(s.A,{className:"w-16 h-16 text-gray-600 mx-auto mb-6"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-white mb-2",children:"No content found"}),(0,r.jsx)("p",{className:"text-gray-400 mb-8",children:k?"Try adjusting your search terms":"Start creating content to build your library"}),(0,r.jsx)(f(),{href:"/invincible",className:"px-6 py-3 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors",children:"Create Content"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"grid gap-6 ".concat("grid"===U?"grid-cols-1 md:grid-cols-2 lg:grid-cols-3":"grid-cols-1"),children:K.map((e,t)=>(0,r.jsx)($,{item:e,index:t},e.id))}),R.hasMore&&(0,r.jsx)("div",{className:"text-center mt-12",children:(0,r.jsx)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>{R.hasMore&&z(R.offset+R.limit,!0)},className:"px-8 py-3 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl text-white transition-colors",children:"Load More Content"})})]})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-gray-900 to-violet-900 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-white mb-4",children:"Access Denied"}),(0,r.jsx)("p",{className:"text-gray-400 mb-6",children:"Please sign in to view your content."}),(0,r.jsx)(f(),{href:"/login",className:"px-6 py-3 bg-violet-600 hover:bg-violet-700 text-white rounded-lg transition-colors",children:"Sign In"})]})})}},50863:(e,t,o)=>{Promise.resolve().then(o.bind(o,5594))}},e=>{var t=t=>e(e.s=t);e.O(0,[2108,9061,8284,8441,1684,7358],()=>t(50863)),_N_E=e.O()}]);