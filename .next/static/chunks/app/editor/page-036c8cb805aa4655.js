(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6766],{25968:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>k});var a=s(95155),l=s(12115),i=s(12108),n=s(35695),r=s(1978),c=s(60760),o=s(28199),x=s(57100),d=s(53311),h=s(49376),m=s(78749),u=s(92657),p=s(43900),b=s(57918),w=s(57434),j=s(4229),g=s(91788),v=s(381),N=s(6874),f=s.n(N),y=s(50270);function k(){let{data:e,status:t}=(0,i.useSession)(),s=(0,n.useRouter)(),[N,k]=(0,l.useState)(""),[S,P]=(0,l.useState)("Untitled Document"),[A,C]=(0,l.useState)(!1),[H,E]=(0,l.useState)(!1),[_,D]=(0,l.useState)(!1),[L,O]=(0,l.useState)([]),[R,T]=(0,l.useState)(!1),[U,G]=(0,l.useState)(0),[B,F]=(0,l.useState)(0);if((0,l.useEffect)(()=>{"unauthenticated"===t&&s.push("/login")},[t,s]),"loading"===t)return(0,a.jsx)("div",{className:"min-h-screen bg-black flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Loading editor..."})]})});if("unauthenticated"===t)return null;(0,l.useEffect)(()=>{let e=setTimeout(()=>{N&&S&&I()},2e3);return()=>clearTimeout(e)},[N,S]),(0,l.useEffect)(()=>{let e=N.replace(/<[^>]*>/g,"").trim();G(e.split(/\s+/).filter(e=>e.length>0).length),F(e.length)},[N]);let I=async()=>{D(!0),await new Promise(e=>setTimeout(e,500)),D(!1)},z=async()=>{D(!0);let e={id:Date.now().toString(),title:S,content:N,createdAt:new Date,updatedAt:new Date};await new Promise(e=>setTimeout(e,1e3)),O(t=>[e,...t]),D(!1)},J=e=>{P(e.title),k(e.content),T(!1)},M=e=>{window.open("/generate/".concat(e),"_blank")};return(0,a.jsxs)("div",{className:"min-h-screen transition-all duration-300 ".concat(H?"fixed inset-0 z-50":""),children:[!H&&(0,a.jsx)("div",{className:"border-b border-white/10 bg-black/20 backdrop-blur-sm",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between h-16",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(f(),{href:"/",children:(0,a.jsxs)(r.P.button,{whileHover:{scale:1.05},className:"flex items-center space-x-2 text-white hover:text-blue-400 transition-colors",children:[(0,a.jsx)(o.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"font-semibold",children:"Invincible"})]})}),(0,a.jsx)("div",{className:"text-gray-400",children:"|"}),(0,a.jsx)("input",{type:"text",value:S,onChange:e=>P(e.target.value),className:"bg-transparent text-white font-medium text-lg focus:outline-none focus:text-blue-400 transition-colors",placeholder:"Document title..."}),_&&(0,a.jsxs)("div",{className:"flex items-center text-green-400 text-sm",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full animate-pulse mr-2"}),"Saving..."]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(r.P.button,{whileHover:{scale:1.05},onClick:()=>M("blog"),className:"flex items-center space-x-2 px-3 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors",title:"Open Blog Generator",children:(0,a.jsx)(x.A,{className:"w-4 h-4"})}),(0,a.jsx)(r.P.button,{whileHover:{scale:1.05},onClick:()=>M("email"),className:"flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors",title:"Open Email Generator",children:(0,a.jsx)(d.A,{className:"w-4 h-4"})}),(0,a.jsx)(r.P.button,{whileHover:{scale:1.05},onClick:()=>M("youtube"),className:"flex items-center space-x-2 px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors",title:"Open Video Script Generator",children:(0,a.jsx)(h.A,{className:"w-4 h-4"})}),(0,a.jsx)("div",{className:"w-px h-6 bg-white/20"}),(0,a.jsx)(r.P.button,{whileHover:{scale:1.05},onClick:()=>C(!A),className:"p-2 rounded-lg transition-colors ".concat(A?"bg-blue-600 text-white":"text-gray-300 hover:text-white hover:bg-white/10"),title:A?"Exit Preview":"Preview",children:A?(0,a.jsx)(m.A,{className:"w-4 h-4"}):(0,a.jsx)(u.A,{className:"w-4 h-4"})}),(0,a.jsx)(r.P.button,{whileHover:{scale:1.05},onClick:()=>E(!H),className:"p-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors",title:H?"Exit Fullscreen":"Fullscreen",children:H?(0,a.jsx)(p.A,{className:"w-4 h-4"}):(0,a.jsx)(b.A,{className:"w-4 h-4"})}),(0,a.jsx)("div",{className:"w-px h-6 bg-white/20"}),(0,a.jsxs)(r.P.button,{whileHover:{scale:1.05},onClick:()=>T(!R),className:"flex items-center space-x-2 px-3 py-2 glass-card text-white hover:bg-white/20 rounded-lg transition-colors",children:[(0,a.jsx)(w.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Open"})]}),(0,a.jsxs)(r.P.button,{whileHover:{scale:1.05},onClick:z,disabled:_,className:"flex items-center space-x-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50",children:[(0,a.jsx)(j.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Save"})]}),(0,a.jsx)(r.P.button,{whileHover:{scale:1.05},onClick:()=>{let e=new Blob([N],{type:"text/html"}),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download="".concat(S,".html"),document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(t)},className:"p-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors",title:"Export",children:(0,a.jsx)(g.A,{className:"w-4 h-4"})}),(0,a.jsx)(f(),{href:"/dashboard",children:(0,a.jsx)(r.P.button,{whileHover:{scale:1.05},className:"p-2 text-gray-300 hover:text-white hover:bg-white/10 rounded-lg transition-colors",title:"Dashboard",children:(0,a.jsx)(v.A,{className:"w-4 h-4"})})})]})]})})}),(0,a.jsxs)("div",{className:"flex h-full",children:[(0,a.jsx)(c.N,{children:R&&!H&&(0,a.jsxs)(r.P.div,{initial:{x:-300,opacity:0},animate:{x:0,opacity:1},exit:{x:-300,opacity:0},className:"w-80 bg-black/30 backdrop-blur-sm border-r border-white/10 p-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Documents"}),0===L.length?(0,a.jsx)("p",{className:"text-gray-400 text-sm",children:"No saved documents yet"}):(0,a.jsx)("div",{className:"space-y-2",children:L.map(e=>(0,a.jsxs)(r.P.div,{whileHover:{scale:1.02},onClick:()=>J(e),className:"glass-card p-3 cursor-pointer hover:bg-white/20 transition-colors",children:[(0,a.jsx)("h4",{className:"text-white font-medium truncate",children:e.title}),(0,a.jsx)("p",{className:"text-gray-400 text-xs",children:e.createdAt.toLocaleDateString()})]},e.id))})]})}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsx)("div",{className:"flex-1 p-8 ".concat(H?"h-screen":""),children:A?(0,a.jsxs)("div",{className:"glass-card p-8 max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-white mb-6",children:S}),(0,a.jsx)("div",{className:"prose prose-invert max-w-none",dangerouslySetInnerHTML:{__html:N}})]}):(0,a.jsx)("div",{className:"max-w-6xl mx-auto",children:(0,a.jsx)(y.default,{content:N,onChange:k,placeholder:"Start writing your masterpiece...",className:"min-h-[600px]"})})}),!H&&(0,a.jsx)("div",{className:"border-t border-white/10 bg-black/20 backdrop-blur-sm px-8 py-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm text-gray-400",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("span",{children:[U," words"]}),(0,a.jsxs)("span",{children:[B," characters"]}),(0,a.jsxs)("span",{children:["Last saved: ",_?"Saving...":"Just now"]})]}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsx)("span",{children:"Ready"})})]})})]})]})]})}},35083:(e,t,s)=>{Promise.resolve().then(s.bind(s,25968))}},e=>{var t=t=>e(e.s=t);e.O(0,[5004,277,2108,9061,5947,9809,270,8441,1684,7358],()=>t(35083)),_N_E=e.O()}]);