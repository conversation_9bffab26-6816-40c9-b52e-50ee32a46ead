(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4662],{381:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},4229:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},14186:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},16785:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},17951:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},23861:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},28199:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},33109:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},33127:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},34576:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},34835:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},35695:(e,t,s)=>{"use strict";var l=s(18999);s.o(l,"useParams")&&s.d(t,{useParams:function(){return l.useParams}}),s.o(l,"useRouter")&&s.d(t,{useRouter:function(){return l.useRouter}}),s.o(l,"useSearchParams")&&s.d(t,{useSearchParams:function(){return l.useSearchParams}})},40646:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("CircleCheckBig",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},61591:(e,t,s)=>{Promise.resolve().then(s.bind(s,72608))},71007:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},71539:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},72608:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>P});var l=s(95155),a=s(12115),i=s(1978),r=s(12108),n=s(35695),c=s(71007),o=s(381),d=s(23861),x=s(33127),h=s(75525),m=s(28199),u=s(40646),b=s(85339),p=s(4229),g=s(34835),y=s(84355),f=s(17951),v=s(34576),j=s(33109),w=s(14186),N=s(16785),k=s(71539),A=s(6874),C=s.n(A);function P(){var e,t,s,A,P,S,M,z,T,E;let{data:H,status:R}=(0,r.useSession)(),V=(0,n.useRouter)(),[O,q]=(0,a.useState)(null),[L,D]=(0,a.useState)(!1),[U,B]=(0,a.useState)("idle"),[_,I]=(0,a.useState)(!0),[W,F]=(0,a.useState)("profile");(0,a.useEffect)(()=>{"unauthenticated"===R&&V.push("/login")},[R,V]),(0,a.useEffect)(()=>{(null==H?void 0:H.user)&&G()},[H]);let G=async()=>{try{let e=await fetch("/api/user/profile");if(e.ok){let t=await e.json();q(t)}}catch(e){console.error("Error fetching user profile:",e)}finally{I(!1)}},J=async()=>{if(O){D(!0),B("idle");try{let e=await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:O.firstName,lastName:O.lastName,bio:O.bio})}),t=await fetch("/api/settings",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(O.settings)});e.ok&&t.ok?(B("success"),setTimeout(()=>B("idle"),3e3),await G()):B("error")}catch(e){console.error("Error saving settings:",e),B("error")}finally{D(!1)}}},Q=(e,t)=>{q(s=>s?{...s,[e]:t}:null)},Z=(e,t)=>{q(s=>s?{...s,settings:{...s.settings,[e]:t}}:null)},K=()=>(null==O?void 0:O.firstName)&&(null==O?void 0:O.lastName)?"".concat(O.firstName," ").concat(O.lastName):(null==O?void 0:O.name)?O.name:(null==O?void 0:O.email)?O.email.split("@")[0]:"User",X=async()=>{await (0,r.signOut)({callbackUrl:"/login"})},Y=[{id:"profile",label:"Profile",icon:c.A,color:"from-blue-500 to-cyan-500"},{id:"preferences",label:"Preferences",icon:o.A,color:"from-emerald-500 to-teal-500"},{id:"notifications",label:"Notifications",icon:d.A,color:"from-yellow-500 to-orange-500"},{id:"appearance",label:"Appearance",icon:x.A,color:"from-purple-500 to-pink-500"},{id:"privacy",label:"Privacy",icon:h.A,color:"from-red-500 to-pink-500"}];return"loading"===R||_?(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center space-y-4",children:[(0,l.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"}),(0,l.jsx)("p",{className:"text-gray-400",children:"Loading your settings..."})]})}):"unauthenticated"===R?null:O?(0,l.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900",children:[(0,l.jsx)("div",{className:"border-b border-white/10 bg-black/20 backdrop-blur-xl",children:(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,l.jsx)(C(),{href:"/dashboard",children:(0,l.jsxs)(i.P.button,{whileHover:{scale:1.05},className:"flex items-center space-x-3 text-white hover:text-violet-400 transition-colors",children:[(0,l.jsx)(m.A,{className:"w-5 h-5"}),(0,l.jsx)("span",{className:"font-semibold",children:"Dashboard"})]})}),(0,l.jsx)("div",{className:"text-gray-600",children:"|"}),(0,l.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent",children:"Personal Settings"})]}),(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:["success"===U&&(0,l.jsxs)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"flex items-center text-emerald-400 text-sm bg-emerald-400/10 px-3 py-2 rounded-lg",children:[(0,l.jsx)(u.A,{className:"w-4 h-4 mr-2"}),"Changes saved!"]}),"error"===U&&(0,l.jsxs)(i.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},className:"flex items-center text-red-400 text-sm bg-red-400/10 px-3 py-2 rounded-lg",children:[(0,l.jsx)(b.A,{className:"w-4 h-4 mr-2"}),"Error saving"]}),(0,l.jsxs)(i.P.button,{whileHover:{scale:1.05},onClick:J,disabled:L,className:"flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 text-white rounded-xl transition-all disabled:opacity-50 shadow-lg",children:[(0,l.jsx)(p.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:L?"Saving...":"Save Changes"})]}),(0,l.jsxs)(i.P.button,{whileHover:{scale:1.05},onClick:X,className:"flex items-center space-x-2 px-4 py-3 bg-red-600/20 hover:bg-red-600/30 border border-red-500/30 text-red-400 rounded-xl transition-all",children:[(0,l.jsx)(g.A,{className:"w-4 h-4"}),(0,l.jsx)("span",{children:"Sign Out"})]})]})]})})}),(0,l.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,l.jsx)("div",{className:"lg:col-span-1",children:(0,l.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-6 sticky top-8",children:[(0,l.jsxs)("div",{className:"text-center mb-6",children:[(0,l.jsxs)("div",{className:"relative inline-block",children:[O.image?(0,l.jsx)("img",{src:O.image,alt:K(),className:"w-24 h-24 rounded-2xl object-cover border-4 border-white/20 mb-4 mx-auto"}):(0,l.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-2xl flex items-center justify-center text-white text-2xl font-bold mb-4 mx-auto",children:(()=>{if((null==O?void 0:O.firstName)&&(null==O?void 0:O.lastName))return"".concat(O.firstName[0]).concat(O.lastName[0]);if(null==O?void 0:O.name){let e=O.name.split(" ");return e.length>1?"".concat(e[0][0]).concat(e[e.length-1][0]):e[0][0]}return(null==O?void 0:O.email)?O.email[0].toUpperCase():"U"})()}),(0,l.jsx)("button",{className:"absolute -bottom-1 -right-1 w-8 h-8 bg-violet-600 hover:bg-violet-700 rounded-full flex items-center justify-center text-white transition-colors",children:(0,l.jsx)(y.A,{className:"w-4 h-4"})})]}),(0,l.jsx)("h3",{className:"text-xl font-bold text-white mb-1",children:K()}),(0,l.jsx)("p",{className:"text-gray-400 text-sm mb-2",children:O.email}),(0,l.jsxs)("div",{className:"flex items-center justify-center space-x-1 mb-4",children:[(0,l.jsx)(f.A,{className:"w-4 h-4 text-yellow-400"}),(0,l.jsx)("span",{className:"text-yellow-400 text-sm font-medium",children:(null==(e=O.subscription)?void 0:e.plan)==="free"?"Free Plan":(null==(t=O.subscription)?void 0:t.plan)==="pro"?"Pro Member":"Member"})]})]}),(0,l.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-xl",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center",children:(0,l.jsx)(v.A,{className:"w-4 h-4 text-blue-400"})}),(0,l.jsx)("span",{className:"text-gray-300 text-sm",children:"Content Created"})]}),(0,l.jsx)("span",{className:"text-white font-semibold",children:(null==(s=O.stats)?void 0:s.totalContent)||0})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-xl",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center",children:(0,l.jsx)(j.A,{className:"w-4 h-4 text-emerald-400"})}),(0,l.jsx)("span",{className:"text-gray-300 text-sm",children:"Quality Score"})]}),(0,l.jsx)("span",{className:"text-white font-semibold",children:"9.8/10"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between p-3 bg-white/5 rounded-xl",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,l.jsx)("div",{className:"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center",children:(0,l.jsx)(w.A,{className:"w-4 h-4 text-purple-400"})}),(0,l.jsx)("span",{className:"text-gray-300 text-sm",children:"Time Saved"})]}),(0,l.jsx)("span",{className:"text-white font-semibold",children:"48h"})]})]}),(0,l.jsx)("div",{className:"space-y-2",children:Y.map(e=>(0,l.jsxs)(i.P.button,{whileHover:{scale:1.02},onClick:()=>F(e.id),className:"w-full flex items-center space-x-3 p-3 rounded-xl transition-all ".concat(W===e.id?"bg-gradient-to-r from-violet-600/20 to-indigo-600/20 border border-violet-500/30 text-white":"text-gray-400 hover:text-white hover:bg-white/5"),children:[(0,l.jsx)("div",{className:"w-8 h-8 rounded-lg flex items-center justify-center ".concat(W===e.id?"bg-gradient-to-r ".concat(e.color):"bg-white/10"),children:(0,l.jsx)(e.icon,{className:"w-4 h-4"})}),(0,l.jsx)("span",{className:"font-medium",children:e.label})]},e.id))})]})}),(0,l.jsx)("div",{className:"lg:col-span-3",children:(0,l.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"space-y-6",children:["profile"===W&&(0,l.jsxs)("div",{className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center",children:(0,l.jsx)(c.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Profile Information"}),(0,l.jsx)("p",{className:"text-gray-400",children:"Manage your personal details and bio"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"First Name"}),(0,l.jsx)("input",{type:"text",value:O.firstName||"",onChange:e=>Q("firstName",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-violet-500/50 focus:bg-white/10 transition-all"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Last Name"}),(0,l.jsx)("input",{type:"text",value:O.lastName||"",onChange:e=>Q("lastName",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-violet-500/50 focus:bg-white/10 transition-all"})]})]}),(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Email Address"}),(0,l.jsx)("input",{type:"email",value:O.email||"",onChange:e=>Q("email",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-violet-500/50 focus:bg-white/10 transition-all"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Bio"}),(0,l.jsx)("textarea",{value:O.bio||"",onChange:e=>Q("bio",e.target.value),rows:4,placeholder:"Tell us about yourself...",className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:border-violet-500/50 focus:bg-white/10 transition-all resize-none"})]})]}),"preferences"===W&&(0,l.jsxs)("div",{className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center",children:(0,l.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Content Preferences"}),(0,l.jsx)("p",{className:"text-gray-400",children:"Customize your content creation defaults"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Word Count"}),(0,l.jsx)("input",{type:"number",min:"100",max:"10000",value:null==(A=O.settings)?void 0:A.defaultWordCount,onChange:e=>Z("defaultWordCount",parseInt(e.target.value)),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Tone"}),(0,l.jsxs)("select",{value:null==(P=O.settings)?void 0:P.defaultTone,onChange:e=>Z("defaultTone",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all",children:[(0,l.jsx)("option",{value:"professional",children:"Professional"}),(0,l.jsx)("option",{value:"casual",children:"Casual"}),(0,l.jsx)("option",{value:"authoritative",children:"Authoritative"}),(0,l.jsx)("option",{value:"conversational",children:"Conversational"}),(0,l.jsx)("option",{value:"technical",children:"Technical"}),(0,l.jsx)("option",{value:"friendly",children:"Friendly"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("label",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-blue-500/20 rounded-xl flex items-center justify-center",children:(0,l.jsx)(N.A,{className:"w-5 h-5 text-blue-400"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-white font-medium",children:"Include Research by Default"}),(0,l.jsx)("p",{className:"text-gray-400 text-sm",children:"Automatically include research in content generation"})]})]}),(0,l.jsx)("input",{type:"checkbox",checked:null==(S=O.settings)?void 0:S.includeResearchByDefault,onChange:e=>Z("includeResearchByDefault",e.target.checked),className:"w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"})]}),(0,l.jsxs)("label",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,l.jsx)("div",{className:"w-10 h-10 bg-emerald-500/20 rounded-xl flex items-center justify-center",children:(0,l.jsx)(k.A,{className:"w-5 h-5 text-emerald-400"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-white font-medium",children:"Auto-Save"}),(0,l.jsx)("p",{className:"text-gray-400 text-sm",children:"Automatically save your work as you type"})]})]}),(0,l.jsx)("input",{type:"checkbox",checked:null==(M=O.settings)?void 0:M.autoSaveEnabled,onChange:e=>Z("autoSaveEnabled",e.target.checked),className:"w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"})]})]})]}),"notifications"===W&&(0,l.jsxs)("div",{className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center",children:(0,l.jsx)(d.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Notification Preferences"}),(0,l.jsx)("p",{className:"text-gray-400",children:"Choose how you want to be notified"})]})]}),(0,l.jsx)("div",{className:"space-y-4",children:[{key:"emailNotifications",title:"Email Notifications",desc:"Receive updates and alerts via email"},{key:"pushNotifications",title:"Push Notifications",desc:"Get instant browser notifications"},{key:"weeklyReports",title:"Weekly Reports",desc:"Summary of your content creation activity"},{key:"marketingEmails",title:"Marketing Emails",desc:"Product updates, tips, and special offers"}].map(e=>{var t;return(0,l.jsxs)("label",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-white font-medium",children:e.title}),(0,l.jsx)("p",{className:"text-gray-400 text-sm",children:e.desc})]}),(0,l.jsx)("input",{type:"checkbox",checked:null==(t=O.settings)?void 0:t[e.key],onChange:t=>Z(e.key,t.target.checked),className:"w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"})]},e.key)})})]}),"appearance"===W&&(0,l.jsxs)("div",{className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center",children:(0,l.jsx)(x.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Appearance & Theme"}),(0,l.jsx)("p",{className:"text-gray-400",children:"Customize how the interface looks"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-8",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Theme"}),(0,l.jsxs)("select",{value:null==(z=O.settings)?void 0:z.theme,onChange:e=>Z("theme",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all",children:[(0,l.jsx)("option",{value:"dark",children:"Dark"}),(0,l.jsx)("option",{value:"light",children:"Light"}),(0,l.jsx)("option",{value:"auto",children:"Auto"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Accent Color"}),(0,l.jsxs)("select",{value:null==(T=O.settings)?void 0:T.accentColor,onChange:e=>Z("accentColor",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all",children:[(0,l.jsx)("option",{value:"blue",children:"Blue"}),(0,l.jsx)("option",{value:"purple",children:"Purple"}),(0,l.jsx)("option",{value:"green",children:"Green"}),(0,l.jsx)("option",{value:"red",children:"Red"}),(0,l.jsx)("option",{value:"orange",children:"Orange"})]})]})]}),(0,l.jsx)("div",{className:"space-y-4",children:[{key:"animationsEnabled",title:"Enable Animations",desc:"Smooth transitions and motion effects"},{key:"compactMode",title:"Compact Mode",desc:"Reduce spacing for more content on screen"}].map(e=>{var t;return(0,l.jsxs)("label",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-white font-medium",children:e.title}),(0,l.jsx)("p",{className:"text-gray-400 text-sm",children:e.desc})]}),(0,l.jsx)("input",{type:"checkbox",checked:null==(t=O.settings)?void 0:t[e.key],onChange:t=>Z(e.key,t.target.checked),className:"w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"})]},e.key)})})]}),"privacy"===W&&(0,l.jsxs)("div",{className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-4 mb-8",children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-red-500 to-pink-500 rounded-2xl flex items-center justify-center",children:(0,l.jsx)(h.A,{className:"w-6 h-6 text-white"})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-white",children:"Privacy & Security"}),(0,l.jsx)("p",{className:"text-gray-400",children:"Control your data and privacy settings"})]})]}),(0,l.jsxs)("div",{className:"mb-8",children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Profile Visibility"}),(0,l.jsxs)("select",{value:null==(E=O.settings)?void 0:E.profileVisibility,onChange:e=>Z("profileVisibility",e.target.value),className:"w-full px-4 py-4 bg-white/5 border border-white/10 rounded-xl text-white focus:border-violet-500/50 focus:bg-white/10 transition-all",children:[(0,l.jsx)("option",{value:"private",children:"Private"}),(0,l.jsx)("option",{value:"team",children:"Team Only"}),(0,l.jsx)("option",{value:"public",children:"Public"})]})]}),(0,l.jsx)("div",{className:"space-y-4",children:[{key:"dataSharing",title:"Data Sharing",desc:"Share anonymized usage data to improve our services"},{key:"analyticsTracking",title:"Analytics Tracking",desc:"Help us improve the product with usage analytics"}].map(e=>{var t;return(0,l.jsxs)("label",{className:"flex items-center justify-between p-4 bg-white/5 rounded-xl hover:bg-white/10 transition-all cursor-pointer",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"text-white font-medium",children:e.title}),(0,l.jsx)("p",{className:"text-gray-400 text-sm",children:e.desc})]}),(0,l.jsx)("input",{type:"checkbox",checked:null==(t=O.settings)?void 0:t[e.key],onChange:t=>Z(e.key,t.target.checked),className:"w-5 h-5 rounded bg-white/10 border border-white/20 text-violet-600 focus:ring-violet-500"})]},e.key)})})]})]},W)})]})})]}):(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center space-y-4",children:[(0,l.jsx)("p",{className:"text-gray-400",children:"Unable to load profile"}),(0,l.jsx)(C(),{href:"/login",children:(0,l.jsx)("button",{className:"px-4 py-2 bg-violet-600 text-white rounded-lg",children:"Sign In Again"})})]})})}},75525:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},84355:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},85339:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});let l=(0,s(90163).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[2108,9061,8441,1684,7358],()=>t(61591)),_N_E=e.O()}]);