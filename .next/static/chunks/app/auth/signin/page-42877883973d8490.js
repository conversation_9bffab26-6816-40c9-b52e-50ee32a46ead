(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{35695:(e,t,r)=>{"use strict";var s=r(18999);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},43738:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(95155),a=r(12108),n=r(35695),l=r(12115),i=r(83029);function o(){let e=(0,n.useRouter)(),[t,r]=(0,l.useState)(!1);(0,l.useEffect)(()=>{(async()=>{await (0,a.getSession)()&&e.push("/dashboard")})()},[e]);let o=async()=>{r(!0);try{let t=await (0,a.signIn)("google",{callbackUrl:"/dashboard",redirect:!1});(null==t?void 0:t.ok)?e.push("/dashboard"):console.error("Sign in failed:",null==t?void 0:t.error)}catch(e){console.error("Sign in error:",e)}finally{r(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"max-w-md w-full",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-white mb-2",children:"Invincible AI"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Your intelligent content creation platform"})]}),(0,s.jsxs)("div",{className:"bg-gray-900/50 backdrop-blur-xl border border-gray-800 rounded-2xl p-8 shadow-2xl",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:"Welcome Back"}),(0,s.jsx)("p",{className:"text-gray-400",children:"Sign in to access your AI-powered workspace"})]}),(0,s.jsxs)("button",{onClick:o,disabled:t,className:"w-full flex items-center justify-center gap-3 bg-white hover:bg-gray-50 text-gray-900 font-semibold py-3 px-4 rounded-xl transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:[(0,s.jsx)(i.F4b,{className:"w-5 h-5"}),t?"Signing in...":"Continue with Google"]}),(0,s.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-800",children:[(0,s.jsx)("p",{className:"text-sm text-gray-400 text-center mb-4",children:"What you'll get:"}),(0,s.jsxs)("div",{className:"space-y-2 text-sm text-gray-300",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-violet-500 rounded-full"}),(0,s.jsx)("span",{children:"AI-powered content generation"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-violet-500 rounded-full"}),(0,s.jsx)("span",{children:"Smart quota management"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-violet-500 rounded-full"}),(0,s.jsx)("span",{children:"Advanced research capabilities"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-1.5 h-1.5 bg-violet-500 rounded-full"}),(0,s.jsx)("span",{children:"Multi-format content creation"})]})]})]})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 text-center mt-6",children:["By signing in, you agree to our"," ",(0,s.jsx)("a",{href:"#",className:"text-violet-400 hover:text-violet-300",children:"Terms of Service"})," ","and"," ",(0,s.jsx)("a",{href:"#",className:"text-violet-400 hover:text-violet-300",children:"Privacy Policy"})]})]})})}},74436:(e,t,r)=>{"use strict";r.d(t,{k5:()=>u});var s=r(12115),a={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},n=s.createContext&&s.createContext(a),l=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var s in r)Object.prototype.hasOwnProperty.call(r,s)&&(e[s]=r[s])}return e}).apply(this,arguments)}function o(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function c(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?o(Object(r),!0).forEach(function(t){var s,a,n;s=e,a=t,n=r[t],(a=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var s=r.call(e,t||"default");if("object"!=typeof s)return s;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(a))in s?Object.defineProperty(s,a,{value:n,enumerable:!0,configurable:!0,writable:!0}):s[a]=n}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function u(e){return t=>s.createElement(d,i({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,r)=>s.createElement(t.tag,c({key:r},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var r,{attr:a,size:n,title:o}=e,u=function(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r={};for(var s in e)if(Object.prototype.hasOwnProperty.call(e,s)){if(t.indexOf(s)>=0)continue;r[s]=e[s]}return r}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)r=n[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}(e,l),d=n||t.size||"1em";return t.className&&(r=t.className),e.className&&(r=(r?r+" ":"")+e.className),s.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,a,u,{className:r,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),o&&s.createElement("title",null,o),e.children)};return void 0!==n?s.createElement(n.Consumer,null,e=>t(e)):t(a)}},82105:(e,t,r)=>{Promise.resolve().then(r.bind(r,43738))}},e=>{var t=t=>e(e.s=t);e.O(0,[7753,2108,8441,1684,7358],()=>t(82105)),_N_E=e.O()}]);