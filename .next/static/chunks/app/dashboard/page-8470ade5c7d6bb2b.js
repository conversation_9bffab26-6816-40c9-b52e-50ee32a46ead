(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5105],{381:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},9803:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Video",[["path",{d:"m22 8-6 4 6 4V8Z",key:"50v9me"}],["rect",{width:"14",height:"12",x:"2",y:"6",rx:"2",ry:"2",key:"1rqjg6"}]])},14186:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17951:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},23861:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},26669:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>$});var i=a(95155),s=a(12115),l=a(1978),r=a(60760),n=a(6874),o=a.n(n),c=a(55028),d=a(12108),x=a(35695),h=a(49376),m=a(17951),u=a(28883),p=a(81497),b=a(57434),g=a(9803),v=a(20690),y=a(53311),w=a(28199),f=a(94498),j=a(381);let N=(0,a(90163).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var k=a(42355),A=a(74783),C=a(47924),S=a(23861),M=a(59964),P=a(33109),T=a(92138),H=a(92657),z=a(69037),E=a(14186),_=a(79397),L=a(59434),V=a(66474),q=a(34576),G=a(71007),R=a(34835);function I(e){var t,a;let{userProfile:n,className:c="",showDropdown:x=!0}=e,{data:h}=(0,d.useSession)(),[u,p]=(0,s.useState)(!1),b=(0,s.useRef)(null);(0,s.useEffect)(()=>{function e(e){b.current&&!b.current.contains(e.target)&&p(!1)}return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let g=()=>{if((null==n?void 0:n.firstName)&&(null==n?void 0:n.lastName))return"".concat(n.firstName[0]).concat(n.lastName[0]);if(null==n?void 0:n.name){let e=n.name.split(" ");return e.length>1?"".concat(e[0][0]).concat(e[e.length-1][0]):e[0][0]}return(null==n?void 0:n.email)?n.email[0].toUpperCase():"U"},v=()=>(null==n?void 0:n.firstName)&&(null==n?void 0:n.lastName)?"".concat(n.firstName," ").concat(n.lastName):(null==n?void 0:n.name)?n.name:(null==n?void 0:n.email)?n.email.split("@")[0]:"User",y=()=>{var e,t;return(null==n||null==(e=n.subscription)?void 0:e.plan)==="free"?"Free Plan":(null==n||null==(t=n.subscription)?void 0:t.plan)==="pro"?"Pro Member":"Member"},w=()=>{var e,t;return(null==n||null==(e=n.subscription)?void 0:e.plan)==="free"?"text-gray-400":(null==n||null==(t=n.subscription)?void 0:t.plan)==="pro"?"text-yellow-400":"text-blue-400"},f=async()=>{await (0,d.signOut)({callbackUrl:"/login"})};return n?(0,i.jsxs)("div",{className:"relative ".concat(c),ref:b,children:[(0,i.jsxs)(l.P.button,{onClick:()=>x&&p(!u),className:"flex items-center space-x-3 p-2 rounded-xl hover:bg-white/5 transition-all group",whileHover:{scale:1.02},whileTap:{scale:.98},children:[(0,i.jsxs)("div",{className:"relative",children:[n.image?(0,i.jsx)("img",{src:n.image,alt:v(),className:"w-10 h-10 rounded-xl object-cover border-2 border-white/20 group-hover:border-violet-400/50 transition-colors"}):(0,i.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-xl flex items-center justify-center text-white font-semibold group-hover:from-violet-400 group-hover:to-indigo-400 transition-all",children:g()}),(0,i.jsx)("div",{className:"absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-emerald-400 border-2 border-gray-900 rounded-full"})]}),(0,i.jsxs)("div",{className:"text-left",children:[(0,i.jsx)("p",{className:"text-sm font-medium text-white group-hover:text-violet-300 transition-colors",children:v()}),(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(m.A,{className:"w-3 h-3 ".concat(w())}),(0,i.jsx)("p",{className:"text-xs ".concat(w()),children:y()})]})]}),x&&(0,i.jsx)(l.P.div,{animate:{rotate:180*!!u},transition:{duration:.2},children:(0,i.jsx)(V.A,{className:"w-4 h-4 text-gray-400 group-hover:text-white transition-colors"})})]}),x&&(0,i.jsx)(r.N,{children:u&&(0,i.jsxs)(l.P.div,{initial:{opacity:0,y:10,scale:.95},animate:{opacity:1,y:0,scale:1},exit:{opacity:0,y:10,scale:.95},transition:{duration:.2},className:"absolute top-full right-0 mt-2 w-80 bg-black/90 backdrop-blur-xl border border-white/10 rounded-2xl shadow-2xl z-50",children:[(0,i.jsx)("div",{className:"p-4 border-b border-white/10",children:(0,i.jsxs)("div",{className:"flex items-center space-x-3",children:[n.image?(0,i.jsx)("img",{src:n.image,alt:v(),className:"w-12 h-12 rounded-xl object-cover border-2 border-white/20"}):(0,i.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-xl flex items-center justify-center text-white font-semibold text-lg",children:g()}),(0,i.jsxs)("div",{className:"flex-1",children:[(0,i.jsx)("h3",{className:"font-semibold text-white",children:v()}),(0,i.jsx)("p",{className:"text-sm text-gray-400",children:n.email}),(0,i.jsxs)("div",{className:"flex items-center space-x-1 mt-1",children:[(0,i.jsx)(m.A,{className:"w-3 h-3 ".concat(w())}),(0,i.jsx)("span",{className:"text-xs ".concat(w()),children:y()})]})]})]})}),(0,i.jsx)("div",{className:"p-4 border-b border-white/10",children:(0,i.jsxs)("div",{className:"grid grid-cols-3 gap-3",children:[(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center mx-auto mb-1",children:(0,i.jsx)(q.A,{className:"w-4 h-4 text-blue-400"})}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Content"}),(0,i.jsx)("p",{className:"text-sm font-semibold text-white",children:(null==(t=n.stats)?void 0:t.totalContent)||0})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center mx-auto mb-1",children:(0,i.jsx)(P.A,{className:"w-4 h-4 text-emerald-400"})}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Quality"}),(0,i.jsx)("p",{className:"text-sm font-semibold text-white",children:"9.8/10"})]}),(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center mx-auto mb-1",children:(0,i.jsx)(E.A,{className:"w-4 h-4 text-purple-400"})}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Saved"}),(0,i.jsx)("p",{className:"text-sm font-semibold text-white",children:"48h"})]})]})}),(0,i.jsxs)("div",{className:"p-2",children:[(0,i.jsx)(o(),{href:"/profile",children:(0,i.jsxs)(l.P.button,{whileHover:{backgroundColor:"rgba(255, 255, 255, 0.05)"},className:"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors",onClick:()=>p(!1),children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center",children:(0,i.jsx)(G.A,{className:"w-4 h-4 text-blue-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white font-medium",children:"View Profile"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Manage your profile information"})]})]})}),(0,i.jsx)(o(),{href:"/settings",children:(0,i.jsxs)(l.P.button,{whileHover:{backgroundColor:"rgba(255, 255, 255, 0.05)"},className:"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors",onClick:()=>p(!1),children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-emerald-500/20 rounded-lg flex items-center justify-center",children:(0,i.jsx)(j.A,{className:"w-4 h-4 text-emerald-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white font-medium",children:"Settings"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Preferences and configuration"})]})]})}),(0,i.jsxs)(l.P.button,{whileHover:{backgroundColor:"rgba(255, 255, 255, 0.05)"},className:"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-yellow-500/20 rounded-lg flex items-center justify-center",children:(0,i.jsx)(S.A,{className:"w-4 h-4 text-yellow-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white font-medium",children:"Notifications"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Manage your alerts"})]}),(0,i.jsx)("div",{className:"ml-auto",children:(0,i.jsx)("div",{className:"w-2 h-2 bg-violet-500 rounded-full"})})]}),(null==(a=n.subscription)?void 0:a.plan)==="free"&&(0,i.jsxs)(l.P.button,{whileHover:{backgroundColor:"rgba(255, 255, 255, 0.05)"},className:"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-lg flex items-center justify-center",children:(0,i.jsx)(m.A,{className:"w-4 h-4 text-white"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white font-medium",children:"Upgrade to Pro"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Unlock premium features"})]})]}),(0,i.jsxs)(l.P.button,{whileHover:{backgroundColor:"rgba(255, 255, 255, 0.05)"},className:"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-purple-500/20 rounded-lg flex items-center justify-center",children:(0,i.jsx)(N,{className:"w-4 h-4 text-purple-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-white font-medium",children:"Help & Support"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Get help and documentation"})]})]})]}),(0,i.jsx)("div",{className:"p-2 border-t border-white/10",children:(0,i.jsxs)(l.P.button,{whileHover:{backgroundColor:"rgba(239, 68, 68, 0.1)"},onClick:f,className:"w-full flex items-center space-x-3 p-3 rounded-xl text-left transition-colors",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-red-500/20 rounded-lg flex items-center justify-center",children:(0,i.jsx)(R.A,{className:"w-4 h-4 text-red-400"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-red-400 font-medium",children:"Sign Out"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Sign out of your account"})]})]})})]})})]}):(0,i.jsxs)("div",{className:"flex items-center space-x-3 ".concat(c),children:[(0,i.jsx)("div",{className:"w-10 h-10 bg-gray-600 rounded-xl animate-pulse"}),(0,i.jsxs)("div",{className:"space-y-1",children:[(0,i.jsx)("div",{className:"w-20 h-3 bg-gray-600 rounded animate-pulse"}),(0,i.jsx)("div",{className:"w-16 h-2 bg-gray-700 rounded animate-pulse"})]})]})}var U=a(53904),O=a(24357),B=a(62525);let D={blog:{icon:b.A,label:"Blog Post",color:"from-pink-500 to-rose-500",bgColor:"bg-pink-500/10",borderColor:"border-pink-500/20"},email:{icon:u.A,label:"Email",color:"from-emerald-500 to-teal-500",bgColor:"bg-emerald-500/10",borderColor:"border-emerald-500/20"},youtube_script:{icon:g.A,label:"YouTube Script",color:"from-red-500 to-orange-500",bgColor:"bg-red-500/10",borderColor:"border-red-500/20"},invincible_research:{icon:m.A,label:"Invincible V.1",color:"from-violet-700 to-indigo-700",bgColor:"bg-violet-700/10",borderColor:"border-violet-700/20"}};function Q(e){let{limit:t=6,showFilters:a=!0}=e,[n,c]=(0,s.useState)([]),[d,x]=(0,s.useState)(!0),[h,m]=(0,s.useState)("all"),[u,p]=(0,s.useState)(!1),g=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"all";try{p(!0);let a=new URLSearchParams({limit:t.toString(),offset:"0"});"all"!==e&&a.append("type",e);let i=await fetch("/api/content?".concat(a));if(i.ok){let e=await i.json();c(e.content||[])}}catch(e){console.error("Error fetching content:",e)}finally{x(!1),p(!1)}};(0,s.useEffect)(()=>{g(h)},[h,t]);let v=e=>{m(e)},y=async e=>{try{(await fetch("/api/content?id=".concat(e),{method:"DELETE"})).ok&&c(t=>t.filter(t=>t.id!==e))}catch(e){console.error("Error deleting content:",e)}},w=async e=>{try{await navigator.clipboard.writeText(e)}catch(e){console.error("Error copying content:",e)}},f=async e=>{try{if(e.id&&(await fetch("/api/articles/".concat(e.id))).ok)return"/article-view/".concat(e.id);let t=await fetch("/api/articles/store",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({title:e.title,content:e.content,type:e.type,metadata:e.metadata})}),a=await t.json();if(t.ok&&a.success&&a.url)return a.url;return console.warn("Failed to store article, using fallback URL"),"/article-view?article=".concat(encodeURIComponent(e.content),"&title=").concat(encodeURIComponent(e.title))}catch(t){return console.error("Error generating article URL:",t),"/article-view?article=".concat(encodeURIComponent(e.content),"&title=").concat(encodeURIComponent(e.title))}},j=async(e,t)=>{t.preventDefault();try{let t=await f(e);window.location.href=t}catch(t){console.error("Error handling view click:",t),window.location.href="/article-view?article=".concat(encodeURIComponent(e.content),"&title=").concat(encodeURIComponent(e.title))}},N=e=>new Date(e).toLocaleDateString("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),k=e=>D[e]||D.blog;return d?(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("h3",{className:"text-xl font-bold text-white",children:"Recent Content"}),(0,i.jsx)("div",{className:"animate-spin w-5 h-5 border-2 border-violet-400 border-t-transparent rounded-full"})]}),(0,i.jsx)("div",{className:"grid gap-4",children:[1,2,3].map(e=>(0,i.jsx)("div",{className:"animate-pulse",children:(0,i.jsx)("div",{className:"h-24 bg-white/5 rounded-xl"})},e))})]}):(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsx)("h3",{className:"text-xl font-bold text-white",children:"Recent Content"}),(0,i.jsx)("div",{className:"flex items-center space-x-2",children:(0,i.jsx)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},onClick:()=>g(h),disabled:u,className:"p-2 bg-white/5 hover:bg-white/10 border border-white/10 rounded-lg transition-colors disabled:opacity-50",children:(0,i.jsx)(U.A,{className:"w-4 h-4 text-gray-400 ".concat(u?"animate-spin":"")})})})]}),a&&(0,i.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,i.jsx)("button",{onClick:()=>v("all"),className:"px-3 py-1.5 text-sm rounded-lg transition-colors ".concat("all"===h?"bg-violet-600 text-white":"bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white"),children:"All"}),Object.entries(D).map(e=>{let[t,a]=e;return(0,i.jsxs)("button",{onClick:()=>v(t),className:"px-3 py-1.5 text-sm rounded-lg transition-colors flex items-center space-x-1 ".concat(h===t?"bg-violet-600 text-white":"bg-white/5 text-gray-400 hover:bg-white/10 hover:text-white"),children:[(0,i.jsx)(a.icon,{className:"w-3 h-3"}),(0,i.jsx)("span",{children:a.label})]},t)})]}),(0,i.jsx)("div",{className:"space-y-4",children:(0,i.jsx)(r.N,{children:0===n.length?(0,i.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"text-center py-12",children:[(0,i.jsx)(b.A,{className:"w-12 h-12 text-gray-600 mx-auto mb-4"}),(0,i.jsx)("p",{className:"text-gray-400",children:"No content found"}),(0,i.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Start creating content to see it here"})]}):n.map((e,t)=>{let a=k(e.type),s=a.icon;return(0,i.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{delay:.1*t},className:"group relative backdrop-blur-xl border rounded-xl p-4 transition-all duration-300 ".concat("invincible_research"===e.type?"bg-white/10 border-white/20 hover:bg-white/15 hover:border-white/30 shadow-xl":"bg-white/5 ".concat(a.borderColor," hover:bg-white/10")),children:["invincible_research"===e.type&&(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-xl"}),(0,i.jsxs)("div",{className:"relative flex items-start justify-between",children:[(0,i.jsxs)("div",{className:"flex items-start space-x-3 flex-1",children:[(0,i.jsx)("div",{className:"p-2 rounded-lg border ".concat("invincible_research"===e.type?"bg-violet-700/20 border-violet-700/30 backdrop-blur-sm":"".concat(a.bgColor," ").concat(a.borderColor)),children:(0,i.jsx)(s,{className:"w-4 h-4 text-white"})}),(0,i.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,i.jsx)("h4",{className:"text-white font-medium truncate",children:e.title}),(0,i.jsx)("p",{className:"text-gray-400 text-sm mt-1 line-clamp-2",children:e.preview}),(0,i.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(E.A,{className:"w-3 h-3"}),(0,i.jsx)("span",{children:N(e.createdAt)})]}),e.wordCount&&(0,i.jsxs)("span",{children:[e.wordCount.toLocaleString()," words"]}),e.tone&&(0,i.jsx)("span",{className:"capitalize",children:e.tone})]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity",children:[(0,i.jsx)(l.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>w(e.content),className:"p-1.5 text-gray-400 hover:text-white rounded-lg transition-colors ".concat("invincible_research"===e.type?"hover:bg-white/15 backdrop-blur-sm":"hover:bg-white/10"),title:"Copy content",children:(0,i.jsx)(O.A,{className:"w-4 h-4"})}),(0,i.jsx)(l.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:t=>j(e,t),className:"p-1.5 text-gray-400 hover:text-white rounded-lg transition-colors ".concat("invincible_research"===e.type?"hover:bg-white/15 backdrop-blur-sm":"hover:bg-white/10"),title:"View content",children:(0,i.jsx)(H.A,{className:"w-4 h-4"})}),(0,i.jsx)(l.P.button,{whileHover:{scale:1.1},whileTap:{scale:.9},onClick:()=>y(e.id),className:"p-1.5 text-gray-400 hover:text-red-400 rounded-lg transition-colors ".concat("invincible_research"===e.type?"hover:bg-red-500/20 backdrop-blur-sm":"hover:bg-red-500/10"),title:"Delete content",children:(0,i.jsx)(B.A,{className:"w-4 h-4"})})]})]})]},e.id)})})}),n.length>=t&&(0,i.jsx)("div",{className:"text-center",children:(0,i.jsx)(o(),{href:"/content",className:"text-violet-400 hover:text-violet-300 text-sm font-medium",children:"View all content →"})})]})}let W=(0,c.default)(()=>a.e(9678).then(a.bind(a,19678)),{loadableGenerated:{webpack:()=>[19678]},ssr:!1,loading:()=>(0,i.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,i.jsx)("div",{className:"w-16 h-16 border-4 border-violet-500/30 border-t-violet-500 rounded-full animate-spin"})})}),Y=(0,c.default)(()=>a.e(5228).then(a.bind(a,25228)),{loadableGenerated:{webpack:()=>[25228]},ssr:!1,loading:()=>(0,i.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,i.jsx)("div",{className:"w-16 h-16 border-4 border-pink-500/30 border-t-pink-500 rounded-full animate-spin"})})}),F=(0,c.default)(()=>a.e(1136).then(a.bind(a,1136)),{loadableGenerated:{webpack:()=>[1136]},ssr:!1,loading:()=>(0,i.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,i.jsx)("div",{className:"w-16 h-16 border-4 border-emerald-500/30 border-t-emerald-500 rounded-full animate-spin"})})}),Z=(0,c.default)(()=>a.e(1801).then(a.bind(a,1801)),{loadableGenerated:{webpack:()=>[1801]},ssr:!1,loading:()=>(0,i.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,i.jsx)("div",{className:"w-16 h-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin"})})}),K=(0,c.default)(()=>a.e(8984).then(a.bind(a,78984)),{loadableGenerated:{webpack:()=>[78984]},ssr:!1,loading:()=>(0,i.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,i.jsx)("div",{className:"w-16 h-16 border-4 border-red-500/30 border-t-red-500 rounded-full animate-spin"})})}),J=(0,c.default)(()=>a.e(2598).then(a.bind(a,2598)),{loadableGenerated:{webpack:()=>[2598]},ssr:!1,loading:()=>(0,i.jsx)("div",{className:"w-full h-full flex items-center justify-center",children:(0,i.jsx)("div",{className:"w-16 h-16 border-4 border-purple-500/30 border-t-purple-500 rounded-full animate-spin"})})});function $(){let{data:e,status:t}=(0,d.useSession)(),a=(0,x.useRouter)(),[n,c]=(0,s.useState)("invincible-agent"),[M,P]=(0,s.useState)(!0),[T,H]=(0,s.useState)(""),[z,E]=(0,s.useState)(!1),[_,V]=(0,s.useState)(null),[q,G]=(0,s.useState)(null),[R,U]=(0,s.useState)(!0),[O,B]=(0,s.useState)(null),[D,Q]=(0,s.useState)(!0);(0,s.useEffect)(()=>{"unauthenticated"===t&&a.push("/login")},[t,a]),(0,s.useEffect)(()=>{(null==e?void 0:e.user)&&($(),et())},[e]);let $=async()=>{try{let e=await fetch("/api/user/profile");if(e.ok){let t=await e.json();G(t)}}catch(e){console.error("Error fetching user profile:",e)}finally{U(!1)}},et=async()=>{try{let e=await fetch("/api/stats");if(e.ok){let t=await e.json();B(t.stats)}}catch(e){console.error("Error fetching user stats:",e)}finally{Q(!1)}};if("loading"===t||R)return(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center space-y-4",children:[(0,i.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"}),(0,i.jsx)("p",{className:"text-gray-400",children:"Loading your dashboard..."})]})});if("unauthenticated"===t)return null;let ea=e=>{if(!(null==O?void 0:O.contentBreakdown))return{generated:0,quality:9,avgTime:"0 min"};let t=O.contentBreakdown[e]||0,a=Math.min(9.8,8.5+.05*t);return{generated:t,quality:Math.round(10*a)/10,avgTime:({invincible_research:"4 min",blog:"3 min",email:"1 min",social_media:"30 sec",youtube_script:"4 min"})[e]||"2 min"}},ei=[{id:"invincible-v2",title:"Invincible V2",subtitle:"Autonomous AI Agent",description:"Next-generation fully autonomous content creation with multi-agent orchestration, self-improvement learning, and guaranteed article superiority.",icon:h.A,color:"from-purple-600/90 to-indigo-600/90",bgGradient:"from-purple-950/40 to-indigo-950/40",accentColor:"purple",stats:ea("invincible_v2"),features:["Fully Autonomous","Multi-Agent System","Self-Improvement","Real-Time Streaming","Guaranteed Superiority","Advanced Reasoning"],href:"/invincible-v2",preview:(0,i.jsxs)("div",{className:"w-full h-32 bg-gradient-to-br from-purple-600 to-indigo-600 rounded-lg flex items-center justify-center relative overflow-hidden",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-purple-400/20 to-indigo-400/20 animate-pulse"}),(0,i.jsxs)("div",{className:"text-white text-center relative z-10",children:[(0,i.jsx)(h.A,{className:"h-10 w-10 mx-auto mb-2 animate-pulse"}),(0,i.jsx)("div",{className:"text-sm font-bold",children:"V2 AGENT"}),(0,i.jsx)("div",{className:"text-xs opacity-90",children:"Autonomous System"})]})]})},{id:"invincible-agent",title:"Invincible V.1",subtitle:"Superior Content",description:"RAG-based content generation that analyzes competition, understands human writing patterns, and creates superior articles that dominate search results.",icon:m.A,color:"from-violet-800/80 to-indigo-800/80",bgGradient:"from-violet-950/30 to-indigo-950/30",accentColor:"violet",stats:ea("invincible_research"),features:["RAG Research","Competitive Analysis","Human Writing Style","SEO Optimization","Superior Quality","Knowledge Base"],href:"/invincible",preview:(0,i.jsx)(W,{})},{id:"email-generator",title:"Email Composer",subtitle:"Professional Emails",description:"Create compelling email campaigns, newsletters, and personal messages with AI-powered suggestions.",icon:u.A,color:"from-emerald-600 to-teal-600",bgGradient:"from-emerald-900 to-teal-900",accentColor:"emerald",stats:ea("email"),features:["Templates","Personalization","A/B Testing","Analytics"],href:"/generate/email",preview:(0,i.jsx)(F,{})},{id:"tweet-generator",title:"Social Media",subtitle:"Viral Content",description:"Generate engaging tweets, threads, and social media content optimized for maximum engagement.",icon:p.A,color:"from-blue-600 to-cyan-600",bgGradient:"from-blue-900 to-cyan-900",accentColor:"blue",stats:ea("social_media"),features:["Trending Topics","Hashtags","Thread Builder","Scheduling"],href:"/generate/tweet",preview:(0,i.jsx)(Z,{})},{id:"blog-generator",title:"Blog Writer",subtitle:"SEO-Optimized",description:"Create comprehensive blog posts with built-in SEO optimization and competitive analysis.",icon:b.A,color:"from-pink-600 to-rose-600",bgGradient:"from-pink-900 to-rose-900",accentColor:"pink",stats:ea("blog"),features:["SEO Analysis","Keyword Research","Competition Check","Readability"],href:"/generate/blog",preview:(0,i.jsx)(Y,{})},{id:"youtube-script",title:"Video Scripts",subtitle:"YouTube & More",description:"Write engaging scripts for YouTube videos, tutorials, and presentations with timing cues.",icon:g.A,color:"from-red-600 to-orange-600",bgGradient:"from-red-900 to-orange-900",accentColor:"red",stats:ea("youtube_script"),features:["Hook Generator","Scene Breakdown","CTA Builder","Timing"],href:"/generate/youtube",preview:(0,i.jsx)(K,{})},{id:"video-alchemy",title:"Video Alchemy",subtitle:"Video to Article",description:"Transform YouTube videos into SEO-optimized articles by extracting captions and using AI to create comprehensive content.",icon:v.A,color:"from-purple-600 to-pink-600",bgGradient:"from-purple-900 to-pink-900",accentColor:"purple",stats:ea("video_alchemy"),features:["Caption Extraction","Multi-language","SEO/AEO/GEO","Human-like Writing","Custom Tones","Up to 5 Videos"],href:"/video-alchemy",preview:(0,i.jsx)(J,{})}],es=ei.find(e=>e.id===n)||ei[0];return(0,i.jsxs)("div",{className:"min-h-screen bg-black",children:[(0,i.jsxs)("div",{className:"fixed inset-0 z-0",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-violet-950/10 via-black to-indigo-950/10"}),(0,i.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},transition:{duration:.5},className:(0,L.cn)("absolute inset-0 bg-gradient-to-br opacity-20",es.bgGradient)},es.id),(0,i.jsx)(l.P.div,{animate:{x:[0,100,0],y:[0,-100,0]},transition:{duration:20,repeat:1/0,ease:"linear"},className:"absolute top-1/4 left-1/4 w-[500px] h-[500px] bg-violet-700/10 rounded-full blur-[100px]"}),(0,i.jsx)(l.P.div,{animate:{x:[0,-100,0],y:[0,100,0]},transition:{duration:15,repeat:1/0,ease:"linear"},className:"absolute bottom-1/4 right-1/4 w-[600px] h-[600px] bg-indigo-700/10 rounded-full blur-[120px]"})]}),(0,i.jsxs)("div",{className:"relative z-10 flex min-h-screen",children:[(0,i.jsx)(r.N,{children:M&&(0,i.jsx)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:()=>P(!1),className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"})}),(0,i.jsxs)(l.P.aside,{initial:{x:0},animate:{x:M?0:-280},transition:{type:"spring",damping:25},className:"fixed left-0 top-0 h-screen w-[280px] bg-black/60 backdrop-blur-2xl border-r border-white/10 flex flex-col z-50",children:[(0,i.jsx)("div",{className:"p-6 border-b border-white/10",children:(0,i.jsxs)(o(),{href:"/",className:"flex items-center space-x-3",children:[(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-violet-800 to-indigo-800 rounded-xl blur-lg opacity-70"}),(0,i.jsx)("div",{className:"relative bg-black rounded-xl p-2.5 border border-white/20",children:(0,i.jsx)(y.A,{className:"w-6 h-6 text-white"})})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-xl font-bold text-white",children:"Invincible"}),(0,i.jsx)("p",{className:"text-xs text-gray-400",children:"Creative AI Suite"})]})]})}),(0,i.jsxs)("nav",{className:"flex-1 p-4 space-y-2 overflow-y-auto",children:[(0,i.jsxs)("button",{onClick:()=>c(""),className:(0,L.cn)("w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all",n?"text-gray-400 hover:text-white hover:bg-white/5":"bg-white/10 text-white"),children:[(0,i.jsx)(w.A,{className:"w-5 h-5"}),(0,i.jsx)("span",{className:"font-medium",children:"Dashboard"})]}),(0,i.jsx)("div",{className:"pt-4 pb-2",children:(0,i.jsx)("p",{className:"text-xs font-semibold text-gray-500 uppercase tracking-wider px-4",children:"AI Tools"})}),(0,i.jsx)(o(),{href:"/content",children:(0,i.jsxs)("button",{className:"w-full flex items-center space-x-3 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/5 rounded-xl transition-all",children:[(0,i.jsx)(f.A,{className:"w-5 h-5"}),(0,i.jsxs)("div",{className:"text-left",children:[(0,i.jsx)("p",{className:"font-medium",children:"Content Library"}),(0,i.jsx)("p",{className:"text-xs opacity-70",children:"View Past Content"})]})]})}),ei.map(e=>(0,i.jsxs)(l.P.button,{onClick:()=>c(e.id),onMouseEnter:()=>V(e.id),onMouseLeave:()=>V(null),className:(0,L.cn)("w-full flex items-center space-x-3 px-4 py-3 rounded-xl transition-all relative overflow-hidden",n===e.id?"invincible-agent"===e.id?"bg-gradient-to-r text-white shadow-lg backdrop-blur-xl border border-white/20":"bg-gradient-to-r text-white shadow-lg":"text-gray-400 hover:text-white hover:bg-white/5",n===e.id&&e.color),children:[(0,i.jsx)(r.N,{children:_===e.id&&n!==e.id&&(0,i.jsx)(l.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:.1,scale:1},exit:{opacity:0,scale:.8},className:(0,L.cn)("absolute inset-0 bg-gradient-to-r",e.color)})}),(0,i.jsxs)("div",{className:"relative z-10 flex items-center space-x-3",children:[(0,i.jsx)(e.icon,{className:"w-5 h-5"}),(0,i.jsxs)("div",{className:"text-left",children:[(0,i.jsx)("p",{className:"font-medium",children:e.title}),(0,i.jsx)("p",{className:"text-xs opacity-70",children:e.subtitle})]})]}),n===e.id&&(0,i.jsx)(l.P.div,{layoutId:"activeToolIndicator",className:"absolute right-2 w-1 h-6 bg-white rounded-full",initial:!1,transition:{type:"spring",damping:20}})]},e.id))]}),(0,i.jsxs)("div",{className:"p-4 border-t border-white/10 space-y-2",children:[(0,i.jsx)(o(),{href:"/settings",children:(0,i.jsxs)("button",{className:"w-full flex items-center space-x-3 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/5 rounded-xl transition-all",children:[(0,i.jsx)(j.A,{className:"w-5 h-5"}),(0,i.jsx)("span",{children:"Settings"})]})}),(0,i.jsxs)("button",{className:"w-full flex items-center space-x-3 px-4 py-3 text-gray-400 hover:text-white hover:bg-white/5 rounded-xl transition-all",children:[(0,i.jsx)(N,{className:"w-5 h-5"}),(0,i.jsx)("span",{children:"Help & Support"})]})]})]}),(0,i.jsx)("button",{onClick:()=>P(!M),className:(0,L.cn)("fixed top-6 z-50 p-2 bg-black/60 backdrop-blur-xl border border-white/10 rounded-lg transition-all",M?"left-[268px]":"left-4"),children:M?(0,i.jsx)(k.A,{className:"w-5 h-5 text-white"}):(0,i.jsx)(A.A,{className:"w-5 h-5 text-white"})}),(0,i.jsxs)("main",{className:(0,L.cn)("flex-1 min-h-screen transition-all duration-300",M?"lg:ml-[280px]":"ml-0"),children:[(0,i.jsx)("header",{className:"sticky top-0 z-40 backdrop-blur-xl bg-black/40 border-b border-white/10",children:(0,i.jsx)("div",{className:(0,L.cn)("px-8 py-4 transition-all duration-300",M?"pl-8":"pl-16"),children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-8",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-white",children:n?es.title:"Dashboard Overview"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(C.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,i.jsx)("input",{type:"text",placeholder:"Search tools, features...",value:T,onChange:e=>H(e.target.value),className:"w-80 pl-10 pr-4 py-2.5 bg-white/5 border border-white/10 rounded-xl text-white placeholder-gray-400 focus:bg-white/10 focus:border-violet-500/50 transition-all"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsxs)("button",{className:"relative p-2.5 text-gray-400 hover:text-white transition-colors",onClick:()=>E(!z),children:[(0,i.jsx)(S.A,{className:"w-5 h-5"}),(0,i.jsx)("span",{className:"absolute top-1 right-1 w-2 h-2 bg-violet-500 rounded-full"})]}),(0,i.jsx)(I,{userProfile:q,className:"pl-4 border-l border-white/10"})]})]})})}),(0,i.jsx)("div",{className:(0,L.cn)("p-8 pb-16 transition-all duration-300",M?"pl-8":"pl-16"),children:(0,i.jsx)(r.N,{mode:"wait",children:n?(0,i.jsx)(X,{tool:es},n):(0,i.jsx)(ee,{tools:ei,userProfile:q,getDisplayName:()=>(null==q?void 0:q.firstName)&&(null==q?void 0:q.lastName)?"".concat(q.firstName," ").concat(q.lastName):(null==q?void 0:q.name)?q.name:(null==q?void 0:q.email)?q.email.split("@")[0]:"User",userStats:O,isLoadingStats:D},"overview")})})]})]})]})}function X(e){let{tool:t}=e;return(0,i.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.3},className:"space-y-8",children:[(0,i.jsxs)("div",{className:(0,L.cn)("relative overflow-hidden rounded-3xl backdrop-blur-xl border p-8","invincible-agent"===t.id?"bg-gradient-to-br from-white/10 to-white/5 border-white/20 shadow-2xl":"bg-gradient-to-br from-white/5 to-white/0 border-white/10"),children:["invincible-agent"===t.id&&(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/20 via-transparent to-transparent rounded-3xl"}),(0,i.jsx)("div",{className:(0,L.cn)("absolute inset-0 bg-gradient-to-br opacity-20",t.color)}),(0,i.jsxs)("div",{className:"relative z-10 grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,i.jsx)("div",{className:(0,L.cn)("p-4 rounded-2xl text-white shadow-2xl","invincible-agent"===t.id?"bg-gradient-to-br from-violet-800/70 to-indigo-800/70 backdrop-blur-sm border border-white/30":(0,L.cn)("bg-gradient-to-br",t.color)),children:(0,i.jsx)(t.icon,{className:"w-8 h-8"})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-white",children:t.title}),(0,i.jsx)("p",{className:"text-lg text-gray-300",children:t.subtitle})]})]}),(0,i.jsx)("p",{className:"text-gray-300 leading-relaxed",children:t.description}),(0,i.jsx)("div",{className:"flex flex-wrap gap-3",children:t.features.map((e,a)=>(0,i.jsx)("span",{className:(0,L.cn)("px-4 py-2 rounded-full text-sm text-white border","invincible-agent"===t.id?"bg-white/15 backdrop-blur-sm border-white/30":"bg-white/10 backdrop-blur-sm border-white/20"),children:e},a))}),(0,i.jsx)("div",{className:"flex items-center space-x-4",children:(0,i.jsx)(o(),{href:t.href,children:(0,i.jsxs)(l.P.button,{whileHover:{scale:1.05},whileTap:{scale:.95},className:(0,L.cn)("px-12 py-6 rounded-2xl font-bold text-lg text-white shadow-2xl transition-all flex items-center space-x-3","invincible-agent"===t.id?"bg-gradient-to-r from-violet-800/80 to-indigo-800/80 backdrop-blur-sm border border-white/30 hover:from-violet-700/90 hover:to-indigo-700/90":(0,L.cn)("bg-gradient-to-r",t.color)),children:[(0,i.jsx)(M.A,{className:"w-6 h-6"}),(0,i.jsxs)("span",{children:["Launch ",t.title]})]})})})]}),(0,i.jsx)("div",{className:(0,L.cn)("relative h-[400px] rounded-2xl overflow-hidden border","invincible-agent"===t.id?"bg-black/30 backdrop-blur-sm border-white/20":"bg-black/40 border-white/10"),children:t.preview||(0,i.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,i.jsxs)("div",{className:"text-center space-y-4",children:[(0,i.jsx)(t.icon,{className:"w-24 h-24 text-white/20 mx-auto"}),(0,i.jsx)("p",{className:"text-gray-400",children:"Interactive preview coming soon"})]})})})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsx)(o(),{href:"/content?type=".concat("invincible-agent"===t.id?"invincible_research":"blog-generator"===t.id?"blog":"email-generator"===t.id?"email":"youtube-script"===t.id?"youtube_script":t.id.replace("-","_")),children:(0,i.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:(0,L.cn)("backdrop-blur-xl border rounded-2xl p-6 cursor-pointer hover:bg-white/10 transition-all duration-200 group","invincible-agent"===t.id?"bg-white/10 border-white/20 shadow-xl hover:border-white/30":"bg-white/5 border-white/10 hover:border-white/20"),title:"View all ".concat(t.title," content in your library"),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)(b.A,{className:(0,L.cn)("w-8 h-8 group-hover:scale-110 transition-transform duration-200","text-".concat(t.accentColor,"-400"))}),(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)(P.A,{className:"w-5 h-5 text-emerald-400"}),(0,i.jsx)(T.A,{className:"w-4 h-4 text-gray-400 group-hover:text-white transition-colors"})]})]}),(0,i.jsx)("p",{className:"text-3xl font-bold text-white mb-1 group-hover:text-violet-200 transition-colors",children:t.stats.generated}),(0,i.jsx)("p",{className:"text-sm text-gray-400 group-hover:text-gray-300 transition-colors",children:"Content Generated - Click to view"}),(0,i.jsx)("div",{className:"mt-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,i.jsxs)("div",{className:"flex items-center text-xs text-violet-300",children:[(0,i.jsx)(H.A,{className:"w-3 h-3 mr-1"}),(0,i.jsx)("span",{children:"View in Content Library"})]})})]})}),(0,i.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:(0,L.cn)("backdrop-blur-xl border rounded-2xl p-6","invincible-agent"===t.id?"bg-white/10 border-white/20 shadow-xl":"bg-white/5 border-white/10"),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)(z.A,{className:(0,L.cn)("w-8 h-8","text-".concat(t.accentColor,"-400"))}),(0,i.jsx)("div",{className:"flex items-center space-x-1",children:[1,2,3,4,5].map(e=>(0,i.jsx)("div",{className:(0,L.cn)("w-3 h-3 rounded-full",e<=Math.round(t.stats.quality/2)?"bg-yellow-400":"bg-gray-600")},e))})]}),(0,i.jsxs)("p",{className:"text-3xl font-bold text-white mb-1",children:[t.stats.quality,"/10"]}),(0,i.jsx)("p",{className:"text-sm text-gray-400",children:"Quality Score"})]}),(0,i.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:(0,L.cn)("backdrop-blur-xl border rounded-2xl p-6","invincible-agent"===t.id?"bg-white/10 border-white/20 shadow-xl":"bg-white/5 border-white/10"),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,i.jsx)(E.A,{className:(0,L.cn)("w-8 h-8","text-".concat(t.accentColor,"-400"))}),(0,i.jsx)(_.A,{className:"w-5 h-5 text-blue-400"})]}),(0,i.jsx)("p",{className:"text-3xl font-bold text-white mb-1",children:t.stats.avgTime}),(0,i.jsx)("p",{className:"text-sm text-gray-400",children:"Average Time"})]})]})]})}function ee(e){var t,a,s,r,n,c,d;let{tools:x,userProfile:h,getDisplayName:m,userStats:u,isLoadingStats:p}=e;return(0,i.jsxs)(l.P.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"space-y-8",children:[(0,i.jsxs)("div",{className:"bg-gradient-to-r from-violet-800/20 to-indigo-800/20 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,i.jsxs)("h1",{className:"text-4xl font-bold text-white mb-4",children:["Welcome back, ",m(),"! ✨"]}),(0,i.jsx)("p",{className:"text-xl text-gray-300 mb-6",children:"Your creative AI toolkit is ready. What will you create today?"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:p?Array.from({length:4}).map((e,t)=>(0,i.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4 animate-pulse",children:[(0,i.jsx)("div",{className:"h-5 bg-white/10 rounded mb-2"}),(0,i.jsx)("div",{className:"h-8 bg-white/10 rounded mb-1"}),(0,i.jsx)("div",{className:"h-4 bg-white/10 rounded"})]},t)):[{label:"Total Created",value:(null==u||null==(t=u.totalContent)?void 0:t.toString())||"0",icon:b.A,change:(null==u||null==(a=u.trends)?void 0:a.contentGrowth)||"+0%"},{label:"Time Saved",value:"".concat((null==u?void 0:u.timeSavedHours)||0," hrs"),icon:E.A,change:(null==u||null==(s=u.trends)?void 0:s.timeEfficiency)||"+0%"},{label:"Quality Score",value:"".concat((null==u?void 0:u.qualityScore)||9,"/10"),icon:z.A,change:(null==u||null==(r=u.trends)?void 0:r.qualityImprovement)||"+0.0"},{label:"Active Tools",value:(null==u||null==(c=u.trends)||null==(n=c.toolsActive)?void 0:n.toString())||"0",icon:f.A,change:"+"+((null==u||null==(d=u.trends)?void 0:d.toolsActive)||0)}].map((e,t)=>(0,i.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},className:"bg-black/40 backdrop-blur-sm border border-white/10 rounded-xl p-4",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,i.jsx)(e.icon,{className:"w-5 h-5 text-violet-400"}),(0,i.jsx)("span",{className:"text-xs text-emerald-400",children:e.change})]}),(0,i.jsx)("p",{className:"text-2xl font-bold text-white",children:e.value}),(0,i.jsx)("p",{className:"text-sm text-gray-400",children:e.label})]},e.label))})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-white mb-6",children:"Your AI Tools"}),(0,i.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:x.map((e,t)=>(0,i.jsx)(o(),{href:e.href,children:(0,i.jsxs)(l.P.div,{initial:{opacity:0,scale:.9},animate:{opacity:1,scale:1},transition:{delay:.1*t},whileHover:{y:-8},className:"group relative cursor-pointer",children:[(0,i.jsx)("div",{className:(0,L.cn)("absolute inset-0 bg-gradient-to-br opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl",e.color)}),(0,i.jsxs)("div",{className:(0,L.cn)("relative backdrop-blur-xl border transition-all","invincible-agent"===e.id?"bg-black/40 border-white/20 hover:border-white/30 rounded-2xl shadow-2xl":"bg-black/60 border-white/10 hover:border-white/20 rounded-2xl"),children:["invincible-agent"===e.id&&(0,i.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-white/10 via-transparent to-transparent rounded-2xl"}),(0,i.jsxs)("div",{className:"relative p-6",children:[(0,i.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,i.jsx)("div",{className:(0,L.cn)("p-3 rounded-xl text-white","invincible-agent"===e.id?"bg-gradient-to-br from-violet-800/60 to-indigo-800/60 backdrop-blur-sm border border-white/20":(0,L.cn)("bg-gradient-to-br",e.color)),children:(0,i.jsx)(e.icon,{className:"w-6 h-6"})}),(0,i.jsx)(T.A,{className:"w-5 h-5 text-gray-400 group-hover:text-white transition-colors"})]}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-white mb-1",children:e.title}),(0,i.jsx)("p",{className:"text-sm text-gray-400 mb-4",children:e.subtitle}),(0,i.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,i.jsxs)(o(),{href:"/content?type=".concat("invincible-agent"===e.id?"invincible_research":"blog-generator"===e.id?"blog":"email-generator"===e.id?"email":"youtube-script"===e.id?"youtube_script":e.id.replace("-","_")),onClick:e=>e.stopPropagation(),className:"hover:bg-white/10 rounded px-2 py-1 transition-colors group/stat",title:"View all ".concat(e.title," content"),children:[(0,i.jsx)("span",{className:"text-gray-500 group-hover/stat:text-gray-400",children:"Generated"}),(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)("p",{className:"text-white font-medium group-hover/stat:text-violet-200",children:e.stats.generated}),(0,i.jsx)(T.A,{className:"w-3 h-3 text-gray-400 group-hover/stat:text-white opacity-0 group-hover/stat:opacity-100 transition-all"})]}),(0,i.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-black/90 text-white text-xs rounded opacity-0 group-hover/stat:opacity-100 transition-opacity pointer-events-none whitespace-nowrap",children:"Click to view content"})]}),(0,i.jsxs)("div",{className:"text-right",children:[(0,i.jsx)("span",{className:"text-gray-500",children:"Quality"}),(0,i.jsxs)("p",{className:"text-white font-medium",children:[e.stats.quality,"/10"]})]})]})]})]})]})},e.id))})]}),(0,i.jsx)("div",{className:"bg-white/5 backdrop-blur-xl border border-white/10 rounded-2xl p-6",children:(0,i.jsx)(Q,{limit:5,showFilters:!0})})]})}},28199:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},28883:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},33109:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},34576:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},34835:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},42355:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},46024:(e,t,a)=>{Promise.resolve().then(a.bind(a,26669))},47924:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},49376:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},53904:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},57434:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},59434:(e,t,a)=>{"use strict";a.d(t,{T:()=>r,cn:()=>l});var i=a(52596),s=a(39688);function l(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,i.$)(t))}function r(e,t){if(!e)return t||"";try{return decodeURIComponent(e)}catch(a){return console.warn("URI decode failed, using fallback:",a),t||e}}},59964:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66474:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},69037:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},74783:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},79397:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},81497:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},92138:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},94498:(e,t,a)=>{"use strict";a.d(t,{A:()=>i});let i=(0,a(90163).A)("Layers",[["path",{d:"m12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83Z",key:"8b97xw"}],["path",{d:"m22 17.65-9.17 4.16a2 2 0 0 1-1.66 0L2 17.65",key:"dd6zsq"}],["path",{d:"m22 12.65-9.17 4.16a2 2 0 0 1-1.66 0L2 12.65",key:"ep9fru"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[2108,9061,6635,7862,8441,1684,7358],()=>t(46024)),_N_E=e.O()}]);