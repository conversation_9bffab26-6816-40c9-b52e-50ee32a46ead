(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{381:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17951:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},24399:(e,t,s)=>{Promise.resolve().then(s.bind(s,33246))},28199:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},33109:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},33246:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>N});var a=s(95155),l=s(12115),r=s(12108),i=s(35695),c=s(1978),n=s(6874),d=s.n(n),o=s(28199),x=s(381),h=s(84355),m=s(17951),u=s(69074),b=s(34576),p=s(33109),f=s(69037),v=s(14186),y=s(79397),j=s(71539),g=s(57434);function N(){var e,t,s,n,N;let{data:w,status:k}=(0,r.useSession)(),A=(0,i.useRouter)(),[M,P]=(0,l.useState)(null),[H,S]=(0,l.useState)(!0);(0,l.useEffect)(()=>{"unauthenticated"===k&&A.push("/login")},[k,A]),(0,l.useEffect)(()=>{(null==w?void 0:w.user)&&C()},[w]);let C=async()=>{try{let e=await fetch("/api/user/profile");if(e.ok){let t=await e.json();P(t)}}catch(e){console.error("Error fetching user profile:",e)}finally{S(!1)}};if("loading"===k||H)return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"animate-spin w-12 h-12 border-2 border-violet-400 border-t-transparent rounded-full mx-auto"}),(0,a.jsx)("p",{className:"text-gray-400",children:"Loading your profile..."})]})});if("unauthenticated"===k)return null;if(!M)return(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("p",{className:"text-gray-400",children:"Unable to load profile"}),(0,a.jsx)(d(),{href:"/dashboard",children:(0,a.jsx)("button",{className:"px-4 py-2 bg-violet-600 text-white rounded-lg",children:"Back to Dashboard"})})]})});let z=()=>(null==M?void 0:M.firstName)&&(null==M?void 0:M.lastName)?"".concat(M.firstName," ").concat(M.lastName):(null==M?void 0:M.name)?M.name:(null==M?void 0:M.email)?M.email.split("@")[0]:"User";return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900",children:[(0,a.jsx)("div",{className:"border-b border-white/10 bg-black/20 backdrop-blur-xl",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsx)(d(),{href:"/dashboard",children:(0,a.jsxs)(c.P.button,{whileHover:{scale:1.05},className:"flex items-center space-x-3 text-white hover:text-violet-400 transition-colors",children:[(0,a.jsx)(o.A,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"font-semibold",children:"Dashboard"})]})}),(0,a.jsx)("div",{className:"text-gray-600",children:"|"}),(0,a.jsx)("h1",{className:"text-2xl font-bold bg-gradient-to-r from-violet-400 to-indigo-400 bg-clip-text text-transparent",children:"My Profile"})]}),(0,a.jsx)(d(),{href:"/settings",children:(0,a.jsxs)(c.P.button,{whileHover:{scale:1.05},className:"flex items-center space-x-2 px-4 py-2 bg-violet-600 hover:bg-violet-700 text-white rounded-xl transition-colors",children:[(0,a.jsx)(x.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Edit Profile"})]})})]})})}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-6 py-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8 text-center",children:[(0,a.jsxs)("div",{className:"relative inline-block mb-6",children:[M.image?(0,a.jsx)("img",{src:M.image,alt:z(),className:"w-32 h-32 rounded-3xl object-cover border-4 border-white/20"}):(0,a.jsx)("div",{className:"w-32 h-32 bg-gradient-to-br from-violet-500 to-indigo-500 rounded-3xl flex items-center justify-center text-white text-4xl font-bold",children:(()=>{if((null==M?void 0:M.firstName)&&(null==M?void 0:M.lastName))return"".concat(M.firstName[0]).concat(M.lastName[0]);if(null==M?void 0:M.name){let e=M.name.split(" ");return e.length>1?"".concat(e[0][0]).concat(e[e.length-1][0]):e[0][0]}return(null==M?void 0:M.email)?M.email[0].toUpperCase():"U"})()}),(0,a.jsx)("button",{className:"absolute -bottom-2 -right-2 w-10 h-10 bg-violet-600 hover:bg-violet-700 rounded-full flex items-center justify-center text-white transition-colors",children:(0,a.jsx)(h.A,{className:"w-5 h-5"})})]}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-white mb-2",children:z()}),(0,a.jsx)("p",{className:"text-gray-400 mb-4",children:M.email}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 mb-6",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 ".concat((null==(e=M.subscription)?void 0:e.plan)==="free"?"text-gray-400":"text-yellow-400")}),(0,a.jsx)("span",{className:"font-medium ".concat((null==(t=M.subscription)?void 0:t.plan)==="free"?"text-gray-400":"text-yellow-400"),children:(null==(s=M.subscription)?void 0:s.plan)==="free"?"Free Plan":(null==(n=M.subscription)?void 0:n.plan)==="pro"?"Pro Member":"Member"})]}),M.bio&&(0,a.jsx)("p",{className:"text-gray-300 text-sm leading-relaxed mb-6",children:M.bio}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-gray-400 text-sm",children:[(0,a.jsx)(u.A,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:["Joined ",new Date(M.createdAt).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]})]})]})}),(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-500/20 to-blue-600/10 backdrop-blur-xl border border-blue-500/20 rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(b.A,{className:"w-8 h-8 text-blue-400"}),(0,a.jsx)(p.A,{className:"w-5 h-5 text-emerald-400"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white mb-1",children:(null==(N=M.stats)?void 0:N.totalContent)||0}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Content Created"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-emerald-500/20 to-emerald-600/10 backdrop-blur-xl border border-emerald-500/20 rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(f.A,{className:"w-8 h-8 text-emerald-400"}),(0,a.jsx)("div",{className:"flex space-x-1",children:[1,2,3,4,5].map(e=>(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-yellow-400"},e))})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white mb-1",children:"9.8/10"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Quality Score"})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-purple-500/20 to-purple-600/10 backdrop-blur-xl border border-purple-500/20 rounded-2xl p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)(v.A,{className:"w-8 h-8 text-purple-400"}),(0,a.jsx)(y.A,{className:"w-5 h-5 text-blue-400"})]}),(0,a.jsx)("p",{className:"text-3xl font-bold text-white mb-1",children:"48h"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Time Saved"})]})]}),(0,a.jsxs)(c.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"bg-gradient-to-br from-white/10 to-white/5 backdrop-blur-xl border border-white/10 rounded-3xl p-8",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-white mb-6 flex items-center",children:[(0,a.jsx)(j.A,{className:"w-6 h-6 mr-3 text-violet-400"}),"Quick Actions"]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsx)(d(),{href:"/settings",children:(0,a.jsxs)(c.P.button,{whileHover:{scale:1.02},className:"w-full flex items-center space-x-3 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 text-violet-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-white",children:"Account Settings"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Manage preferences"})]})]})}),(0,a.jsx)(d(),{href:"/dashboard",children:(0,a.jsxs)(c.P.button,{whileHover:{scale:1.02},className:"w-full flex items-center space-x-3 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left",children:[(0,a.jsx)(o.A,{className:"w-5 h-5 text-blue-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-white",children:"Dashboard"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Back to main hub"})]})]})}),(0,a.jsx)(d(),{href:"/invincible",children:(0,a.jsxs)(c.P.button,{whileHover:{scale:1.02},className:"w-full flex items-center space-x-3 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left",children:[(0,a.jsx)(m.A,{className:"w-5 h-5 text-yellow-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-white",children:"Invincible V.1"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Superior content"})]})]})}),(0,a.jsx)(d(),{href:"/generate/blog",children:(0,a.jsxs)(c.P.button,{whileHover:{scale:1.02},className:"w-full flex items-center space-x-3 p-4 bg-white/5 hover:bg-white/10 border border-white/10 rounded-xl transition-all text-left",children:[(0,a.jsx)(g.A,{className:"w-5 h-5 text-pink-400"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-white",children:"Create Content"}),(0,a.jsx)("p",{className:"text-sm text-gray-400",children:"Start writing"})]})]})})]})]})]})]})})]})}},34576:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},35695:(e,t,s)=>{"use strict";var a=s(18999);s.o(a,"useParams")&&s.d(t,{useParams:function(){return a.useParams}}),s.o(a,"useRouter")&&s.d(t,{useRouter:function(){return a.useRouter}}),s.o(a,"useSearchParams")&&s.d(t,{useSearchParams:function(){return a.useSearchParams}})},57434:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},69037:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},69074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71539:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},79397:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},84355:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(90163).A)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])}},e=>{var t=t=>e(e.s=t);e.O(0,[2108,9061,8441,1684,7358],()=>t(24399)),_N_E=e.O()}]);