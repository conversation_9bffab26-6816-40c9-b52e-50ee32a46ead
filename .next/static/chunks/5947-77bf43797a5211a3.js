"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5947],{423:(t,e,n)=>{n.d(e,{A:()=>i});var r=n(4701);let i=r.bP.create({name:"tableRow",addOptions:()=>({HTMLAttributes:{}}),content:"(tableCell | tableHeader)*",tableRole:"row",parseHTML:()=>[{tag:"tr"}],renderHTML({HTMLAttributes:t}){return["tr",(0,r.KV)(this.options.HTMLAttributes,t),0]}})},808:(t,e,n)=>{n.d(e,{$L:()=>R,Ln:()=>m,N0:()=>T,Um:()=>E,Wg:()=>g,X9:()=>s,dL:()=>B,jP:()=>k,n9:()=>M,oM:()=>b,zy:()=>x});var r=n(10156);class i{constructor(t,e,n){this.pos=t,this.delInfo=e,this.recover=n}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class o{constructor(t,e=!1){if(this.ranges=t,this.inverted=e,!t.length&&o.empty)return o.empty}recover(t){let e=0,n=65535&t;if(!this.inverted)for(let t=0;t<n;t++)e+=this.ranges[3*t+2]-this.ranges[3*t+1];return this.ranges[3*n]+e+(t-(65535&t))/65536}mapResult(t,e=1){return this._map(t,e,!1)}map(t,e=1){return this._map(t,e,!0)}_map(t,e,n){let r=0,o=this.inverted?2:1,s=this.inverted?1:2;for(let l=0;l<this.ranges.length;l+=3){let a=this.ranges[l]-(this.inverted?r:0);if(a>t)break;let h=this.ranges[l+o],c=this.ranges[l+s],p=a+h;if(t<=p){let o=h?t==a?-1:t==p?1:e:e,s=a+r+(o<0?0:c);if(n)return s;let d=t==(e<0?a:p)?null:l/3+(t-a)*65536,u=t==a?2:t==p?1:4;return(e<0?t!=a:t!=p)&&(u|=8),new i(s,u,d)}r+=c-h}return n?t+r:new i(t+r,0,null)}touches(t,e){let n=0,r=65535&e,i=this.inverted?2:1,o=this.inverted?1:2;for(let e=0;e<this.ranges.length;e+=3){let s=this.ranges[e]-(this.inverted?n:0);if(s>t)break;let l=this.ranges[e+i];if(t<=s+l&&e==3*r)return!0;n+=this.ranges[e+o]-l}return!1}forEach(t){let e=this.inverted?2:1,n=this.inverted?1:2;for(let r=0,i=0;r<this.ranges.length;r+=3){let o=this.ranges[r],s=o-(this.inverted?i:0),l=o+(this.inverted?0:i),a=this.ranges[r+e],h=this.ranges[r+n];t(s,s+a,l,l+h),i+=h-a}}invert(){return new o(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(t){return 0==t?o.empty:new o(t<0?[0,-t,0]:[0,0,t])}}o.empty=new o([]);class s{constructor(t,e,n=0,r=t?t.length:0){this.mirror=e,this.from=n,this.to=r,this._maps=t||[],this.ownData=!(t||e)}get maps(){return this._maps}slice(t=0,e=this.maps.length){return new s(this._maps,this.mirror,t,e)}appendMap(t,e){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(t),null!=e&&this.setMirror(this._maps.length-1,e)}appendMapping(t){for(let e=0,n=this._maps.length;e<t._maps.length;e++){let r=t.getMirror(e);this.appendMap(t._maps[e],null!=r&&r<e?n+r:void 0)}}getMirror(t){if(this.mirror){for(let e=0;e<this.mirror.length;e++)if(this.mirror[e]==t)return this.mirror[e+(e%2?-1:1)]}}setMirror(t,e){this.mirror||(this.mirror=[]),this.mirror.push(t,e)}appendMappingInverted(t){for(let e=t.maps.length-1,n=this._maps.length+t._maps.length;e>=0;e--){let r=t.getMirror(e);this.appendMap(t._maps[e].invert(),null!=r&&r>e?n-r-1:void 0)}}invert(){let t=new s;return t.appendMappingInverted(this),t}map(t,e=1){if(this.mirror)return this._map(t,e,!0);for(let n=this.from;n<this.to;n++)t=this._maps[n].map(t,e);return t}mapResult(t,e=1){return this._map(t,e,!1)}_map(t,e,n){let r=0;for(let n=this.from;n<this.to;n++){let i=this._maps[n].mapResult(t,e);if(null!=i.recover){let e=this.getMirror(n);if(null!=e&&e>n&&e<this.to){n=e,t=this._maps[e].recover(i.recover);continue}}r|=i.delInfo,t=i.pos}return n?t:new i(t,r,null)}}let l=Object.create(null);class a{getMap(){return o.empty}merge(t){return null}static fromJSON(t,e){if(!e||!e.stepType)throw RangeError("Invalid input for Step.fromJSON");let n=l[e.stepType];if(!n)throw RangeError(`No step type ${e.stepType} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in l)throw RangeError("Duplicate use of step JSON ID "+t);return l[t]=e,e.prototype.jsonID=t,e}}class h{constructor(t,e){this.doc=t,this.failed=e}static ok(t){return new h(t,null)}static fail(t){return new h(null,t)}static fromReplace(t,e,n,i){try{return h.ok(t.replace(e,n,i))}catch(t){if(t instanceof r.vI)return h.fail(t.message);throw t}}}function c(t,e,n){let i=[];for(let r=0;r<t.childCount;r++){let o=t.child(r);o.content.size&&(o=o.copy(c(o.content,e,o))),o.isInline&&(o=e(o,n,r)),i.push(o)}return r.FK.fromArray(i)}class p extends a{constructor(t,e,n){super(),this.from=t,this.to=e,this.mark=n}apply(t){let e=t.slice(this.from,this.to),n=t.resolve(this.from),i=n.node(n.sharedDepth(this.to)),o=new r.Ji(c(e.content,(t,e)=>t.isAtom&&e.type.allowsMarkType(this.mark.type)?t.mark(this.mark.addToSet(t.marks)):t,i),e.openStart,e.openEnd);return h.fromReplace(t,this.from,this.to,o)}invert(){return new d(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new p(e.pos,n.pos,this.mark)}merge(t){return t instanceof p&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new p(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new p(e.from,e.to,t.markFromJSON(e.mark))}}a.jsonID("addMark",p);class d extends a{constructor(t,e,n){super(),this.from=t,this.to=e,this.mark=n}apply(t){let e=t.slice(this.from,this.to),n=new r.Ji(c(e.content,t=>t.mark(this.mark.removeFromSet(t.marks)),t),e.openStart,e.openEnd);return h.fromReplace(t,this.from,this.to,n)}invert(){return new p(this.from,this.to,this.mark)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deleted&&n.deleted||e.pos>=n.pos?null:new d(e.pos,n.pos,this.mark)}merge(t){return t instanceof d&&t.mark.eq(this.mark)&&this.from<=t.to&&this.to>=t.from?new d(Math.min(this.from,t.from),Math.max(this.to,t.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new d(e.from,e.to,t.markFromJSON(e.mark))}}a.jsonID("removeMark",d);class u extends a{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return h.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.addToSet(e.marks));return h.fromReplace(t,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,+!e.isLeaf))}invert(t){let e=t.nodeAt(this.pos);if(e){let t=this.mark.addToSet(e.marks);if(t.length==e.marks.length){for(let n=0;n<e.marks.length;n++)if(!e.marks[n].isInSet(t))return new u(this.pos,e.marks[n]);return new u(this.pos,this.mark)}}return new f(this.pos,this.mark)}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new u(e.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new u(e.pos,t.markFromJSON(e.mark))}}a.jsonID("addNodeMark",u);class f extends a{constructor(t,e){super(),this.pos=t,this.mark=e}apply(t){let e=t.nodeAt(this.pos);if(!e)return h.fail("No node at mark step's position");let n=e.type.create(e.attrs,null,this.mark.removeFromSet(e.marks));return h.fromReplace(t,this.pos,this.pos+1,new r.Ji(r.FK.from(n),0,+!e.isLeaf))}invert(t){let e=t.nodeAt(this.pos);return e&&this.mark.isInSet(e.marks)?new u(this.pos,this.mark):this}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new f(e.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(t,e){if("number"!=typeof e.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new f(e.pos,t.markFromJSON(e.mark))}}a.jsonID("removeNodeMark",f);class m extends a{constructor(t,e,n,r=!1){super(),this.from=t,this.to=e,this.slice=n,this.structure=r}apply(t){return this.structure&&y(t,this.from,this.to)?h.fail("Structure replace would overwrite content"):h.fromReplace(t,this.from,this.to,this.slice)}getMap(){return new o([this.from,this.to-this.from,this.slice.size])}invert(t){return new m(this.from,this.from+this.slice.size,t.slice(this.from,this.to))}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1);return e.deletedAcross&&n.deletedAcross?null:new m(e.pos,Math.max(e.pos,n.pos),this.slice,this.structure)}merge(t){if(!(t instanceof m)||t.structure||this.structure)return null;if(this.from+this.slice.size!=t.from||this.slice.openEnd||t.slice.openStart)if(t.to!=this.from||this.slice.openStart||t.slice.openEnd)return null;else{let e=this.slice.size+t.slice.size==0?r.Ji.empty:new r.Ji(t.slice.content.append(this.slice.content),t.slice.openStart,this.slice.openEnd);return new m(t.from,this.to,e,this.structure)}{let e=this.slice.size+t.slice.size==0?r.Ji.empty:new r.Ji(this.slice.content.append(t.slice.content),this.slice.openStart,t.slice.openEnd);return new m(this.from,this.to+(t.to-t.from),e,this.structure)}}toJSON(){let t={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new m(e.from,e.to,r.Ji.fromJSON(t,e.slice),!!e.structure)}}a.jsonID("replace",m);class g extends a{constructor(t,e,n,r,i,o,s=!1){super(),this.from=t,this.to=e,this.gapFrom=n,this.gapTo=r,this.slice=i,this.insert=o,this.structure=s}apply(t){if(this.structure&&(y(t,this.from,this.gapFrom)||y(t,this.gapTo,this.to)))return h.fail("Structure gap-replace would overwrite content");let e=t.slice(this.gapFrom,this.gapTo);if(e.openStart||e.openEnd)return h.fail("Gap is not a flat range");let n=this.slice.insertAt(this.insert,e.content);return n?h.fromReplace(t,this.from,this.to,n):h.fail("Content does not fit in gap")}getMap(){return new o([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(t){let e=this.gapTo-this.gapFrom;return new g(this.from,this.from+this.slice.size+e,this.from+this.insert,this.from+this.insert+e,t.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(t){let e=t.mapResult(this.from,1),n=t.mapResult(this.to,-1),r=this.from==this.gapFrom?e.pos:t.map(this.gapFrom,-1),i=this.to==this.gapTo?n.pos:t.map(this.gapTo,1);return e.deletedAcross&&n.deletedAcross||r<e.pos||i>n.pos?null:new g(e.pos,n.pos,r,i,this.slice,this.insert,this.structure)}toJSON(){let t={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(t.slice=this.slice.toJSON()),this.structure&&(t.structure=!0),t}static fromJSON(t,e){if("number"!=typeof e.from||"number"!=typeof e.to||"number"!=typeof e.gapFrom||"number"!=typeof e.gapTo||"number"!=typeof e.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new g(e.from,e.to,e.gapFrom,e.gapTo,r.Ji.fromJSON(t,e.slice),e.insert,!!e.structure)}}function y(t,e,n){let r=t.resolve(e),i=n-e,o=r.depth;for(;i>0&&o>0&&r.indexAfter(o)==r.node(o).childCount;)o--,i--;if(i>0){let t=r.node(o).maybeChild(r.indexAfter(o));for(;i>0;){if(!t||t.isLeaf)return!0;t=t.firstChild,i--}}return!1}function w(t,e,n,i=n.contentMatch,o=!0){let s=t.doc.nodeAt(e),l=[],a=e+1;for(let e=0;e<s.childCount;e++){let h=s.child(e),c=a+h.nodeSize,p=i.matchType(h.type);if(p){i=p;for(let e=0;e<h.marks.length;e++)n.allowsMarkType(h.marks[e].type)||t.step(new d(a,c,h.marks[e]));if(o&&h.isText&&"pre"!=n.whitespace){let t,e=/\r?\n|\r/g,i;for(;t=e.exec(h.text);)i||(i=new r.Ji(r.FK.from(n.schema.text(" ",n.allowedMarks(h.marks))),0,0)),l.push(new m(a+t.index,a+t.index+t[0].length,i))}}else l.push(new m(a,c,r.Ji.empty));a=c}if(!i.validEnd){let e=i.fillBefore(r.FK.empty,!0);t.replace(a,a,new r.Ji(e,0,0))}for(let e=l.length-1;e>=0;e--)t.step(l[e])}function k(t){let e=t.parent.content.cutByIndex(t.startIndex,t.endIndex);for(let n=t.depth;;--n){let r=t.$from.node(n),i=t.$from.index(n),o=t.$to.indexAfter(n);if(n<t.depth&&r.canReplace(i,o,e))return n;if(0==n||r.type.spec.isolating||!((0==i||r.canReplace(i,r.childCount))&&(o==r.childCount||r.canReplace(0,o))))break}return null}function b(t,e,n=null,r=t){let i=function(t,e){let{parent:n,startIndex:r,endIndex:i}=t,o=n.contentMatchAt(r).findWrapping(e);if(!o)return null;let s=o.length?o[0]:e;return n.canReplaceWith(r,i,s)?o:null}(t,e),o=i&&function(t,e){let{parent:n,startIndex:r,endIndex:i}=t,o=n.child(r),s=e.contentMatch.findWrapping(o.type);if(!s)return null;let l=(s.length?s[s.length-1]:e).contentMatch;for(let t=r;l&&t<i;t++)l=l.matchType(n.child(t).type);return l&&l.validEnd?s:null}(r,e);return o?i.map(v).concat({type:e,attrs:n}).concat(o.map(v)):null}function v(t){return{type:t,attrs:null}}function S(t,e,n,r){e.forEach((i,o)=>{if(i.isText){let s,l=/\r?\n|\r/g;for(;s=l.exec(i.text);){let i=t.mapping.slice(r).map(n+1+o+s.index);t.replaceWith(i,i+1,e.type.schema.linebreakReplacement.create())}}})}function C(t,e,n,r){e.forEach((i,o)=>{if(i.type==i.type.schema.linebreakReplacement){let i=t.mapping.slice(r).map(n+1+o);t.replaceWith(i,i+1,e.type.schema.text("\n"))}})}function x(t,e,n=1,r){let i=t.resolve(e),o=i.depth-n,s=r&&r[r.length-1]||i.parent;if(o<0||i.parent.type.spec.isolating||!i.parent.canReplace(i.index(),i.parent.childCount)||!s.type.validContent(i.parent.content.cutByIndex(i.index(),i.parent.childCount)))return!1;for(let t=i.depth-1,e=n-2;t>o;t--,e--){let n=i.node(t),o=i.index(t);if(n.type.spec.isolating)return!1;let s=n.content.cutByIndex(o,n.childCount),l=r&&r[e+1];l&&(s=s.replaceChild(0,l.type.create(l.attrs)));let a=r&&r[e]||n;if(!n.canReplace(o+1,n.childCount)||!a.type.validContent(s))return!1}let l=i.indexAfter(o),a=r&&r[0];return i.node(o).canReplaceWith(l,l,a?a.type:i.node(o+1).type)}function M(t,e){let n=t.resolve(e),r=n.index();return A(n.nodeBefore,n.nodeAfter)&&n.parent.canReplace(r,r+1)}function A(t,e){return!!(t&&e&&!t.isLeaf&&function(t,e){e.content.size||t.type.compatibleContent(e.type);let n=t.contentMatchAt(t.childCount),{linebreakReplacement:r}=t.type.schema;for(let i=0;i<e.childCount;i++){let o=e.child(i),s=o.type==r?t.type.schema.nodes.text:o.type;if(!(n=n.matchType(s))||!t.type.allowsMarks(o.marks))return!1}return n.validEnd}(t,e))}function T(t,e,n=-1){let r=t.resolve(e);for(let t=r.depth;;t--){let i,o,s=r.index(t);if(t==r.depth?(i=r.nodeBefore,o=r.nodeAfter):n>0?(i=r.node(t+1),s++,o=r.node(t).maybeChild(s)):(i=r.node(t).maybeChild(s-1),o=r.node(t+1)),i&&!i.isTextblock&&A(i,o)&&r.node(t).canReplace(s,s+1))return e;if(0==t)break;e=n<0?r.before(t):r.after(t)}}function E(t,e,n){let r=t.resolve(e);if(!n.content.size)return e;let i=n.content;for(let t=0;t<n.openStart;t++)i=i.firstChild.content;for(let t=1;t<=(0==n.openStart&&n.size?2:1);t++)for(let e=r.depth;e>=0;e--){let n=e==r.depth?0:r.pos<=(r.start(e+1)+r.end(e+1))/2?-1:1,o=r.index(e)+ +(n>0),s=r.node(e),l=!1;if(1==t)l=s.canReplace(o,o,i);else{let t=s.contentMatchAt(o).findWrapping(i.firstChild.type);l=t&&s.canReplaceWith(o,o,t[0])}if(l)return 0==n?r.pos:n<0?r.before(e+1):r.after(e+1)}return null}function R(t,e,n=e,i=r.Ji.empty){if(e==n&&!i.size)return null;let o=t.resolve(e),s=t.resolve(n);return O(o,s,i)?new m(e,n,i):new N(o,s,i).fit()}function O(t,e,n){return!n.openStart&&!n.openEnd&&t.start()==e.start()&&t.parent.canReplace(t.index(),e.index(),n.content)}a.jsonID("replaceAround",g);class N{constructor(t,e,n){this.$from=t,this.$to=e,this.unplaced=n,this.frontier=[],this.placed=r.FK.empty;for(let e=0;e<=t.depth;e++){let n=t.node(e);this.frontier.push({type:n.type,match:n.contentMatchAt(t.indexAfter(e))})}for(let e=t.depth;e>0;e--)this.placed=r.FK.from(t.node(e).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let t=this.findFittable();t?this.placeNodes(t):this.openMore()||this.dropNode()}let t=this.mustMoveInline(),e=this.placed.size-this.depth-this.$from.depth,n=this.$from,i=this.close(t<0?this.$to:n.doc.resolve(t));if(!i)return null;let o=this.placed,s=n.depth,l=i.depth;for(;s&&l&&1==o.childCount;)o=o.firstChild.content,s--,l--;let a=new r.Ji(o,s,l);return t>-1?new g(n.pos,t,this.$to.pos,this.$to.end(),a,e):a.size||n.pos!=this.$to.pos?new m(n.pos,i.pos,a):null}findFittable(){let t=this.unplaced.openStart;for(let e=this.unplaced.content,n=0,r=this.unplaced.openEnd;n<t;n++){let i=e.firstChild;if(e.childCount>1&&(r=0),i.type.spec.isolating&&r<=n){t=n;break}e=i.content}for(let e=1;e<=2;e++)for(let n=1==e?t:this.unplaced.openStart;n>=0;n--){let t,i=null,o=(n?(i=z(this.unplaced.content,n-1).firstChild).content:this.unplaced.content).firstChild;for(let t=this.depth;t>=0;t--){let{type:s,match:l}=this.frontier[t],a,h=null;if(1==e&&(o?l.matchType(o.type)||(h=l.fillBefore(r.FK.from(o),!1)):i&&s.compatibleContent(i.type)))return{sliceDepth:n,frontierDepth:t,parent:i,inject:h};if(2==e&&o&&(a=l.findWrapping(o.type)))return{sliceDepth:n,frontierDepth:t,parent:i,wrap:a};if(i&&l.matchType(i.type))break}}}openMore(){let{content:t,openStart:e,openEnd:n}=this.unplaced,i=z(t,e);return!!i.childCount&&!i.firstChild.isLeaf&&(this.unplaced=new r.Ji(t,e+1,Math.max(n,i.size+e>=t.size-n?e+1:0)),!0)}dropNode(){let{content:t,openStart:e,openEnd:n}=this.unplaced,i=z(t,e);if(i.childCount<=1&&e>0){let o=t.size-e<=e+i.size;this.unplaced=new r.Ji(I(t,e-1,1),e-1,o?e-1:n)}else this.unplaced=new r.Ji(I(t,e,1),e,n)}placeNodes({sliceDepth:t,frontierDepth:e,parent:n,inject:i,wrap:o}){for(;this.depth>e;)this.closeFrontierNode();if(o)for(let t=0;t<o.length;t++)this.openFrontierNode(o[t]);let s=this.unplaced,l=n?n.content:s.content,a=s.openStart-t,h=0,c=[],{match:p,type:d}=this.frontier[e];if(i){for(let t=0;t<i.childCount;t++)c.push(i.child(t));p=p.matchFragment(i)}let u=l.size+t-(s.content.size-s.openEnd);for(;h<l.childCount;){let t=l.child(h),e=p.matchType(t.type);if(!e)break;(++h>1||0==a||t.content.size)&&(p=e,c.push(function t(e,n,i){if(n<=0)return e;let o=e.content;return n>1&&(o=o.replaceChild(0,t(o.firstChild,n-1,1==o.childCount?i-1:0))),n>0&&(o=e.type.contentMatch.fillBefore(o).append(o),i<=0&&(o=o.append(e.type.contentMatch.matchFragment(o).fillBefore(r.FK.empty,!0)))),e.copy(o)}(t.mark(d.allowedMarks(t.marks)),1==h?a:0,h==l.childCount?u:-1)))}let f=h==l.childCount;f||(u=-1),this.placed=L(this.placed,e,r.FK.from(c)),this.frontier[e].match=p,f&&u<0&&n&&n.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let t=0,e=l;t<u;t++){let t=e.lastChild;this.frontier.push({type:t.type,match:t.contentMatchAt(t.childCount)}),e=t.content}this.unplaced=f?0==t?r.Ji.empty:new r.Ji(I(s.content,t-1,1),t-1,u<0?s.openEnd:t-1):new r.Ji(I(s.content,t,h),s.openStart,s.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let t=this.frontier[this.depth],e;if(!t.type.isTextblock||!$(this.$to,this.$to.depth,t.type,t.match,!1)||this.$to.depth==this.depth&&(e=this.findCloseLevel(this.$to))&&e.depth==this.depth)return -1;let{depth:n}=this.$to,r=this.$to.after(n);for(;n>1&&r==this.$to.end(--n);)++r;return r}findCloseLevel(t){t:for(let e=Math.min(this.depth,t.depth);e>=0;e--){let{match:n,type:r}=this.frontier[e],i=e<t.depth&&t.end(e+1)==t.pos+(t.depth-(e+1)),o=$(t,e,r,n,i);if(o){for(let n=e-1;n>=0;n--){let{match:e,type:r}=this.frontier[n],i=$(t,n,r,e,!0);if(!i||i.childCount)continue t}return{depth:e,fit:o,move:i?t.doc.resolve(t.after(e+1)):t}}}}close(t){let e=this.findCloseLevel(t);if(!e)return null;for(;this.depth>e.depth;)this.closeFrontierNode();e.fit.childCount&&(this.placed=L(this.placed,e.depth,e.fit)),t=e.move;for(let n=e.depth+1;n<=t.depth;n++){let e=t.node(n),r=e.type.contentMatch.fillBefore(e.content,!0,t.index(n));this.openFrontierNode(e.type,e.attrs,r)}return t}openFrontierNode(t,e=null,n){let i=this.frontier[this.depth];i.match=i.match.matchType(t),this.placed=L(this.placed,this.depth,r.FK.from(t.create(e,n))),this.frontier.push({type:t,match:t.contentMatch})}closeFrontierNode(){let t=this.frontier.pop().match.fillBefore(r.FK.empty,!0);t.childCount&&(this.placed=L(this.placed,this.frontier.length,t))}}function I(t,e,n){return 0==e?t.cutByIndex(n,t.childCount):t.replaceChild(0,t.firstChild.copy(I(t.firstChild.content,e-1,n)))}function L(t,e,n){return 0==e?t.append(n):t.replaceChild(t.childCount-1,t.lastChild.copy(L(t.lastChild.content,e-1,n)))}function z(t,e){for(let n=0;n<e;n++)t=t.firstChild.content;return t}function $(t,e,n,r,i){let o=t.node(e),s=i?t.indexAfter(e):t.index(e);if(s==o.childCount&&!n.compatibleContent(o.type))return null;let l=r.fillBefore(o.content,!0,s);return l&&!function(t,e,n){for(let r=n;r<e.childCount;r++)if(!t.allowsMarks(e.child(r).marks))return!0;return!1}(n,o.content,s)?l:null}function F(t,e){let n=[],r=Math.min(t.depth,e.depth);for(let i=r;i>=0;i--){let r=t.start(i);if(r<t.pos-(t.depth-i)||e.end(i)>e.pos+(e.depth-i)||t.node(i).type.spec.isolating||e.node(i).type.spec.isolating)break;(r==e.start(i)||i==t.depth&&i==e.depth&&t.parent.inlineContent&&e.parent.inlineContent&&i&&e.start(i-1)==r-1)&&n.push(i)}return n}class D extends a{constructor(t,e,n){super(),this.pos=t,this.attr=e,this.value=n}apply(t){let e=t.nodeAt(this.pos);if(!e)return h.fail("No node at attribute step's position");let n=Object.create(null);for(let t in e.attrs)n[t]=e.attrs[t];n[this.attr]=this.value;let i=e.type.create(n,null,e.marks);return h.fromReplace(t,this.pos,this.pos+1,new r.Ji(r.FK.from(i),0,+!e.isLeaf))}getMap(){return o.empty}invert(t){return new D(this.pos,this.attr,t.nodeAt(this.pos).attrs[this.attr])}map(t){let e=t.mapResult(this.pos,1);return e.deletedAfter?null:new D(e.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(t,e){if("number"!=typeof e.pos||"string"!=typeof e.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new D(e.pos,e.attr,e.value)}}a.jsonID("attr",D);class H extends a{constructor(t,e){super(),this.attr=t,this.value=e}apply(t){let e=Object.create(null);for(let n in t.attrs)e[n]=t.attrs[n];e[this.attr]=this.value;let n=t.type.create(e,t.content,t.marks);return h.ok(n)}getMap(){return o.empty}invert(t){return new H(this.attr,t.attrs[this.attr])}map(t){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(t,e){if("string"!=typeof e.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new H(e.attr,e.value)}}a.jsonID("docAttr",H);let P=class extends Error{};(P=function t(e){let n=Error.call(this,e);return n.__proto__=t.prototype,n}).prototype=Object.create(Error.prototype),P.prototype.constructor=P,P.prototype.name="TransformError";class B{constructor(t){this.doc=t,this.steps=[],this.docs=[],this.mapping=new s}get before(){return this.docs.length?this.docs[0]:this.doc}step(t){let e=this.maybeStep(t);if(e.failed)throw new P(e.failed);return this}maybeStep(t){let e=t.apply(this.doc);return e.failed||this.addStep(t,e.doc),e}get docChanged(){return this.steps.length>0}addStep(t,e){this.docs.push(this.doc),this.steps.push(t),this.mapping.appendMap(t.getMap()),this.doc=e}replace(t,e=t,n=r.Ji.empty){let i=R(this.doc,t,e,n);return i&&this.step(i),this}replaceWith(t,e,n){return this.replace(t,e,new r.Ji(r.FK.from(n),0,0))}delete(t,e){return this.replace(t,e,r.Ji.empty)}insert(t,e){return this.replaceWith(t,t,e)}replaceRange(t,e,n){return!function(t,e,n,i){if(!i.size)return t.deleteRange(e,n);let o=t.doc.resolve(e),s=t.doc.resolve(n);if(O(o,s,i))return t.step(new m(e,n,i));let l=F(o,t.doc.resolve(n));0==l[l.length-1]&&l.pop();let a=-(o.depth+1);l.unshift(a);for(let t=o.depth,e=o.pos-1;t>0;t--,e--){let n=o.node(t).type.spec;if(n.defining||n.definingAsContext||n.isolating)break;l.indexOf(t)>-1?a=t:o.before(t)==e&&l.splice(1,0,-t)}let h=l.indexOf(a),c=[],p=i.openStart;for(let t=i.content,e=0;;e++){let n=t.firstChild;if(c.push(n),e==i.openStart)break;t=n.content}for(let t=p-1;t>=0;t--){var d;let e=c[t],n=(d=e.type).spec.defining||d.spec.definingForContent;if(n&&!e.sameMarkup(o.node(Math.abs(a)-1)))p=t;else if(n||!e.type.isTextblock)break}for(let e=i.openStart;e>=0;e--){let a=(e+p+1)%(i.openStart+1),d=c[a];if(d)for(let e=0;e<l.length;e++){let c=l[(e+h)%l.length],p=!0;c<0&&(p=!1,c=-c);let u=o.node(c-1),f=o.index(c-1);if(u.canReplaceWith(f,f,d.type,d.marks))return t.replace(o.before(c),p?s.after(c):n,new r.Ji(function t(e,n,i,o,s){if(n<i){let r=e.firstChild;e=e.replaceChild(0,r.copy(t(r.content,n+1,i,o,r)))}if(n>o){let t=s.contentMatchAt(0),n=t.fillBefore(e).append(e);e=n.append(t.matchFragment(n).fillBefore(r.FK.empty,!0))}return e}(i.content,0,i.openStart,a),a,i.openEnd))}}let u=t.steps.length;for(let r=l.length-1;r>=0&&(t.replace(e,n,i),!(t.steps.length>u));r--){let t=l[r];t<0||(e=o.before(t),n=s.after(t))}}(this,t,e,n),this}replaceRangeWith(t,e,n){var i=t,o=e;if(!n.isInline&&i==o&&this.doc.resolve(i).parent.content.size){let t=function(t,e,n){let r=t.resolve(e);if(r.parent.canReplaceWith(r.index(),r.index(),n))return e;if(0==r.parentOffset)for(let t=r.depth-1;t>=0;t--){let e=r.index(t);if(r.node(t).canReplaceWith(e,e,n))return r.before(t+1);if(e>0)return null}if(r.parentOffset==r.parent.content.size)for(let t=r.depth-1;t>=0;t--){let e=r.indexAfter(t);if(r.node(t).canReplaceWith(e,e,n))return r.after(t+1);if(e<r.node(t).childCount)break}return null}(this.doc,i,n.type);null!=t&&(i=o=t)}return this.replaceRange(i,o,new r.Ji(r.FK.from(n),0,0)),this}deleteRange(t,e){return!function(t,e,n){let r=t.doc.resolve(e),i=t.doc.resolve(n),o=F(r,i);for(let e=0;e<o.length;e++){let n=o[e],s=e==o.length-1;if(s&&0==n||r.node(n).type.contentMatch.validEnd)return t.delete(r.start(n),i.end(n));if(n>0&&(s||r.node(n-1).canReplace(r.index(n-1),i.indexAfter(n-1))))return t.delete(r.before(n),i.after(n))}for(let o=1;o<=r.depth&&o<=i.depth;o++)if(e-r.start(o)==r.depth-o&&n>r.end(o)&&i.end(o)-n!=i.depth-o&&r.start(o-1)==i.start(o-1)&&r.node(o-1).canReplace(r.index(o-1),i.index(o-1)))return t.delete(r.before(o),n);t.delete(e,n)}(this,t,e),this}lift(t,e){return!function(t,e,n){let{$from:i,$to:o,depth:s}=e,l=i.before(s+1),a=o.after(s+1),h=l,c=a,p=r.FK.empty,d=0;for(let t=s,e=!1;t>n;t--)e||i.index(t)>0?(e=!0,p=r.FK.from(i.node(t).copy(p)),d++):h--;let u=r.FK.empty,f=0;for(let t=s,e=!1;t>n;t--)e||o.after(t+1)<o.end(t)?(e=!0,u=r.FK.from(o.node(t).copy(u)),f++):c++;t.step(new g(h,c,l,a,new r.Ji(p.append(u),d,f),p.size-d,!0))}(this,t,e),this}join(t,e=1){return!function(t,e,n){let i=null,{linebreakReplacement:o}=t.doc.type.schema,s=t.doc.resolve(e-n),l=s.node().type;if(o&&l.inlineContent){let t="pre"==l.whitespace,e=!!l.contentMatch.matchType(o);t&&!e?i=!1:!t&&e&&(i=!0)}let a=t.steps.length;if(!1===i){let r=t.doc.resolve(e+n);C(t,r.node(),r.before(),a)}l.inlineContent&&w(t,e+n-1,l,s.node().contentMatchAt(s.index()),null==i);let h=t.mapping.slice(a),c=h.map(e-n);if(t.step(new m(c,h.map(e+n,-1),r.Ji.empty,!0)),!0===i){let e=t.doc.resolve(c);S(t,e.node(),e.before(),t.steps.length)}}(this,t,e),this}wrap(t,e){return!function(t,e,n){let i=r.FK.empty;for(let t=n.length-1;t>=0;t--){if(i.size){let e=n[t].type.contentMatch.matchFragment(i);if(!e||!e.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}i=r.FK.from(n[t].type.create(n[t].attrs,i))}let o=e.start,s=e.end;t.step(new g(o,s,o,s,new r.Ji(i,0,0),n.length,!0))}(this,t,e),this}setBlockType(t,e=t,n,i=null){var o=this;if(!n.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let s=o.steps.length;return o.doc.nodesBetween(t,e,(t,e)=>{var l,a,h;let c,p,d="function"==typeof i?i(t):i;if(t.isTextblock&&!t.hasMarkup(n,d)&&(l=o.doc,a=o.mapping.slice(s).map(e),h=n,p=(c=l.resolve(a)).index(),c.parent.canReplaceWith(p,p+1,h))){let i=null;if(n.schema.linebreakReplacement){let t="pre"==n.whitespace,e=!!n.contentMatch.matchType(n.schema.linebreakReplacement);t&&!e?i=!1:!t&&e&&(i=!0)}!1===i&&C(o,t,e,s),w(o,o.mapping.slice(s).map(e,1),n,void 0,null===i);let l=o.mapping.slice(s),a=l.map(e,1),h=l.map(e+t.nodeSize,1);return o.step(new g(a,h,a+1,h-1,new r.Ji(r.FK.from(n.create(d,null,t.marks)),0,0),1,!0)),!0===i&&S(o,t,e,s),!1}}),this}setNodeMarkup(t,e,n=null,i){return!function(t,e,n,i,o){let s=t.doc.nodeAt(e);if(!s)throw RangeError("No node at given position");n||(n=s.type);let l=n.create(i,null,o||s.marks);if(s.isLeaf)return t.replaceWith(e,e+s.nodeSize,l);if(!n.validContent(s.content))throw RangeError("Invalid content for node type "+n.name);t.step(new g(e,e+s.nodeSize,e+1,e+s.nodeSize-1,new r.Ji(r.FK.from(l),0,0),1,!0))}(this,t,e,n,i),this}setNodeAttribute(t,e,n){return this.step(new D(t,e,n)),this}setDocAttribute(t,e){return this.step(new H(t,e)),this}addNodeMark(t,e){return this.step(new u(t,e)),this}removeNodeMark(t,e){let n=this.doc.nodeAt(t);if(!n)throw RangeError("No node at position "+t);if(e instanceof r.CU)e.isInSet(n.marks)&&this.step(new f(t,e));else{let r=n.marks,i,o=[];for(;i=e.isInSet(r);)o.push(new f(t,i)),r=i.removeFromSet(r);for(let t=o.length-1;t>=0;t--)this.step(o[t])}return this}split(t,e=1,n){return!function(t,e,n=1,i){let o=t.doc.resolve(e),s=r.FK.empty,l=r.FK.empty;for(let t=o.depth,e=o.depth-n,a=n-1;t>e;t--,a--){s=r.FK.from(o.node(t).copy(s));let e=i&&i[a];l=r.FK.from(e?e.type.create(e.attrs,l):o.node(t).copy(l))}t.step(new m(e,e,new r.Ji(s.append(l),n,n),!0))}(this,t,e,n),this}addMark(t,e,n){var r;let i,o,s,l;return r=this,s=[],l=[],r.doc.nodesBetween(t,e,(r,a,h)=>{if(!r.isInline)return;let c=r.marks;if(!n.isInSet(c)&&h.type.allowsMarkType(n.type)){let h=Math.max(a,t),u=Math.min(a+r.nodeSize,e),f=n.addToSet(c);for(let t=0;t<c.length;t++)c[t].isInSet(f)||(i&&i.to==h&&i.mark.eq(c[t])?i.to=u:s.push(i=new d(h,u,c[t])));o&&o.to==h?o.to=u:l.push(o=new p(h,u,n))}}),s.forEach(t=>r.step(t)),l.forEach(t=>r.step(t)),this}removeMark(t,e,n){var i;let o,s;return i=this,o=[],s=0,i.doc.nodesBetween(t,e,(i,l)=>{if(!i.isInline)return;s++;let a=null;if(n instanceof r.sX){let t=i.marks,e;for(;e=n.isInSet(t);)(a||(a=[])).push(e),t=e.removeFromSet(t)}else n?n.isInSet(i.marks)&&(a=[n]):a=i.marks;if(a&&a.length){let n=Math.min(l+i.nodeSize,e);for(let e=0;e<a.length;e++){let r=a[e],i;for(let t=0;t<o.length;t++){let e=o[t];e.step==s-1&&r.eq(o[t].style)&&(i=e)}i?(i.to=n,i.step=s):o.push({style:r,from:Math.max(l,t),to:n,step:s})}}}),o.forEach(t=>i.step(new d(t.from,t.to,t.style))),this}clearIncompatible(t,e,n){return w(this,t,e,n),this}}},4652:(t,e,n)=>{n.d(e,{Ay:()=>s});var r=n(4701);let i=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))$/,o=/(?:^|\s)(==(?!\s+==)((?:[^=]+))==(?!\s+==))/g,s=r.CU.create({name:"highlight",addOptions:()=>({multicolor:!1,HTMLAttributes:{}}),addAttributes(){return this.options.multicolor?{color:{default:null,parseHTML:t=>t.getAttribute("data-color")||t.style.backgroundColor,renderHTML:t=>t.color?{"data-color":t.color,style:`background-color: ${t.color}; color: inherit`}:{}}}:{}},parseHTML:()=>[{tag:"mark"}],renderHTML({HTMLAttributes:t}){return["mark",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setHighlight:t=>({commands:e})=>e.setMark(this.name,t),toggleHighlight:t=>({commands:e})=>e.toggleMark(this.name,t),unsetHighlight:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-h":()=>this.editor.commands.toggleHighlight()}},addInputRules(){return[(0,r.OX)({find:i,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:o,type:this.type})]}})},9727:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Bold",[["path",{d:"M14 12a4 4 0 0 0 0-8H6v8",key:"v2sylx"}],["path",{d:"M15 20a4 4 0 0 0 0-8H6v8Z",key:"1ef5ya"}]])},10156:(t,e,n)=>{function r(t){this.content=t}n.d(e,{S4:()=>B,ZF:()=>G,FK:()=>i,CU:()=>a,sX:()=>D,bP:()=>C,u$:()=>v,vI:()=>h,Sj:()=>H,Ji:()=>c}),r.prototype={constructor:r,find:function(t){for(var e=0;e<this.content.length;e+=2)if(this.content[e]===t)return e;return -1},get:function(t){var e=this.find(t);return -1==e?void 0:this.content[e+1]},update:function(t,e,n){var i=n&&n!=t?this.remove(n):this,o=i.find(t),s=i.content.slice();return -1==o?s.push(n||t,e):(s[o+1]=e,n&&(s[o]=n)),new r(s)},remove:function(t){var e=this.find(t);if(-1==e)return this;var n=this.content.slice();return n.splice(e,2),new r(n)},addToStart:function(t,e){return new r([t,e].concat(this.remove(t).content))},addToEnd:function(t,e){var n=this.remove(t).content.slice();return n.push(t,e),new r(n)},addBefore:function(t,e,n){var i=this.remove(e),o=i.content.slice(),s=i.find(t);return o.splice(-1==s?o.length:s,0,e,n),new r(o)},forEach:function(t){for(var e=0;e<this.content.length;e+=2)t(this.content[e],this.content[e+1])},prepend:function(t){return(t=r.from(t)).size?new r(t.content.concat(this.subtract(t).content)):this},append:function(t){return(t=r.from(t)).size?new r(this.subtract(t).content.concat(t.content)):this},subtract:function(t){var e=this;t=r.from(t);for(var n=0;n<t.content.length;n+=2)e=e.remove(t.content[n]);return e},toObject:function(){var t={};return this.forEach(function(e,n){t[e]=n}),t},get size(){return this.content.length>>1}},r.from=function(t){if(t instanceof r)return t;var e=[];if(t)for(var n in t)e.push(n,t[n]);return new r(e)};class i{constructor(t,e){if(this.content=t,this.size=e||0,null==e)for(let e=0;e<t.length;e++)this.size+=t[e].nodeSize}nodesBetween(t,e,n,r=0,i){for(let o=0,s=0;s<e;o++){let l=this.content[o],a=s+l.nodeSize;if(a>t&&!1!==n(l,r+s,i||null,o)&&l.content.size){let i=s+1;l.nodesBetween(Math.max(0,t-i),Math.min(l.content.size,e-i),n,r+i)}s=a}}descendants(t){this.nodesBetween(0,this.size,t)}textBetween(t,e,n,r){let i="",o=!0;return this.nodesBetween(t,e,(s,l)=>{let a=s.isText?s.text.slice(Math.max(t,l)-l,e-l):s.isLeaf?r?"function"==typeof r?r(s):r:s.type.spec.leafText?s.type.spec.leafText(s):"":"";s.isBlock&&(s.isLeaf&&a||s.isTextblock)&&n&&(o?o=!1:i+=n),i+=a},0),i}append(t){if(!t.size)return this;if(!this.size)return t;let e=this.lastChild,n=t.firstChild,r=this.content.slice(),o=0;for(e.isText&&e.sameMarkup(n)&&(r[r.length-1]=e.withText(e.text+n.text),o=1);o<t.content.length;o++)r.push(t.content[o]);return new i(r,this.size+t.size)}cut(t,e=this.size){if(0==t&&e==this.size)return this;let n=[],r=0;if(e>t)for(let i=0,o=0;o<e;i++){let s=this.content[i],l=o+s.nodeSize;l>t&&((o<t||l>e)&&(s=s.isText?s.cut(Math.max(0,t-o),Math.min(s.text.length,e-o)):s.cut(Math.max(0,t-o-1),Math.min(s.content.size,e-o-1))),n.push(s),r+=s.nodeSize),o=l}return new i(n,r)}cutByIndex(t,e){return t==e?i.empty:0==t&&e==this.content.length?this:new i(this.content.slice(t,e))}replaceChild(t,e){let n=this.content[t];if(n==e)return this;let r=this.content.slice(),o=this.size+e.nodeSize-n.nodeSize;return r[t]=e,new i(r,o)}addToStart(t){return new i([t].concat(this.content),this.size+t.nodeSize)}addToEnd(t){return new i(this.content.concat(t),this.size+t.nodeSize)}eq(t){if(this.content.length!=t.content.length)return!1;for(let e=0;e<this.content.length;e++)if(!this.content[e].eq(t.content[e]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(t){let e=this.content[t];if(!e)throw RangeError("Index "+t+" out of range for "+this);return e}maybeChild(t){return this.content[t]||null}forEach(t){for(let e=0,n=0;e<this.content.length;e++){let r=this.content[e];t(r,n,e),n+=r.nodeSize}}findDiffStart(t,e=0){return function t(e,n,r){for(let i=0;;i++){if(i==e.childCount||i==n.childCount)return e.childCount==n.childCount?null:r;let o=e.child(i),s=n.child(i);if(o==s){r+=o.nodeSize;continue}if(!o.sameMarkup(s))return r;if(o.isText&&o.text!=s.text){for(let t=0;o.text[t]==s.text[t];t++)r++;return r}if(o.content.size||s.content.size){let e=t(o.content,s.content,r+1);if(null!=e)return e}r+=o.nodeSize}}(this,t,e)}findDiffEnd(t,e=this.size,n=t.size){return function t(e,n,r,i){for(let o=e.childCount,s=n.childCount;;){if(0==o||0==s)return o==s?null:{a:r,b:i};let l=e.child(--o),a=n.child(--s),h=l.nodeSize;if(l==a){r-=h,i-=h;continue}if(!l.sameMarkup(a))return{a:r,b:i};if(l.isText&&l.text!=a.text){let t=0,e=Math.min(l.text.length,a.text.length);for(;t<e&&l.text[l.text.length-t-1]==a.text[a.text.length-t-1];)t++,r--,i--;return{a:r,b:i}}if(l.content.size||a.content.size){let e=t(l.content,a.content,r-1,i-1);if(e)return e}r-=h,i-=h}}(this,t,e,n)}findIndex(t,e=-1){if(0==t)return s(0,t);if(t==this.size)return s(this.content.length,t);if(t>this.size||t<0)throw RangeError(`Position ${t} outside of fragment (${this})`);for(let n=0,r=0;;n++){let i=r+this.child(n).nodeSize;if(i>=t){if(i==t||e>0)return s(n+1,i);return s(n,r)}r=i}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(t=>t.toJSON()):null}static fromJSON(t,e){if(!e)return i.empty;if(!Array.isArray(e))throw RangeError("Invalid input for Fragment.fromJSON");return new i(e.map(t.nodeFromJSON))}static fromArray(t){if(!t.length)return i.empty;let e,n=0;for(let r=0;r<t.length;r++){let i=t[r];n+=i.nodeSize,r&&i.isText&&t[r-1].sameMarkup(i)?(e||(e=t.slice(0,r)),e[e.length-1]=i.withText(e[e.length-1].text+i.text)):e&&e.push(i)}return new i(e||t,n)}static from(t){if(!t)return i.empty;if(t instanceof i)return t;if(Array.isArray(t))return this.fromArray(t);if(t.attrs)return new i([t],t.nodeSize);throw RangeError("Can not convert "+t+" to a Fragment"+(t.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}i.empty=new i([],0);let o={index:0,offset:0};function s(t,e){return o.index=t,o.offset=e,o}function l(t,e){if(t===e)return!0;if(!(t&&"object"==typeof t)||!(e&&"object"==typeof e))return!1;let n=Array.isArray(t);if(Array.isArray(e)!=n)return!1;if(n){if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!l(t[n],e[n]))return!1}else{for(let n in t)if(!(n in e)||!l(t[n],e[n]))return!1;for(let n in e)if(!(n in t))return!1}return!0}class a{constructor(t,e){this.type=t,this.attrs=e}addToSet(t){let e,n=!1;for(let r=0;r<t.length;r++){let i=t[r];if(this.eq(i))return t;if(this.type.excludes(i.type))e||(e=t.slice(0,r));else{if(i.type.excludes(this.type))return t;!n&&i.type.rank>this.type.rank&&(e||(e=t.slice(0,r)),e.push(this),n=!0),e&&e.push(i)}}return e||(e=t.slice()),n||e.push(this),e}removeFromSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return t.slice(0,e).concat(t.slice(e+1));return t}isInSet(t){for(let e=0;e<t.length;e++)if(this.eq(t[e]))return!0;return!1}eq(t){return this==t||this.type==t.type&&l(this.attrs,t.attrs)}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return t}static fromJSON(t,e){if(!e)throw RangeError("Invalid input for Mark.fromJSON");let n=t.marks[e.type];if(!n)throw RangeError(`There is no mark type ${e.type} in this schema`);let r=n.create(e.attrs);return n.checkAttrs(r.attrs),r}static sameSet(t,e){if(t==e)return!0;if(t.length!=e.length)return!1;for(let n=0;n<t.length;n++)if(!t[n].eq(e[n]))return!1;return!0}static setFrom(t){if(!t||Array.isArray(t)&&0==t.length)return a.none;if(t instanceof a)return[t];let e=t.slice();return e.sort((t,e)=>t.type.rank-e.type.rank),e}}a.none=[];class h extends Error{}class c{constructor(t,e,n){this.content=t,this.openStart=e,this.openEnd=n}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(t,e){let n=function t(e,n,r,i){let{index:o,offset:s}=e.findIndex(n),l=e.maybeChild(o);if(s==n||l.isText)return i&&!i.canReplace(o,o,r)?null:e.cut(0,n).append(r).append(e.cut(n));let a=t(l.content,n-s-1,r);return a&&e.replaceChild(o,l.copy(a))}(this.content,t+this.openStart,e);return n&&new c(n,this.openStart,this.openEnd)}removeBetween(t,e){return new c(function t(e,n,r){let{index:i,offset:o}=e.findIndex(n),s=e.maybeChild(i),{index:l,offset:a}=e.findIndex(r);if(o==n||s.isText){if(a!=r&&!e.child(l).isText)throw RangeError("Removing non-flat range");return e.cut(0,n).append(e.cut(r))}if(i!=l)throw RangeError("Removing non-flat range");return e.replaceChild(i,s.copy(t(s.content,n-o-1,r-o-1)))}(this.content,t+this.openStart,e+this.openStart),this.openStart,this.openEnd)}eq(t){return this.content.eq(t.content)&&this.openStart==t.openStart&&this.openEnd==t.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let t={content:this.content.toJSON()};return this.openStart>0&&(t.openStart=this.openStart),this.openEnd>0&&(t.openEnd=this.openEnd),t}static fromJSON(t,e){if(!e)return c.empty;let n=e.openStart||0,r=e.openEnd||0;if("number"!=typeof n||"number"!=typeof r)throw RangeError("Invalid input for Slice.fromJSON");return new c(i.fromJSON(t,e.content),n,r)}static maxOpen(t,e=!0){let n=0,r=0;for(let r=t.firstChild;r&&!r.isLeaf&&(e||!r.type.spec.isolating);r=r.firstChild)n++;for(let n=t.lastChild;n&&!n.isLeaf&&(e||!n.type.spec.isolating);n=n.lastChild)r++;return new c(t,n,r)}}function p(t,e){if(!e.type.compatibleContent(t.type))throw new h("Cannot join "+e.type.name+" onto "+t.type.name)}function d(t,e,n){let r=t.node(n);return p(r,e.node(n)),r}function u(t,e){let n=e.length-1;n>=0&&t.isText&&t.sameMarkup(e[n])?e[n]=t.withText(e[n].text+t.text):e.push(t)}function f(t,e,n,r){let i=(e||t).node(n),o=0,s=e?e.index(n):i.childCount;t&&(o=t.index(n),t.depth>n?o++:t.textOffset&&(u(t.nodeAfter,r),o++));for(let t=o;t<s;t++)u(i.child(t),r);e&&e.depth==n&&e.textOffset&&u(e.nodeBefore,r)}function m(t,e){return t.type.checkContent(e),t.copy(e)}function g(t,e,n){let r=[];return f(null,t,n,r),t.depth>n&&u(m(d(t,e,n+1),g(t,e,n+1)),r),f(e,null,n,r),new i(r)}c.empty=new c(i.empty,0,0);class y{constructor(t,e,n){this.pos=t,this.path=e,this.parentOffset=n,this.depth=e.length/3-1}resolveDepth(t){return null==t?this.depth:t<0?this.depth+t:t}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(t){return this.path[3*this.resolveDepth(t)]}index(t){return this.path[3*this.resolveDepth(t)+1]}indexAfter(t){return t=this.resolveDepth(t),this.index(t)+(t!=this.depth||this.textOffset?1:0)}start(t){return 0==(t=this.resolveDepth(t))?0:this.path[3*t-1]+1}end(t){return t=this.resolveDepth(t),this.start(t)+this.node(t).content.size}before(t){if(!(t=this.resolveDepth(t)))throw RangeError("There is no position before the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]}after(t){if(!(t=this.resolveDepth(t)))throw RangeError("There is no position after the top-level node");return t==this.depth+1?this.pos:this.path[3*t-1]+this.path[3*t].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let t=this.parent,e=this.index(this.depth);if(e==t.childCount)return null;let n=this.pos-this.path[this.path.length-1],r=t.child(e);return n?t.child(e).cut(n):r}get nodeBefore(){let t=this.index(this.depth),e=this.pos-this.path[this.path.length-1];return e?this.parent.child(t).cut(0,e):0==t?null:this.parent.child(t-1)}posAtIndex(t,e){e=this.resolveDepth(e);let n=this.path[3*e],r=0==e?0:this.path[3*e-1]+1;for(let e=0;e<t;e++)r+=n.child(e).nodeSize;return r}marks(){let t=this.parent,e=this.index();if(0==t.content.size)return a.none;if(this.textOffset)return t.child(e).marks;let n=t.maybeChild(e-1),r=t.maybeChild(e);if(!n){let t=n;n=r,r=t}let i=n.marks;for(var o=0;o<i.length;o++)!1!==i[o].type.spec.inclusive||r&&i[o].isInSet(r.marks)||(i=i[o--].removeFromSet(i));return i}marksAcross(t){let e=this.parent.maybeChild(this.index());if(!e||!e.isInline)return null;let n=e.marks,r=t.parent.maybeChild(t.index());for(var i=0;i<n.length;i++)!1!==n[i].type.spec.inclusive||r&&n[i].isInSet(r.marks)||(n=n[i--].removeFromSet(n));return n}sharedDepth(t){for(let e=this.depth;e>0;e--)if(this.start(e)<=t&&this.end(e)>=t)return e;return 0}blockRange(t=this,e){if(t.pos<this.pos)return t.blockRange(this);for(let n=this.depth-(this.parent.inlineContent||this.pos==t.pos?1:0);n>=0;n--)if(t.pos<=this.end(n)&&(!e||e(this.node(n))))return new v(this,t,n);return null}sameParent(t){return this.pos-this.parentOffset==t.pos-t.parentOffset}max(t){return t.pos>this.pos?t:this}min(t){return t.pos<this.pos?t:this}toString(){let t="";for(let e=1;e<=this.depth;e++)t+=(t?"/":"")+this.node(e).type.name+"_"+this.index(e-1);return t+":"+this.parentOffset}static resolve(t,e){if(!(e>=0&&e<=t.content.size))throw RangeError("Position "+e+" out of range");let n=[],r=0,i=e;for(let e=t;;){let{index:t,offset:o}=e.content.findIndex(i),s=i-o;if(n.push(e,t,r+o),!s||(e=e.child(t)).isText)break;i=s-1,r+=o+1}return new y(e,n,i)}static resolveCached(t,e){let n=b.get(t);if(n)for(let t=0;t<n.elts.length;t++){let r=n.elts[t];if(r.pos==e)return r}else b.set(t,n=new w);let r=n.elts[n.i]=y.resolve(t,e);return n.i=(n.i+1)%k,r}}class w{constructor(){this.elts=[],this.i=0}}let k=12,b=new WeakMap;class v{constructor(t,e,n){this.$from=t,this.$to=e,this.depth=n}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let S=Object.create(null);class C{constructor(t,e,n,r=a.none){this.type=t,this.attrs=e,this.marks=r,this.content=n||i.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(t){return this.content.child(t)}maybeChild(t){return this.content.maybeChild(t)}forEach(t){this.content.forEach(t)}nodesBetween(t,e,n,r=0){this.content.nodesBetween(t,e,n,r,this)}descendants(t){this.nodesBetween(0,this.content.size,t)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(t,e,n,r){return this.content.textBetween(t,e,n,r)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(t){return this==t||this.sameMarkup(t)&&this.content.eq(t.content)}sameMarkup(t){return this.hasMarkup(t.type,t.attrs,t.marks)}hasMarkup(t,e,n){return this.type==t&&l(this.attrs,e||t.defaultAttrs||S)&&a.sameSet(this.marks,n||a.none)}copy(t=null){return t==this.content?this:new C(this.type,this.attrs,t,this.marks)}mark(t){return t==this.marks?this:new C(this.type,this.attrs,this.content,t)}cut(t,e=this.content.size){return 0==t&&e==this.content.size?this:this.copy(this.content.cut(t,e))}slice(t,e=this.content.size,n=!1){if(t==e)return c.empty;let r=this.resolve(t),i=this.resolve(e),o=n?0:r.sharedDepth(e),s=r.start(o);return new c(r.node(o).content.cut(r.pos-s,i.pos-s),r.depth-o,i.depth-o)}replace(t,e,n){var r=this.resolve(t),o=this.resolve(e);if(n.openStart>r.depth)throw new h("Inserted content deeper than insertion position");if(r.depth-n.openStart!=o.depth-n.openEnd)throw new h("Inconsistent open depths");return function t(e,n,r,o){let s=e.index(o),l=e.node(o);if(s==n.index(o)&&o<e.depth-r.openStart){let i=t(e,n,r,o+1);return l.copy(l.content.replaceChild(s,i))}if(!r.content.size)return m(l,g(e,n,o));if(r.openStart||r.openEnd||e.depth!=o||n.depth!=o){let{start:t,end:s}=function(t,e){let n=e.depth-t.openStart,r=e.node(n).copy(t.content);for(let t=n-1;t>=0;t--)r=e.node(t).copy(i.from(r));return{start:r.resolveNoCache(t.openStart+n),end:r.resolveNoCache(r.content.size-t.openEnd-n)}}(r,e);return m(l,function t(e,n,r,o,s){let l=e.depth>s&&d(e,n,s+1),a=o.depth>s&&d(r,o,s+1),h=[];return f(null,e,s,h),l&&a&&n.index(s)==r.index(s)?(p(l,a),u(m(l,t(e,n,r,o,s+1)),h)):(l&&u(m(l,g(e,n,s+1)),h),f(n,r,s,h),a&&u(m(a,g(r,o,s+1)),h)),f(o,null,s,h),new i(h)}(e,t,s,n,o))}{let t=e.parent,i=t.content;return m(t,i.cut(0,e.parentOffset).append(r.content).append(i.cut(n.parentOffset)))}}(r,o,n,0)}nodeAt(t){for(let e=this;;){let{index:n,offset:r}=e.content.findIndex(t);if(!(e=e.maybeChild(n)))return null;if(r==t||e.isText)return e;t-=r+1}}childAfter(t){let{index:e,offset:n}=this.content.findIndex(t);return{node:this.content.maybeChild(e),index:e,offset:n}}childBefore(t){if(0==t)return{node:null,index:0,offset:0};let{index:e,offset:n}=this.content.findIndex(t);if(n<t)return{node:this.content.child(e),index:e,offset:n};let r=this.content.child(e-1);return{node:r,index:e-1,offset:n-r.nodeSize}}resolve(t){return y.resolveCached(this,t)}resolveNoCache(t){return y.resolve(this,t)}rangeHasMark(t,e,n){let r=!1;return e>t&&this.nodesBetween(t,e,t=>(n.isInSet(t.marks)&&(r=!0),!r)),r}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let t=this.type.name;return this.content.size&&(t+="("+this.content.toStringInner()+")"),M(this.marks,t)}contentMatchAt(t){let e=this.type.contentMatch.matchFragment(this.content,0,t);if(!e)throw Error("Called contentMatchAt on a node with invalid content");return e}canReplace(t,e,n=i.empty,r=0,o=n.childCount){let s=this.contentMatchAt(t).matchFragment(n,r,o),l=s&&s.matchFragment(this.content,e);if(!l||!l.validEnd)return!1;for(let t=r;t<o;t++)if(!this.type.allowsMarks(n.child(t).marks))return!1;return!0}canReplaceWith(t,e,n,r){if(r&&!this.type.allowsMarks(r))return!1;let i=this.contentMatchAt(t).matchType(n),o=i&&i.matchFragment(this.content,e);return!!o&&o.validEnd}canAppend(t){return t.content.size?this.canReplace(this.childCount,this.childCount,t.content):this.type.compatibleContent(t.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let t=a.none;for(let e=0;e<this.marks.length;e++){let n=this.marks[e];n.type.checkAttrs(n.attrs),t=n.addToSet(t)}if(!a.sameSet(t,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(t=>t.type.name)}`);this.content.forEach(t=>t.check())}toJSON(){let t={type:this.type.name};for(let e in this.attrs){t.attrs=this.attrs;break}return this.content.size&&(t.content=this.content.toJSON()),this.marks.length&&(t.marks=this.marks.map(t=>t.toJSON())),t}static fromJSON(t,e){let n;if(!e)throw RangeError("Invalid input for Node.fromJSON");if(e.marks){if(!Array.isArray(e.marks))throw RangeError("Invalid mark data for Node.fromJSON");n=e.marks.map(t.markFromJSON)}if("text"==e.type){if("string"!=typeof e.text)throw RangeError("Invalid text node in JSON");return t.text(e.text,n)}let r=i.fromJSON(t,e.content),o=t.nodeType(e.type).create(e.attrs,r,n);return o.type.checkAttrs(o.attrs),o}}C.prototype.text=void 0;class x extends C{constructor(t,e,n,r){if(super(t,e,null,r),!n)throw RangeError("Empty text nodes are not allowed");this.text=n}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):M(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(t,e){return this.text.slice(t,e)}get nodeSize(){return this.text.length}mark(t){return t==this.marks?this:new x(this.type,this.attrs,this.text,t)}withText(t){return t==this.text?this:new x(this.type,this.attrs,t,this.marks)}cut(t=0,e=this.text.length){return 0==t&&e==this.text.length?this:this.withText(this.text.slice(t,e))}eq(t){return this.sameMarkup(t)&&this.text==t.text}toJSON(){let t=super.toJSON();return t.text=this.text,t}}function M(t,e){for(let n=t.length-1;n>=0;n--)e=t[n].type.name+"("+e+")";return e}class A{constructor(t){this.validEnd=t,this.next=[],this.wrapCache=[]}static parse(t,e){var n;let r,i=new T(t,e);if(null==i.next)return A.empty;let o=function t(e){let n=[];do n.push(function(e){let n=[];do n.push(function(e){let n=function(e){if(e.eat("(")){let n=t(e);return e.eat(")")||e.err("Missing closing paren"),n}if(/\W/.test(e.next))e.err("Unexpected token '"+e.next+"'");else{let t=(function(t,e){let n=t.nodeTypes,r=n[e];if(r)return[r];let i=[];for(let t in n){let r=n[t];r.isInGroup(e)&&i.push(r)}return 0==i.length&&t.err("No node type or group '"+e+"' found"),i})(e,e.next).map(t=>(null==e.inline?e.inline=t.isInline:e.inline!=t.isInline&&e.err("Mixing inline and block content"),{type:"name",value:t}));return e.pos++,1==t.length?t[0]:{type:"choice",exprs:t}}}(e);for(;;)if(e.eat("+"))n={type:"plus",expr:n};else if(e.eat("*"))n={type:"star",expr:n};else if(e.eat("?"))n={type:"opt",expr:n};else if(e.eat("{"))n=function(t,e){let n=E(t),r=n;return t.eat(",")&&(r="}"!=t.next?E(t):-1),t.eat("}")||t.err("Unclosed braced range"),{type:"range",min:n,max:r,expr:e}}(e,n);else break;return n}(e));while(e.next&&")"!=e.next&&"|"!=e.next);return 1==n.length?n[0]:{type:"seq",exprs:n}}(e));while(e.eat("|"));return 1==n.length?n[0]:{type:"choice",exprs:n}}(i);i.next&&i.err("Unexpected trailing text");let s=(n=function(t){let e=[[]];return i(function t(e,o){if("choice"==e.type)return e.exprs.reduce((e,n)=>e.concat(t(n,o)),[]);if("seq"==e.type)for(let r=0;;r++){let s=t(e.exprs[r],o);if(r==e.exprs.length-1)return s;i(s,o=n())}else if("star"==e.type){let s=n();return r(o,s),i(t(e.expr,s),s),[r(s)]}else if("plus"==e.type){let s=n();return i(t(e.expr,o),s),i(t(e.expr,s),s),[r(s)]}else if("opt"==e.type)return[r(o)].concat(t(e.expr,o));else if("range"==e.type){let s=o;for(let r=0;r<e.min;r++){let r=n();i(t(e.expr,s),r),s=r}if(-1==e.max)i(t(e.expr,s),s);else for(let o=e.min;o<e.max;o++){let o=n();r(s,o),i(t(e.expr,s),o),s=o}return[r(s)]}else if("name"==e.type)return[r(o,void 0,e.value)];else throw Error("Unknown expr type")}(t,0),n()),e;function n(){return e.push([])-1}function r(t,n,r){let i={term:r,to:n};return e[t].push(i),i}function i(t,e){t.forEach(t=>t.to=e)}}(o),r=Object.create(null),function t(e){let i=[];e.forEach(t=>{n[t].forEach(({term:t,to:e})=>{let r;if(t){for(let e=0;e<i.length;e++)i[e][0]==t&&(r=i[e][1]);O(n,e).forEach(e=>{r||i.push([t,r=[]]),-1==r.indexOf(e)&&r.push(e)})}})});let o=r[e.join(",")]=new A(e.indexOf(n.length-1)>-1);for(let e=0;e<i.length;e++){let n=i[e][1].sort(R);o.next.push({type:i[e][0],next:r[n.join(",")]||t(n)})}return o}(O(n,0)));return function(t,e){for(let n=0,r=[t];n<r.length;n++){let t=r[n],i=!t.validEnd,o=[];for(let e=0;e<t.next.length;e++){let{type:n,next:s}=t.next[e];o.push(n.name),i&&!(n.isText||n.hasRequiredAttrs())&&(i=!1),-1==r.indexOf(s)&&r.push(s)}i&&e.err("Only non-generatable nodes ("+o.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(s,i),s}matchType(t){for(let e=0;e<this.next.length;e++)if(this.next[e].type==t)return this.next[e].next;return null}matchFragment(t,e=0,n=t.childCount){let r=this;for(let i=e;r&&i<n;i++)r=r.matchType(t.child(i).type);return r}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let t=0;t<this.next.length;t++){let{type:e}=this.next[t];if(!(e.isText||e.hasRequiredAttrs()))return e}return null}compatible(t){for(let e=0;e<this.next.length;e++)for(let n=0;n<t.next.length;n++)if(this.next[e].type==t.next[n].type)return!0;return!1}fillBefore(t,e=!1,n=0){let r=[this];return function o(s,l){let a=s.matchFragment(t,n);if(a&&(!e||a.validEnd))return i.from(l.map(t=>t.createAndFill()));for(let t=0;t<s.next.length;t++){let{type:e,next:n}=s.next[t];if(!(e.isText||e.hasRequiredAttrs())&&-1==r.indexOf(n)){r.push(n);let t=o(n,l.concat(e));if(t)return t}}return null}(this,[])}findWrapping(t){for(let e=0;e<this.wrapCache.length;e+=2)if(this.wrapCache[e]==t)return this.wrapCache[e+1];let e=this.computeWrapping(t);return this.wrapCache.push(t,e),e}computeWrapping(t){let e=Object.create(null),n=[{match:this,type:null,via:null}];for(;n.length;){let r=n.shift(),i=r.match;if(i.matchType(t)){let t=[];for(let e=r;e.type;e=e.via)t.push(e.type);return t.reverse()}for(let t=0;t<i.next.length;t++){let{type:o,next:s}=i.next[t];o.isLeaf||o.hasRequiredAttrs()||o.name in e||r.type&&!s.validEnd||(n.push({match:o.contentMatch,type:o,via:r}),e[o.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(t){if(t>=this.next.length)throw RangeError(`There's no ${t}th edge in this content match`);return this.next[t]}toString(){let t=[];return!function e(n){t.push(n);for(let r=0;r<n.next.length;r++)-1==t.indexOf(n.next[r].next)&&e(n.next[r].next)}(this),t.map((e,n)=>{let r=n+(e.validEnd?"*":" ")+" ";for(let n=0;n<e.next.length;n++)r+=(n?", ":"")+e.next[n].type.name+"->"+t.indexOf(e.next[n].next);return r}).join("\n")}}A.empty=new A(!0);class T{constructor(t,e){this.string=t,this.nodeTypes=e,this.inline=null,this.pos=0,this.tokens=t.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(t){return this.next==t&&(this.pos++||!0)}err(t){throw SyntaxError(t+" (in content expression '"+this.string+"')")}}function E(t){/\D/.test(t.next)&&t.err("Expected number, got '"+t.next+"'");let e=Number(t.next);return t.pos++,e}function R(t,e){return e-t}function O(t,e){let n=[];return function e(r){let i=t[r];if(1==i.length&&!i[0].term)return e(i[0].to);n.push(r);for(let t=0;t<i.length;t++){let{term:r,to:o}=i[t];r||-1!=n.indexOf(o)||e(o)}}(e),n.sort(R)}function N(t){let e=Object.create(null);for(let n in t){let r=t[n];if(!r.hasDefault)return null;e[n]=r.default}return e}function I(t,e){let n=Object.create(null);for(let r in t){let i=e&&e[r];if(void 0===i){let e=t[r];if(e.hasDefault)i=e.default;else throw RangeError("No value supplied for attribute "+r)}n[r]=i}return n}function L(t,e,n,r){for(let r in e)if(!(r in t))throw RangeError(`Unsupported attribute ${r} for ${n} of type ${r}`);for(let n in t){let r=t[n];r.validate&&r.validate(e[n])}}function z(t,e){let n=Object.create(null);if(e)for(let r in e)n[r]=new F(t,r,e[r]);return n}class ${constructor(t,e,n){this.name=t,this.schema=e,this.spec=n,this.markSet=null,this.groups=n.group?n.group.split(" "):[],this.attrs=z(t,n.attrs),this.defaultAttrs=N(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(n.inline||"text"==t),this.isText="text"==t}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==A.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(t){return this.groups.indexOf(t)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let t in this.attrs)if(this.attrs[t].isRequired)return!0;return!1}compatibleContent(t){return this==t||this.contentMatch.compatible(t.contentMatch)}computeAttrs(t){return!t&&this.defaultAttrs?this.defaultAttrs:I(this.attrs,t)}create(t=null,e,n){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new C(this,this.computeAttrs(t),i.from(e),a.setFrom(n))}createChecked(t=null,e,n){return e=i.from(e),this.checkContent(e),new C(this,this.computeAttrs(t),e,a.setFrom(n))}createAndFill(t=null,e,n){if(t=this.computeAttrs(t),(e=i.from(e)).size){let t=this.contentMatch.fillBefore(e);if(!t)return null;e=t.append(e)}let r=this.contentMatch.matchFragment(e),o=r&&r.fillBefore(i.empty,!0);return o?new C(this,t,e.append(o),a.setFrom(n)):null}validContent(t){let e=this.contentMatch.matchFragment(t);if(!e||!e.validEnd)return!1;for(let e=0;e<t.childCount;e++)if(!this.allowsMarks(t.child(e).marks))return!1;return!0}checkContent(t){if(!this.validContent(t))throw RangeError(`Invalid content for node ${this.name}: ${t.toString().slice(0,50)}`)}checkAttrs(t){L(this.attrs,t,"node",this.name)}allowsMarkType(t){return null==this.markSet||this.markSet.indexOf(t)>-1}allowsMarks(t){if(null==this.markSet)return!0;for(let e=0;e<t.length;e++)if(!this.allowsMarkType(t[e].type))return!1;return!0}allowedMarks(t){let e;if(null==this.markSet)return t;for(let n=0;n<t.length;n++)this.allowsMarkType(t[n].type)?e&&e.push(t[n]):e||(e=t.slice(0,n));return e?e.length?e:a.none:t}static compile(t,e){let n=Object.create(null);t.forEach((t,r)=>n[t]=new $(t,e,r));let r=e.spec.topNode||"doc";if(!n[r])throw RangeError("Schema is missing its top node type ('"+r+"')");if(!n.text)throw RangeError("Every schema needs a 'text' type");for(let t in n.text.attrs)throw RangeError("The text node type should not have attributes");return n}}class F{constructor(t,e,n){this.hasDefault=Object.prototype.hasOwnProperty.call(n,"default"),this.default=n.default,this.validate="string"==typeof n.validate?function(t,e,n){let r=n.split("|");return n=>{let i=null===n?"null":typeof n;if(0>r.indexOf(i))throw RangeError(`Expected value of type ${r} for attribute ${e} on type ${t}, got ${i}`)}}(t,e,n.validate):n.validate}get isRequired(){return!this.hasDefault}}class D{constructor(t,e,n,r){this.name=t,this.rank=e,this.schema=n,this.spec=r,this.attrs=z(t,r.attrs),this.excluded=null;let i=N(this.attrs);this.instance=i?new a(this,i):null}create(t=null){return!t&&this.instance?this.instance:new a(this,I(this.attrs,t))}static compile(t,e){let n=Object.create(null),r=0;return t.forEach((t,i)=>n[t]=new D(t,r++,e,i)),n}removeFromSet(t){for(var e=0;e<t.length;e++)t[e].type==this&&(t=t.slice(0,e).concat(t.slice(e+1)),e--);return t}isInSet(t){for(let e=0;e<t.length;e++)if(t[e].type==this)return t[e]}checkAttrs(t){L(this.attrs,t,"mark",this.name)}excludes(t){return this.excluded.indexOf(t)>-1}}class H{constructor(t){this.linebreakReplacement=null,this.cached=Object.create(null);let e=this.spec={};for(let n in t)e[n]=t[n];e.nodes=r.from(t.nodes),e.marks=r.from(t.marks||{}),this.nodes=$.compile(this.spec.nodes,this),this.marks=D.compile(this.spec.marks,this);let n=Object.create(null);for(let t in this.nodes){if(t in this.marks)throw RangeError(t+" can not be both a node and a mark");let e=this.nodes[t],r=e.spec.content||"",i=e.spec.marks;if(e.contentMatch=n[r]||(n[r]=A.parse(r,this.nodes)),e.inlineContent=e.contentMatch.inlineContent,e.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!e.isInline||!e.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=e}e.markSet="_"==i?null:i?P(this,i.split(" ")):""!=i&&e.inlineContent?null:[]}for(let t in this.marks){let e=this.marks[t],n=e.spec.excludes;e.excluded=null==n?[e]:""==n?[]:P(this,n.split(" "))}this.nodeFromJSON=this.nodeFromJSON.bind(this),this.markFromJSON=this.markFromJSON.bind(this),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(t,e=null,n,r){if("string"==typeof t)t=this.nodeType(t);else if(t instanceof $){if(t.schema!=this)throw RangeError("Node type from different schema used ("+t.name+")")}else throw RangeError("Invalid node type: "+t);return t.createChecked(e,n,r)}text(t,e){let n=this.nodes.text;return new x(n,n.defaultAttrs,t,a.setFrom(e))}mark(t,e){return"string"==typeof t&&(t=this.marks[t]),t.create(e)}nodeFromJSON(t){return C.fromJSON(this,t)}markFromJSON(t){return a.fromJSON(this,t)}nodeType(t){let e=this.nodes[t];if(!e)throw RangeError("Unknown node type: "+t);return e}}function P(t,e){let n=[];for(let r=0;r<e.length;r++){let i=e[r],o=t.marks[i],s=o;if(o)n.push(o);else for(let e in t.marks){let r=t.marks[e];("_"==i||r.spec.group&&r.spec.group.split(" ").indexOf(i)>-1)&&n.push(s=r)}if(!s)throw SyntaxError("Unknown mark type: '"+e[r]+"'")}return n}class B{constructor(t,e){this.schema=t,this.rules=e,this.tags=[],this.styles=[];let n=this.matchedStyles=[];e.forEach(t=>{if(null!=t.tag)this.tags.push(t);else if(null!=t.style){let e=/[^=]*/.exec(t.style)[0];0>n.indexOf(e)&&n.push(e),this.styles.push(t)}}),this.normalizeLists=!this.tags.some(e=>{if(!/^(ul|ol)\b/.test(e.tag)||!e.node)return!1;let n=t.nodes[e.node];return n.contentMatch.matchType(n)})}parse(t,e={}){let n=new V(this,e,!1);return n.addAll(t,a.none,e.from,e.to),n.finish()}parseSlice(t,e={}){let n=new V(this,e,!0);return n.addAll(t,a.none,e.from,e.to),c.maxOpen(n.finish())}matchTag(t,e,n){for(let o=n?this.tags.indexOf(n)+1:0;o<this.tags.length;o++){var r,i;let n=this.tags[o];if(r=t,i=n.tag,(r.matches||r.msMatchesSelector||r.webkitMatchesSelector||r.mozMatchesSelector).call(r,i)&&(void 0===n.namespace||t.namespaceURI==n.namespace)&&(!n.context||e.matchesContext(n.context))){if(n.getAttrs){let e=n.getAttrs(t);if(!1===e)continue;n.attrs=e||void 0}return n}}}matchStyle(t,e,n,r){for(let i=r?this.styles.indexOf(r)+1:0;i<this.styles.length;i++){let r=this.styles[i],o=r.style;if(0==o.indexOf(t)&&(!r.context||n.matchesContext(r.context))&&(!(o.length>t.length)||61==o.charCodeAt(t.length)&&o.slice(t.length+1)==e)){if(r.getAttrs){let t=r.getAttrs(e);if(!1===t)continue;r.attrs=t||void 0}return r}}}static schemaRules(t){let e=[];function n(t){let n=null==t.priority?50:t.priority,r=0;for(;r<e.length;r++){let t=e[r];if((null==t.priority?50:t.priority)<n)break}e.splice(r,0,t)}for(let e in t.marks){let r=t.marks[e].spec.parseDOM;r&&r.forEach(t=>{n(t=U(t)),t.mark||t.ignore||t.clearMark||(t.mark=e)})}for(let e in t.nodes){let r=t.nodes[e].spec.parseDOM;r&&r.forEach(t=>{n(t=U(t)),t.node||t.ignore||t.mark||(t.node=e)})}return e}static fromSchema(t){return t.cached.domParser||(t.cached.domParser=new B(t,B.schemaRules(t)))}}let J={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},j={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},K={ol:!0,ul:!0};function _(t,e,n){return null!=e?!!e|2*("full"===e):t&&"pre"==t.whitespace?3:-5&n}class W{constructor(t,e,n,r,i,o){this.type=t,this.attrs=e,this.marks=n,this.solid=r,this.options=o,this.content=[],this.activeMarks=a.none,this.match=i||(4&o?null:t.contentMatch)}findWrapping(t){if(!this.match){if(!this.type)return[];let e=this.type.contentMatch.fillBefore(i.from(t));if(e)this.match=this.type.contentMatch.matchFragment(e);else{let e=this.type.contentMatch,n;return(n=e.findWrapping(t.type))?(this.match=e,n):null}}return this.match.findWrapping(t.type)}finish(t){if(!(1&this.options)){let t=this.content[this.content.length-1],e;t&&t.isText&&(e=/[ \t\r\n\u000c]+$/.exec(t.text))&&(t.text.length==e[0].length?this.content.pop():this.content[this.content.length-1]=t.withText(t.text.slice(0,t.text.length-e[0].length)))}let e=i.from(this.content);return!t&&this.match&&(e=e.append(this.match.fillBefore(i.empty,!0))),this.type?this.type.create(this.attrs,e,this.marks):e}inlineContext(t){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:t.parentNode&&!J.hasOwnProperty(t.parentNode.nodeName.toLowerCase())}}class V{constructor(t,e,n){this.parser=t,this.options=e,this.isOpen=n,this.open=0,this.localPreserveWS=!1;let r=e.topNode,i,o=_(null,e.preserveWhitespace,0)|4*!!n;i=r?new W(r.type,r.attrs,a.none,!0,e.topMatch||r.type.contentMatch,o):n?new W(null,null,a.none,!0,null,o):new W(t.schema.topNodeType,null,a.none,!0,null,o),this.nodes=[i],this.find=e.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(t,e){3==t.nodeType?this.addTextNode(t,e):1==t.nodeType&&this.addElement(t,e)}addTextNode(t,e){let n=t.nodeValue,r=this.top,i=2&r.options?"full":this.localPreserveWS||(1&r.options)>0;if("full"===i||r.inlineContext(t)||/[^ \t\r\n\u000c]/.test(n)){if(i)n="full"!==i?n.replace(/\r?\n|\r/g," "):n.replace(/\r\n?/g,"\n");else if(n=n.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(n)&&this.open==this.nodes.length-1){let e=r.content[r.content.length-1],i=t.previousSibling;(!e||i&&"BR"==i.nodeName||e.isText&&/[ \t\r\n\u000c]$/.test(e.text))&&(n=n.slice(1))}n&&this.insertNode(this.parser.schema.text(n),e,!/\S/.test(n)),this.findInText(t)}else this.findInside(t)}addElement(t,e,n){let r=this.localPreserveWS,i=this.top;("PRE"==t.tagName||/pre/.test(t.style&&t.style.whiteSpace))&&(this.localPreserveWS=!0);let o=t.nodeName.toLowerCase(),s;K.hasOwnProperty(o)&&this.parser.normalizeLists&&function(t){for(let e=t.firstChild,n=null;e;e=e.nextSibling){let t=1==e.nodeType?e.nodeName.toLowerCase():null;t&&K.hasOwnProperty(t)&&n?(n.appendChild(e),e=n):"li"==t?n=e:t&&(n=null)}}(t);let l=this.options.ruleFromNode&&this.options.ruleFromNode(t)||(s=this.parser.matchTag(t,this,n));e:if(l?l.ignore:j.hasOwnProperty(o))this.findInside(t),this.ignoreFallback(t,e);else if(!l||l.skip||l.closeParent){l&&l.closeParent?this.open=Math.max(0,this.open-1):l&&l.skip.nodeType&&(t=l.skip);let n,r=this.needsBlock;if(J.hasOwnProperty(o))i.content.length&&i.content[0].isInline&&this.open&&(this.open--,i=this.top),n=!0,i.type||(this.needsBlock=!0);else if(!t.firstChild){this.leafFallback(t,e);break e}let s=l&&l.skip?e:this.readStyles(t,e);s&&this.addAll(t,s),n&&this.sync(i),this.needsBlock=r}else{let n=this.readStyles(t,e);n&&this.addElementByRule(t,l,n,!1===l.consuming?s:void 0)}this.localPreserveWS=r}leafFallback(t,e){"BR"==t.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(t.ownerDocument.createTextNode("\n"),e)}ignoreFallback(t,e){"BR"!=t.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),e,!0)}readStyles(t,e){let n=t.style;if(n&&n.length)for(let t=0;t<this.parser.matchedStyles.length;t++){let r=this.parser.matchedStyles[t],i=n.getPropertyValue(r);if(i)for(let t;;){let n=this.parser.matchStyle(r,i,this,t);if(!n)break;if(n.ignore)return null;if(e=n.clearMark?e.filter(t=>!n.clearMark(t)):e.concat(this.parser.schema.marks[n.mark].create(n.attrs)),!1===n.consuming)t=n;else break}}return e}addElementByRule(t,e,n,r){let i,o;if(e.node)if((o=this.parser.schema.nodes[e.node]).isLeaf)this.insertNode(o.create(e.attrs),n,"BR"==t.nodeName)||this.leafFallback(t,n);else{let t=this.enter(o,e.attrs||null,n,e.preserveWhitespace);t&&(i=!0,n=t)}else{let t=this.parser.schema.marks[e.mark];n=n.concat(t.create(e.attrs))}let s=this.top;if(o&&o.isLeaf)this.findInside(t);else if(r)this.addElement(t,n,r);else if(e.getContent)this.findInside(t),e.getContent(t,this.parser.schema).forEach(t=>this.insertNode(t,n,!1));else{let r=t;"string"==typeof e.contentElement?r=t.querySelector(e.contentElement):"function"==typeof e.contentElement?r=e.contentElement(t):e.contentElement&&(r=e.contentElement),this.findAround(t,r,!0),this.addAll(r,n),this.findAround(t,r,!1)}i&&this.sync(s)&&this.open--}addAll(t,e,n,r){let i=n||0;for(let o=n?t.childNodes[n]:t.firstChild,s=null==r?null:t.childNodes[r];o!=s;o=o.nextSibling,++i)this.findAtPoint(t,i),this.addDOM(o,e);this.findAtPoint(t,i)}findPlace(t,e,n){let r,i;for(let e=this.open,o=0;e>=0;e--){let s=this.nodes[e],l=s.findWrapping(t);if(l&&(!r||r.length>l.length+o)&&(r=l,i=s,!l.length))break;if(s.solid){if(n)break;o+=2}}if(!r)return null;this.sync(i);for(let t=0;t<r.length;t++)e=this.enterInner(r[t],null,e,!1);return e}insertNode(t,e,n){if(t.isInline&&this.needsBlock&&!this.top.type){let t=this.textblockFromContext();t&&(e=this.enterInner(t,null,e))}let r=this.findPlace(t,e,n);if(r){this.closeExtra();let e=this.top;e.match&&(e.match=e.match.matchType(t.type));let n=a.none;for(let i of r.concat(t.marks))(e.type?e.type.allowsMarkType(i.type):q(i.type,t.type))&&(n=i.addToSet(n));return e.content.push(t.mark(n)),!0}return!1}enter(t,e,n,r){let i=this.findPlace(t.create(e),n,!1);return i&&(i=this.enterInner(t,e,n,!0,r)),i}enterInner(t,e,n,r=!1,i){this.closeExtra();let o=this.top;o.match=o.match&&o.match.matchType(t);let s=_(t,i,o.options);4&o.options&&0==o.content.length&&(s|=4);let l=a.none;return n=n.filter(e=>(o.type?!o.type.allowsMarkType(e.type):!q(e.type,t))||(l=e.addToSet(l),!1)),this.nodes.push(new W(t,e,l,r,null,s)),this.open++,n}closeExtra(t=!1){let e=this.nodes.length-1;if(e>this.open){for(;e>this.open;e--)this.nodes[e-1].content.push(this.nodes[e].finish(t));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(t){for(let e=this.open;e>=0;e--)if(this.nodes[e]==t)return this.open=e,!0;else this.localPreserveWS&&(this.nodes[e].options|=1);return!1}get currentPos(){this.closeExtra();let t=0;for(let e=this.open;e>=0;e--){let n=this.nodes[e].content;for(let e=n.length-1;e>=0;e--)t+=n[e].nodeSize;e&&t++}return t}findAtPoint(t,e){if(this.find)for(let n=0;n<this.find.length;n++)this.find[n].node==t&&this.find[n].offset==e&&(this.find[n].pos=this.currentPos)}findInside(t){if(this.find)for(let e=0;e<this.find.length;e++)null==this.find[e].pos&&1==t.nodeType&&t.contains(this.find[e].node)&&(this.find[e].pos=this.currentPos)}findAround(t,e,n){if(t!=e&&this.find)for(let r=0;r<this.find.length;r++)null==this.find[r].pos&&1==t.nodeType&&t.contains(this.find[r].node)&&e.compareDocumentPosition(this.find[r].node)&(n?2:4)&&(this.find[r].pos=this.currentPos)}findInText(t){if(this.find)for(let e=0;e<this.find.length;e++)this.find[e].node==t&&(this.find[e].pos=this.currentPos-(t.nodeValue.length-this.find[e].offset))}matchesContext(t){if(t.indexOf("|")>-1)return t.split(/\s*\|\s*/).some(this.matchesContext,this);let e=t.split("/"),n=this.options.context,r=!this.isOpen&&(!n||n.parent.type==this.nodes[0].type),i=-(n?n.depth+1:0)+ +!r,o=(t,s)=>{for(;t>=0;t--){let l=e[t];if(""==l){if(t==e.length-1||0==t)continue;for(;s>=i;s--)if(o(t-1,s))return!0;return!1}{let t=s>0||0==s&&r?this.nodes[s].type:n&&s>=i?n.node(s-i).type:null;if(!t||t.name!=l&&!t.isInGroup(l))return!1;s--}}return!0};return o(e.length-1,this.open)}textblockFromContext(){let t=this.options.context;if(t)for(let e=t.depth;e>=0;e--){let n=t.node(e).contentMatchAt(t.indexAfter(e)).defaultType;if(n&&n.isTextblock&&n.defaultAttrs)return n}for(let t in this.parser.schema.nodes){let e=this.parser.schema.nodes[t];if(e.isTextblock&&e.defaultAttrs)return e}}}function U(t){let e={};for(let n in t)e[n]=t[n];return e}function q(t,e){let n=e.schema.nodes;for(let r in n){let i=n[r];if(!i.allowsMarkType(t))continue;let o=[],s=t=>{o.push(t);for(let n=0;n<t.edgeCount;n++){let{type:r,next:i}=t.edge(n);if(r==e||0>o.indexOf(i)&&s(i))return!0}};if(s(i.contentMatch))return!0}}class G{constructor(t,e){this.nodes=t,this.marks=e}serializeFragment(t,e={},n){n||(n=X(e).createDocumentFragment());let r=n,i=[];return t.forEach(t=>{if(i.length||t.marks.length){let n=0,o=0;for(;n<i.length&&o<t.marks.length;){let e=t.marks[o];if(!this.marks[e.type.name]){o++;continue}if(!e.eq(i[n][0])||!1===e.type.spec.spanning)break;n++,o++}for(;n<i.length;)r=i.pop()[1];for(;o<t.marks.length;){let n=t.marks[o++],s=this.serializeMark(n,t.isInline,e);s&&(i.push([n,r]),r.appendChild(s.dom),r=s.contentDOM||s.dom)}}r.appendChild(this.serializeNodeInner(t,e))}),n}serializeNodeInner(t,e){let{dom:n,contentDOM:r}=Q(X(e),this.nodes[t.type.name](t),null,t.attrs);if(r){if(t.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(t.content,e,r)}return n}serializeNode(t,e={}){let n=this.serializeNodeInner(t,e);for(let r=t.marks.length-1;r>=0;r--){let i=this.serializeMark(t.marks[r],t.isInline,e);i&&((i.contentDOM||i.dom).appendChild(n),n=i.dom)}return n}serializeMark(t,e,n={}){let r=this.marks[t.type.name];return r&&Q(X(n),r(t,e),null,t.attrs)}static renderSpec(t,e,n=null,r){return Q(t,e,n,r)}static fromSchema(t){return t.cached.domSerializer||(t.cached.domSerializer=new G(this.nodesFromSchema(t),this.marksFromSchema(t)))}static nodesFromSchema(t){let e=Y(t.nodes);return e.text||(e.text=t=>t.text),e}static marksFromSchema(t){return Y(t.marks)}}function Y(t){let e={};for(let n in t){let r=t[n].spec.toDOM;r&&(e[n]=r)}return e}function X(t){return t.document||window.document}let Z=new WeakMap;function Q(t,e,n,r){let i,o,s;if("string"==typeof e)return{dom:t.createTextNode(e)};if(null!=e.nodeType)return{dom:e};if(e.dom&&null!=e.dom.nodeType)return e;let l=e[0],a;if("string"!=typeof l)throw RangeError("Invalid array passed to renderSpec");if(r&&(void 0===(o=Z.get(r))&&Z.set(r,(s=null,!function t(e){if(e&&"object"==typeof e)if(Array.isArray(e))if("string"==typeof e[0])s||(s=[]),s.push(e);else for(let n=0;n<e.length;n++)t(e[n]);else for(let n in e)t(e[n])}(r),o=s)),a=o)&&a.indexOf(e)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let h=l.indexOf(" ");h>0&&(n=l.slice(0,h),l=l.slice(h+1));let c=n?t.createElementNS(n,l):t.createElement(l),p=e[1],d=1;if(p&&"object"==typeof p&&null==p.nodeType&&!Array.isArray(p)){for(let t in d=2,p)if(null!=p[t]){let e=t.indexOf(" ");e>0?c.setAttributeNS(t.slice(0,e),t.slice(e+1),p[t]):c.setAttribute(t,p[t])}}for(let o=d;o<e.length;o++){let s=e[o];if(0===s){if(o<e.length-1||o>d)throw RangeError("Content hole must be the only child of its parent node");return{dom:c,contentDOM:c}}{let{dom:e,contentDOM:o}=Q(t,s,n,r);if(c.appendChild(e),o){if(i)throw RangeError("Multiple content holes");i=o}}}return{dom:c,contentDOM:i}}},15968:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},19144:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},22705:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Heading2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]])},28440:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Heading1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]])},32643:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]])},33083:(t,e,n)=>{n.d(e,{A:()=>o});var r=n(4701),i=n(52571);let o=r.YY.create({name:"characterCount",addOptions:()=>({limit:null,mode:"textSize",textCounter:t=>t.length,wordCounter:t=>t.split(" ").filter(t=>""!==t).length}),addStorage:()=>({characters:()=>0,words:()=>0}),onBeforeCreate(){this.storage.characters=t=>{let e=(null==t?void 0:t.node)||this.editor.state.doc;if("textSize"===((null==t?void 0:t.mode)||this.options.mode)){let t=e.textBetween(0,e.content.size,void 0," ");return this.options.textCounter(t)}return e.nodeSize},this.storage.words=t=>{let e=(null==t?void 0:t.node)||this.editor.state.doc,n=e.textBetween(0,e.content.size," "," ");return this.options.wordCounter(n)}},addProseMirrorPlugins(){let t=!1;return[new i.k_({key:new i.hs("characterCount"),appendTransaction:(e,n,r)=>{if(t)return;let i=this.options.limit;if(null==i||0===i){t=!0;return}let o=this.storage.characters({node:r.doc});if(o>i){console.warn(`[CharacterCount] Initial content exceeded limit of ${i} characters. Content was automatically trimmed.`);let e=r.tr.deleteRange(0,o-i);return t=!0,e}t=!0},filterTransaction:(t,e)=>{let n=this.options.limit;if(!t.docChanged||0===n||null==n)return!0;let r=this.storage.characters({node:e.doc}),i=this.storage.characters({node:t.doc});if(i<=n||r>n&&i>n&&i<=r)return!0;if(r>n&&i>n&&i>r||!t.getMeta("paste"))return!1;let o=t.selection.$head.pos;return t.deleteRange(o-(i-n),o),!(this.storage.characters({node:t.doc})>n)}})]}})},33786:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]])},34869:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},36761:(t,e,n)=>{n.d(e,{Ay:()=>tV});var r=n(4701);let i=(t,e)=>{for(let n in e)t[n]=e[n];return t},o="numeric",s="ascii",l="alpha",a="asciinumeric",h="alphanumeric",c="domain",p="emoji",d="whitespace";function u(t,e,n){for(let r in e[o]&&(e[a]=!0,e[h]=!0),e[s]&&(e[a]=!0,e[l]=!0),e[a]&&(e[h]=!0),e[l]&&(e[h]=!0),e[h]&&(e[c]=!0),e[p]&&(e[c]=!0),e){let e=(r in n||(n[r]=[]),n[r]);0>e.indexOf(t)&&e.push(t)}}function f(t=null){this.j={},this.jr=[],this.jd=null,this.t=t}f.groups={},f.prototype={accepts(){return!!this.t},go(t){let e=this.j[t];if(e)return e;for(let e=0;e<this.jr.length;e++){let n=this.jr[e][0],r=this.jr[e][1];if(r&&n.test(t))return r}return this.jd},has(t,e=!1){return e?t in this.j:!!this.go(t)},ta(t,e,n,r){for(let i=0;i<t.length;i++)this.tt(t[i],e,n,r)},tr(t,e,n,r){let i;return r=r||f.groups,e&&e.j?i=e:(i=new f(e),n&&r&&u(e,n,r)),this.jr.push([t,i]),i},ts(t,e,n,r){let i=this,o=t.length;if(!o)return i;for(let e=0;e<o-1;e++)i=i.tt(t[e]);return i.tt(t[o-1],e,n,r)},tt(t,e,n,r){if(r=r||f.groups,e&&e.j)return this.j[t]=e,e;let o,s=this.go(t);return s?(i((o=new f).j,s.j),o.jr.push.apply(o.jr,s.jr),o.jd=s.jd,o.t=s.t):o=new f,e&&(r&&(o.t&&"string"==typeof o.t?u(e,i(function(t,e){let n={};for(let r in e)e[r].indexOf(t)>=0&&(n[r]=!0);return n}(o.t,r),n),r):n&&u(e,n,r)),o.t=e),this.j[t]=o,o}};let m=(t,e,n,r,i)=>t.ta(e,n,r,i),g=(t,e,n,r,i)=>t.tr(e,n,r,i),y=(t,e,n,r,i)=>t.ts(e,n,r,i),w=(t,e,n,r,i)=>t.tt(e,n,r,i),k="WORD",b="UWORD",v="ASCIINUMERICAL",S="ALPHANUMERICAL",C="LOCALHOST",x="UTLD",M="SCHEME",A="SLASH_SCHEME",T="OPENBRACE",E="CLOSEBRACE",R="OPENBRACKET",O="CLOSEBRACKET",N="OPENPAREN",I="CLOSEPAREN",L="OPENANGLEBRACKET",z="CLOSEANGLEBRACKET",$="FULLWIDTHLEFTPAREN",F="FULLWIDTHRIGHTPAREN",D="LEFTCORNERBRACKET",H="RIGHTCORNERBRACKET",P="LEFTWHITECORNERBRACKET",B="RIGHTWHITECORNERBRACKET",J="FULLWIDTHLESSTHAN",j="FULLWIDTHGREATERTHAN",K="AMPERSAND",_="APOSTROPHE",W="ASTERISK",V="BACKSLASH",U="BACKTICK",q="CARET",G="COLON",Y="COMMA",X="DOLLAR",Z="EQUALS",Q="EXCLAMATION",tt="HYPHEN",te="PERCENT",tn="PIPE",tr="PLUS",ti="POUND",to="QUERY",ts="QUOTE",tl="FULLWIDTHMIDDLEDOT",ta="SEMI",th="SLASH",tc="TILDE",tp="UNDERSCORE",td="EMOJI";var tu=Object.freeze({__proto__:null,ALPHANUMERICAL:S,AMPERSAND:K,APOSTROPHE:_,ASCIINUMERICAL:v,ASTERISK:W,AT:"AT",BACKSLASH:V,BACKTICK:U,CARET:q,CLOSEANGLEBRACKET:z,CLOSEBRACE:E,CLOSEBRACKET:O,CLOSEPAREN:I,COLON:G,COMMA:Y,DOLLAR:X,DOT:"DOT",EMOJI:td,EQUALS:Z,EXCLAMATION:Q,FULLWIDTHGREATERTHAN:j,FULLWIDTHLEFTPAREN:$,FULLWIDTHLESSTHAN:J,FULLWIDTHMIDDLEDOT:tl,FULLWIDTHRIGHTPAREN:F,HYPHEN:tt,LEFTCORNERBRACKET:D,LEFTWHITECORNERBRACKET:P,LOCALHOST:C,NL:"NL",NUM:"NUM",OPENANGLEBRACKET:L,OPENBRACE:T,OPENBRACKET:R,OPENPAREN:N,PERCENT:te,PIPE:tn,PLUS:tr,POUND:ti,QUERY:to,QUOTE:ts,RIGHTCORNERBRACKET:H,RIGHTWHITECORNERBRACKET:B,SCHEME:M,SEMI:ta,SLASH:th,SLASH_SCHEME:A,SYM:"SYM",TILDE:tc,TLD:"TLD",UNDERSCORE:tp,UTLD:x,UWORD:b,WORD:k,WS:"WS"});let tf=/[a-z]/,tm=/\p{L}/u,tg=/\p{Emoji}/u,ty=/\d/,tw=/\s/,tk=null,tb=null;function tv(t,e){let n=function(t){let e=[],n=t.length,r=0;for(;r<n;){let i,o=t.charCodeAt(r),s=o<55296||o>56319||r+1===n||(i=t.charCodeAt(r+1))<56320||i>57343?t[r]:t.slice(r,r+2);e.push(s),r+=s.length}return e}(e.replace(/[A-Z]/g,t=>t.toLowerCase())),r=n.length,i=[],o=0,s=0;for(;s<r;){let l=t,a=null,h=0,c=null,p=-1,d=-1;for(;s<r&&(a=l.go(n[s]));)(l=a).accepts()?(p=0,d=0,c=l):p>=0&&(p+=n[s].length,d++),h+=n[s].length,o+=n[s].length,s++;o-=p,s-=d,h-=p,i.push({t:c.t,v:e.slice(o-h,o),s:o-h,e:o})}return i}function tS(t,e,n,r,i){let o,s=e.length;for(let n=0;n<s-1;n++){let s=e[n];t.j[s]?o=t.j[s]:((o=new f(r)).jr=i.slice(),t.j[s]=o),t=o}return(o=new f(n)).jr=i.slice(),t.j[e[s-1]]=o,o}function tC(t){let e=[],n=[],r=0;for(;r<t.length;){let i=0;for(;"0123456789".indexOf(t[r+i])>=0;)i++;if(i>0){e.push(n.join(""));for(let e=parseInt(t.substring(r,r+i),10);e>0;e--)n.pop();r+=i}else n.push(t[r]),r++}return e}let tx={defaultProtocol:"http",events:null,format:tA,formatHref:tA,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function tM(t,e=null){let n=i({},tx);t&&(n=i(n,t instanceof tM?t.o:t));let r=n.ignoreTags,o=[];for(let t=0;t<r.length;t++)o.push(r[t].toUpperCase());this.o=n,e&&(this.defaultRender=e),this.ignoreTags=o}function tA(t){return t}function tT(t,e){this.t="token",this.v=t,this.tk=e}function tE(t,e){class n extends tT{constructor(e,n){super(e,n),this.t=t}}for(let t in e)n.prototype[t]=e[t];return n.t=t,n}tM.prototype={o:tx,ignoreTags:[],defaultRender:t=>t,check(t){return this.get("validate",t.toString(),t)},get(t,e,n){let r=null!=e,i=this.o[t];return i&&("object"==typeof i?"function"==typeof(i=n.t in i?i[n.t]:tx[t])&&r&&(i=i(e,n)):"function"==typeof i&&r&&(i=i(e,n.t,n))),i},getObj(t,e,n){let r=this.o[t];return"function"==typeof r&&null!=e&&(r=r(e,n.t,n)),r},render(t){let e=t.render(this);return(this.get("render",null,t)||this.defaultRender)(e,t.t,t)}},tT.prototype={isLink:!1,toString(){return this.v},toHref(t){return this.toString()},toFormattedString(t){let e=this.toString(),n=t.get("truncate",e,this),r=t.get("format",e,this);return n&&r.length>n?r.substring(0,n)+"…":r},toFormattedHref(t){return t.get("formatHref",this.toHref(t.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(t=tx.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(t),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(t){return{type:this.t,value:this.toFormattedString(t),isLink:this.isLink,href:this.toFormattedHref(t),start:this.startIndex(),end:this.endIndex()}},validate(t){return t.get("validate",this.toString(),this)},render(t){let e=this.toHref(t.get("defaultProtocol")),n=t.get("formatHref",e,this),r=t.get("tagName",e,this),o=this.toFormattedString(t),s={},l=t.get("className",e,this),a=t.get("target",e,this),h=t.get("rel",e,this),c=t.getObj("attributes",e,this),p=t.getObj("events",e,this);return s.href=n,l&&(s.class=l),a&&(s.target=a),h&&(s.rel=h),c&&i(s,c),{tagName:r,attributes:s,content:o,eventListeners:p}}};let tR=tE("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),tO=tE("text"),tN=tE("nl"),tI=tE("url",{isLink:!0,toHref(t=tx.defaultProtocol){return this.hasProtocol()?this.v:`${t}://${this.v}`},hasProtocol(){let t=this.tk;return t.length>=2&&t[0].t!==C&&t[1].t===G}}),tL=t=>new f(t);function tz(t,e,n){let r=n[0].s,i=n[n.length-1].e;return new t(e.slice(r,i),n)}let t$="undefined"!=typeof console&&console&&console.warn||(()=>{}),tF={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function tD(t,e=!1){if(tF.initialized&&t$(`linkifyjs: already initialized - will not register custom scheme "${t}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(t))throw Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);tF.customSchemes.push([t,e])}function tH(t){return tF.initialized||function(){tF.scanner=function(t=[]){let e={};f.groups=e;let n=new f;null==tk&&(tk=tC("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xf6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==tb&&(tb=tC("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),w(n,"'",_),w(n,"{",T),w(n,"}",E),w(n,"[",R),w(n,"]",O),w(n,"(",N),w(n,")",I),w(n,"<",L),w(n,">",z),w(n,"（",$),w(n,"）",F),w(n,"「",D),w(n,"」",H),w(n,"『",P),w(n,"』",B),w(n,"＜",J),w(n,"＞",j),w(n,"&",K),w(n,"*",W),w(n,"@","AT"),w(n,"`",U),w(n,"^",q),w(n,":",G),w(n,",",Y),w(n,"$",X),w(n,".","DOT"),w(n,"=",Z),w(n,"!",Q),w(n,"-",tt),w(n,"%",te),w(n,"|",tn),w(n,"+",tr),w(n,"#",ti),w(n,"?",to),w(n,'"',ts),w(n,"/",th),w(n,";",ta),w(n,"~",tc),w(n,"_",tp),w(n,"\\",V),w(n,"・",tl);let r=g(n,ty,"NUM",{[o]:!0});g(r,ty,r);let m=g(r,tf,v,{[a]:!0}),tv=g(r,tm,S,{[h]:!0}),tx=g(n,tf,k,{[s]:!0});g(tx,ty,m),g(tx,tf,tx),g(m,ty,m),g(m,tf,m);let tM=g(n,tm,b,{[l]:!0});g(tM,tf),g(tM,ty,tv),g(tM,tm,tM),g(tv,ty,tv),g(tv,tf),g(tv,tm,tv);let tA=w(n,"\n","NL",{[d]:!0}),tT=w(n,"\r","WS",{[d]:!0}),tE=g(n,tw,"WS",{[d]:!0});w(n,"￼",tE),w(tT,"\n",tA),w(tT,"￼",tE),g(tT,tw,tE),w(tE,"\r"),w(tE,"\n"),g(tE,tw,tE),w(tE,"￼",tE);let tR=g(n,tg,td,{[p]:!0});w(tR,"#"),g(tR,tg,tR),w(tR,"️",tR);let tO=w(tR,"‍");w(tO,"#"),g(tO,tg,tR);let tN=[[tf,tx],[ty,m]],tI=[[tf,null],[tm,tM],[ty,tv]];for(let t=0;t<tk.length;t++)tS(n,tk[t],"TLD",k,tN);for(let t=0;t<tb.length;t++)tS(n,tb[t],x,b,tI);u("TLD",{tld:!0,ascii:!0},e),u(x,{utld:!0,alpha:!0},e),tS(n,"file",M,k,tN),tS(n,"mailto",M,k,tN),tS(n,"http",A,k,tN),tS(n,"https",A,k,tN),tS(n,"ftp",A,k,tN),tS(n,"ftps",A,k,tN),u(M,{scheme:!0,ascii:!0},e),u(A,{slashscheme:!0,ascii:!0},e),t=t.sort((t,e)=>t[0]>e[0]?1:-1);for(let e=0;e<t.length;e++){let r=t[e][0],i=t[e][1]?{scheme:!0}:{slashscheme:!0};r.indexOf("-")>=0?i[c]=!0:tf.test(r)?ty.test(r)?i[a]=!0:i[s]=!0:i[o]=!0,y(n,r,r,i)}return y(n,"localhost",C,{ascii:!0}),n.jd=new f("SYM"),{start:n,tokens:i({groups:e},tu)}}(tF.customSchemes);for(let t=0;t<tF.tokenQueue.length;t++)tF.tokenQueue[t][1]({scanner:tF.scanner});tF.parser=function({groups:t}){let e=t.domain.concat([K,W,"AT",V,U,q,X,Z,tt,"NUM",te,tn,tr,ti,th,"SYM",tc,tp]),n=[_,G,Y,"DOT",Q,te,to,ts,ta,L,z,T,E,O,R,N,I,$,F,D,H,P,B,J,j],r=[K,_,W,V,U,q,X,Z,tt,T,E,te,tn,tr,ti,to,th,"SYM",tc,tp],i=tL(),o=w(i,tc);m(o,r,o),m(o,t.domain,o);let s=tL(),l=tL(),a=tL();m(i,t.domain,s),m(i,t.scheme,l),m(i,t.slashscheme,a),m(s,r,o),m(s,t.domain,s);let h=w(s,"AT");w(o,"AT",h),w(l,"AT",h),w(a,"AT",h);let c=w(o,"DOT");m(c,r,o),m(c,t.domain,o);let p=tL();m(h,t.domain,p),m(p,t.domain,p);let d=w(p,"DOT");m(d,t.domain,p);let u=tL(tR);m(d,t.tld,u),m(d,t.utld,u),w(h,C,u);let f=w(p,tt);w(f,tt,f),m(f,t.domain,p),m(u,t.domain,p),w(u,"DOT",d),w(u,tt,f),m(w(u,G),t.numeric,tR);let g=w(s,tt),y=w(s,"DOT");w(g,tt,g),m(g,t.domain,s),m(y,r,o),m(y,t.domain,s);let k=tL(tI);m(y,t.tld,k),m(y,t.utld,k),m(k,t.domain,s),m(k,r,o),w(k,"DOT",y),w(k,tt,g),w(k,"AT",h);let b=w(k,G),v=tL(tI);m(b,t.numeric,v);let S=tL(tI),x=tL();m(S,e,S),m(S,n,x),m(x,e,S),m(x,n,x),w(k,th,S),w(v,th,S);let M=w(l,G),A=w(a,G),tl=w(A,th),td=w(tl,th);m(l,t.domain,s),w(l,"DOT",y),w(l,tt,g),m(a,t.domain,s),w(a,"DOT",y),w(a,tt,g),m(M,t.domain,S),w(M,th,S),w(M,to,S),m(td,t.domain,S),m(td,e,S),w(td,th,S);let tf=[[T,E],[R,O],[N,I],[L,z],[$,F],[D,H],[P,B],[J,j]];for(let t=0;t<tf.length;t++){let[r,i]=tf[t],o=w(S,r);w(x,r,o),w(o,i,S);let s=tL(tI);m(o,e,s);let l=tL();m(o,n),m(s,e,s),m(s,n,l),m(l,e,s),m(l,n,l),w(s,i,S),w(l,i,S)}return w(i,C,k),w(i,"NL",tN),{start:i,tokens:tu}}(tF.scanner.tokens);for(let t=0;t<tF.pluginQueue.length;t++)tF.pluginQueue[t][1]({scanner:tF.scanner,parser:tF.parser});tF.initialized=!0}(),function(t,e,n){let r=n.length,i=0,o=[],s=[];for(;i<r;){let l=t,a=null,h=null,c=0,p=null,d=-1;for(;i<r&&!(a=l.go(n[i].t));)s.push(n[i++]);for(;i<r&&(h=a||l.go(n[i].t));)a=null,(l=h).accepts()?(d=0,p=l):d>=0&&d++,i++,c++;if(d<0)(i-=c)<r&&(s.push(n[i]),i++);else{s.length>0&&(o.push(tz(tO,e,s)),s=[]),i-=d,c-=d;let t=p.t,r=n.slice(i-c,i);o.push(tz(t,e,r))}}return s.length>0&&o.push(tz(tO,e,s)),o}(tF.parser.start,t,tv(tF.scanner.start,t))}function tP(t,e=null,n=null){if(e&&"object"==typeof e){if(n)throw Error(`linkifyjs: Invalid link type ${e}; must be a string`);n=e,e=null}let r=new tM(n),i=tH(t),o=[];for(let t=0;t<i.length;t++){let n=i[t];n.isLink&&(!e||n.t===e)&&r.check(n)&&o.push(n.toFormattedObject(r))}return o}tH.scan=tv;var tB=n(52571);let tJ="[\0- \xa0 ᠎ -\u2029 　]",tj=new RegExp(tJ),tK=RegExp(`${tJ}$`),t_=RegExp(tJ,"g");function tW(t,e){let n=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return e&&e.forEach(t=>{let e="string"==typeof t?t:t.scheme;e&&n.push(e)}),!t||t.replace(t_,"").match(RegExp(`^(?:(?:${n.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}let tV=r.CU.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(t=>{if("string"==typeof t)return void tD(t);tD(t.scheme,t.optionalSlashes)})},onDestroy(){f.groups={},tF.scanner=null,tF.parser=null,tF.tokenQueue=[],tF.pluginQueue=[],tF.customSchemes=[],tF.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(t,e)=>!!tW(t,e.protocols),validate:t=>!!t,shouldAutoLink:t=>!!t}),addAttributes(){return{href:{default:null,parseHTML:t=>t.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:t=>{let e=t.getAttribute("href");return!!e&&!!this.options.isAllowedUri(e,{defaultValidate:t=>!!tW(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&null}}]},renderHTML({HTMLAttributes:t}){return this.options.isAllowedUri(t.href,{defaultValidate:t=>!!tW(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",(0,r.KV)(this.options.HTMLAttributes,t),0]:["a",(0,r.KV)(this.options.HTMLAttributes,{...t,href:""}),0]},addCommands(){return{setLink:t=>({chain:e})=>{let{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!tW(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().setMark(this.name,t).setMeta("preventAutolink",!0).run()},toggleLink:t=>({chain:e})=>{let{href:n}=t;return!!this.options.isAllowedUri(n,{defaultValidate:t=>!!tW(t,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&e().toggleMark(this.name,t,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:t})=>t().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[(0,r.Zc)({find:t=>{let e=[];if(t){let{protocols:n,defaultProtocol:r}=this.options,i=tP(t).filter(t=>t.isLink&&this.options.isAllowedUri(t.value,{defaultValidate:t=>!!tW(t,n),protocols:n,defaultProtocol:r}));i.length&&i.forEach(t=>e.push({text:t.value,data:{href:t.href},index:t.start}))}return e},type:this.type,getAttributes:t=>{var e;return{href:null==(e=t.data)?void 0:e.href}}})]},addProseMirrorPlugins(){var t,e,n;let i=[],{protocols:o,defaultProtocol:s}=this.options;return this.options.autolink&&i.push((t={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:t=>this.options.isAllowedUri(t,{defaultValidate:t=>!!tW(t,o),protocols:o,defaultProtocol:s}),shouldAutoLink:this.options.shouldAutoLink},new tB.k_({key:new tB.hs("autolink"),appendTransaction:(e,n,i)=>{let o=e.some(t=>t.docChanged)&&!n.doc.eq(i.doc),s=e.some(t=>t.getMeta("preventAutolink"));if(!o||s)return;let{tr:l}=i,a=(0,r.T7)(n.doc,[...e]);if((0,r.FF)(a).forEach(({newRange:e})=>{let n,o,s=(0,r.Nx)(i.doc,e,t=>t.isTextblock);if(s.length>1)n=s[0],o=i.doc.textBetween(n.pos,n.pos+n.node.nodeSize,void 0," ");else if(s.length){let t=i.doc.textBetween(e.from,e.to," "," ");if(!tK.test(t))return;n=s[0],o=i.doc.textBetween(n.pos,e.to,void 0," ")}if(n&&o){let e=o.split(tj).filter(Boolean);if(e.length<=0)return!1;let s=e[e.length-1],a=n.pos+o.lastIndexOf(s);if(!s)return!1;let h=tH(s).map(e=>e.toObject(t.defaultProtocol));if(!(1===h.length?h[0].isLink:3===h.length&&!!h[1].isLink&&["()","[]"].includes(h[0].value+h[2].value)))return!1;h.filter(t=>t.isLink).map(t=>({...t,from:a+t.start+1,to:a+t.end+1})).filter(t=>!i.schema.marks.code||!i.doc.rangeHasMark(t.from,t.to,i.schema.marks.code)).filter(e=>t.validate(e.value)).filter(e=>t.shouldAutoLink(e.value)).forEach(e=>{(0,r.hO)(e.from,e.to,i.doc).some(e=>e.mark.type===t.type)||l.addMark(e.from,e.to,t.type.create({href:e.href}))})}}),l.steps.length)return l}}))),!0===this.options.openOnClick&&i.push((e={type:this.type},new tB.k_({key:new tB.hs("handleClickLink"),props:{handleClick:(t,n,i)=>{var o,s;if(0!==i.button||!t.editable)return!1;let l=i.target,a=[];for(;"DIV"!==l.nodeName;)a.push(l),l=l.parentNode;if(!a.find(t=>"A"===t.nodeName))return!1;let h=(0,r.gu)(t.state,e.type.name),c=i.target,p=null!=(o=null==c?void 0:c.href)?o:h.href,d=null!=(s=null==c?void 0:c.target)?s:h.target;return!!c&&!!p&&(window.open(p,d),!0)}}}))),this.options.linkOnPaste&&i.push((n={editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type},new tB.k_({key:new tB.hs("handlePasteLink"),props:{handlePaste:(t,e,r)=>{let{state:i}=t,{selection:o}=i,{empty:s}=o;if(s)return!1;let l="";r.content.forEach(t=>{l+=t.textContent});let a=tP(l,{defaultProtocol:n.defaultProtocol}).find(t=>t.isLink&&t.value===l);return!!l&&!!a&&n.editor.commands.setMark(n.type,{href:a.href})}}}))),i}})},38164:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},40224:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Quote",[["path",{d:"M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z",key:"4rm80e"}],["path",{d:"M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z",key:"10za9r"}]])},43557:(t,e,n)=>{n.d(e,{A:()=>i});var r=n(4701);let i=r.bP.create({name:"tableCell",addOptions:()=>({HTMLAttributes:{}}),content:"block+",addAttributes:()=>({colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:t=>{let e=t.getAttribute("colwidth");return e?e.split(",").map(t=>parseInt(t,10)):null}}}),tableRole:"cell",isolating:!0,parseHTML:()=>[{tag:"td"}],renderHTML({HTMLAttributes:t}){return["td",(0,r.KV)(this.options.HTMLAttributes,t),0]}})},48932:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},52571:(t,e,n)=>{n.d(e,{$t:()=>C,LN:()=>s,U3:()=>c,hs:()=>T,i5:()=>f,k_:()=>x,nh:()=>d,yn:()=>l});var r=n(10156),i=n(808);let o=Object.create(null);class s{constructor(t,e,n){this.$anchor=t,this.$head=e,this.ranges=n||[new l(t.min(e),t.max(e))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let t=this.ranges;for(let e=0;e<t.length;e++)if(t[e].$from.pos!=t[e].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(t,e=r.Ji.empty){let n=e.content.lastChild,i=null;for(let t=0;t<e.openEnd;t++)i=n,n=n.lastChild;let o=t.steps.length,s=this.ranges;for(let l=0;l<s.length;l++){let{$from:a,$to:h}=s[l],c=t.mapping.slice(o);t.replaceRange(c.map(a.pos),c.map(h.pos),l?r.Ji.empty:e),0==l&&y(t,o,(n?n.isInline:i&&i.isTextblock)?-1:1)}}replaceWith(t,e){let n=t.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:o,$to:s}=r[i],l=t.mapping.slice(n),a=l.map(o.pos),h=l.map(s.pos);i?t.deleteRange(a,h):(t.replaceRangeWith(a,h,e),y(t,n,e.isInline?-1:1))}}static findFrom(t,e,n=!1){let r=t.parent.inlineContent?new c(t):g(t.node(0),t.parent,t.pos,t.index(),e,n);if(r)return r;for(let r=t.depth-1;r>=0;r--){let i=e<0?g(t.node(0),t.node(r),t.before(r+1),t.index(r),e,n):g(t.node(0),t.node(r),t.after(r+1),t.index(r)+1,e,n);if(i)return i}return null}static near(t,e=1){return this.findFrom(t,e)||this.findFrom(t,-e)||new f(t.node(0))}static atStart(t){return g(t,t,0,0,1)||new f(t)}static atEnd(t){return g(t,t,t.content.size,t.childCount,-1)||new f(t)}static fromJSON(t,e){if(!e||!e.type)throw RangeError("Invalid input for Selection.fromJSON");let n=o[e.type];if(!n)throw RangeError(`No selection type ${e.type} defined`);return n.fromJSON(t,e)}static jsonID(t,e){if(t in o)throw RangeError("Duplicate use of selection JSON ID "+t);return o[t]=e,e.prototype.jsonID=t,e}getBookmark(){return c.between(this.$anchor,this.$head).getBookmark()}}s.prototype.visible=!0;class l{constructor(t,e){this.$from=t,this.$to=e}}let a=!1;function h(t){a||t.parent.inlineContent||(a=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+t.parent.type.name+")"))}class c extends s{constructor(t,e=t){h(t),h(e),super(t,e)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(t,e){let n=t.resolve(e.map(this.head));if(!n.parent.inlineContent)return s.near(n);let r=t.resolve(e.map(this.anchor));return new c(r.parent.inlineContent?r:n,n)}replace(t,e=r.Ji.empty){if(super.replace(t,e),e==r.Ji.empty){let e=this.$from.marksAcross(this.$to);e&&t.ensureMarks(e)}}eq(t){return t instanceof c&&t.anchor==this.anchor&&t.head==this.head}getBookmark(){return new p(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(t,e){if("number"!=typeof e.anchor||"number"!=typeof e.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new c(t.resolve(e.anchor),t.resolve(e.head))}static create(t,e,n=e){let r=t.resolve(e);return new this(r,n==e?r:t.resolve(n))}static between(t,e,n){let r=t.pos-e.pos;if((!n||r)&&(n=r>=0?1:-1),!e.parent.inlineContent){let t=s.findFrom(e,n,!0)||s.findFrom(e,-n,!0);if(!t)return s.near(e,n);e=t.$head}return t.parent.inlineContent||(0==r?t=e:(t=(s.findFrom(t,-n,!0)||s.findFrom(t,n,!0)).$anchor).pos<e.pos!=r<0&&(t=e)),new c(t,e)}}s.jsonID("text",c);class p{constructor(t,e){this.anchor=t,this.head=e}map(t){return new p(t.map(this.anchor),t.map(this.head))}resolve(t){return c.between(t.resolve(this.anchor),t.resolve(this.head))}}class d extends s{constructor(t){let e=t.nodeAfter;super(t,t.node(0).resolve(t.pos+e.nodeSize)),this.node=e}map(t,e){let{deleted:n,pos:r}=e.mapResult(this.anchor),i=t.resolve(r);return n?s.near(i):new d(i)}content(){return new r.Ji(r.FK.from(this.node),0,0)}eq(t){return t instanceof d&&t.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new u(this.anchor)}static fromJSON(t,e){if("number"!=typeof e.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new d(t.resolve(e.anchor))}static create(t,e){return new d(t.resolve(e))}static isSelectable(t){return!t.isText&&!1!==t.type.spec.selectable}}d.prototype.visible=!1,s.jsonID("node",d);class u{constructor(t){this.anchor=t}map(t){let{deleted:e,pos:n}=t.mapResult(this.anchor);return e?new p(n,n):new u(n)}resolve(t){let e=t.resolve(this.anchor),n=e.nodeAfter;return n&&d.isSelectable(n)?new d(e):s.near(e)}}class f extends s{constructor(t){super(t.resolve(0),t.resolve(t.content.size))}replace(t,e=r.Ji.empty){if(e==r.Ji.empty){t.delete(0,t.doc.content.size);let e=s.atStart(t.doc);e.eq(t.selection)||t.setSelection(e)}else super.replace(t,e)}toJSON(){return{type:"all"}}static fromJSON(t){return new f(t)}map(t){return new f(t)}eq(t){return t instanceof f}getBookmark(){return m}}s.jsonID("all",f);let m={map(){return this},resolve:t=>new f(t)};function g(t,e,n,r,i,o=!1){if(e.inlineContent)return c.create(t,n);for(let s=r-(i>0?0:1);i>0?s<e.childCount:s>=0;s+=i){let r=e.child(s);if(r.isAtom){if(!o&&d.isSelectable(r))return d.create(t,n-(i<0?r.nodeSize:0))}else{let e=g(t,r,n+i,i<0?r.childCount:0,i,o);if(e)return e}n+=r.nodeSize*i}return null}function y(t,e,n){let r,o=t.steps.length-1;if(o<e)return;let l=t.steps[o];(l instanceof i.Ln||l instanceof i.Wg)&&(t.mapping.maps[o].forEach((t,e,n,i)=>{null==r&&(r=i)}),t.setSelection(s.near(t.doc.resolve(r),n)))}class w extends i.dL{constructor(t){super(t.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=t.selection,this.storedMarks=t.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(t){if(t.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=t,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(t){return this.storedMarks=t,this.updated|=2,this}ensureMarks(t){return r.CU.sameSet(this.storedMarks||this.selection.$from.marks(),t)||this.setStoredMarks(t),this}addStoredMark(t){return this.ensureMarks(t.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(t){return this.ensureMarks(t.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(t,e){super.addStep(t,e),this.updated=-3&this.updated,this.storedMarks=null}setTime(t){return this.time=t,this}replaceSelection(t){return this.selection.replace(this,t),this}replaceSelectionWith(t,e=!0){let n=this.selection;return e&&(t=t.mark(this.storedMarks||(n.empty?n.$from.marks():n.$from.marksAcross(n.$to)||r.CU.none))),n.replaceWith(this,t),this}deleteSelection(){return this.selection.replace(this),this}insertText(t,e,n){let r=this.doc.type.schema;if(null==e)return t?this.replaceSelectionWith(r.text(t),!0):this.deleteSelection();{if(null==n&&(n=e),n=null==n?e:n,!t)return this.deleteRange(e,n);let i=this.storedMarks;if(!i){let t=this.doc.resolve(e);i=n==e?t.marks():t.marksAcross(this.doc.resolve(n))}return this.replaceRangeWith(e,n,r.text(t,i)),this.selection.empty||this.setSelection(s.near(this.selection.$to)),this}}setMeta(t,e){return this.meta["string"==typeof t?t:t.key]=e,this}getMeta(t){return this.meta["string"==typeof t?t:t.key]}get isGeneric(){for(let t in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function k(t,e){return e&&t?t.bind(e):t}class b{constructor(t,e,n){this.name=t,this.init=k(e.init,n),this.apply=k(e.apply,n)}}let v=[new b("doc",{init:t=>t.doc||t.schema.topNodeType.createAndFill(),apply:t=>t.doc}),new b("selection",{init:(t,e)=>t.selection||s.atStart(e.doc),apply:t=>t.selection}),new b("storedMarks",{init:t=>t.storedMarks||null,apply:(t,e,n,r)=>r.selection.$cursor?t.storedMarks:null}),new b("scrollToSelection",{init:()=>0,apply:(t,e)=>t.scrolledIntoView?e+1:e})];class S{constructor(t,e){this.schema=t,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=v.slice(),e&&e.forEach(t=>{if(this.pluginsByKey[t.key])throw RangeError("Adding different instances of a keyed plugin ("+t.key+")");this.plugins.push(t),this.pluginsByKey[t.key]=t,t.spec.state&&this.fields.push(new b(t.key,t.spec.state,t))})}}class C{constructor(t){this.config=t}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(t){return this.applyTransaction(t).state}filterTransaction(t,e=-1){for(let n=0;n<this.config.plugins.length;n++)if(n!=e){let e=this.config.plugins[n];if(e.spec.filterTransaction&&!e.spec.filterTransaction.call(e,t,this))return!1}return!0}applyTransaction(t){if(!this.filterTransaction(t))return{state:this,transactions:[]};let e=[t],n=this.applyInner(t),r=null;for(;;){let i=!1;for(let o=0;o<this.config.plugins.length;o++){let s=this.config.plugins[o];if(s.spec.appendTransaction){let l=r?r[o].n:0,a=r?r[o].state:this,h=l<e.length&&s.spec.appendTransaction.call(s,l?e.slice(l):e,a,n);if(h&&n.filterTransaction(h,o)){if(h.setMeta("appendedTransaction",t),!r){r=[];for(let t=0;t<this.config.plugins.length;t++)r.push(t<o?{state:n,n:e.length}:{state:this,n:0})}e.push(h),n=n.applyInner(h),i=!0}r&&(r[o]={state:n,n:e.length})}}if(!i)return{state:n,transactions:e}}}applyInner(t){if(!t.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let e=new C(this.config),n=this.config.fields;for(let r=0;r<n.length;r++){let i=n[r];e[i.name]=i.apply(t,this[i.name],this,e)}return e}get tr(){return new w(this)}static create(t){let e=new S(t.doc?t.doc.type.schema:t.schema,t.plugins),n=new C(e);for(let r=0;r<e.fields.length;r++)n[e.fields[r].name]=e.fields[r].init(t,n);return n}reconfigure(t){let e=new S(this.schema,t.plugins),n=e.fields,r=new C(e);for(let e=0;e<n.length;e++){let i=n[e].name;r[i]=this.hasOwnProperty(i)?this[i]:n[e].init(t,r)}return r}toJSON(t){let e={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(e.storedMarks=this.storedMarks.map(t=>t.toJSON())),t&&"object"==typeof t)for(let n in t){if("doc"==n||"selection"==n)throw RangeError("The JSON fields `doc` and `selection` are reserved");let r=t[n],i=r.spec.state;i&&i.toJSON&&(e[n]=i.toJSON.call(r,this[r.key]))}return e}static fromJSON(t,e,n){if(!e)throw RangeError("Invalid input for EditorState.fromJSON");if(!t.schema)throw RangeError("Required config field 'schema' missing");let i=new S(t.schema,t.plugins),o=new C(i);return i.fields.forEach(i=>{if("doc"==i.name)o.doc=r.bP.fromJSON(t.schema,e.doc);else if("selection"==i.name)o.selection=s.fromJSON(o.doc,e.selection);else if("storedMarks"==i.name)e.storedMarks&&(o.storedMarks=e.storedMarks.map(t.schema.markFromJSON));else{if(n)for(let r in n){let s=n[r],l=s.spec.state;if(s.key==i.name&&l&&l.fromJSON&&Object.prototype.hasOwnProperty.call(e,r)){o[i.name]=l.fromJSON.call(s,t,e[r],o);return}}o[i.name]=i.init(t,o)}}),o}}class x{constructor(t){this.spec=t,this.props={},t.props&&function t(e,n,r){for(let i in e){let o=e[i];o instanceof Function?o=o.bind(n):"handleDOMEvents"==i&&(o=t(o,n,{})),r[i]=o}return r}(t.props,this,this.props),this.key=t.key?t.key.key:A("plugin")}getState(t){return t[this.key]}}let M=Object.create(null);function A(t){return t in M?t+"$"+ ++M[t]:(M[t]=0,t+"$")}class T{constructor(t="key"){this.key=A(t)}get(t){return t.config.pluginsByKey[this.key]}getState(t){return t[this.key]}}},54416:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},58292:(t,e,n)=>{n.d(e,{A:()=>ty});var r=n(4701);let i=/^\s*>\s$/,o=r.bP.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:t}){return["blockquote",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBlockquote:()=>({commands:t})=>t.wrapIn(this.name),toggleBlockquote:()=>({commands:t})=>t.toggleWrap(this.name),unsetBlockquote:()=>({commands:t})=>t.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,r.tG)({find:i,type:this.type})]}}),s=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,l=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,a=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,h=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,c=r.CU.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:t=>"normal"!==t.style.fontWeight&&null},{style:"font-weight=400",clearMark:t=>t.type.name===this.name},{style:"font-weight",getAttrs:t=>/^(bold(er)?|[5-9]\d{2,})$/.test(t)&&null}]},renderHTML({HTMLAttributes:t}){return["strong",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setBold:()=>({commands:t})=>t.setMark(this.name),toggleBold:()=>({commands:t})=>t.toggleMark(this.name),unsetBold:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[(0,r.OX)({find:s,type:this.type}),(0,r.OX)({find:a,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:l,type:this.type}),(0,r.Zc)({find:h,type:this.type})]}}),p="textStyle",d=/^\s*([-+*])\s$/,u=r.bP.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:t}){return["ul",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleBulletList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(p)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let t=(0,r.tG)({find:d,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(t=(0,r.tG)({find:d,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(p),editor:this.editor})),[t]}}),f=/(^|[^`])`([^`]+)`(?!`)/,m=/(^|[^`])`([^`]+)`(?!`)/g,g=r.CU.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:t}){return["code",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setCode:()=>({commands:t})=>t.setMark(this.name),toggleCode:()=>({commands:t})=>t.toggleMark(this.name),unsetCode:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[(0,r.OX)({find:f,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:m,type:this.type})]}});var y=n(52571);let w=/^```([a-z]+)?[\s\n]$/,k=/^~~~([a-z]+)?[\s\n]$/,b=r.bP.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:t=>{var e;let{languageClassPrefix:n}=this.options,r=[...(null==(e=t.firstElementChild)?void 0:e.classList)||[]].filter(t=>t.startsWith(n)).map(t=>t.replace(n,""))[0];return r||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:t,HTMLAttributes:e}){return["pre",(0,r.KV)(this.options.HTMLAttributes,e),["code",{class:t.attrs.language?this.options.languageClassPrefix+t.attrs.language:null},0]]},addCommands(){return{setCodeBlock:t=>({commands:e})=>e.setNode(this.name,t),toggleCodeBlock:t=>({commands:e})=>e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:t,$anchor:e}=this.editor.state.selection,n=1===e.pos;return!!t&&e.parent.type.name===this.name&&(!!n||!e.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:t})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:e}=t,{selection:n}=e,{$from:r,empty:i}=n;if(!i||r.parent.type!==this.type)return!1;let o=r.parentOffset===r.parent.nodeSize-2,s=r.parent.textContent.endsWith("\n\n");return!!o&&!!s&&t.chain().command(({tr:t})=>(t.delete(r.pos-2,r.pos),!0)).exitCode().run()},ArrowDown:({editor:t})=>{if(!this.options.exitOnArrowDown)return!1;let{state:e}=t,{selection:n,doc:r}=e,{$from:i,empty:o}=n;if(!o||i.parent.type!==this.type||i.parentOffset!==i.parent.nodeSize-2)return!1;let s=i.after();return void 0!==s&&(r.nodeAt(s)?t.commands.command(({tr:t})=>(t.setSelection(y.LN.near(r.resolve(s))),!0)):t.commands.exitCode())}}},addInputRules(){return[(0,r.JJ)({find:w,type:this.type,getAttributes:t=>({language:t[1]})}),(0,r.JJ)({find:k,type:this.type,getAttributes:t=>({language:t[1]})})]},addProseMirrorPlugins(){return[new y.k_({key:new y.hs("codeBlockVSCodeHandler"),props:{handlePaste:(t,e)=>{if(!e.clipboardData||this.editor.isActive(this.type.name))return!1;let n=e.clipboardData.getData("text/plain"),r=e.clipboardData.getData("vscode-editor-data"),i=r?JSON.parse(r):void 0,o=null==i?void 0:i.mode;if(!n||!o)return!1;let{tr:s,schema:l}=t.state,a=l.text(n.replace(/\r\n?/g,"\n"));return s.replaceSelectionWith(this.type.create({language:o},a)),s.selection.$from.parent.type!==this.type&&s.setSelection(y.U3.near(s.doc.resolve(Math.max(0,s.selection.from-2)))),s.setMeta("paste",!0),t.dispatch(s),!0}}})]}}),v=r.bP.create({name:"doc",topNode:!0,content:"block+"});var S=n(808);class C{constructor(t,e){var n;this.editorView=t,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!=(n=e.width)?n:1,this.color=!1===e.color?void 0:e.color||"black",this.class=e.class,this.handlers=["dragover","dragend","drop","dragleave"].map(e=>{let n=t=>{this[e](t)};return t.dom.addEventListener(e,n),{name:e,handler:n}})}destroy(){this.handlers.forEach(({name:t,handler:e})=>this.editorView.dom.removeEventListener(t,e))}update(t,e){null!=this.cursorPos&&e.doc!=t.state.doc&&(this.cursorPos>t.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(t){t!=this.cursorPos&&(this.cursorPos=t,null==t?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let t,e,n=this.editorView.state.doc.resolve(this.cursorPos),r=!n.parent.inlineContent,i,o=this.editorView.dom,s=o.getBoundingClientRect(),l=s.width/o.offsetWidth,a=s.height/o.offsetHeight;if(r){let t=n.nodeBefore,e=n.nodeAfter;if(t||e){let n=this.editorView.nodeDOM(this.cursorPos-(t?t.nodeSize:0));if(n){let r=n.getBoundingClientRect(),o=t?r.bottom:r.top;t&&e&&(o=(o+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let s=this.width/2*a;i={left:r.left,right:r.right,top:o-s,bottom:o+s}}}}if(!i){let t=this.editorView.coordsAtPos(this.cursorPos),e=this.width/2*l;i={left:t.left-e,right:t.left+e,top:t.top,bottom:t.bottom}}let h=this.editorView.dom.offsetParent;if(!this.element&&(this.element=h.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",r),this.element.classList.toggle("prosemirror-dropcursor-inline",!r),h&&(h!=document.body||"static"!=getComputedStyle(h).position)){let n=h.getBoundingClientRect(),r=n.width/h.offsetWidth,i=n.height/h.offsetHeight;t=n.left-h.scrollLeft*r,e=n.top-h.scrollTop*i}else t=-pageXOffset,e=-pageYOffset;this.element.style.left=(i.left-t)/l+"px",this.element.style.top=(i.top-e)/a+"px",this.element.style.width=(i.right-i.left)/l+"px",this.element.style.height=(i.bottom-i.top)/a+"px"}scheduleRemoval(t){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),t)}dragover(t){if(!this.editorView.editable)return;let e=this.editorView.posAtCoords({left:t.clientX,top:t.clientY}),n=e&&e.inside>=0&&this.editorView.state.doc.nodeAt(e.inside),r=n&&n.type.spec.disableDropCursor,i="function"==typeof r?r(this.editorView,e,t):r;if(e&&!i){let t=e.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let e=(0,S.Um)(this.editorView.state.doc,t,this.editorView.dragging.slice);null!=e&&(t=e)}this.setCursor(t),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(t){this.editorView.dom.contains(t.relatedTarget)||this.setCursor(null)}}let x=r.YY.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(t={}){return new y.k_({view:e=>new C(e,t)})}(this.options)]}});var M=n(96770),A=n(10156),T=n(42695);class E extends y.LN{constructor(t){super(t,t)}map(t,e){let n=t.resolve(e.map(this.head));return E.valid(n)?new E(n):y.LN.near(n)}content(){return A.Ji.empty}eq(t){return t instanceof E&&t.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(t,e){if("number"!=typeof e.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new E(t.resolve(e.pos))}getBookmark(){return new R(this.anchor)}static valid(t){let e=t.parent;if(e.isTextblock||!function(t){for(let e=t.depth;e>=0;e--){let n=t.index(e),r=t.node(e);if(0==n){if(r.type.spec.isolating)return!0;continue}for(let t=r.child(n-1);;t=t.lastChild){if(0==t.childCount&&!t.inlineContent||t.isAtom||t.type.spec.isolating)return!0;if(t.inlineContent)return!1}}return!0}(t)||!function(t){for(let e=t.depth;e>=0;e--){let n=t.indexAfter(e),r=t.node(e);if(n==r.childCount){if(r.type.spec.isolating)return!0;continue}for(let t=r.child(n);;t=t.firstChild){if(0==t.childCount&&!t.inlineContent||t.isAtom||t.type.spec.isolating)return!0;if(t.inlineContent)return!1}}return!0}(t))return!1;let n=e.type.spec.allowGapCursor;if(null!=n)return n;let r=e.contentMatchAt(t.index()).defaultType;return r&&r.isTextblock}static findGapCursorFrom(t,e,n=!1){n:for(;;){if(!n&&E.valid(t))return t;let r=t.pos,i=null;for(let n=t.depth;;n--){let o=t.node(n);if(e>0?t.indexAfter(n)<o.childCount:t.index(n)>0){i=o.child(e>0?t.indexAfter(n):t.index(n)-1);break}if(0==n)return null;r+=e;let s=t.doc.resolve(r);if(E.valid(s))return s}for(;;){let o=e>0?i.firstChild:i.lastChild;if(!o){if(i.isAtom&&!i.isText&&!y.nh.isSelectable(i)){t=t.doc.resolve(r+i.nodeSize*e),n=!1;continue n}break}i=o,r+=e;let s=t.doc.resolve(r);if(E.valid(s))return s}return null}}}E.prototype.visible=!1,E.findFrom=E.findGapCursorFrom,y.LN.jsonID("gapcursor",E);class R{constructor(t){this.pos=t}map(t){return new R(t.map(this.pos))}resolve(t){let e=t.resolve(this.pos);return E.valid(e)?new E(e):y.LN.near(e)}}let O=(0,M.K)({ArrowLeft:N("horiz",-1),ArrowRight:N("horiz",1),ArrowUp:N("vert",-1),ArrowDown:N("vert",1)});function N(t,e){let n="vert"==t?e>0?"down":"up":e>0?"right":"left";return function(t,r,i){let o=t.selection,s=e>0?o.$to:o.$from,l=o.empty;if(o instanceof y.U3){if(!i.endOfTextblock(n)||0==s.depth)return!1;l=!1,s=t.doc.resolve(e>0?s.after():s.before())}let a=E.findGapCursorFrom(s,e,l);return!!a&&(r&&r(t.tr.setSelection(new E(a))),!0)}}function I(t,e,n){if(!t||!t.editable)return!1;let r=t.state.doc.resolve(e);if(!E.valid(r))return!1;let i=t.posAtCoords({left:n.clientX,top:n.clientY});return!(i&&i.inside>-1&&y.nh.isSelectable(t.state.doc.nodeAt(i.inside)))&&(t.dispatch(t.state.tr.setSelection(new E(r))),!0)}function L(t,e){if("insertCompositionText"!=e.inputType||!(t.state.selection instanceof E))return!1;let{$from:n}=t.state.selection,r=n.parent.contentMatchAt(n.index()).findWrapping(t.state.schema.nodes.text);if(!r)return!1;let i=A.FK.empty;for(let t=r.length-1;t>=0;t--)i=A.FK.from(r[t].createAndFill(null,i));let o=t.state.tr.replace(n.pos,n.pos,new A.Ji(i,0,0));return o.setSelection(y.U3.near(o.doc.resolve(n.pos+1))),t.dispatch(o),!1}function z(t){if(!(t.selection instanceof E))return null;let e=document.createElement("div");return e.className="ProseMirror-gapcursor",T.zF.create(t.doc,[T.NZ.widget(t.selection.head,e,{key:"gapcursor"})])}let $=r.YY.create({name:"gapCursor",addProseMirrorPlugins:()=>[new y.k_({props:{decorations:z,createSelectionBetween:(t,e,n)=>e.pos==n.pos&&E.valid(n)?new E(n):null,handleClick:I,handleKeyDown:O,handleDOMEvents:{beforeinput:L}}})],extendNodeSchema(t){var e;let n={name:t.name,options:t.options,storage:t.storage};return{allowGapCursor:null!=(e=(0,r.gk)((0,r.iI)(t,"allowGapCursor",n)))?e:null}}}),F=r.bP.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:t}){return["br",(0,r.KV)(this.options.HTMLAttributes,t)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:t,chain:e,state:n,editor:r})=>t.first([()=>t.exitCode(),()=>t.command(()=>{let{selection:t,storedMarks:i}=n;if(t.$from.parent.type.spec.isolating)return!1;let{keepMarks:o}=this.options,{splittableMarks:s}=r.extensionManager,l=i||t.$to.parentOffset&&t.$from.marks();return e().insertContent({type:this.name}).command(({tr:t,dispatch:e})=>{if(e&&l&&o){let e=l.filter(t=>s.includes(t.type.name));t.ensureMarks(e)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),D=r.bP.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(t=>({tag:`h${t}`,attrs:{level:t}}))},renderHTML({node:t,HTMLAttributes:e}){let n=this.options.levels.includes(t.attrs.level)?t.attrs.level:this.options.levels[0];return[`h${n}`,(0,r.KV)(this.options.HTMLAttributes,e),0]},addCommands(){return{setHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.setNode(this.name,t),toggleHeading:t=>({commands:e})=>!!this.options.levels.includes(t.level)&&e.toggleNode(this.name,"paragraph",t)}},addKeyboardShortcuts(){return this.options.levels.reduce((t,e)=>({...t,...{[`Mod-Alt-${e}`]:()=>this.editor.commands.toggleHeading({level:e})}}),{})},addInputRules(){return this.options.levels.map(t=>(0,r.JJ)({find:RegExp(`^(#{${Math.min(...this.options.levels)},${t}})\\s$`),type:this.type,getAttributes:{level:t}}))}});var H=function(){};H.prototype.append=function(t){return t.length?(t=H.from(t),!this.length&&t||t.length<200&&this.leafAppend(t)||this.length<200&&t.leafPrepend(this)||this.appendInner(t)):this},H.prototype.prepend=function(t){return t.length?H.from(t).append(this):this},H.prototype.appendInner=function(t){return new B(this,t)},H.prototype.slice=function(t,e){return(void 0===t&&(t=0),void 0===e&&(e=this.length),t>=e)?H.empty:this.sliceInner(Math.max(0,t),Math.min(this.length,e))},H.prototype.get=function(t){if(!(t<0)&&!(t>=this.length))return this.getInner(t)},H.prototype.forEach=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=this.length),e<=n?this.forEachInner(t,e,n,0):this.forEachInvertedInner(t,e,n,0)},H.prototype.map=function(t,e,n){void 0===e&&(e=0),void 0===n&&(n=this.length);var r=[];return this.forEach(function(e,n){return r.push(t(e,n))},e,n),r},H.from=function(t){return t instanceof H?t:t&&t.length?new P(t):H.empty};var P=function(t){function e(e){t.call(this),this.values=e}t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e;var n={length:{configurable:!0},depth:{configurable:!0}};return e.prototype.flatten=function(){return this.values},e.prototype.sliceInner=function(t,n){return 0==t&&n==this.length?this:new e(this.values.slice(t,n))},e.prototype.getInner=function(t){return this.values[t]},e.prototype.forEachInner=function(t,e,n,r){for(var i=e;i<n;i++)if(!1===t(this.values[i],r+i))return!1},e.prototype.forEachInvertedInner=function(t,e,n,r){for(var i=e-1;i>=n;i--)if(!1===t(this.values[i],r+i))return!1},e.prototype.leafAppend=function(t){if(this.length+t.length<=200)return new e(this.values.concat(t.flatten()))},e.prototype.leafPrepend=function(t){if(this.length+t.length<=200)return new e(t.flatten().concat(this.values))},n.length.get=function(){return this.values.length},n.depth.get=function(){return 0},Object.defineProperties(e.prototype,n),e}(H);H.empty=new P([]);var B=function(t){function e(e,n){t.call(this),this.left=e,this.right=n,this.length=e.length+n.length,this.depth=Math.max(e.depth,n.depth)+1}return t&&(e.__proto__=t),e.prototype=Object.create(t&&t.prototype),e.prototype.constructor=e,e.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},e.prototype.getInner=function(t){return t<this.left.length?this.left.get(t):this.right.get(t-this.left.length)},e.prototype.forEachInner=function(t,e,n,r){var i=this.left.length;if(e<i&&!1===this.left.forEachInner(t,e,Math.min(n,i),r)||n>i&&!1===this.right.forEachInner(t,Math.max(e-i,0),Math.min(this.length,n)-i,r+i))return!1},e.prototype.forEachInvertedInner=function(t,e,n,r){var i=this.left.length;if(e>i&&!1===this.right.forEachInvertedInner(t,e-i,Math.max(n,i)-i,r+i)||n<i&&!1===this.left.forEachInvertedInner(t,Math.min(e,i),n,r))return!1},e.prototype.sliceInner=function(t,e){if(0==t&&e==this.length)return this;var n=this.left.length;return e<=n?this.left.slice(t,e):t>=n?this.right.slice(t-n,e-n):this.left.slice(t,n).append(this.right.slice(0,e-n))},e.prototype.leafAppend=function(t){var n=this.right.leafAppend(t);if(n)return new e(this.left,n)},e.prototype.leafPrepend=function(t){var n=this.left.leafPrepend(t);if(n)return new e(n,this.right)},e.prototype.appendInner=function(t){return this.left.depth>=Math.max(this.right.depth,t.depth)+1?new e(this.left,new e(this.right,t)):new e(this,t)},e}(H);class J{constructor(t,e){this.items=t,this.eventCount=e}popEvent(t,e){let n,r,i,o;if(0==this.eventCount)return null;let s=this.items.length;for(;;s--)if(this.items.get(s-1).selection){--s;break}e&&(r=(n=this.remapping(s,this.items.length)).maps.length);let l=t.tr,a=[],h=[];return this.items.forEach((t,e)=>{if(!t.step){n||(r=(n=this.remapping(s,e+1)).maps.length),r--,h.push(t);return}if(n){h.push(new j(t.map));let e=t.step.map(n.slice(r)),i;e&&l.maybeStep(e).doc&&(i=l.mapping.maps[l.mapping.maps.length-1],a.push(new j(i,void 0,void 0,a.length+h.length))),r--,i&&n.appendMap(i,r)}else l.maybeStep(t.step);if(t.selection)return i=n?t.selection.map(n.slice(r)):t.selection,o=new J(this.items.slice(0,s).append(h.reverse().concat(a)),this.eventCount-1),!1},this.items.length,0),{remaining:o,transform:l,selection:i}}addTransform(t,e,n,r){var i,o;let s,l=[],a=this.eventCount,h=this.items,c=!r&&h.length?h.get(h.length-1):null;for(let n=0;n<t.steps.length;n++){let i=t.steps[n].invert(t.docs[n]),o=new j(t.mapping.maps[n],i,e),s;(s=c&&c.merge(o))&&(o=s,n?l.pop():h=h.slice(0,h.length-1)),l.push(o),e&&(a++,e=void 0),r||(c=o)}let p=a-n.depth;return p>_&&(i=h,o=p,i.forEach((t,e)=>{if(t.selection&&0==o--)return s=e,!1}),h=i.slice(s),a-=p),new J(h.append(l),a)}remapping(t,e){let n=new S.X9;return this.items.forEach((e,r)=>{let i=null!=e.mirrorOffset&&r-e.mirrorOffset>=t?n.maps.length-e.mirrorOffset:void 0;n.appendMap(e.map,i)},t,e),n}addMaps(t){return 0==this.eventCount?this:new J(this.items.append(t.map(t=>new j(t))),this.eventCount)}rebased(t,e){if(!this.eventCount)return this;let n=[],r=Math.max(0,this.items.length-e),i=t.mapping,o=t.steps.length,s=this.eventCount;this.items.forEach(t=>{t.selection&&s--},r);let l=e;this.items.forEach(e=>{let r=i.getMirror(--l);if(null==r)return;o=Math.min(o,r);let a=i.maps[r];if(e.step){let o=t.steps[r].invert(t.docs[r]),h=e.selection&&e.selection.map(i.slice(l+1,r));h&&s++,n.push(new j(a,o,h))}else n.push(new j(a))},r);let a=[];for(let t=e;t<o;t++)a.push(new j(i.maps[t]));let h=new J(this.items.slice(0,r).append(a).append(n),s);return h.emptyItemCount()>500&&(h=h.compress(this.items.length-n.length)),h}emptyItemCount(){let t=0;return this.items.forEach(e=>{!e.step&&t++}),t}compress(t=this.items.length){let e=this.remapping(0,t),n=e.maps.length,r=[],i=0;return this.items.forEach((o,s)=>{if(s>=t)r.push(o),o.selection&&i++;else if(o.step){let t=o.step.map(e.slice(n)),s=t&&t.getMap();if(n--,s&&e.appendMap(s,n),t){let l=o.selection&&o.selection.map(e.slice(n));l&&i++;let a=new j(s.invert(),t,l),h,c=r.length-1;(h=r.length&&r[c].merge(a))?r[c]=h:r.push(a)}}else o.map&&n--},this.items.length,0),new J(H.from(r.reverse()),i)}}J.empty=new J(H.empty,0);class j{constructor(t,e,n,r){this.map=t,this.step=e,this.selection=n,this.mirrorOffset=r}merge(t){if(this.step&&t.step&&!t.selection){let e=t.step.merge(this.step);if(e)return new j(e.getMap().invert(),e,this.selection)}}}class K{constructor(t,e,n,r,i){this.done=t,this.undone=e,this.prevRanges=n,this.prevTime=r,this.prevComposition=i}}let _=20;function W(t){let e=[];for(let n=t.length-1;n>=0&&0==e.length;n--)t[n].forEach((t,n,r,i)=>e.push(r,i));return e}function V(t,e){if(!t)return null;let n=[];for(let r=0;r<t.length;r+=2){let i=e.map(t[r],1),o=e.map(t[r+1],-1);i<=o&&n.push(i,o)}return n}let U=!1,q=null;function G(t){let e=t.plugins;if(q!=e){U=!1,q=e;for(let t=0;t<e.length;t++)if(e[t].spec.historyPreserveItems){U=!0;break}}return U}let Y=new y.hs("history"),X=new y.hs("closeHistory");function Z(t,e){return(n,r)=>{let i=Y.getState(n);if(!i||0==(t?i.undone:i.done).eventCount)return!1;if(r){let o=function(t,e,n){let r=G(e),i=Y.get(e).spec.config,o=(n?t.undone:t.done).popEvent(e,r);if(!o)return null;let s=o.selection.resolve(o.transform.doc),l=(n?t.done:t.undone).addTransform(o.transform,e.selection.getBookmark(),i,r),a=new K(n?l:o.remaining,n?o.remaining:l,null,0,-1);return o.transform.setSelection(s).setMeta(Y,{redo:n,historyState:a})}(i,n,t);o&&r(e?o.scrollIntoView():o)}return!0}}let Q=Z(!1,!0),tt=Z(!0,!0);Z(!1,!1),Z(!0,!1);let te=r.YY.create({name:"history",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:t,dispatch:e})=>Q(t,e),redo:()=>({state:t,dispatch:e})=>tt(t,e)}),addProseMirrorPlugins(){return[function(t={}){return t={depth:t.depth||100,newGroupDelay:t.newGroupDelay||500},new y.k_({key:Y,state:{init:()=>new K(J.empty,J.empty,null,0,-1),apply:(e,n,r)=>(function(t,e,n,r){let i=n.getMeta(Y),o;if(i)return i.historyState;n.getMeta(X)&&(t=new K(t.done,t.undone,null,0,-1));let s=n.getMeta("appendedTransaction");if(0==n.steps.length)return t;if(s&&s.getMeta(Y))if(s.getMeta(Y).redo)return new K(t.done.addTransform(n,void 0,r,G(e)),t.undone,W(n.mapping.maps),t.prevTime,t.prevComposition);else return new K(t.done,t.undone.addTransform(n,void 0,r,G(e)),null,t.prevTime,t.prevComposition);if(!1===n.getMeta("addToHistory")||s&&!1===s.getMeta("addToHistory"))if(o=n.getMeta("rebased"))return new K(t.done.rebased(n,o),t.undone.rebased(n,o),V(t.prevRanges,n.mapping),t.prevTime,t.prevComposition);else return new K(t.done.addMaps(n.mapping.maps),t.undone.addMaps(n.mapping.maps),V(t.prevRanges,n.mapping),t.prevTime,t.prevComposition);{let i=n.getMeta("composition"),o=0==t.prevTime||!s&&t.prevComposition!=i&&(t.prevTime<(n.time||0)-r.newGroupDelay||!function(t,e){if(!e)return!1;if(!t.docChanged)return!0;let n=!1;return t.mapping.maps[0].forEach((t,r)=>{for(let i=0;i<e.length;i+=2)t<=e[i+1]&&r>=e[i]&&(n=!0)}),n}(n,t.prevRanges)),l=s?V(t.prevRanges,n.mapping):W(n.mapping.maps);return new K(t.done.addTransform(n,o?e.selection.getBookmark():void 0,r,G(e)),J.empty,l,n.time,null==i?t.prevComposition:i)}})(n,r,e,t)},config:t,props:{handleDOMEvents:{beforeinput(t,e){let n=e.inputType,r="historyUndo"==n?Q:"historyRedo"==n?tt:null;return!!r&&(e.preventDefault(),r(t.state,t.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),tn=r.bP.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:t}){return["hr",(0,r.KV)(this.options.HTMLAttributes,t)]},addCommands(){return{setHorizontalRule:()=>({chain:t,state:e})=>{if(!(0,r.AB)(e,e.schema.nodes[this.name]))return!1;let{selection:n}=e,{$from:i,$to:o}=n,s=t();return 0===i.parentOffset?s.insertContentAt({from:Math.max(i.pos-1,0),to:o.pos},{type:this.name}):(0,r.BQ)(n)?s.insertContentAt(o.pos,{type:this.name}):s.insertContent({type:this.name}),s.command(({tr:t,dispatch:e})=>{var n;if(e){let{$to:e}=t.selection,r=e.end();if(e.nodeAfter)e.nodeAfter.isTextblock?t.setSelection(y.U3.create(t.doc,e.pos+1)):e.nodeAfter.isBlock?t.setSelection(y.nh.create(t.doc,e.pos)):t.setSelection(y.U3.create(t.doc,e.pos));else{let i=null==(n=e.parent.type.contentMatch.defaultType)?void 0:n.create();i&&(t.insert(r,i),t.setSelection(y.U3.create(t.doc,r+1)))}t.scrollIntoView()}return!0}).run()}}},addInputRules(){return[(0,r.jT)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),tr=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,ti=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,to=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,ts=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,tl=r.CU.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:t=>"normal"!==t.style.fontStyle&&null},{style:"font-style=normal",clearMark:t=>t.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:t}){return["em",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setItalic:()=>({commands:t})=>t.setMark(this.name),toggleItalic:()=>({commands:t})=>t.toggleMark(this.name),unsetItalic:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[(0,r.OX)({find:tr,type:this.type}),(0,r.OX)({find:to,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:ti,type:this.type}),(0,r.Zc)({find:ts,type:this.type})]}}),ta=r.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:t}){return["li",(0,r.KV)(this.options.HTMLAttributes,t),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}}),th="textStyle",tc=/^(\d+)\.\s$/,tp=r.bP.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:t=>t.hasAttribute("start")?parseInt(t.getAttribute("start")||"",10):1},type:{default:null,parseHTML:t=>t.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:t}){let{start:e,...n}=t;return 1===e?["ol",(0,r.KV)(this.options.HTMLAttributes,n),0]:["ol",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{toggleOrderedList:()=>({commands:t,chain:e})=>this.options.keepAttributes?e().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(th)).run():t.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let t=(0,r.tG)({find:tc,type:this.type,getAttributes:t=>({start:+t[1]}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(t=(0,r.tG)({find:tc,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:t=>({start:+t[1],...this.editor.getAttributes(th)}),joinPredicate:(t,e)=>e.childCount+e.attrs.start===+t[1],editor:this.editor})),[t]}}),td=r.bP.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:t}){return["p",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setParagraph:()=>({commands:t})=>t.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),tu=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,tf=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,tm=r.CU.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:t=>!!t.includes("line-through")&&{}}],renderHTML({HTMLAttributes:t}){return["s",(0,r.KV)(this.options.HTMLAttributes,t),0]},addCommands(){return{setStrike:()=>({commands:t})=>t.setMark(this.name),toggleStrike:()=>({commands:t})=>t.toggleMark(this.name),unsetStrike:()=>({commands:t})=>t.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[(0,r.OX)({find:tu,type:this.type})]},addPasteRules(){return[(0,r.Zc)({find:tf,type:this.type})]}}),tg=r.bP.create({name:"text",group:"inline"}),ty=r.YY.create({name:"starterKit",addExtensions(){let t=[];return!1!==this.options.bold&&t.push(c.configure(this.options.bold)),!1!==this.options.blockquote&&t.push(o.configure(this.options.blockquote)),!1!==this.options.bulletList&&t.push(u.configure(this.options.bulletList)),!1!==this.options.code&&t.push(g.configure(this.options.code)),!1!==this.options.codeBlock&&t.push(b.configure(this.options.codeBlock)),!1!==this.options.document&&t.push(v.configure(this.options.document)),!1!==this.options.dropcursor&&t.push(x.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&t.push($.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&t.push(F.configure(this.options.hardBreak)),!1!==this.options.heading&&t.push(D.configure(this.options.heading)),!1!==this.options.history&&t.push(te.configure(this.options.history)),!1!==this.options.horizontalRule&&t.push(tn.configure(this.options.horizontalRule)),!1!==this.options.italic&&t.push(tl.configure(this.options.italic)),!1!==this.options.listItem&&t.push(ta.configure(this.options.listItem)),!1!==this.options.orderedList&&t.push(tp.configure(this.options.orderedList)),!1!==this.options.paragraph&&t.push(td.configure(this.options.paragraph)),!1!==this.options.strike&&t.push(tm.configure(this.options.strike)),!1!==this.options.text&&t.push(tg.configure(this.options.text)),t}})},61514:(t,e,n)=>{n.d(e,{$B:()=>c,Sd:()=>a,T2:()=>h});var r=n(808),i=n(10156);let o=["ol",0],s=["ul",0],l=["li",0];function a(t,e=null){return function(n,o){let{$from:s,$to:l}=n.selection,a=s.blockRange(l);if(!a)return!1;let h=o?n.tr:null;return!!function(t,e,n,o=null){let s=!1,l=e,a=e.$from.doc;if(e.depth>=2&&e.$from.node(e.depth-1).type.compatibleContent(n)&&0==e.startIndex){if(0==e.$from.index(e.depth-1))return!1;let t=a.resolve(e.start-2);l=new i.u$(t,t,e.depth),e.endIndex<e.parent.childCount&&(e=new i.u$(e.$from,a.resolve(e.$to.end(e.depth)),e.depth)),s=!0}let h=(0,r.oM)(l,n,o,e);return!!h&&(t&&function(t,e,n,o,s){let l=i.FK.empty;for(let t=n.length-1;t>=0;t--)l=i.FK.from(n[t].type.create(n[t].attrs,l));t.step(new r.Wg(e.start-2*!!o,e.end,e.start,e.end,new i.Ji(l,0,0),n.length,!0));let a=0;for(let t=0;t<n.length;t++)n[t].type==s&&(a=t+1);let h=n.length-a,c=e.start+n.length-2*!!o,p=e.parent;for(let n=e.startIndex,i=e.endIndex,o=!0;n<i;n++,o=!1)!o&&(0,r.zy)(t.doc,c,h)&&(t.split(c,h),c+=2*h),c+=p.child(n).nodeSize}(t,e,h,s,n),!0)}(h,a,t,e)&&(o&&o(h.scrollIntoView()),!0)}}function h(t){return function(e,n){let{$from:o,$to:s}=e.selection,l=o.blockRange(s,e=>e.childCount>0&&e.firstChild.type==t);return!!l&&(!n||(o.node(l.depth-1).type==t?function(t,e,n,o){let s=t.tr,l=o.end,a=o.$to.end(o.depth);l<a&&(s.step(new r.Wg(l-1,a,l,a,new i.Ji(i.FK.from(n.create(null,o.parent.copy())),1,0),1,!0)),o=new i.u$(s.doc.resolve(o.$from.pos),s.doc.resolve(a),o.depth));let h=(0,r.jP)(o);if(null==h)return!1;s.lift(o,h);let c=s.doc.resolve(s.mapping.map(l,-1)-1);return(0,r.n9)(s.doc,c.pos)&&c.nodeBefore.type==c.nodeAfter.type&&s.join(c.pos),e(s.scrollIntoView()),!0}(e,n,t,l):function(t,e,n){let o=t.tr,s=n.parent;for(let t=n.end,e=n.endIndex-1,r=n.startIndex;e>r;e--)t-=s.child(e).nodeSize,o.delete(t-1,t+1);let l=o.doc.resolve(n.start),a=l.nodeAfter;if(o.mapping.map(n.end)!=n.start+l.nodeAfter.nodeSize)return!1;let h=0==n.startIndex,c=n.endIndex==s.childCount,p=l.node(-1),d=l.index(-1);if(!p.canReplace(d+ +!h,d+1,a.content.append(c?i.FK.empty:i.FK.from(s))))return!1;let u=l.pos,f=u+a.nodeSize;return o.step(new r.Wg(u-!!h,f+ +!!c,u+1,f-1,new i.Ji((h?i.FK.empty:i.FK.from(s.copy(i.FK.empty))).append(c?i.FK.empty:i.FK.from(s.copy(i.FK.empty))),+!h,+!c),+!h)),e(o.scrollIntoView()),!0}(e,n,l)))}}function c(t){return function(e,n){let{$from:o,$to:s}=e.selection,l=o.blockRange(s,e=>e.childCount>0&&e.firstChild.type==t);if(!l)return!1;let a=l.startIndex;if(0==a)return!1;let h=l.parent,c=h.child(a-1);if(c.type!=t)return!1;if(n){let o=c.lastChild&&c.lastChild.type==h.type,s=i.FK.from(o?t.create():null),a=new i.Ji(i.FK.from(t.create(null,i.FK.from(h.type.create(null,s)))),o?3:1,0),p=l.start,d=l.end;n(e.tr.step(new r.Wg(p-(o?3:1),d,p,d,a,1,!0)).scrollIntoView())}return!0}}},65112:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Table",[["path",{d:"M12 3v18",key:"108xh3"}],["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}]])},74347:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Highlighter",[["path",{d:"m9 11-6 6v3h9l3-3",key:"1a3l36"}],["path",{d:"m22 12-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4",key:"14a9rk"}]])},74979:(t,e,n)=>{n.d(e,{A:()=>s});var r=n(4701),i=n(52571),o=n(42695);let s=r.YY.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new i.k_({key:new i.hs("placeholder"),props:{decorations:({doc:t,selection:e})=>{let n=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:i}=e,s=[];if(!n)return null;let l=this.editor.isEmpty;return t.descendants((t,e)=>{let n=i>=e&&i<=e+t.nodeSize,a=!t.isLeaf&&(0,r.Op)(t);if((n||!this.options.showOnlyCurrent)&&a){let r=[this.options.emptyNodeClass];l&&r.push(this.options.emptyEditorClass);let i=o.NZ.node(e,e+t.nodeSize,{class:r.join(" "),"data-placeholder":"function"==typeof this.options.placeholder?this.options.placeholder({editor:this.editor,node:t,pos:e,hasAnchor:n}):this.options.placeholder});s.push(i)}return this.options.includeChildren}),o.zF.create(t,s)}}})]}})},75109:(t,e,n)=>{n.d(e,{$Z:()=>m,hG:()=>M});var r,i,o=n(12115),s=n(47650),l=n(4701),a={exports:{}},h={};a.exports=function(){if(r)return h;r=1;var t="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},e=o.useState,n=o.useEffect,i=o.useLayoutEffect,s=o.useDebugValue;function l(e){var n=e.getSnapshot;e=e.value;try{var r=n();return!t(e,r)}catch(t){return!0}}var a="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,r){var o=r(),a=e({inst:{value:o,getSnapshot:r}}),h=a[0].inst,c=a[1];return i(function(){h.value=o,h.getSnapshot=r,l(h)&&c({inst:h})},[t,o,r]),n(function(){return l(h)&&c({inst:h}),t(function(){l(h)&&c({inst:h})})},[t]),s(o),o};return h.useSyncExternalStore=void 0!==o.useSyncExternalStore?o.useSyncExternalStore:a,h}();var c=a.exports;let p=(...t)=>e=>{t.forEach(t=>{"function"==typeof t?t(e):t&&(t.current=e)})},d=({contentComponent:t})=>{let e=c.useSyncExternalStore(t.subscribe,t.getSnapshot,t.getServerSnapshot);return o.createElement(o.Fragment,null,Object.values(e))};class u extends o.Component{constructor(t){var e;super(t),this.editorContentRef=o.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null==(e=t.editor)?void 0:e.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let t=this.props.editor;if(t&&!t.isDestroyed&&t.options.element){if(t.contentComponent)return;let e=this.editorContentRef.current;e.append(...t.options.element.childNodes),t.setOptions({element:e}),t.contentComponent=function(){let t=new Set,e={};return{subscribe:e=>(t.add(e),()=>{t.delete(e)}),getSnapshot:()=>e,getServerSnapshot:()=>e,setRenderer(n,r){e={...e,[n]:s.createPortal(r.reactElement,r.element,n)},t.forEach(t=>t())},removeRenderer(n){let r={...e};delete r[n],e=r,t.forEach(t=>t())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=t.contentComponent.subscribe(()=>{this.setState(t=>t.hasContentComponentInitialized?t:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),t.createNodeViews(),this.initialized=!0}}componentWillUnmount(){let t=this.props.editor;if(!t||(this.initialized=!1,t.isDestroyed||t.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),t.contentComponent=null,!t.options.element.firstChild))return;let e=document.createElement("div");e.append(...t.options.element.childNodes),t.setOptions({element:e})}render(){let{editor:t,innerRef:e,...n}=this.props;return o.createElement(o.Fragment,null,o.createElement("div",{ref:p(e,this.editorContentRef),...n}),(null==t?void 0:t.contentComponent)&&o.createElement(d,{contentComponent:t.contentComponent}))}}let f=(0,o.forwardRef)((t,e)=>{let n=o.useMemo(()=>Math.floor(0xffffffff*Math.random()).toString(),[t.editor]);return o.createElement(u,{key:n,innerRef:e,...t})}),m=o.memo(f);var g=function(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}(function t(e,n){if(e===n)return!0;if(e&&n&&"object"==typeof e&&"object"==typeof n){if(e.constructor!==n.constructor)return!1;if(Array.isArray(e)){if((r=e.length)!=n.length)return!1;for(i=r;0!=i--;)if(!t(e[i],n[i]))return!1;return!0}if(e instanceof Map&&n instanceof Map){if(e.size!==n.size)return!1;for(i of e.entries())if(!n.has(i[0]))return!1;for(i of e.entries())if(!t(i[1],n.get(i[0])))return!1;return!0}if(e instanceof Set&&n instanceof Set){if(e.size!==n.size)return!1;for(i of e.entries())if(!n.has(i[0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(n)){if((r=e.length)!=n.length)return!1;for(i=r;0!=i--;)if(e[i]!==n[i])return!1;return!0}if(e.constructor===RegExp)return e.source===n.source&&e.flags===n.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===n.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===n.toString();if((r=(o=Object.keys(e)).length)!==Object.keys(n).length)return!1;for(i=r;0!=i--;)if(!Object.prototype.hasOwnProperty.call(n,o[i]))return!1;for(i=r;0!=i--;){var r,i,o,s=o[i];if(("_owner"!==s||!e.$$typeof)&&!t(e[s],n[s]))return!1}return!0}return e!=e&&n!=n}),y={exports:{}},w={};y.exports=function(){if(i)return w;i=1;var t="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},e=c.useSyncExternalStore,n=o.useRef,r=o.useEffect,s=o.useMemo,l=o.useDebugValue;return w.useSyncExternalStoreWithSelector=function(i,o,a,h,c){var p=n(null);if(null===p.current){var d={hasValue:!1,value:null};p.current=d}else d=p.current;var u=e(i,(p=s(function(){function e(e){if(!i){if(i=!0,n=e,e=h(e),void 0!==c&&d.hasValue){var o=d.value;if(c(o,e))return r=o}return r=e}if(o=r,t(n,e))return o;var s=h(e);return void 0!==c&&c(o,s)?o:(n=e,r=s)}var n,r,i=!1,s=void 0===a?null:a;return[function(){return e(o())},null===s?void 0:function(){return e(s())}]},[o,a,h,c]))[0],p[1]);return r(function(){d.hasValue=!0,d.value=u},[u]),l(u),u},w}();var k=y.exports;let b="undefined"!=typeof window?o.useLayoutEffect:o.useEffect;class v{constructor(t){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=t,this.lastSnapshot={editor:t,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(t){return this.subscribers.add(t),()=>{this.subscribers.delete(t)}}watch(t){if(this.editor=t,this.editor){let t=()=>{this.transactionNumber+=1,this.subscribers.forEach(t=>t())},e=this.editor;return e.on("transaction",t),()=>{e.off("transaction",t)}}}}let S="undefined"==typeof window,C=S||!!("undefined"!=typeof window&&window.next);class x{constructor(t){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=t,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(t){this.editor=t,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(t=>t())}getInitialEditor(){return void 0===this.options.current.immediatelyRender?S||C?null:this.createEditor():(this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null)}createEditor(){let t={...this.options.current,onBeforeCreate:(...t)=>{var e,n;return null==(n=(e=this.options.current).onBeforeCreate)?void 0:n.call(e,...t)},onBlur:(...t)=>{var e,n;return null==(n=(e=this.options.current).onBlur)?void 0:n.call(e,...t)},onCreate:(...t)=>{var e,n;return null==(n=(e=this.options.current).onCreate)?void 0:n.call(e,...t)},onDestroy:(...t)=>{var e,n;return null==(n=(e=this.options.current).onDestroy)?void 0:n.call(e,...t)},onFocus:(...t)=>{var e,n;return null==(n=(e=this.options.current).onFocus)?void 0:n.call(e,...t)},onSelectionUpdate:(...t)=>{var e,n;return null==(n=(e=this.options.current).onSelectionUpdate)?void 0:n.call(e,...t)},onTransaction:(...t)=>{var e,n;return null==(n=(e=this.options.current).onTransaction)?void 0:n.call(e,...t)},onUpdate:(...t)=>{var e,n;return null==(n=(e=this.options.current).onUpdate)?void 0:n.call(e,...t)},onContentError:(...t)=>{var e,n;return null==(n=(e=this.options.current).onContentError)?void 0:n.call(e,...t)},onDrop:(...t)=>{var e,n;return null==(n=(e=this.options.current).onDrop)?void 0:n.call(e,...t)},onPaste:(...t)=>{var e,n;return null==(n=(e=this.options.current).onPaste)?void 0:n.call(e,...t)}};return new l.KE(t)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(t){return this.subscriptions.add(t),()=>{this.subscriptions.delete(t)}}static compareOptions(t,e){return Object.keys(t).every(n=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(n)||("extensions"===n&&t.extensions&&e.extensions?t.extensions.length===e.extensions.length&&t.extensions.every((t,n)=>{var r;return t===(null==(r=e.extensions)?void 0:r[n])}):t[n]===e[n]))}onRender(t){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===t.length?x.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(t),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(t){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=t;return}if(this.previousDeps.length===t.length&&this.previousDeps.every((e,n)=>e===t[n]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=t}scheduleDestroy(){let t=this.instanceId,e=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===t){e&&e.setOptions(this.options.current);return}e&&!e.isDestroyed&&(e.destroy(),this.instanceId===t&&this.setEditor(null))},1)}}function M(t={},e=[]){let n=(0,o.useRef)(t);n.current=t;let[r]=(0,o.useState)(()=>new x(n)),i=c.useSyncExternalStore(r.subscribe,r.getEditor,r.getServerSnapshot);return(0,o.useDebugValue)(i),(0,o.useEffect)(r.onRender(e)),!function(t){var e;let[n]=(0,o.useState)(()=>new v(t.editor)),r=k.useSyncExternalStoreWithSelector(n.subscribe,n.getSnapshot,n.getServerSnapshot,t.selector,null!=(e=t.equalityFn)?e:g);b(()=>n.watch(t.editor),[t.editor,n]),(0,o.useDebugValue)(r)}({editor:i,selector:({transactionNumber:e})=>!1===t.shouldRerenderOnTransaction?null:t.immediatelyRender&&0===e?0:e+1}),i}let A=((0,o.createContext)({editor:null}).Consumer,(0,o.createContext)({onDragStart:void 0})),T=()=>(0,o.useContext)(A);function E(t){return!!("function"==typeof t&&t.prototype&&t.prototype.isReactComponent)}function R(t){return!!("object"==typeof t&&t.$$typeof&&("Symbol(react.forward_ref)"===t.$$typeof.toString()||"react.forward_ref"===t.$$typeof.description))}o.forwardRef((t,e)=>{let{onDragStart:n}=T(),r=t.as||"div";return o.createElement(r,{...t,ref:e,"data-node-view-wrapper":"",onDragStart:n,style:{whiteSpace:"normal",...t.style}})});class O{constructor(t,{editor:e,props:n={},as:r="div",className:i=""}){this.ref=null,this.id=Math.floor(0xffffffff*Math.random()).toString(),this.component=t,this.editor=e,this.props=n,this.element=document.createElement(r),this.element.classList.add("react-renderer"),i&&this.element.classList.add(...i.split(" ")),queueMicrotask(()=>{this.render()})}render(){var t;let e=this.component,n=this.props,r=this.editor,i=function(){try{if(o.version)return parseInt(o.version.split(".")[0],10)>=19}catch{}return!1}(),s=function(t){if(E(t)||R(t))return!0;if("object"==typeof t&&t.$$typeof&&("Symbol(react.memo)"===t.$$typeof.toString()||"react.memo"===t.$$typeof.description)){let e=t.type;if(e)return E(e)||R(e)}return!1}(e),l={...n};l.ref&&!(i||s)&&delete l.ref,!l.ref&&(i||s)&&(l.ref=t=>{this.ref=t}),this.reactElement=o.createElement(e,{...l}),null==(t=null==r?void 0:r.contentComponent)||t.setRenderer(this.id,this)}updateProps(t={}){this.props={...this.props,...t},this.render()}destroy(){var t;let e=this.editor;null==(t=null==e?void 0:e.contentComponent)||t.removeRenderer(this.id)}updateAttributes(t){Object.keys(t).forEach(e=>{this.element.setAttribute(e,t[e])})}}l.Yv},79030:(t,e,n)=>{n.d(e,{h:()=>i});var r=n(4701);let i=r.bP.create({name:"tableHeader",addOptions:()=>({HTMLAttributes:{}}),content:"block+",addAttributes:()=>({colspan:{default:1},rowspan:{default:1},colwidth:{default:null,parseHTML:t=>{let e=t.getAttribute("colwidth");return e?e.split(",").map(t=>parseInt(t,10)):null}}}),tableRole:"header_cell",isolating:!0,parseHTML:()=>[{tag:"th"}],renderHTML({HTMLAttributes:t}){return["th",(0,r.KV)(this.options.HTMLAttributes,t),0]}})},89140:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("ListOrdered",[["line",{x1:"10",x2:"21",y1:"6",y2:"6",key:"76qw6h"}],["line",{x1:"10",x2:"21",y1:"12",y2:"12",key:"16nom4"}],["line",{x1:"10",x2:"21",y1:"18",y2:"18",key:"u3jurt"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},90192:(t,e,n)=>{n.d(e,{Ay:()=>tp});var r,i,o=n(4701),s=n(52571),l=n(10156),a=n(42695),h=n(96770),c=n(808);if("undefined"!=typeof WeakMap){let t=new WeakMap;r=e=>t.get(e),i=(e,n)=>(t.set(e,n),n)}else{let t=[],e=0;r=e=>{for(let n=0;n<t.length;n+=2)if(t[n]==e)return t[n+1]},i=(n,r)=>(10==e&&(e=0),t[e++]=n,t[e++]=r)}var p=class{constructor(t,e,n,r){this.width=t,this.height=e,this.map=n,this.problems=r}findCell(t){for(let e=0;e<this.map.length;e++){let n=this.map[e];if(n!=t)continue;let r=e%this.width,i=e/this.width|0,o=r+1,s=i+1;for(let t=1;o<this.width&&this.map[e+t]==n;t++)o++;for(let t=1;s<this.height&&this.map[e+this.width*t]==n;t++)s++;return{left:r,top:i,right:o,bottom:s}}throw RangeError(`No cell with offset ${t} found`)}colCount(t){for(let e=0;e<this.map.length;e++)if(this.map[e]==t)return e%this.width;throw RangeError(`No cell with offset ${t} found`)}nextCell(t,e,n){let{left:r,right:i,top:o,bottom:s}=this.findCell(t);return"horiz"==e?(n<0?0==r:i==this.width)?null:this.map[o*this.width+(n<0?r-1:i)]:(n<0?0==o:s==this.height)?null:this.map[r+this.width*(n<0?o-1:s)]}rectBetween(t,e){let{left:n,right:r,top:i,bottom:o}=this.findCell(t),{left:s,right:l,top:a,bottom:h}=this.findCell(e);return{left:Math.min(n,s),top:Math.min(i,a),right:Math.max(r,l),bottom:Math.max(o,h)}}cellsInRect(t){let e=[],n={};for(let r=t.top;r<t.bottom;r++)for(let i=t.left;i<t.right;i++){let o=r*this.width+i,s=this.map[o];!n[s]&&(n[s]=!0,i==t.left&&i&&this.map[o-1]==s||r==t.top&&r&&this.map[o-this.width]==s||e.push(s))}return e}positionAt(t,e,n){for(let r=0,i=0;;r++){let o=i+n.child(r).nodeSize;if(r==t){let n=e+t*this.width,r=(t+1)*this.width;for(;n<r&&this.map[n]<i;)n++;return n==r?o-1:this.map[n]}i=o}}static get(t){return r(t)||i(t,function(t){if("table"!=t.type.spec.tableRole)throw RangeError("Not a table node: "+t.type.name);let e=function(t){let e=-1,n=!1;for(let r=0;r<t.childCount;r++){let i=t.child(r),o=0;if(n)for(let e=0;e<r;e++){let n=t.child(e);for(let t=0;t<n.childCount;t++){let i=n.child(t);e+i.attrs.rowspan>r&&(o+=i.attrs.colspan)}}for(let t=0;t<i.childCount;t++){let e=i.child(t);o+=e.attrs.colspan,e.attrs.rowspan>1&&(n=!0)}-1==e?e=o:e!=o&&(e=Math.max(e,o))}return e}(t),n=t.childCount,r=[],i=0,o=null,s=[];for(let t=0,i=e*n;t<i;t++)r[t]=0;for(let l=0,a=0;l<n;l++){let h=t.child(l);a++;for(let t=0;;t++){for(;i<r.length&&0!=r[i];)i++;if(t==h.childCount)break;let c=h.child(t),{colspan:p,rowspan:d,colwidth:u}=c.attrs;for(let t=0;t<d;t++){if(t+l>=n){(o||(o=[])).push({type:"overlong_rowspan",pos:a,n:d-t});break}let h=i+t*e;for(let t=0;t<p;t++){0==r[h+t]?r[h+t]=a:(o||(o=[])).push({type:"collision",row:l,pos:a,n:p-t});let n=u&&u[t];if(n){let r=(h+t)%e*2,i=s[r];null==i||i!=n&&1==s[r+1]?(s[r]=n,s[r+1]=1):i==n&&s[r+1]++}}}i+=p,a+=c.nodeSize}let c=(l+1)*e,p=0;for(;i<c;)0==r[i++]&&p++;p&&(o||(o=[])).push({type:"missing",row:l,n:p}),a++}(0===e||0===n)&&(o||(o=[])).push({type:"zero_sized"});let l=new p(e,n,r,o),a=!1;for(let t=0;!a&&t<s.length;t+=2)null!=s[t]&&s[t+1]<n&&(a=!0);return a&&function(t,e,n){t.problems||(t.problems=[]);let r={};for(let i=0;i<t.map.length;i++){let o=t.map[i];if(r[o])continue;r[o]=!0;let s=n.nodeAt(o);if(!s)throw RangeError(`No cell with offset ${o} found`);let l=null,a=s.attrs;for(let n=0;n<a.colspan;n++){let r=e[2*((i+n)%t.width)];null==r||a.colwidth&&a.colwidth[n]==r||((l||(l=function(t){if(t.colwidth)return t.colwidth.slice();let e=[];for(let n=0;n<t.colspan;n++)e.push(0);return e}(a)))[n]=r)}l&&t.problems.unshift({type:"colwidth mismatch",pos:o,colwidth:l})}}(l,s,t),l}(t))}};function d(t){let e=t.cached.tableNodeTypes;if(!e)for(let n in e=t.cached.tableNodeTypes={},t.nodes){let r=t.nodes[n],i=r.spec.tableRole;i&&(e[i]=r)}return e}var u=new s.hs("selectingCells");function f(t){for(let e=t.depth-1;e>0;e--)if("row"==t.node(e).type.spec.tableRole)return t.node(0).resolve(t.before(e+1));return null}function m(t){let e=t.selection.$head;for(let t=e.depth;t>0;t--)if("row"==e.node(t).type.spec.tableRole)return!0;return!1}function g(t){let e=t.selection;if("$anchorCell"in e&&e.$anchorCell)return e.$anchorCell.pos>e.$headCell.pos?e.$anchorCell:e.$headCell;if("node"in e&&e.node&&"cell"==e.node.type.spec.tableRole)return e.$anchor;let n=f(e.$head)||function(t){for(let e=t.nodeAfter,n=t.pos;e;e=e.firstChild,n++){let r=e.type.spec.tableRole;if("cell"==r||"header_cell"==r)return t.doc.resolve(n)}for(let e=t.nodeBefore,n=t.pos;e;e=e.lastChild,n--){let r=e.type.spec.tableRole;if("cell"==r||"header_cell"==r)return t.doc.resolve(n-e.nodeSize)}}(e.$head);if(n)return n;throw RangeError(`No cell found around position ${e.head}`)}function y(t){return"row"==t.parent.type.spec.tableRole&&!!t.nodeAfter}function w(t,e){return t.depth==e.depth&&t.pos>=e.start(-1)&&t.pos<=e.end(-1)}function k(t,e,n){let r=t.node(-1),i=p.get(r),o=t.start(-1),s=i.nextCell(t.pos-o,e,n);return null==s?null:t.node(0).resolve(o+s)}function b(t,e,n=1){let r={...t,colspan:t.colspan-n};return r.colwidth&&(r.colwidth=r.colwidth.slice(),r.colwidth.splice(e,n),r.colwidth.some(t=>t>0)||(r.colwidth=null)),r}function v(t,e,n=1){let r={...t,colspan:t.colspan+n};if(r.colwidth){r.colwidth=r.colwidth.slice();for(let t=0;t<n;t++)r.colwidth.splice(e,0,0)}return r}var S=class t extends s.LN{constructor(t,e=t){let n=t.node(-1),r=p.get(n),i=t.start(-1),o=r.rectBetween(t.pos-i,e.pos-i),l=t.node(0),a=r.cellsInRect(o).filter(t=>t!=e.pos-i);a.unshift(e.pos-i);let h=a.map(t=>{let e=n.nodeAt(t);if(!e)throw RangeError(`No cell with offset ${t} found`);let r=i+t+1;return new s.yn(l.resolve(r),l.resolve(r+e.content.size))});super(h[0].$from,h[0].$to,h),this.$anchorCell=t,this.$headCell=e}map(e,n){let r=e.resolve(n.map(this.$anchorCell.pos)),i=e.resolve(n.map(this.$headCell.pos));if(y(r)&&y(i)&&w(r,i)){let e=this.$anchorCell.node(-1)!=r.node(-1);return e&&this.isRowSelection()?t.rowSelection(r,i):e&&this.isColSelection()?t.colSelection(r,i):new t(r,i)}return s.U3.between(r,i)}content(){let t=this.$anchorCell.node(-1),e=p.get(t),n=this.$anchorCell.start(-1),r=e.rectBetween(this.$anchorCell.pos-n,this.$headCell.pos-n),i={},o=[];for(let n=r.top;n<r.bottom;n++){let s=[];for(let o=n*e.width+r.left,l=r.left;l<r.right;l++,o++){let n=e.map[o];if(i[n])continue;i[n]=!0;let l=e.findCell(n),a=t.nodeAt(n);if(!a)throw RangeError(`No cell with offset ${n} found`);let h=r.left-l.left,c=l.right-r.right;if(h>0||c>0){let t=a.attrs;if(h>0&&(t=b(t,0,h)),c>0&&(t=b(t,t.colspan-c,c)),l.left<r.left){if(!(a=a.type.createAndFill(t)))throw RangeError(`Could not create cell with attrs ${JSON.stringify(t)}`)}else a=a.type.create(t,a.content)}if(l.top<r.top||l.bottom>r.bottom){let t={...a.attrs,rowspan:Math.min(l.bottom,r.bottom)-Math.max(l.top,r.top)};a=l.top<r.top?a.type.createAndFill(t):a.type.create(t,a.content)}s.push(a)}o.push(t.child(n).copy(l.FK.from(s)))}let s=this.isColSelection()&&this.isRowSelection()?t:o;return new l.Ji(l.FK.from(s),1,1)}replace(t,e=l.Ji.empty){let n=t.steps.length,r=this.ranges;for(let i=0;i<r.length;i++){let{$from:o,$to:s}=r[i],a=t.mapping.slice(n);t.replace(a.map(o.pos),a.map(s.pos),i?l.Ji.empty:e)}let i=s.LN.findFrom(t.doc.resolve(t.mapping.slice(n).map(this.to)),-1);i&&t.setSelection(i)}replaceWith(t,e){this.replace(t,new l.Ji(l.FK.from(e),0,0))}forEachCell(t){let e=this.$anchorCell.node(-1),n=p.get(e),r=this.$anchorCell.start(-1),i=n.cellsInRect(n.rectBetween(this.$anchorCell.pos-r,this.$headCell.pos-r));for(let n=0;n<i.length;n++)t(e.nodeAt(i[n]),r+i[n])}isColSelection(){let t=this.$anchorCell.index(-1),e=this.$headCell.index(-1);return!(Math.min(t,e)>0)&&Math.max(t+this.$anchorCell.nodeAfter.attrs.rowspan,e+this.$headCell.nodeAfter.attrs.rowspan)==this.$headCell.node(-1).childCount}static colSelection(e,n=e){let r=e.node(-1),i=p.get(r),o=e.start(-1),s=i.findCell(e.pos-o),l=i.findCell(n.pos-o),a=e.node(0);return s.top<=l.top?(s.top>0&&(e=a.resolve(o+i.map[s.left])),l.bottom<i.height&&(n=a.resolve(o+i.map[i.width*(i.height-1)+l.right-1]))):(l.top>0&&(n=a.resolve(o+i.map[l.left])),s.bottom<i.height&&(e=a.resolve(o+i.map[i.width*(i.height-1)+s.right-1]))),new t(e,n)}isRowSelection(){let t=this.$anchorCell.node(-1),e=p.get(t),n=this.$anchorCell.start(-1),r=e.colCount(this.$anchorCell.pos-n),i=e.colCount(this.$headCell.pos-n);return!(Math.min(r,i)>0)&&Math.max(r+this.$anchorCell.nodeAfter.attrs.colspan,i+this.$headCell.nodeAfter.attrs.colspan)==e.width}eq(e){return e instanceof t&&e.$anchorCell.pos==this.$anchorCell.pos&&e.$headCell.pos==this.$headCell.pos}static rowSelection(e,n=e){let r=e.node(-1),i=p.get(r),o=e.start(-1),s=i.findCell(e.pos-o),l=i.findCell(n.pos-o),a=e.node(0);return s.left<=l.left?(s.left>0&&(e=a.resolve(o+i.map[s.top*i.width])),l.right<i.width&&(n=a.resolve(o+i.map[i.width*(l.top+1)-1]))):(l.left>0&&(n=a.resolve(o+i.map[l.top*i.width])),s.right<i.width&&(e=a.resolve(o+i.map[i.width*(s.top+1)-1]))),new t(e,n)}toJSON(){return{type:"cell",anchor:this.$anchorCell.pos,head:this.$headCell.pos}}static fromJSON(e,n){return new t(e.resolve(n.anchor),e.resolve(n.head))}static create(e,n,r=n){return new t(e.resolve(n),e.resolve(r))}getBookmark(){return new C(this.$anchorCell.pos,this.$headCell.pos)}};S.prototype.visible=!1,s.LN.jsonID("cell",S);var C=class t{constructor(t,e){this.anchor=t,this.head=e}map(e){return new t(e.map(this.anchor),e.map(this.head))}resolve(t){let e=t.resolve(this.anchor),n=t.resolve(this.head);return"row"==e.parent.type.spec.tableRole&&"row"==n.parent.type.spec.tableRole&&e.index()<e.parent.childCount&&n.index()<n.parent.childCount&&w(e,n)?new S(e,n):s.LN.near(n,1)}};function x(t){if(!(t.selection instanceof S))return null;let e=[];return t.selection.forEachCell((t,n)=>{e.push(a.NZ.node(n,n+t.nodeSize,{class:"selectedCell"}))}),a.zF.create(t.doc,e)}var M=new s.hs("fix-tables");function A(t,e){let n,r=(e,r)=>{"table"==e.type.spec.tableRole&&(n=function(t,e,n,r){let i,o,s=p.get(e);if(!s.problems)return r;r||(r=t.tr);let l=[];for(let t=0;t<s.height;t++)l.push(0);for(let t=0;t<s.problems.length;t++){let i=s.problems[t];if("collision"==i.type){let t=e.nodeAt(i.pos);if(!t)continue;let o=t.attrs;for(let t=0;t<o.rowspan;t++)l[i.row+t]+=i.n;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,b(o,o.colspan-i.n,i.n))}else if("missing"==i.type)l[i.row]+=i.n;else if("overlong_rowspan"==i.type){let t=e.nodeAt(i.pos);if(!t)continue;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,{...t.attrs,rowspan:t.attrs.rowspan-i.n})}else if("colwidth mismatch"==i.type){let t=e.nodeAt(i.pos);if(!t)continue;r.setNodeMarkup(r.mapping.map(n+1+i.pos),null,{...t.attrs,colwidth:i.colwidth})}else if("zero_sized"==i.type){let t=r.mapping.map(n);r.delete(t,t+e.nodeSize)}}for(let t=0;t<l.length;t++)l[t]&&(null==i&&(i=t),o=t);for(let a=0,h=n+1;a<s.height;a++){let n=e.child(a),s=h+n.nodeSize,c=l[a];if(c>0){let e="cell";n.firstChild&&(e=n.firstChild.type.spec.tableRole);let l=[];for(let n=0;n<c;n++){let n=d(t.schema)[e].createAndFill();n&&l.push(n)}let p=(0==a||i==a-1)&&o==a?h+1:s-1;r.insert(r.mapping.map(p),l)}h=s}return r.setMeta(M,{fixTables:!0})}(t,e,r,n))};return e?e.doc!=t.doc&&function t(e,n,r,i){let o=e.childCount,s=n.childCount;r:for(let l=0,a=0;l<s;l++){let s=n.child(l);for(let t=a,n=Math.min(o,l+3);t<n;t++)if(e.child(t)==s){a=t+1,r+=s.nodeSize;continue r}i(s,r),a<o&&e.child(a).sameMarkup(s)?t(e.child(a),s,r+1,i):s.nodesBetween(0,s.content.size,i,r+1),r+=s.nodeSize}}(e.doc,t.doc,0,r):t.doc.descendants(r),n}function T(t){let e=t.selection,n=g(t),r=n.node(-1),i=n.start(-1),o=p.get(r);return{...e instanceof S?o.rectBetween(e.$anchorCell.pos-i,e.$headCell.pos-i):o.findCell(n.pos-i),tableStart:i,map:o,table:r}}function E(t,{map:e,tableStart:n,table:r},i){let o=i>0?-1:0;(function(t,e,n){let r=d(e.type.schema).header_cell;for(let i=0;i<t.height;i++)if(e.nodeAt(t.map[n+i*t.width]).type!=r)return!1;return!0})(e,r,i+o)&&(o=0==i||i==e.width?null:0);for(let s=0;s<e.height;s++){let l=s*e.width+i;if(i>0&&i<e.width&&e.map[l-1]==e.map[l]){let o=e.map[l],a=r.nodeAt(o);t.setNodeMarkup(t.mapping.map(n+o),null,v(a.attrs,i-e.colCount(o))),s+=a.attrs.rowspan-1}else{let a=null==o?d(r.type.schema).cell:r.nodeAt(e.map[l+o]).type,h=e.positionAt(s,i,r);t.insert(t.mapping.map(n+h),a.createAndFill())}}return t}function R(t,{map:e,tableStart:n,table:r},i){var o;let s=n;for(let t=0;t<i;t++)s+=r.child(t).nodeSize;let l=[],a=i>0?-1:0;(function(t,e,n){var r;let i=d(e.type.schema).header_cell;for(let o=0;o<t.width;o++)if((null==(r=e.nodeAt(t.map[o+n*t.width]))?void 0:r.type)!=i)return!1;return!0})(e,r,i+a)&&(a=0==i||i==e.height?null:0);for(let s=0,h=e.width*i;s<e.width;s++,h++)if(i>0&&i<e.height&&e.map[h]==e.map[h-e.width]){let i=e.map[h],o=r.nodeAt(i).attrs;t.setNodeMarkup(n+i,null,{...o,rowspan:o.rowspan+1}),s+=o.colspan-1}else{let t=null==a?d(r.type.schema).cell:null==(o=r.nodeAt(e.map[h+a*e.width]))?void 0:o.type,n=null==t?void 0:t.createAndFill();n&&l.push(n)}return t.insert(s,d(r.type.schema).row.create(null,l)),t}function O(t){let e=t.content;return 1==e.childCount&&e.child(0).isTextblock&&0==e.child(0).childCount}function N(t,e){let n=t.selection;if(!(n instanceof S)||n.$anchorCell.pos==n.$headCell.pos)return!1;let r=T(t),{map:i}=r;if(function({width:t,height:e,map:n},r){let i=r.top*t+r.left,o=i,s=(r.bottom-1)*t+r.left,l=i+(r.right-r.left-1);for(let e=r.top;e<r.bottom;e++){if(r.left>0&&n[o]==n[o-1]||r.right<t&&n[l]==n[l+1])return!0;o+=t,l+=t}for(let o=r.left;o<r.right;o++){if(r.top>0&&n[i]==n[i-t]||r.bottom<e&&n[s]==n[s+t])return!0;i++,s++}return!1}(i,r))return!1;if(e){let n,o,s=t.tr,a={},h=l.FK.empty;for(let t=r.top;t<r.bottom;t++)for(let e=r.left;e<r.right;e++){let l=i.map[t*i.width+e],c=r.table.nodeAt(l);if(!a[l]&&c)if(a[l]=!0,null==n)n=l,o=c;else{O(c)||(h=h.append(c.content));let t=s.mapping.map(l+r.tableStart);s.delete(t,t+c.nodeSize)}}if(null==n||null==o)return!0;if(s.setNodeMarkup(n+r.tableStart,null,{...v(o.attrs,o.attrs.colspan,r.right-r.left-o.attrs.colspan),rowspan:r.bottom-r.top}),h.size){let t=n+1+o.content.size,e=O(o)?n+1:t;s.replaceWith(e+r.tableStart,t+r.tableStart,h)}s.setSelection(new S(s.doc.resolve(n+r.tableStart))),e(s)}return!0}function I(t,e){var n;let r=d(t.schema);return(n=({node:t})=>r[t.type.spec.tableRole],(t,e)=>{var r;let i,o,s=t.selection;if(s instanceof S){if(s.$anchorCell.pos!=s.$headCell.pos)return!1;i=s.$anchorCell.nodeAfter,o=s.$anchorCell.pos}else{if(!(i=function(t){for(let e=t.depth;e>0;e--){let n=t.node(e).type.spec.tableRole;if("cell"===n||"header_cell"===n)return t.node(e)}return null}(s.$from)))return!1;o=null==(r=f(s.$from))?void 0:r.pos}if(null==i||null==o||1==i.attrs.colspan&&1==i.attrs.rowspan)return!1;if(e){let r,l=i.attrs,a=[],h=l.colwidth;l.rowspan>1&&(l={...l,rowspan:1}),l.colspan>1&&(l={...l,colspan:1});let c=T(t),p=t.tr;for(let t=0;t<c.right-c.left;t++)a.push(h?{...l,colwidth:h&&h[t]?[h[t]]:null}:l);for(let t=c.top;t<c.bottom;t++){let e=c.map.positionAt(t,c.left,c.table);t==c.top&&(e+=i.nodeSize);for(let o=c.left,s=0;o<c.right;o++,s++)(o!=c.left||t!=c.top)&&p.insert(r=p.mapping.map(e+c.tableStart,1),n({node:i,row:t,col:o}).createAndFill(a[s]))}p.setNodeMarkup(o,n({node:i,row:c.top,col:c.left}),a[0]),s instanceof S&&p.setSelection(new S(p.doc.resolve(s.$anchorCell.pos),r?p.doc.resolve(r):void 0)),e(p)}return!0})(t,e)}function L(t,e,n){let r=e.map.cellsInRect({left:0,top:0,right:"row"==t?e.map.width:1,bottom:"column"==t?e.map.height:1});for(let t=0;t<r.length;t++){let i=e.table.nodeAt(r[t]);if(i&&i.type!==n.header_cell)return!1}return!0}function z(t,e){if((e=e||{useDeprecatedLogic:!1}).useDeprecatedLogic)return function(e,n){if(!m(e))return!1;if(n){let r=d(e.schema),i=T(e),o=e.tr,s=i.map.cellsInRect("column"==t?{left:i.left,top:0,right:i.right,bottom:i.map.height}:"row"==t?{left:0,top:i.top,right:i.map.width,bottom:i.bottom}:i),l=s.map(t=>i.table.nodeAt(t));for(let t=0;t<s.length;t++)l[t].type==r.header_cell&&o.setNodeMarkup(i.tableStart+s[t],r.cell,l[t].attrs);if(0==o.steps.length)for(let t=0;t<s.length;t++)o.setNodeMarkup(i.tableStart+s[t],r.header_cell,l[t].attrs);n(o)}return!0};return function(e,n){if(!m(e))return!1;if(n){let r=d(e.schema),i=T(e),o=e.tr,s=L("row",i,r),l=L("column",i,r),a=+!!("column"===t?s:"row"===t&&l),h="column"==t?{left:0,top:a,right:1,bottom:i.map.height}:"row"==t?{left:a,top:0,right:i.map.width,bottom:1}:i,c="column"==t?l?r.cell:r.header_cell:"row"==t?s?r.cell:r.header_cell:r.cell;i.map.cellsInRect(h).forEach(t=>{let e=t+i.tableStart,n=o.doc.nodeAt(e);n&&o.setNodeMarkup(e,c,n.attrs)}),n(o)}return!0}}z("row",{useDeprecatedLogic:!0}),z("column",{useDeprecatedLogic:!0});var $=z("cell",{useDeprecatedLogic:!0});function F(t){return function(e,n){if(!m(e))return!1;let r=function(t,e){if(e<0){let e=t.nodeBefore;if(e)return t.pos-e.nodeSize;for(let e=t.index(-1)-1,n=t.before();e>=0;e--){let r=t.node(-1).child(e),i=r.lastChild;if(i)return n-1-i.nodeSize;n-=r.nodeSize}}else{if(t.index()<t.parent.childCount-1)return t.pos+t.nodeAfter.nodeSize;let e=t.node(-1);for(let n=t.indexAfter(-1),r=t.after();n<e.childCount;n++){let t=e.child(n);if(t.childCount)return r+1;r+=t.nodeSize}}return null}(g(e),t);if(null==r)return!1;if(n){let t=e.doc.resolve(r);n(e.tr.setSelection(s.U3.between(t,t.node(0).resolve(t.pos+t.nodeAfter.nodeSize))).scrollIntoView())}return!0}}function D(t,e){let n=t.selection;if(!(n instanceof S))return!1;if(e){let r=t.tr,i=d(t.schema).cell.createAndFill().content;n.forEachCell((t,e)=>{t.content.eq(i)||r.replace(r.mapping.map(e+1),r.mapping.map(e+t.nodeSize-1),new l.Ji(i,0,0))}),r.docChanged&&e(r)}return!0}function H(t,e){let n=t.createAndFill();return new c.dL(n).replace(0,n.content.size,e).doc}function P(t,e,n,r,i,o,s,l){if(0==s||s==e.height)return!1;let a=!1;for(let h=i;h<o;h++){let i=s*e.width+h,o=e.map[i];if(e.map[i-e.width]==o){a=!0;let i=n.nodeAt(o),{top:c,left:p}=e.findCell(o);t.setNodeMarkup(t.mapping.slice(l).map(o+r),null,{...i.attrs,rowspan:s-c}),t.insert(t.mapping.slice(l).map(e.positionAt(s,p,n)),i.type.createAndFill({...i.attrs,rowspan:c+i.attrs.rowspan-s})),h+=i.attrs.colspan-1}}return a}function B(t,e,n,r,i,o,s,l){if(0==s||s==e.width)return!1;let a=!1;for(let h=i;h<o;h++){let i=h*e.width+s,o=e.map[i];if(e.map[i-1]==o){a=!0;let i=n.nodeAt(o),c=e.colCount(o),p=t.mapping.slice(l).map(o+r);t.setNodeMarkup(p,null,b(i.attrs,s-c,i.attrs.colspan-(s-c))),t.insert(p+i.nodeSize,i.type.createAndFill(b(i.attrs,0,s-c))),h+=i.attrs.rowspan-1}}return a}function J(t,e,n,r,i){let o=n?t.doc.nodeAt(n-1):t.doc;if(!o)throw Error("No table found");let s=p.get(o),{top:a,left:h}=r,c=h+i.width,u=a+i.height,f=t.tr,m=0;function g(){if(!(o=n?f.doc.nodeAt(n-1):f.doc))throw Error("No table found");s=p.get(o),m=f.mapping.maps.length}(function(t,e,n,r,i,o,s){let a,h,c=d(t.doc.type.schema);if(i>e.width)for(let o=0,s=0;o<e.height;o++){let l,p=n.child(o);s+=p.nodeSize;let d=[];l=null==p.lastChild||p.lastChild.type==c.cell?a||(a=c.cell.createAndFill()):h||(h=c.header_cell.createAndFill());for(let t=e.width;t<i;t++)d.push(l);t.insert(t.mapping.slice(0).map(s-1+r),d)}if(o>e.height){let p=[];for(let t=0,r=(e.height-1)*e.width;t<Math.max(e.width,i);t++){let i=!(t>=e.width)&&n.nodeAt(e.map[r+t]).type==c.header_cell;p.push(i?h||(h=c.header_cell.createAndFill()):a||(a=c.cell.createAndFill()))}let d=c.row.create(null,l.FK.from(p)),u=[];for(let t=e.height;t<o;t++)u.push(d);t.insert(t.mapping.slice(s).map(r+n.nodeSize-2),u)}return!!(a||h)})(f,s,o,n,c,u,0)&&g(),P(f,s,o,n,h,c,a,m)&&g(),P(f,s,o,n,h,c,u,m)&&g(),B(f,s,o,n,a,u,h,m)&&g(),B(f,s,o,n,a,u,c,m)&&g();for(let t=a;t<u;t++){let e=s.positionAt(t,h,o),r=s.positionAt(t,c,o);f.replace(f.mapping.slice(m).map(e+n),f.mapping.slice(m).map(r+n),new l.Ji(i.rows[t-a],0,0))}g(),f.setSelection(new S(f.doc.resolve(n+s.positionAt(a,h,o)),f.doc.resolve(n+s.positionAt(u-1,c-1,o)))),e(f)}var j=(0,h.K)({ArrowLeft:_("horiz",-1),ArrowRight:_("horiz",1),ArrowUp:_("vert",-1),ArrowDown:_("vert",1),"Shift-ArrowLeft":W("horiz",-1),"Shift-ArrowRight":W("horiz",1),"Shift-ArrowUp":W("vert",-1),"Shift-ArrowDown":W("vert",1),Backspace:D,"Mod-Backspace":D,Delete:D,"Mod-Delete":D});function K(t,e,n){return!n.eq(t.selection)&&(e&&e(t.tr.setSelection(n).scrollIntoView()),!0)}function _(t,e){return(n,r,i)=>{if(!i)return!1;let o=n.selection;if(o instanceof S)return K(n,r,s.LN.near(o.$headCell,e));if("horiz"!=t&&!o.empty)return!1;let l=G(i,t,e);if(null==l)return!1;if("horiz"==t)return K(n,r,s.LN.near(n.doc.resolve(o.head+e),e));{let i,o=n.doc.resolve(l),a=k(o,t,e);return i=a?s.LN.near(a,1):e<0?s.LN.near(n.doc.resolve(o.before(-1)),-1):s.LN.near(n.doc.resolve(o.after(-1)),1),K(n,r,i)}}}function W(t,e){return(n,r,i)=>{let o;if(!i)return!1;let s=n.selection;if(s instanceof S)o=s;else{let r=G(i,t,e);if(null==r)return!1;o=new S(n.doc.resolve(r))}let l=k(o.$headCell,t,e);return!!l&&K(n,r,new S(o.$anchorCell,l))}}function V(t,e){let n=f(t.state.doc.resolve(e));return!!n&&(t.dispatch(t.state.tr.setSelection(new S(n))),!0)}function U(t,e,n){if(!m(t.state))return!1;let r=function(t){if(!t.size)return null;let{content:e,openStart:n,openEnd:r}=t;for(;1==e.childCount&&(n>0&&r>0||"table"==e.child(0).type.spec.tableRole);)n--,r--,e=e.child(0).content;let i=e.child(0),o=i.type.spec.tableRole,s=i.type.schema,a=[];if("row"==o)for(let t=0;t<e.childCount;t++){let i=e.child(t).content,o=t?0:Math.max(0,n-1),h=t<e.childCount-1?0:Math.max(0,r-1);(o||h)&&(i=H(d(s).row,new l.Ji(i,o,h)).content),a.push(i)}else{if("cell"!=o&&"header_cell"!=o)return null;a.push(n||r?H(d(s).row,new l.Ji(e,n,r)).content:e)}return function(t,e){let n=[];for(let t=0;t<e.length;t++){let r=e[t];for(let e=r.childCount-1;e>=0;e--){let{rowspan:i,colspan:o}=r.child(e).attrs;for(let e=t;e<t+i;e++)n[e]=(n[e]||0)+o}}let r=0;for(let t=0;t<n.length;t++)r=Math.max(r,n[t]);for(let i=0;i<n.length;i++)if(i>=e.length&&e.push(l.FK.empty),n[i]<r){let o=d(t).cell.createAndFill(),s=[];for(let t=n[i];t<r;t++)s.push(o);e[i]=e[i].append(l.FK.from(s))}return{height:e.length,width:r,rows:e}}(s,a)}(n),i=t.state.selection;if(i instanceof S){r||(r={width:1,height:1,rows:[l.FK.from(H(d(t.state.schema).cell,n))]});let e=i.$anchorCell.node(-1),o=i.$anchorCell.start(-1),s=p.get(e).rectBetween(i.$anchorCell.pos-o,i.$headCell.pos-o);return r=function({width:t,height:e,rows:n},r,i){if(t!=r){let e=[],i=[];for(let t=0;t<n.length;t++){let o=n[t],s=[];for(let n=e[t]||0,i=0;n<r;i++){let l=o.child(i%o.childCount);n+l.attrs.colspan>r&&(l=l.type.createChecked(b(l.attrs,l.attrs.colspan,n+l.attrs.colspan-r),l.content)),s.push(l),n+=l.attrs.colspan;for(let n=1;n<l.attrs.rowspan;n++)e[t+n]=(e[t+n]||0)+l.attrs.colspan}i.push(l.FK.from(s))}n=i,t=r}if(e!=i){let t=[];for(let r=0,o=0;r<i;r++,o++){let s=[],a=n[o%e];for(let t=0;t<a.childCount;t++){let e=a.child(t);r+e.attrs.rowspan>i&&(e=e.type.create({...e.attrs,rowspan:Math.max(1,i-e.attrs.rowspan)},e.content)),s.push(e)}t.push(l.FK.from(s))}n=t,e=i}return{width:t,height:e,rows:n}}(r,s.right-s.left,s.bottom-s.top),J(t.state,t.dispatch,o,s,r),!0}if(!r)return!1;{let e=g(t.state),n=e.start(-1);return J(t.state,t.dispatch,n,p.get(e.node(-1)).findCell(e.pos-n),r),!0}}function q(t,e){var n;let r;if(e.ctrlKey||e.metaKey)return;let i=Y(t,e.target);if(e.shiftKey&&t.state.selection instanceof S)o(t.state.selection.$anchorCell,e),e.preventDefault();else if(e.shiftKey&&i&&null!=(r=f(t.state.selection.$anchor))&&(null==(n=X(t,e))?void 0:n.pos)!=r.pos)o(r,e),e.preventDefault();else if(!i)return;function o(e,n){let r=X(t,n),i=null==u.getState(t.state);if(!r||!w(e,r))if(!i)return;else r=e;let o=new S(e,r);if(i||!t.state.selection.eq(o)){let n=t.state.tr.setSelection(o);i&&n.setMeta(u,e.pos),t.dispatch(n)}}function s(){t.root.removeEventListener("mouseup",s),t.root.removeEventListener("dragstart",s),t.root.removeEventListener("mousemove",l),null!=u.getState(t.state)&&t.dispatch(t.state.tr.setMeta(u,-1))}function l(n){let r,l=u.getState(t.state);if(null!=l)r=t.state.doc.resolve(l);else if(Y(t,n.target)!=i&&!(r=X(t,e)))return s();r&&o(r,n)}t.root.addEventListener("mouseup",s),t.root.addEventListener("dragstart",s),t.root.addEventListener("mousemove",l)}function G(t,e,n){if(!(t.state.selection instanceof s.U3))return null;let{$head:r}=t.state.selection;for(let i=r.depth-1;i>=0;i--){let o=r.node(i);if((n<0?r.index(i):r.indexAfter(i))!=(n<0?0:o.childCount))break;if("cell"==o.type.spec.tableRole||"header_cell"==o.type.spec.tableRole){let o=r.before(i),s="vert"==e?n>0?"down":"up":n>0?"right":"left";return t.endOfTextblock(s)?o:null}}return null}function Y(t,e){for(;e&&e!=t.dom;e=e.parentNode)if("TD"==e.nodeName||"TH"==e.nodeName)return e;return null}function X(t,e){let n=t.posAtCoords({left:e.clientX,top:e.clientY});return n&&n?f(t.state.doc.resolve(n.pos)):null}var Z=class{constructor(t,e){this.node=t,this.defaultCellMinWidth=e,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.table.style.setProperty("--default-cell-min-width",`${e}px`),this.colgroup=this.table.appendChild(document.createElement("colgroup")),Q(t,this.colgroup,this.table,e),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(t){return t.type==this.node.type&&(this.node=t,Q(t,this.colgroup,this.table,this.defaultCellMinWidth),!0)}ignoreMutation(t){return"attributes"==t.type&&(t.target==this.table||this.colgroup.contains(t.target))}};function Q(t,e,n,r,i,o){var s;let l=0,a=!0,h=e.firstChild,c=t.firstChild;if(c){for(let t=0,n=0;t<c.childCount;t++){let{colspan:s,colwidth:p}=c.child(t).attrs;for(let t=0;t<s;t++,n++){let s=i==n?o:p&&p[t],c=s?s+"px":"";if(l+=s||r,s||(a=!1),h)h.style.width!=c&&(h.style.width=c),h=h.nextSibling;else{let t=document.createElement("col");t.style.width=c,e.appendChild(t)}}}for(;h;){let t=h.nextSibling;null==(s=h.parentNode)||s.removeChild(h),h=t}a?(n.style.width=l+"px",n.style.minWidth=""):(n.style.width="",n.style.minWidth=l+"px")}}var tt=new s.hs("tableColumnResizing"),te=class t{constructor(t,e){this.activeHandle=t,this.dragging=e}apply(e){let n=e.getMeta(tt);if(n&&null!=n.setHandle)return new t(n.setHandle,!1);if(n&&void 0!==n.setDragging)return new t(this.activeHandle,n.setDragging);if(this.activeHandle>-1&&e.docChanged){let n=e.mapping.map(this.activeHandle,-1);return y(e.doc.resolve(n))||(n=-1),new t(n,this.dragging)}return this}};function tn(t,e,n,r){let i=t.posAtCoords({left:e.clientX+("right"==n?-r:r),top:e.clientY});if(!i)return -1;let{pos:o}=i,s=f(t.state.doc.resolve(o));if(!s)return -1;if("right"==n)return s.pos;let l=p.get(s.node(-1)),a=s.start(-1),h=l.map.indexOf(s.pos-a);return h%l.width==0?-1:a+l.map[h-1]}function tr(t,e,n){let r=e.clientX-t.startX;return Math.max(n,t.startWidth+r)}function ti(t,e){t.dispatch(t.state.tr.setMeta(tt,{setHandle:e}))}function to(t,e,n,r){let i=t.state.doc.resolve(e),o=i.node(-1),s=i.start(-1),l=p.get(o).colCount(i.pos-s)+i.nodeAfter.attrs.colspan-1,a=t.domAtPos(i.start(-1)).node;for(;a&&"TABLE"!=a.nodeName;)a=a.parentNode;a&&Q(o,a.firstChild,a,r,l,n)}function ts(t,e){return e?["width",`${Math.max(e,t)}px`]:["min-width",`${t}px`]}function tl(t,e,n,r,i,o){var s;let l=0,a=!0,h=e.firstChild,c=t.firstChild;if(null!==c)for(let t=0,n=0;t<c.childCount;t+=1){let{colspan:s,colwidth:p}=c.child(t).attrs;for(let t=0;t<s;t+=1,n+=1){let s=i===n?o:p&&p[t],c=s?`${s}px`:"";if(l+=s||r,s||(a=!1),h){if(h.style.width!==c){let[t,e]=ts(r,s);h.style.setProperty(t,e)}h=h.nextSibling}else{let t=document.createElement("col"),[n,i]=ts(r,s);t.style.setProperty(n,i),e.appendChild(t)}}}for(;h;){let t=h.nextSibling;null==(s=h.parentNode)||s.removeChild(h),h=t}a?(n.style.width=`${l}px`,n.style.minWidth=""):(n.style.width="",n.style.minWidth=`${l}px`)}class ta{constructor(t,e){this.node=t,this.cellMinWidth=e,this.dom=document.createElement("div"),this.dom.className="tableWrapper",this.table=this.dom.appendChild(document.createElement("table")),this.colgroup=this.table.appendChild(document.createElement("colgroup")),tl(t,this.colgroup,this.table,e),this.contentDOM=this.table.appendChild(document.createElement("tbody"))}update(t){return t.type===this.node.type&&(this.node=t,tl(t,this.colgroup,this.table,this.cellMinWidth),!0)}ignoreMutation(t){return"attributes"===t.type&&(t.target===this.table||this.colgroup.contains(t.target))}}function th(t,e){return e?t.createChecked(null,e):t.createAndFill()}let tc=({editor:t})=>{let{selection:e}=t.state;if(!(e instanceof S))return!1;let n=0,r=(0,o.eL)(e.ranges[0].$from,t=>"table"===t.type.name);return null==r||r.node.descendants(t=>{if("table"===t.type.name)return!1;["tableCell","tableHeader"].includes(t.type.name)&&(n+=1)}),n===e.ranges.length&&(t.commands.deleteTable(),!0)},tp=o.bP.create({name:"table",addOptions:()=>({HTMLAttributes:{},resizable:!1,handleWidth:5,cellMinWidth:25,View:ta,lastColumnResizable:!0,allowTableNodeSelection:!1}),content:"tableRow+",tableRole:"table",isolating:!0,group:"block",parseHTML:()=>[{tag:"table"}],renderHTML({node:t,HTMLAttributes:e}){let{colgroup:n,tableWidth:r,tableMinWidth:i}=function(t,e,n,r){let i=0,o=!0,s=[],l=t.firstChild;if(!l)return{};for(let t=0,n=0;t<l.childCount;t+=1){let{colspan:r,colwidth:a}=l.child(t).attrs;for(let t=0;t<r;t+=1,n+=1){let r=void 0===n?void 0:a&&a[t];i+=r||e,r||(o=!1);let[l,h]=ts(e,r);s.push(["col",{style:`${l}: ${h}`}])}}return{colgroup:["colgroup",{},...s],tableWidth:o?`${i}px`:"",tableMinWidth:o?"":`${i}px`}}(t,this.options.cellMinWidth);return["table",(0,o.KV)(this.options.HTMLAttributes,e,{style:r?`width: ${r}`:`min-width: ${i}`}),n,["tbody",0]]},addCommands:()=>({insertTable:({rows:t=3,cols:e=3,withHeaderRow:n=!0}={})=>({tr:r,dispatch:i,editor:o})=>{let l=function(t,e,n,r,i){let o=function(t){if(t.cached.tableNodeTypes)return t.cached.tableNodeTypes;let e={};return Object.keys(t.nodes).forEach(n=>{let r=t.nodes[n];r.spec.tableRole&&(e[r.spec.tableRole]=r)}),t.cached.tableNodeTypes=e,e}(t),s=[],l=[];for(let t=0;t<n;t+=1){let t=th(o.cell,void 0);if(t&&l.push(t),r){let t=th(o.header_cell,void 0);t&&s.push(t)}}let a=[];for(let t=0;t<e;t+=1)a.push(o.row.createChecked(null,r&&0===t?s:l));return o.table.createChecked(null,a)}(o.schema,t,e,n);if(i){let t=r.selection.from+1;r.replaceSelectionWith(l).scrollIntoView().setSelection(s.U3.near(r.doc.resolve(t)))}return!0},addColumnBefore:()=>({state:t,dispatch:e})=>(function(t,e){if(!m(t))return!1;if(e){let n=T(t);e(E(t.tr,n,n.left))}return!0})(t,e),addColumnAfter:()=>({state:t,dispatch:e})=>(function(t,e){if(!m(t))return!1;if(e){let n=T(t);e(E(t.tr,n,n.right))}return!0})(t,e),deleteColumn:()=>({state:t,dispatch:e})=>(function(t,e){if(!m(t))return!1;if(e){let n=T(t),r=t.tr;if(0==n.left&&n.right==n.map.width)return!1;for(let t=n.right-1;!function(t,{map:e,table:n,tableStart:r},i){let o=t.mapping.maps.length;for(let s=0;s<e.height;){let l=s*e.width+i,a=e.map[l],h=n.nodeAt(a),c=h.attrs;if(i>0&&e.map[l-1]==a||i<e.width-1&&e.map[l+1]==a)t.setNodeMarkup(t.mapping.slice(o).map(r+a),null,b(c,i-e.colCount(a)));else{let e=t.mapping.slice(o).map(r+a);t.delete(e,e+h.nodeSize)}s+=c.rowspan}}(r,n,t),t!=n.left;t--){let t=n.tableStart?r.doc.nodeAt(n.tableStart-1):r.doc;if(!t)throw RangeError("No table found");n.table=t,n.map=p.get(t)}e(r)}return!0})(t,e),addRowBefore:()=>({state:t,dispatch:e})=>(function(t,e){if(!m(t))return!1;if(e){let n=T(t);e(R(t.tr,n,n.top))}return!0})(t,e),addRowAfter:()=>({state:t,dispatch:e})=>(function(t,e){if(!m(t))return!1;if(e){let n=T(t);e(R(t.tr,n,n.bottom))}return!0})(t,e),deleteRow:()=>({state:t,dispatch:e})=>(function(t,e){if(!m(t))return!1;if(e){let n=T(t),r=t.tr;if(0==n.top&&n.bottom==n.map.height)return!1;for(let t=n.bottom-1;!function(t,{map:e,table:n,tableStart:r},i){let o=0;for(let t=0;t<i;t++)o+=n.child(t).nodeSize;let s=o+n.child(i).nodeSize,l=t.mapping.maps.length;t.delete(o+r,s+r);let a=new Set;for(let o=0,s=i*e.width;o<e.width;o++,s++){let h=e.map[s];if(!a.has(h)){if(a.add(h),i>0&&h==e.map[s-e.width]){let e=n.nodeAt(h).attrs;t.setNodeMarkup(t.mapping.slice(l).map(h+r),null,{...e,rowspan:e.rowspan-1}),o+=e.colspan-1}else if(i<e.height&&h==e.map[s+e.width]){let s=n.nodeAt(h),a=s.attrs,c=s.type.create({...a,rowspan:s.attrs.rowspan-1},s.content),p=e.positionAt(i+1,o,n);t.insert(t.mapping.slice(l).map(r+p),c),o+=a.colspan-1}}}}(r,n,t),t!=n.top;t--){let t=n.tableStart?r.doc.nodeAt(n.tableStart-1):r.doc;if(!t)throw RangeError("No table found");n.table=t,n.map=p.get(n.table)}e(r)}return!0})(t,e),deleteTable:()=>({state:t,dispatch:e})=>(function(t,e){let n=t.selection.$anchor;for(let r=n.depth;r>0;r--)if("table"==n.node(r).type.spec.tableRole)return e&&e(t.tr.delete(n.before(r),n.after(r)).scrollIntoView()),!0;return!1})(t,e),mergeCells:()=>({state:t,dispatch:e})=>N(t,e),splitCell:()=>({state:t,dispatch:e})=>I(t,e),toggleHeaderColumn:()=>({state:t,dispatch:e})=>z("column")(t,e),toggleHeaderRow:()=>({state:t,dispatch:e})=>z("row")(t,e),toggleHeaderCell:()=>({state:t,dispatch:e})=>$(t,e),mergeOrSplit:()=>({state:t,dispatch:e})=>!!N(t,e)||I(t,e),setCellAttribute:(t,e)=>({state:n,dispatch:r})=>(function(t,e){return function(n,r){if(!m(n))return!1;let i=g(n);if(i.nodeAfter.attrs[t]===e)return!1;if(r){let o=n.tr;n.selection instanceof S?n.selection.forEachCell((n,r)=>{n.attrs[t]!==e&&o.setNodeMarkup(r,null,{...n.attrs,[t]:e})}):o.setNodeMarkup(i.pos,null,{...i.nodeAfter.attrs,[t]:e}),r(o)}return!0}})(t,e)(n,r),goToNextCell:()=>({state:t,dispatch:e})=>F(1)(t,e),goToPreviousCell:()=>({state:t,dispatch:e})=>F(-1)(t,e),fixTables:()=>({state:t,dispatch:e})=>(e&&A(t),!0),setCellSelection:t=>({tr:e,dispatch:n})=>{if(n){let n=S.create(e.doc,t.anchorCell,t.headCell);e.setSelection(n)}return!0}}),addKeyboardShortcuts(){return{Tab:()=>!!this.editor.commands.goToNextCell()||!!this.editor.can().addRowAfter()&&this.editor.chain().addRowAfter().goToNextCell().run(),"Shift-Tab":()=>this.editor.commands.goToPreviousCell(),Backspace:tc,"Mod-Backspace":tc,Delete:tc,"Mod-Delete":tc}},addProseMirrorPlugins(){return[...this.options.resizable&&this.editor.isEditable?[function({handleWidth:t=5,cellMinWidth:e=25,defaultCellMinWidth:n=100,View:r=Z,lastColumnResizable:i=!0}={}){let o=new s.k_({key:tt,state:{init(t,e){var i,s;let l=null==(s=null==(i=o.spec)?void 0:i.props)?void 0:s.nodeViews,a=d(e.schema).table.name;return r&&l&&(l[a]=(t,e)=>new r(t,n,e)),new te(-1,!1)},apply:(t,e)=>e.apply(t)},props:{attributes:t=>{let e=tt.getState(t);return e&&e.activeHandle>-1?{class:"resize-cursor"}:{}},handleDOMEvents:{mousemove:(e,n)=>{!function(t,e,n,r){if(!t.editable)return;let i=tt.getState(t.state);if(i&&!i.dragging){let o=function(t){for(;t&&"TD"!=t.nodeName&&"TH"!=t.nodeName;)t=t.classList&&t.classList.contains("ProseMirror")?null:t.parentNode;return t}(e.target),s=-1;if(o){let{left:r,right:i}=o.getBoundingClientRect();e.clientX-r<=n?s=tn(t,e,"left",n):i-e.clientX<=n&&(s=tn(t,e,"right",n))}if(s!=i.activeHandle){if(!r&&-1!==s){let e=t.state.doc.resolve(s),n=e.node(-1),r=p.get(n),i=e.start(-1);if(r.colCount(e.pos-i)+e.nodeAfter.attrs.colspan-1==r.width-1)return}ti(t,s)}}}(e,n,t,i)},mouseleave:t=>{!function(t){if(!t.editable)return;let e=tt.getState(t.state);e&&e.activeHandle>-1&&!e.dragging&&ti(t,-1)}(t)},mousedown:(t,r)=>{!function(t,e,n,r){var i;if(!t.editable)return;let o=null!=(i=t.dom.ownerDocument.defaultView)?i:window,s=tt.getState(t.state);if(!s||-1==s.activeHandle||s.dragging)return;let l=t.state.doc.nodeAt(s.activeHandle),a=function(t,e,{colspan:n,colwidth:r}){let i=r&&r[r.length-1];if(i)return i;let o=t.domAtPos(e),s=o.node.childNodes[o.offset].offsetWidth,l=n;if(r)for(let t=0;t<n;t++)r[t]&&(s-=r[t],l--);return s/l}(t,s.activeHandle,l.attrs);function h(e){o.removeEventListener("mouseup",h),o.removeEventListener("mousemove",c);let r=tt.getState(t.state);(null==r?void 0:r.dragging)&&(function(t,e,n){let r=t.state.doc.resolve(e),i=r.node(-1),o=p.get(i),s=r.start(-1),l=o.colCount(r.pos-s)+r.nodeAfter.attrs.colspan-1,a=t.state.tr;for(let t=0;t<o.height;t++){let e=t*o.width+l;if(t&&o.map[e]==o.map[e-o.width])continue;let r=o.map[e],h=i.nodeAt(r).attrs,c=1==h.colspan?0:l-o.colCount(r);if(h.colwidth&&h.colwidth[c]==n)continue;let p=h.colwidth?h.colwidth.slice():Array(h.colspan).fill(0);p[c]=n,a.setNodeMarkup(s+r,null,{...h,colwidth:p})}a.docChanged&&t.dispatch(a)}(t,r.activeHandle,tr(r.dragging,e,n)),t.dispatch(t.state.tr.setMeta(tt,{setDragging:null})))}function c(e){if(!e.which)return h(e);let i=tt.getState(t.state);if(i&&i.dragging){let o=tr(i.dragging,e,n);to(t,i.activeHandle,o,r)}}t.dispatch(t.state.tr.setMeta(tt,{setDragging:{startX:e.clientX,startWidth:a}})),to(t,s.activeHandle,a,r),o.addEventListener("mouseup",h),o.addEventListener("mousemove",c),e.preventDefault()}(t,r,e,n)}},decorations:t=>{let e=tt.getState(t);if(e&&e.activeHandle>-1)return function(t,e){var n;let r=[],i=t.doc.resolve(e),o=i.node(-1);if(!o)return a.zF.empty;let s=p.get(o),l=i.start(-1),h=s.colCount(i.pos-l)+i.nodeAfter.attrs.colspan-1;for(let e=0;e<s.height;e++){let i=h+e*s.width;if((h==s.width-1||s.map[i]!=s.map[i+1])&&(0==e||s.map[i]!=s.map[i-s.width])){let e=s.map[i],h=l+e+o.nodeAt(e).nodeSize-1,c=document.createElement("div");c.className="column-resize-handle",(null==(n=tt.getState(t))?void 0:n.dragging)&&r.push(a.NZ.node(l+e,l+e+o.nodeAt(e).nodeSize,{class:"column-resize-dragging"})),r.push(a.NZ.widget(h,c))}}return a.zF.create(t.doc,r)}(t,e.activeHandle)},nodeViews:{}}});return o}({handleWidth:this.options.handleWidth,cellMinWidth:this.options.cellMinWidth,defaultCellMinWidth:this.options.cellMinWidth,View:this.options.View,lastColumnResizable:this.options.lastColumnResizable})]:[],function({allowTableNodeSelection:t=!1}={}){return new s.k_({key:u,state:{init:()=>null,apply(t,e){let n=t.getMeta(u);if(null!=n)return -1==n?null:n;if(null==e||!t.docChanged)return e;let{deleted:r,pos:i}=t.mapping.mapResult(e);return r?null:i}},props:{decorations:x,handleDOMEvents:{mousedown:q},createSelectionBetween:t=>null!=u.getState(t.state)?t.state.selection:null,handleTripleClick:V,handleKeyDown:j,handlePaste:U},appendTransaction:(e,n,r)=>(function(t,e,n){let r,i,o=(e||t).selection,l=(e||t).doc;if(o instanceof s.nh&&(i=o.node.type.spec.tableRole)){if("cell"==i||"header_cell"==i)r=S.create(l,o.from);else if("row"==i){let t=l.resolve(o.from+1);r=S.rowSelection(t,t)}else if(!n){let t=p.get(o.node),e=o.from+1,n=e+t.map[t.width*t.height-1];r=S.create(l,e+1,n)}}else o instanceof s.U3&&function({$from:t,$to:e}){if(t.pos==e.pos||t.pos<e.pos-6)return!1;let n=t.pos,r=e.pos,i=t.depth;for(;i>=0&&!(t.after(i+1)<t.end(i));i--,n++);for(let t=e.depth;t>=0&&!(e.before(t+1)>e.start(t));t--,r--);return n==r&&/row|table/.test(t.node(i).type.spec.tableRole)}(o)?r=s.U3.create(l,o.from):o instanceof s.U3&&function({$from:t,$to:e}){let n,r;for(let e=t.depth;e>0;e--){let r=t.node(e);if("cell"===r.type.spec.tableRole||"header_cell"===r.type.spec.tableRole){n=r;break}}for(let t=e.depth;t>0;t--){let n=e.node(t);if("cell"===n.type.spec.tableRole||"header_cell"===n.type.spec.tableRole){r=n;break}}return n!==r&&0===e.parentOffset}(o)&&(r=s.U3.create(l,o.$from.start(),o.$from.end()));return r&&(e||(e=t.tr)).setSelection(r),e})(r,A(r,n),t)})}({allowTableNodeSelection:this.options.allowTableNodeSelection})]},extendNodeSchema(t){let e={name:t.name,options:t.options,storage:t.storage};return{tableRole:(0,o.gk)((0,o.iI)(t,"tableRole",e))}}})},90290:(t,e,n)=>{n.d(e,{$f:()=>N,G2:()=>b,I$:()=>M,Im:()=>L,Qv:()=>h,Sd:()=>w,Z1:()=>A,_G:()=>p,_e:()=>f,bh:()=>v,eB:()=>c,eT:()=>y,ec:()=>I,hy:()=>E,ic:()=>l,iz:()=>T,pC:()=>C,yY:()=>S,y_:()=>z});var r,i=n(808),o=n(10156),s=n(52571);let l=(t,e)=>!t.selection.empty&&(e&&e(t.tr.deleteSelection().scrollIntoView()),!0);function a(t,e){let{$cursor:n}=t.selection;return n&&(e?e.endOfTextblock("backward",t):!(n.parentOffset>0))?n:null}let h=(t,e,n)=>{let r=a(t,n);if(!r)return!1;let l=m(r);if(!l){let n=r.blockRange(),o=n&&(0,i.jP)(n);return null!=o&&(e&&e(t.tr.lift(n,o).scrollIntoView()),!0)}let h=l.nodeBefore;if(R(t,l,e,-1))return!0;if(0==r.parent.content.size&&(u(h,"end")||s.nh.isSelectable(h)))for(let n=r.depth;;n--){let a=(0,i.$L)(t.doc,r.before(n),r.after(n),o.Ji.empty);if(a&&a.slice.size<a.to-a.from){if(e){let n=t.tr.step(a);n.setSelection(u(h,"end")?s.LN.findFrom(n.doc.resolve(n.mapping.map(l.pos,-1)),-1):s.nh.create(n.doc,l.pos-h.nodeSize)),e(n.scrollIntoView())}return!0}if(1==n||r.node(n-1).childCount>1)break}return!!h.isAtom&&l.depth==r.depth-1&&(e&&e(t.tr.delete(l.pos-h.nodeSize,l.pos).scrollIntoView()),!0)},c=(t,e,n)=>{let r=a(t,n);if(!r)return!1;let i=m(r);return!!i&&d(t,i,e)},p=(t,e,n)=>{let r=g(t,n);if(!r)return!1;let i=k(r);return!!i&&d(t,i,e)};function d(t,e,n){let r=e.nodeBefore,l=e.pos-1;for(;!r.isTextblock;l--){if(r.type.spec.isolating)return!1;let t=r.lastChild;if(!t)return!1;r=t}let a=e.nodeAfter,h=e.pos+1;for(;!a.isTextblock;h++){if(a.type.spec.isolating)return!1;let t=a.firstChild;if(!t)return!1;a=t}let c=(0,i.$L)(t.doc,l,h,o.Ji.empty);if(!c||c.from!=l||c instanceof i.Ln&&c.slice.size>=h-l)return!1;if(n){let e=t.tr.step(c);e.setSelection(s.U3.create(e.doc,l)),n(e.scrollIntoView())}return!0}function u(t,e,n=!1){for(let r=t;r;r="start"==e?r.firstChild:r.lastChild){if(r.isTextblock)return!0;if(n&&1!=r.childCount)break}return!1}let f=(t,e,n)=>{let{$head:r,empty:i}=t.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("backward",t):r.parentOffset>0)return!1;o=m(r)}let l=o&&o.nodeBefore;return!!l&&!!s.nh.isSelectable(l)&&(e&&e(t.tr.setSelection(s.nh.create(t.doc,o.pos-l.nodeSize)).scrollIntoView()),!0)};function m(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){if(t.index(e)>0)return t.doc.resolve(t.before(e+1));if(t.node(e).type.spec.isolating)break}return null}function g(t,e){let{$cursor:n}=t.selection;return n&&(e?e.endOfTextblock("forward",t):!(n.parentOffset<n.parent.content.size))?n:null}let y=(t,e,n)=>{let r=g(t,n);if(!r)return!1;let l=k(r);if(!l)return!1;let a=l.nodeAfter;if(R(t,l,e,1))return!0;if(0==r.parent.content.size&&(u(a,"start")||s.nh.isSelectable(a))){let n=(0,i.$L)(t.doc,r.before(),r.after(),o.Ji.empty);if(n&&n.slice.size<n.to-n.from){if(e){let r=t.tr.step(n);r.setSelection(u(a,"start")?s.LN.findFrom(r.doc.resolve(r.mapping.map(l.pos)),1):s.nh.create(r.doc,r.mapping.map(l.pos))),e(r.scrollIntoView())}return!0}}return!!a.isAtom&&l.depth==r.depth-1&&(e&&e(t.tr.delete(l.pos,l.pos+a.nodeSize).scrollIntoView()),!0)},w=(t,e,n)=>{let{$head:r,empty:i}=t.selection,o=r;if(!i)return!1;if(r.parent.isTextblock){if(n?!n.endOfTextblock("forward",t):r.parentOffset<r.parent.content.size)return!1;o=k(r)}let l=o&&o.nodeAfter;return!!l&&!!s.nh.isSelectable(l)&&(e&&e(t.tr.setSelection(s.nh.create(t.doc,o.pos)).scrollIntoView()),!0)};function k(t){if(!t.parent.type.spec.isolating)for(let e=t.depth-1;e>=0;e--){let n=t.node(e);if(t.index(e)+1<n.childCount)return t.doc.resolve(t.after(e+1));if(n.type.spec.isolating)break}return null}let b=(t,e)=>{let n=t.selection,r=n instanceof s.nh,o;if(r){if(n.node.isTextblock||!(0,i.n9)(t.doc,n.from))return!1;o=n.from}else if(null==(o=(0,i.N0)(t.doc,n.from,-1)))return!1;if(e){let n=t.tr.join(o);r&&n.setSelection(s.nh.create(n.doc,o-t.doc.resolve(o).nodeBefore.nodeSize)),e(n.scrollIntoView())}return!0},v=(t,e)=>{let n=t.selection,r;if(n instanceof s.nh){if(n.node.isTextblock||!(0,i.n9)(t.doc,n.to))return!1;r=n.to}else if(null==(r=(0,i.N0)(t.doc,n.to,1)))return!1;return e&&e(t.tr.join(r).scrollIntoView()),!0},S=(t,e)=>{let{$from:n,$to:r}=t.selection,o=n.blockRange(r),s=o&&(0,i.jP)(o);return null!=s&&(e&&e(t.tr.lift(o,s).scrollIntoView()),!0)},C=(t,e)=>{let{$head:n,$anchor:r}=t.selection;return!!n.parent.type.spec.code&&!!n.sameParent(r)&&(e&&e(t.tr.insertText("\n").scrollIntoView()),!0)};function x(t){for(let e=0;e<t.edgeCount;e++){let{type:n}=t.edge(e);if(n.isTextblock&&!n.hasRequiredAttrs())return n}return null}let M=(t,e)=>{let{$head:n,$anchor:r}=t.selection;if(!n.parent.type.spec.code||!n.sameParent(r))return!1;let i=n.node(-1),o=n.indexAfter(-1),l=x(i.contentMatchAt(o));if(!l||!i.canReplaceWith(o,o,l))return!1;if(e){let r=n.after(),i=t.tr.replaceWith(r,r,l.createAndFill());i.setSelection(s.LN.near(i.doc.resolve(r),1)),e(i.scrollIntoView())}return!0},A=(t,e)=>{let n=t.selection,{$from:r,$to:i}=n;if(n instanceof s.i5||r.parent.inlineContent||i.parent.inlineContent)return!1;let o=x(i.parent.contentMatchAt(i.indexAfter()));if(!o||!o.isTextblock)return!1;if(e){let n=(!r.parentOffset&&i.index()<i.parent.childCount?r:i).pos,l=t.tr.insert(n,o.createAndFill());l.setSelection(s.U3.create(l.doc,n+1)),e(l.scrollIntoView())}return!0},T=(t,e)=>{let{$cursor:n}=t.selection;if(!n||n.parent.content.size)return!1;if(n.depth>1&&n.after()!=n.end(-1)){let r=n.before();if((0,i.zy)(t.doc,r))return e&&e(t.tr.split(r).scrollIntoView()),!0}let r=n.blockRange(),o=r&&(0,i.jP)(r);return null!=o&&(e&&e(t.tr.lift(r,o).scrollIntoView()),!0)},E=(t,e)=>{let{$from:n,to:r}=t.selection,i,o=n.sharedDepth(r);return 0!=o&&(i=n.before(o),e&&e(t.tr.setSelection(s.nh.create(t.doc,i))),!0)};function R(t,e,n,r){let l,a,h,c=e.nodeBefore,p=e.nodeAfter,d,f,m=c.type.spec.isolating||p.type.spec.isolating;if(!m&&(l=e.nodeBefore,a=e.nodeAfter,h=e.index(),l&&a&&l.type.compatibleContent(a.type)&&(!l.content.size&&e.parent.canReplace(h-1,h)?(n&&n(t.tr.delete(e.pos-l.nodeSize,e.pos).scrollIntoView()),!0):!!e.parent.canReplace(h,h+1)&&!!(a.isTextblock||(0,i.n9)(t.doc,e.pos))&&(n&&n(t.tr.join(e.pos).scrollIntoView()),!0))))return!0;let g=!m&&e.parent.canReplace(e.index(),e.index()+1);if(g&&(d=(f=c.contentMatchAt(c.childCount)).findWrapping(p.type))&&f.matchType(d[0]||p.type).validEnd){if(n){let r=e.pos+p.nodeSize,s=o.FK.empty;for(let t=d.length-1;t>=0;t--)s=o.FK.from(d[t].create(null,s));s=o.FK.from(c.copy(s));let l=t.tr.step(new i.Wg(e.pos-1,r,e.pos,r,new o.Ji(s,1,0),d.length,!0)),a=l.doc.resolve(r+2*d.length);a.nodeAfter&&a.nodeAfter.type==c.type&&(0,i.n9)(l.doc,a.pos)&&l.join(a.pos),n(l.scrollIntoView())}return!0}let y=p.type.spec.isolating||r>0&&m?null:s.LN.findFrom(e,1),w=y&&y.$from.blockRange(y.$to),k=w&&(0,i.jP)(w);if(null!=k&&k>=e.depth)return n&&n(t.tr.lift(w,k).scrollIntoView()),!0;if(g&&u(p,"start",!0)&&u(c,"end")){let r=c,s=[];for(;s.push(r),!r.isTextblock;)r=r.lastChild;let l=p,a=1;for(;!l.isTextblock;l=l.firstChild)a++;if(r.canReplace(r.childCount,r.childCount,l.content)){if(n){let r=o.FK.empty;for(let t=s.length-1;t>=0;t--)r=o.FK.from(s[t].copy(r));n(t.tr.step(new i.Wg(e.pos-s.length,e.pos+p.nodeSize,e.pos+a,e.pos+p.nodeSize-a,new o.Ji(r,s.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function O(t){return function(e,n){let r=e.selection,i=t<0?r.$from:r.$to,o=i.depth;for(;i.node(o).isInline;){if(!o)return!1;o--}return!!i.node(o).isTextblock&&(n&&n(e.tr.setSelection(s.U3.create(e.doc,t<0?i.start(o):i.end(o)))),!0)}}let N=O(-1),I=O(1);function L(t,e=null){return function(n,r){let{$from:o,$to:s}=n.selection,l=o.blockRange(s),a=l&&(0,i.oM)(l,t,e);return!!a&&(r&&r(n.tr.wrap(l,a).scrollIntoView()),!0)}}function z(t,e=null){return function(n,r){let i=!1;for(let r=0;r<n.selection.ranges.length&&!i;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];n.doc.nodesBetween(o,s,(r,o)=>{if(i)return!1;if(!(!r.isTextblock||r.hasMarkup(t,e)))if(r.type==t)i=!0;else{let e=n.doc.resolve(o),r=e.index();i=e.parent.canReplaceWith(r,r+1,t)}})}if(!i)return!1;if(r){let i=n.tr;for(let r=0;r<n.selection.ranges.length;r++){let{$from:{pos:o},$to:{pos:s}}=n.selection.ranges[r];i.setBlockType(o,s,t,e)}r(i.scrollIntoView())}return!0}}function $(...t){return function(e,n,r){for(let i=0;i<t.length;i++)if(t[i](e,n,r))return!0;return!1}}let F=$(l,h,f),D=$(l,y,w),H={Enter:$(C,A,T,(t,e)=>{let{$from:n,$to:r}=t.selection;if(t.selection instanceof s.nh&&t.selection.node.isBlock)return!!n.parentOffset&&!!(0,i.zy)(t.doc,n.pos)&&(e&&e(t.tr.split(n.pos).scrollIntoView()),!0);if(!n.depth)return!1;let o=[],l,a,h=!1,c=!1;for(let t=n.depth;;t--){if(n.node(t).isBlock){let e;h=n.end(t)==n.pos+(n.depth-t),c=n.start(t)==n.pos-(n.depth-t),a=x(n.node(t-1).contentMatchAt(n.indexAfter(t-1)));o.unshift(e||(h&&a?{type:a}:null)),l=t;break}if(1==t)return!1;o.unshift(null)}let p=t.tr;(t.selection instanceof s.U3||t.selection instanceof s.i5)&&p.deleteSelection();let d=p.mapping.map(n.pos),u=(0,i.zy)(p.doc,d,o.length,o);if(u||(o[0]=a?{type:a}:null,u=(0,i.zy)(p.doc,d,o.length,o)),!u)return!1;if(p.split(d,o.length,o),!h&&c&&n.node(l).type!=a){let t=p.mapping.map(n.before(l)),e=p.doc.resolve(t);a&&n.node(l-1).canReplaceWith(e.index(),e.index()+1,a)&&p.setNodeMarkup(p.mapping.map(n.before(l)),a)}return e&&e(p.scrollIntoView()),!0}),"Mod-Enter":M,Backspace:F,"Mod-Backspace":F,"Shift-Backspace":F,Delete:D,"Mod-Delete":D,"Mod-a":(t,e)=>(e&&e(t.tr.setSelection(new s.i5(t.doc))),!0)},P={"Ctrl-h":H.Backspace,"Alt-Backspace":H["Mod-Backspace"],"Ctrl-d":H.Delete,"Ctrl-Alt-Backspace":H["Mod-Delete"],"Alt-Delete":H["Mod-Delete"],"Alt-d":H["Mod-Delete"],"Ctrl-a":N,"Ctrl-e":I};for(let t in H)P[t]=H[t];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform()},92406:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Heading3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]])},93654:(t,e,n)=>{n.d(e,{A:()=>r});let r=(0,n(90163).A)("Undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},96770:(t,e,n)=>{n.d(e,{K:()=>f,w:()=>u});for(var r={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},i={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},o="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),s="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),l=0;l<10;l++)r[48+l]=r[96+l]=String(l);for(var l=1;l<=24;l++)r[l+111]="F"+l;for(var l=65;l<=90;l++)r[l]=String.fromCharCode(l+32),i[l]=String.fromCharCode(l);for(var a in r)i.hasOwnProperty(a)||(i[a]=r[a]);var h=n(52571);let c="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),p="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function d(t,e,n=!0){return e.altKey&&(t="Alt-"+t),e.ctrlKey&&(t="Ctrl-"+t),e.metaKey&&(t="Meta-"+t),n&&e.shiftKey&&(t="Shift-"+t),t}function u(t){return new h.k_({props:{handleKeyDown:f(t)}})}function f(t){let e=function(t){let e=Object.create(null);for(let n in t)e[function(t){let e,n,r,i,o=t.split(/-(?!$)/),s=o[o.length-1];"Space"==s&&(s=" ");for(let t=0;t<o.length-1;t++){let s=o[t];if(/^(cmd|meta|m)$/i.test(s))i=!0;else if(/^a(lt)?$/i.test(s))e=!0;else if(/^(c|ctrl|control)$/i.test(s))n=!0;else if(/^s(hift)?$/i.test(s))r=!0;else if(/^mod$/i.test(s))c?i=!0:n=!0;else throw Error("Unrecognized modifier name: "+s)}return e&&(s="Alt-"+s),n&&(s="Ctrl-"+s),i&&(s="Meta-"+s),r&&(s="Shift-"+s),s}(n)]=t[n];return e}(t);return function(t,n){var l;let a=("Esc"==(l=!(o&&n.metaKey&&n.shiftKey&&!n.ctrlKey&&!n.altKey||s&&n.shiftKey&&n.key&&1==n.key.length||"Unidentified"==n.key)&&n.key||(n.shiftKey?i:r)[n.keyCode]||n.key||"Unidentified")&&(l="Escape"),"Del"==l&&(l="Delete"),"Left"==l&&(l="ArrowLeft"),"Up"==l&&(l="ArrowUp"),"Right"==l&&(l="ArrowRight"),"Down"==l&&(l="ArrowDown"),l),h,c=e[d(a,n)];if(c&&c(t.state,t.dispatch,t))return!0;if(1==a.length&&" "!=a){if(n.shiftKey){let r=e[d(a,n,!1)];if(r&&r(t.state,t.dispatch,t))return!0}if((n.altKey||n.metaKey||n.ctrlKey)&&!(p&&n.ctrlKey&&n.altKey)&&(h=r[n.keyCode])&&h!=a){let r=e[d(h,n)];if(r&&r(t.state,t.dispatch,t))return!0}}return!1}}}}]);