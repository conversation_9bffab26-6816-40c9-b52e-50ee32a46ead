(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9678],{11518:(e,t,s)=>{"use strict";e.exports=s(82269).style},19678:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(95155),n=s(11518),a=s.n(n),i=s(12115);function o(){let[e,t]=(0,i.useState)(0),[s,n]=(0,i.useState)([]),[o,l]=(0,i.useState)("Scanning"),[d,c]=(0,i.useState)(0),[f,u]=(0,i.useState)(94.7),[m,h]=(0,i.useState)(127),x=(0,i.useRef)(null),p=["Deep Scanning","Data Analysis","Pattern Recognition","Content Synthesis","Quality Optimization"],y=["Analyzing market trend patterns...","Processing competitor intelligence...","Extracting user behavior insights...","Evaluating content performance metrics...","Optimizing SEO keyword strategies...","Monitoring social engagement data...","Researching industry benchmarks...","Assessing consumer sentiment analysis...","Tracking conversion rate patterns...","Generating actionable recommendations..."];return(0,i.useEffect)(()=>(x.current=setInterval(()=>{t(e=>{let t=e+12*Math.random()+3;return t>=100?(c(e=>e+Math.floor(3*Math.random())+1),u(e=>Math.min(99.9,e+.8*Math.random())),h(e=>e+Math.floor(15*Math.random())+5),0):t}),l(p[Math.floor(Math.random()*p.length)]);let e={id:Date.now(),text:y[Math.floor(Math.random()*y.length)],speed:2*Math.random()+1};n(t=>[e,...t.slice(0,2)])},1800),()=>{x.current&&clearInterval(x.current)}),[]),(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 relative w-full h-full flex items-center justify-center p-4",children:[(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 absolute inset-0 bg-gradient-to-br from-gray-900/40 via-black/60 to-gray-900/40 backdrop-blur-2xl"}),(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 relative z-10 w-full max-w-lg",children:[(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 w-full h-[320px] bg-black/30 backdrop-blur-2xl rounded-xl border border-white/10 shadow-2xl relative overflow-hidden",children:[(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 h-12 bg-black/50 backdrop-blur-2xl border-b border-white/5 flex items-center justify-between px-4",children:[(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 w-8 h-8 bg-violet-500/20 backdrop-blur-xl rounded-lg flex items-center justify-center border border-violet-500/30",children:(0,r.jsx)("span",{className:"jsx-1d01a1c8d4f34203 text-violet-400 font-bold text-sm",children:"\uD83E\uDDE0"})}),(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203",children:[(0,r.jsx)("h3",{className:"jsx-1d01a1c8d4f34203 text-white/90 font-bold text-sm",children:"INVINCIBLE RESEARCH"}),(0,r.jsx)("p",{className:"jsx-1d01a1c8d4f34203 text-white/60 text-xs",children:"Advanced AI Analysis Engine"})]})]}),(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 w-2 h-2 bg-green-400/60 rounded-full animate-pulse shadow-lg"}),(0,r.jsx)("span",{className:"jsx-1d01a1c8d4f34203 text-white/80 text-xs font-medium",children:"PROCESSING"})]})]}),(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 p-4 space-y-3",children:[(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3",children:[(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 flex justify-between items-center mb-2",children:[(0,r.jsx)("span",{className:"jsx-1d01a1c8d4f34203 text-violet-300/80 font-semibold text-sm",children:"Research Progress"}),(0,r.jsxs)("span",{className:"jsx-1d01a1c8d4f34203 text-white/90 font-bold text-sm",children:[Math.round(e),"%"]})]}),(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 w-full bg-black/40 rounded-full h-3 overflow-hidden",children:(0,r.jsxs)("div",{style:{width:"".concat(e,"%")},className:"jsx-1d01a1c8d4f34203 bg-gradient-to-r from-violet-500/80 to-purple-500/80 h-3 rounded-full transition-all duration-500 relative",children:[(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 absolute inset-0 bg-white/20 animate-pulse rounded-full"}),(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"})]})}),(0,r.jsxs)("p",{className:"jsx-1d01a1c8d4f34203 text-white/60 text-xs mt-2 font-medium",children:["Current Phase: ",o]})]}),(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 h-24 overflow-hidden",children:[(0,r.jsx)("h4",{className:"jsx-1d01a1c8d4f34203 text-violet-300/80 font-semibold text-sm mb-2",children:"Live Data Streams"}),(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 space-y-1",children:s.map((e,t)=>(0,r.jsxs)("div",{style:{opacity:1-.4*t,animation:"slideUp 0.6s ease-out",animationDelay:"".concat(.1*t,"s")},className:"jsx-1d01a1c8d4f34203 text-xs text-emerald-300/80 transition-all duration-500 flex items-center space-x-1",children:[(0,r.jsx)("span",{className:"jsx-1d01a1c8d4f34203 text-violet-400/80 font-bold",children:"▶"}),(0,r.jsx)("span",{className:"jsx-1d01a1c8d4f34203 font-medium truncate",children:e.text})]},e.id))})]}),(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 flex space-x-2",children:[(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 flex-1 text-center",children:[(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 text-xl font-bold text-violet-400/90 animate-pulse mb-1",children:d}),(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 text-white/50 text-xs font-medium",children:"Insights"})]}),(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 flex-1 text-center",children:[(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 text-xl font-bold text-purple-400/90 animate-pulse mb-1",children:m}),(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 text-white/50 text-xs font-medium",children:"Sources"})]}),(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 bg-black/30 backdrop-blur-2xl rounded-lg border border-white/10 p-3 flex-1 text-center",children:[(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 text-xl font-bold text-indigo-400/90 animate-pulse mb-1",children:[f.toFixed(1),"%"]}),(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 text-white/50 text-xs font-medium",children:"Accuracy"})]})]})]}),(0,r.jsxs)("div",{className:"jsx-1d01a1c8d4f34203 absolute inset-0 pointer-events-none",children:[(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 absolute top-0 left-0 w-full h-0.5 bg-gradient-to-r from-transparent via-violet-400 to-transparent animate-pulse"}),(0,r.jsx)("div",{style:{animation:"scan 4s linear infinite"},className:"jsx-1d01a1c8d4f34203 absolute left-0 w-0.5 h-full bg-gradient-to-b from-transparent via-violet-400 to-transparent opacity-60"})]}),(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 absolute inset-0 bg-gradient-to-br from-white/5 to-transparent pointer-events-none"})]}),(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 absolute -top-2 -left-2 bg-violet-500/20 backdrop-blur-2xl text-violet-400 px-3 py-1 rounded-full text-xs font-semibold border border-violet-500/20 animate-float",children:"\uD83D\uDD0D Deep Scan"}),(0,r.jsx)("div",{style:{animationDelay:"0.3s"},className:"jsx-1d01a1c8d4f34203 absolute -top-1 -right-3 bg-purple-500/20 backdrop-blur-2xl text-purple-400 px-3 py-1 rounded-full text-xs font-semibold border border-purple-500/20 animate-float",children:"\uD83D\uDCCA Analytics"}),(0,r.jsx)("div",{style:{animationDelay:"0.6s"},className:"jsx-1d01a1c8d4f34203 absolute -bottom-2 -left-3 bg-indigo-500/20 backdrop-blur-2xl text-indigo-400 px-3 py-1 rounded-full text-xs font-semibold border border-indigo-500/20 animate-float",children:"\uD83E\uDDE0 AI Engine"}),(0,r.jsx)("div",{style:{animationDelay:"0.9s"},className:"jsx-1d01a1c8d4f34203 absolute -bottom-1 -right-2 bg-violet-600/20 backdrop-blur-2xl text-violet-400 px-3 py-1 rounded-full text-xs font-semibold border border-violet-600/20 animate-float",children:"⚡ Results"}),(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none",children:(0,r.jsx)("div",{className:"jsx-1d01a1c8d4f34203 w-4 h-4 bg-violet-400 rounded-full animate-ping opacity-75"})})]}),(0,r.jsx)(a(),{id:"1d01a1c8d4f34203",children:"@-webkit-keyframes slideUp{0%{-webkit-transform:translatey(10px);transform:translatey(10px);opacity:0}100%{-webkit-transform:translatey(0);transform:translatey(0);opacity:1}}@-moz-keyframes slideUp{0%{-moz-transform:translatey(10px);transform:translatey(10px);opacity:0}100%{-moz-transform:translatey(0);transform:translatey(0);opacity:1}}@-o-keyframes slideUp{0%{-o-transform:translatey(10px);transform:translatey(10px);opacity:0}100%{-o-transform:translatey(0);transform:translatey(0);opacity:1}}@keyframes slideUp{0%{-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px);opacity:0}100%{-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0);opacity:1}}@-webkit-keyframes scan{0%{left:0%}100%{left:100%}}@-moz-keyframes scan{0%{left:0%}100%{left:100%}}@-o-keyframes scan{0%{left:0%}100%{left:100%}}@keyframes scan{0%{left:0%}100%{left:100%}}@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-5px);transform:translatey(-5px)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px);transform:translatey(0px)}50%{-moz-transform:translatey(-5px);transform:translatey(-5px)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px);transform:translatey(0px)}50%{-o-transform:translatey(-5px);transform:translatey(-5px)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px);-moz-transform:translatey(0px);-o-transform:translatey(0px);transform:translatey(0px)}50%{-webkit-transform:translatey(-5px);-moz-transform:translatey(-5px);-o-transform:translatey(-5px);transform:translatey(-5px)}}@-webkit-keyframes shimmer{0%{-webkit-transform:translatex(-100%);transform:translatex(-100%)}100%{-webkit-transform:translatex(100%);transform:translatex(100%)}}@-moz-keyframes shimmer{0%{-moz-transform:translatex(-100%);transform:translatex(-100%)}100%{-moz-transform:translatex(100%);transform:translatex(100%)}}@-o-keyframes shimmer{0%{-o-transform:translatex(-100%);transform:translatex(-100%)}100%{-o-transform:translatex(100%);transform:translatex(100%)}}@keyframes shimmer{0%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%)}100%{-webkit-transform:translatex(100%);-moz-transform:translatex(100%);-o-transform:translatex(100%);transform:translatex(100%)}}.animate-float.jsx-1d01a1c8d4f34203{-webkit-animation:float 3s ease-in-out infinite;-moz-animation:float 3s ease-in-out infinite;-o-animation:float 3s ease-in-out infinite;animation:float 3s ease-in-out infinite}.animate-shimmer.jsx-1d01a1c8d4f34203{-webkit-animation:shimmer 2s linear infinite;-moz-animation:shimmer 2s linear infinite;-o-animation:shimmer 2s linear infinite;animation:shimmer 2s linear infinite}"})]})}function l(){return(0,r.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-gray-900 via-violet-900 to-purple-900",children:(0,r.jsx)(o,{})})}},68375:()=>{},82269:(e,t,s)=>{"use strict";var r=s(87358);s(68375);var n=s(12115),a=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(n),i=void 0!==r&&r.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,s=t.name,r=void 0===s?"stylesheet":s,n=t.optimizeForSpeed,a=void 0===n?i:n;d(o(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",d("boolean"==typeof a,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=a,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,s=e.prototype;return s.setOptimizeForSpeed=function(e){d("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),d(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},s.isOptimizeForSpeed=function(){return this._optimizeForSpeed},s.inject=function(){var e=this;if(d(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(i||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,s){return"number"==typeof s?e._serverSheet.cssRules[s]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),s},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},s.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},s.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},s.insertRule=function(e,t){if(d(o(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var s=this.getSheet();"number"!=typeof t&&(t=s.cssRules.length);try{s.insertRule(e,t)}catch(t){return i||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},s.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var s="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!s.cssRules[e])return e;s.deleteRule(e);try{s.insertRule(t,e)}catch(r){i||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),s.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];d(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},s.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];d(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},s.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},s.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,s){return s?t=t.concat(Array.prototype.map.call(e.getSheetForTag(s).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},s.makeStyleTag=function(e,t,s){t&&d(o(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var n=document.head||document.getElementsByTagName("head")[0];return s?n.insertBefore(r,s):n.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var s=0;s<t.length;s++){var r=t[s];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function d(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,s=e.length;s;)t=33*t^e.charCodeAt(--s);return t>>>0},f={};function u(e,t){if(!t)return"jsx-"+e;var s=String(t),r=e+s;return f[r]||(f[r]="jsx-"+c(e+"-"+s)),f[r]}function m(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var s=e+t;return f[s]||(f[s]=t.replace(/__jsx-style-dynamic-selector/g,e)),f[s]}var h=function(){function e(e){var t=void 0===e?{}:e,s=t.styleSheet,r=void 0===s?null:s,n=t.optimizeForSpeed,a=void 0!==n&&n;this._sheet=r||new l({name:"styled-jsx",optimizeForSpeed:a}),this._sheet.inject(),r&&"boolean"==typeof a&&(this._sheet.setOptimizeForSpeed(a),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var s=this.getIdAndRules(e),r=s.styleId,n=s.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var a=n.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=a,this._instancesCounts[r]=1},t.remove=function(e){var t=this,s=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(s in this._instancesCounts,"styleId: `"+s+"` not found"),this._instancesCounts[s]-=1,this._instancesCounts[s]<1){var r=this._fromServer&&this._fromServer[s];r?(r.parentNode.removeChild(r),delete this._fromServer[s]):(this._indices[s].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[s]),delete this._instancesCounts[s]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],s=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return s[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,s;return t=this.cssRules(),void 0===(s=e)&&(s={}),t.map(function(e){var t=e[0],r=e[1];return a.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:s.nonce?s.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,s=e.dynamic,r=e.id;if(s){var n=u(r,s);return{styleId:n,rules:Array.isArray(t)?t.map(function(e){return m(n,e)}):[m(n,t)]}}return{styleId:u(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),x=n.createContext(null);x.displayName="StyleSheetContext";var p=a.default.useInsertionEffect||a.default.useLayoutEffect,y="undefined"!=typeof window?new h:void 0;function b(e){var t=y||n.useContext(x);return t&&("undefined"==typeof window?t.add(e):p(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}b.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=b}}]);