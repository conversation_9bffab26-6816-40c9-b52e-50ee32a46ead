'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { 
  ArrowLeft, 
  Sparkles, 
  Brain, 
  Zap, 
  Target, 
  Search, 
  TrendingUp, 
  Eye, 
  Rocket,
  Plus,
  X,
  Settings,
  Play,
  ChevronRight,
  Shield,
  Crown,
  Lightbulb,
  FileText,
  Users,
  Globe,
  Clock,
  BarChart,
  Monitor,
  Activity,
  Cpu,
  Database,
  Network,
  CheckCircle2,
  AlertCircle,
  Loader2,
  Pause,
  RotateCcw
} from 'lucide-react';
import { cn } from '@/lib/utils';
import InvincibleV2StreamingUI from '@/components/InvincibleV2StreamingUI';

interface InvincibleV2Config {
  topic: string;
  customInstructions?: string;
  targetAudience?: string;
  contentLength?: number;
  tone?: string;
  keywords?: string[];
  autonomyLevel?: 'full' | 'guided' | 'supervised';
  confidenceThreshold?: number;
  enableWebSearch?: boolean;
  enableSelfImprovement?: boolean;
  enableVisualization?: boolean;
  enableMemoryConsolidation?: boolean;
}

export default function InvincibleV2Page() {
  const router = useRouter();
  const { data: session, status } = useSession();
  const [config, setConfig] = useState<InvincibleV2Config>({
    topic: '',
    customInstructions: '',
    targetAudience: 'general audience',
    contentLength: 3000,
    tone: 'professional',
    keywords: [],
    autonomyLevel: 'full',
    confidenceThreshold: 0.95,
    enableWebSearch: true,
    enableSelfImprovement: true,
    enableVisualization: true,
    enableMemoryConsolidation: true
  });
  const [keywordInput, setKeywordInput] = useState('');
  const [isStreaming, setIsStreaming] = useState(false);
  const [showStreamingUI, setShowStreamingUI] = useState(false);
  const [streamingResult, setStreamingResult] = useState<any>(null);
  const [error, setError] = useState('');
  const [isSaving, setIsSaving] = useState(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated') {
      window.location.href = '/login'
    }
  }, [status])

  // Loading state
  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="relative">
            <div className="animate-spin w-16 h-16 border-4 border-purple-400 border-t-transparent rounded-full mx-auto"></div>
            <Brain className="absolute inset-0 m-auto w-6 h-6 text-purple-400" />
          </div>
          <p className="text-gray-400">Initializing Autonomous Agent...</p>
        </div>
      </div>
    )
  }

  // Don't render if not authenticated
  if (status === 'unauthenticated') {
    return null
  }

  const handleStartStreaming = () => {
    if (!config.topic.trim()) {
      setError('Please enter a topic for autonomous generation');
      return;
    }
    setError('');
    setShowStreamingUI(true);
    setIsStreaming(true);
  };

  const handleStreamingComplete = async (result: any) => {
    setStreamingResult(result);
    setIsStreaming(false);
    setIsSaving(true);

    try {
      // Save the article to the database
      const response = await fetch('/api/articles/store', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: result.article?.title || `Autonomous Article: ${config.topic}`,
          content: result.article?.content || result.content || '',
          type: 'invincible-v2',
          metadata: {
            topic: config.topic,
            tone: config.tone,
            targetAudience: config.targetAudience,
            contentLength: config.contentLength,
            customInstructions: config.customInstructions,
            keywords: config.keywords,
            autonomyLevel: config.autonomyLevel,
            confidenceThreshold: config.confidenceThreshold,
            executionTime: result.metrics?.executionTime,
            agentInvocations: result.metrics?.agentInvocations,
            toolUsage: result.metrics?.toolUsage,
            confidence: result.confidence,
            improvements: result.improvements,
            seoScore: result.article?.seoScore,
            readabilityScore: result.article?.readabilityScore,
            originalityScore: result.article?.originalityScore,
            competitorSuperiority: result.article?.competitorSuperiority,
            generatedAt: new Date().toISOString(),
            version: '2.0'
          },
          tone: config.tone,
          language: 'en'
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to save article: ${errorText}`);
      }

      const saveResult = await response.json();
      
      if (saveResult.success) {
        // Small delay to show saving completion
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Redirect to the article view page with the saved article ID
        router.push(`/article-view/${saveResult.article.id}`);
      } else {
        throw new Error(saveResult.error || 'Failed to save article');
      }
    } catch (error) {
      console.error('Error saving article:', error);
      setIsSaving(false);
      setError(`Article generated successfully but failed to save: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again.`);
    }
  };

  const handleStreamingError = (error: string) => {
    setError(error);
    setIsStreaming(false);
  };

  const addKeyword = () => {
    if (keywordInput.trim() && !config.keywords?.includes(keywordInput.trim())) {
      setConfig({
        ...config,
        keywords: [...(config.keywords || []), keywordInput.trim()]
      });
      setKeywordInput('');
    }
  };

  const removeKeyword = (keyword: string) => {
    setConfig({
      ...config,
      keywords: config.keywords?.filter(k => k !== keyword) || []
    });
  };

  const resetToConfiguration = () => {
    setShowStreamingUI(false);
    setIsStreaming(false);
    setStreamingResult(null);
    setError('');
    setIsSaving(false);
  };

  // If showing streaming UI, render that component
  if (showStreamingUI) {
    return (
      <InvincibleV2StreamingUI
        config={config}
        onComplete={handleStreamingComplete}
        onError={handleStreamingError}
        onReset={resetToConfiguration}
        isSaving={isSaving}
      />
    );
  }

  return (
    <div className="min-h-screen bg-black">
      {/* Enhanced Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-950/20 via-black to-indigo-950/20" />
        
        {/* Animated Neural Network Background */}
        <motion.div
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/4 left-1/6 w-96 h-96 bg-purple-600/10 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.1, 1, 1.1],
            opacity: [0.4, 0.6, 0.4],
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute bottom-1/3 right-1/6 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[500px] h-[500px] bg-pink-600/5 rounded-full blur-3xl"
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10">
        {/* Header */}
        <motion.header 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="border-b border-white/10 backdrop-blur-xl bg-black/50"
        >
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link 
                  href="/dashboard"
                  className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors group"
                >
                  <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
                  <span>Dashboard</span>
                </Link>
                <div className="w-px h-6 bg-white/20" />
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl blur-lg opacity-60" />
                    <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                      <Brain className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
                      Invincible V2
                    </h1>
                    <p className="text-sm text-gray-500">Autonomous Content Generation Agent</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <div className="flex items-center space-x-2 text-sm text-gray-400">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                  <span>Agent System Online</span>
                </div>
              </div>
            </div>
          </div>
        </motion.header>

        {/* Hero Section */}
        <motion.section 
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="max-w-7xl mx-auto px-6 py-12"
        >
          <div className="text-center space-y-6 mb-12">
            <div className="inline-flex items-center space-x-2 bg-purple-900/30 px-4 py-2 rounded-full border border-purple-500/30">
              <Crown className="w-4 h-4 text-purple-400" />
              <span className="text-purple-300 text-sm font-medium">Next-Generation Autonomous System</span>
            </div>
            
            <h2 className="text-4xl md:text-6xl font-bold text-white leading-tight">
              Create <span className="bg-gradient-to-r from-purple-400 via-pink-400 to-indigo-400 bg-clip-text text-transparent">Superior</span> Content
            </h2>
            
            <p className="text-xl text-gray-400 max-w-3xl mx-auto leading-relaxed">
              Deploy a fully autonomous multi-agent system that researches, plans, generates, and verifies content 
              with <span className="text-purple-400 font-semibold">guaranteed superiority</span> over competition.
            </p>

            {/* Enhanced Feature Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
              {[
                { icon: Brain, label: 'Autonomous', desc: 'Self-directed reasoning', color: 'purple' },
                { icon: Network, label: 'Multi-Agent', desc: '6 specialized agents', color: 'blue' },
                { icon: TrendingUp, label: 'Self-Improving', desc: 'Learns from experience', color: 'green' },
                { icon: Shield, label: 'Guaranteed', desc: '95%+ confidence', color: 'orange' }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className={cn(
                    "bg-gradient-to-br from-gray-900/50 to-gray-800/30 backdrop-blur-sm border rounded-xl p-4 hover:border-white/20 transition-all duration-300",
                    feature.color === 'purple' && "border-purple-500/20 hover:border-purple-400/40",
                    feature.color === 'blue' && "border-blue-500/20 hover:border-blue-400/40",
                    feature.color === 'green' && "border-green-500/20 hover:border-green-400/40",
                    feature.color === 'orange' && "border-orange-500/20 hover:border-orange-400/40"
                  )}
                >
                  <feature.icon className={cn(
                    "w-8 h-8 mb-2",
                    feature.color === 'purple' && "text-purple-400",
                    feature.color === 'blue' && "text-blue-400",
                    feature.color === 'green' && "text-green-400",
                    feature.color === 'orange' && "text-orange-400"
                  )} />
                  <h3 className="font-semibold text-white text-sm">{feature.label}</h3>
                  <p className="text-xs text-gray-400 mt-1">{feature.desc}</p>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Configuration Form */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="max-w-4xl mx-auto"
          >
            <div className="bg-gradient-to-br from-gray-900/80 via-gray-800/60 to-gray-900/80 backdrop-blur-xl border border-white/10 rounded-2xl p-8 shadow-2xl">
              <div className="space-y-8">
                {/* Topic Input */}
                <div className="space-y-3">
                  <label className="block text-lg font-semibold text-white">
                    Research Topic
                  </label>
                  <div className="relative">
                    <input
                      type="text"
                      value={config.topic}
                      onChange={(e) => setConfig({ ...config, topic: e.target.value })}
                      placeholder="Enter any topic for autonomous content generation..."
                      className="w-full bg-black/50 border border-white/20 rounded-xl px-4 py-4 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none focus:ring-2 focus:ring-purple-400/20 transition-all text-lg"
                    />
                    <Lightbulb className="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  </div>
                </div>

                {/* Advanced Configuration Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Left Column */}
                  <div className="space-y-6">
                    <div className="space-y-3">
                      <label className="block font-medium text-gray-300">Target Audience</label>
                      <select
                        value={config.targetAudience}
                        onChange={(e) => setConfig({ ...config, targetAudience: e.target.value })}
                        className="w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all"
                      >
                        <option value="general audience">General Audience</option>
                        <option value="professionals">Industry Professionals</option>
                        <option value="experts">Subject Matter Experts</option>
                        <option value="beginners">Beginners</option>
                        <option value="students">Students & Academics</option>
                        <option value="business leaders">Business Leaders</option>
                      </select>
                    </div>

                    <div className="space-y-3">
                      <label className="block font-medium text-gray-300">Content Tone</label>
                      <select
                        value={config.tone}
                        onChange={(e) => setConfig({ ...config, tone: e.target.value })}
                        className="w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all"
                      >
                        <option value="professional">Professional</option>
                        <option value="conversational">Conversational</option>
                        <option value="academic">Academic</option>
                        <option value="casual">Casual</option>
                        <option value="authoritative">Authoritative</option>
                        <option value="friendly">Friendly</option>
                      </select>
                    </div>

                    <div className="space-y-3">
                      <label className="block font-medium text-gray-300">Content Length</label>
                      <select
                        value={config.contentLength}
                        onChange={(e) => setConfig({ ...config, contentLength: Number(e.target.value) })}
                        className="w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all"
                      >
                        <option value={1500}>Short Article (1,500 words)</option>
                        <option value={3000}>Medium Article (3,000 words)</option>
                        <option value={5000}>Long Article (5,000 words)</option>
                        <option value={7500}>Comprehensive (7,500 words)</option>
                        <option value={10000}>Ultimate Guide (10,000 words)</option>
                      </select>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="space-y-6">
                    <div className="space-y-3">
                      <label className="block font-medium text-gray-300">Autonomy Level</label>
                      <select
                        value={config.autonomyLevel}
                        onChange={(e) => setConfig({ ...config, autonomyLevel: e.target.value as any })}
                        className="w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white focus:border-purple-400 focus:outline-none transition-all"
                      >
                        <option value="full">Full Autonomy (Recommended)</option>
                        <option value="guided">Guided Generation</option>
                        <option value="supervised">Supervised Mode</option>
                      </select>
                    </div>

                    <div className="space-y-3">
                      <label className="block font-medium text-gray-300">
                        Confidence Threshold: {Math.round((config.confidenceThreshold || 0.95) * 100)}%
                      </label>
                      <input
                        type="range"
                        min="0.8"
                        max="0.99"
                        step="0.01"
                        value={config.confidenceThreshold}
                        onChange={(e) => setConfig({ ...config, confidenceThreshold: Number(e.target.value) })}
                        className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider-purple"
                      />
                      <div className="text-xs text-gray-400">
                        Higher values ensure better quality but may take longer
                      </div>
                    </div>

                    {/* Agent Features */}
                    <div className="space-y-3">
                      <label className="block font-medium text-gray-300">Agent Capabilities</label>
                      <div className="space-y-3">
                        {[
                          { key: 'enableWebSearch', label: 'Real-time Web Research', icon: Search },
                          { key: 'enableSelfImprovement', label: 'Self-Improvement Learning', icon: TrendingUp },
                          { key: 'enableVisualization', label: 'Data Visualizations', icon: BarChart },
                          { key: 'enableMemoryConsolidation', label: 'Memory Consolidation', icon: Database }
                        ].map((feature) => (
                          <label key={feature.key} className="flex items-center space-x-3 cursor-pointer group">
                            <input
                              type="checkbox"
                              checked={config[feature.key as keyof InvincibleV2Config] as boolean}
                              onChange={(e) => setConfig({ ...config, [feature.key]: e.target.checked })}
                              className="w-4 h-4 text-purple-600 bg-black border-gray-600 rounded focus:ring-purple-500"
                            />
                            <feature.icon className="w-4 h-4 text-gray-400 group-hover:text-purple-400 transition-colors" />
                            <span className="text-gray-300 group-hover:text-white transition-colors">{feature.label}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Keywords Section */}
                <div className="space-y-3">
                  <label className="block font-medium text-gray-300">Focus Keywords (Optional)</label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={keywordInput}
                      onChange={(e) => setKeywordInput(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && addKeyword()}
                      placeholder="Add focus keywords..."
                      className="flex-1 bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-all"
                    />
                    <button
                      onClick={addKeyword}
                      className="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-lg transition-colors flex items-center space-x-2"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                  
                  {config.keywords && config.keywords.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-3">
                      {config.keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="bg-purple-900/50 text-purple-300 px-3 py-1 rounded-full text-sm border border-purple-500/30 flex items-center space-x-2"
                        >
                          <span>{keyword}</span>
                          <X
                            className="w-3 h-3 cursor-pointer hover:text-white transition-colors"
                            onClick={() => removeKeyword(keyword)}
                          />
                        </span>
                      ))}
                    </div>
                  )}
                </div>

                {/* Custom Instructions */}
                <div className="space-y-3">
                  <label className="block font-medium text-gray-300">Custom Instructions (Optional)</label>
                  <textarea
                    value={config.customInstructions}
                    onChange={(e) => setConfig({ ...config, customInstructions: e.target.value })}
                    placeholder="Any specific requirements, style preferences, or focus areas..."
                    rows={4}
                    className="w-full bg-black/50 border border-white/20 rounded-lg px-4 py-3 text-white placeholder-gray-400 focus:border-purple-400 focus:outline-none transition-all resize-none"
                  />
                </div>

                {/* Error Display */}
                <AnimatePresence>
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="bg-red-900/50 border border-red-500/50 text-red-300 px-4 py-3 rounded-lg flex items-center space-x-2"
                    >
                      <AlertCircle className="w-5 h-5 flex-shrink-0" />
                      <span>{error}</span>
                    </motion.div>
                  )}
                </AnimatePresence>

                {/* Action Button */}
                <motion.button
                  onClick={handleStartStreaming}
                  disabled={!config.topic.trim() || isStreaming}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={cn(
                    "w-full bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white font-semibold py-4 px-8 rounded-xl transition-all duration-300 flex items-center justify-center space-x-3 text-lg shadow-lg shadow-purple-500/25",
                    (!config.topic.trim() || isStreaming) && "opacity-50 cursor-not-allowed"
                  )}
                >
                  <Brain className="w-6 h-6" />
                  <span>Deploy Autonomous Agent System</span>
                  <ChevronRight className="w-5 h-5" />
                </motion.button>

                <div className="text-center text-sm text-gray-400">
                  The autonomous agent will research, plan, generate, and verify your content with guaranteed superiority
                </div>
              </div>
            </div>
          </motion.div>
        </motion.section>
      </div>
    </div>
  );
} 