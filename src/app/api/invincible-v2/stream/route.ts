import { NextRequest, NextResponse } from 'next/server';
import { InvincibleV2Agent } from '@/lib/agents/invincible-v2-agent';
import { TaskContext } from '@/lib/agents/v2-types';

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  
  // Extract parameters
  const topic = searchParams.get('topic');
  const wordCount = parseInt(searchParams.get('wordCount') || '3000');
  const tone = searchParams.get('tone') || 'professional';
  const audience = searchParams.get('audience') || 'general';
  const autonomyLevel = searchParams.get('autonomyLevel') as 'full' | 'guided' | 'supervised' || 'full';
  const confidenceThreshold = parseFloat(searchParams.get('confidenceThreshold') || '0.95');
  const customInstructions = searchParams.get('customInstructions');
  const enableWebSearch = searchParams.get('enableWebSearch') === 'true';
  const enableSelfImprovement = searchParams.get('enableSelfImprovement') === 'true';
  const enableVisualization = searchParams.get('enableVisualization') === 'true';

  if (!topic) {
    return NextResponse.json({ error: 'Topic is required' }, { status: 400 });
  }

  console.log('🚀 Starting Invincible V2 Agent Stream');
  console.log('📝 Topic:', topic);
  console.log('🎯 Word Count:', wordCount);
  console.log('🎭 Tone:', tone);
  console.log('👥 Audience:', audience);
  console.log('🤖 Autonomy Level:', autonomyLevel);

  // Create SSE response
  const encoder = new TextEncoder();
  const stream = new ReadableStream({
    start(controller) {
      const sendData = (data: any) => {
        const payload = `data: ${JSON.stringify(data)}\n\n`;
        controller.enqueue(encoder.encode(payload));
      };

      // Initialize V2 Agent
      const agent = new InvincibleV2Agent({
        autonomyLevel,
        confidenceThreshold,
        enableWebSearch,
        enableSelfImprovement,
        enableVisualization,
        minWordCount: Math.max(wordCount * 0.8, 2000),
        maxWordCount: Math.max(wordCount * 1.2, 10000),
        temperature: 0.8
      });

      // Create task context
      const taskContext: TaskContext = {
        taskId: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        topic,
        contentLength: wordCount,
        tone,
        targetAudience: audience,
        customInstructions: customInstructions || undefined,
        language: 'en',
        contentType: 'article',
        urgency: 'medium',
        qualityTarget: 'exceptional'
      };

      // Generate session ID
      const sessionId = `v2_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Send session start
      sendData({
        type: 'session',
        sessionId,
        timestamp: Date.now()
      });

      // Execute agent with real-time updates
      const execute = async () => {
        try {
          // Set up real-time event handlers
          const originalLog = console.log;
          console.log = (...args) => {
            originalLog(...args);
            
            // Parse agent state updates from logs
            const message = args.join(' ');
            if (message.includes('🎯') || message.includes('🔍') || message.includes('📝') || message.includes('✅')) {
              sendData({
                type: 'agent_state',
                state: {
                  phase: extractPhase(message),
                  agent: extractAgent(message),
                  action: message,
                  confidence: extractConfidence(message),
                  status: extractStatus(message),
                  timestamp: Date.now()
                }
              });
            }
          };

          // Execute the agent
          const result = await agent.execute(taskContext);
          
          // Restore original console.log
          console.log = originalLog;

          if (result.success && result.article) {
            // Send final content
            sendData({
              type: 'content',
              content: result.article
            });

            // Send metrics
            sendData({
              type: 'metrics',
              metrics: {
                executionTime: result.metrics?.executionTime || 0,
                agentInvocations: result.metrics?.agentInvocations || {},
                toolUsage: result.metrics?.toolUsage || {},
                memoryAccess: result.metrics?.memoryAccess || {},
                confidence: result.confidence || 0,
                improvements: result.improvements || []
              }
            });

            // Send completion
            sendData({
              type: 'complete',
              success: true,
              sessionId,
              timestamp: Date.now()
            });
          } else {
            throw new Error(result.error || 'Generation failed');
          }
        } catch (error) {
          console.error('V2 Agent Error:', error);
          sendData({
            type: 'error',
            error: error instanceof Error ? error.message : 'Unknown error occurred',
            timestamp: Date.now()
          });
        }
        
        controller.close();
      };

      // Start execution
      execute();
    }
  });

  // Return SSE response
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}

// Helper functions to extract information from log messages
function extractPhase(message: string): string {
  if (message.includes('Analysis')) return 'analysis';
  if (message.includes('Research')) return 'research';
  if (message.includes('Planning')) return 'planning';
  if (message.includes('Generation')) return 'generation';
  if (message.includes('Verification')) return 'verification';
  if (message.includes('Learning')) return 'learning';
  return 'processing';
}

function extractAgent(message: string): string {
  if (message.includes('Master')) return 'master';
  if (message.includes('Research')) return 'research';
  if (message.includes('Planning')) return 'planning';
  if (message.includes('Execution')) return 'execution';
  if (message.includes('Verification')) return 'verification';
  if (message.includes('Learning')) return 'learning';
  return 'system';
}

function extractConfidence(message: string): number {
  const match = message.match(/(\d+(?:\.\d+)?)%/);
  return match ? parseFloat(match[1]) / 100 : 0.8;
}

function extractStatus(message: string): 'thinking' | 'acting' | 'completed' | 'failed' {
  if (message.includes('✅') || message.includes('Complete')) return 'completed';
  if (message.includes('❌') || message.includes('Failed')) return 'failed';
  if (message.includes('🔍') || message.includes('Analysis')) return 'thinking';
  return 'acting';
} 