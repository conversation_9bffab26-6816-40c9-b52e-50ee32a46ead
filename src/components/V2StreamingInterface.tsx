'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Brain, 
  Search, 
  FileText, 
  CheckCircle2, 
  AlertCircle,
  Loader2,
  Clock,
  Activity,
  Zap
} from 'lucide-react';

interface AgentState {
  phase: string;
  agent: string;
  action: string;
  confidence: number;
  status: 'thinking' | 'acting' | 'completed' | 'failed';
  timestamp: number;
}

interface V2StreamingInterfaceProps {
  agentStates: AgentState[];
  isGenerating: boolean;
  sessionId: string | null;
}

export function V2StreamingInterface({ agentStates, isGenerating, sessionId }: V2StreamingInterfaceProps) {
  const [currentPhase, setCurrentPhase] = useState('initializing');
  
  useEffect(() => {
    if (agentStates.length > 0) {
      const latestState = agentStates[agentStates.length - 1];
      setCurrentPhase(latestState.phase);
    }
  }, [agentStates]);
  
  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'analysis': return <Brain className="h-4 w-4" />;
      case 'research': return <Search className="h-4 w-4" />;
      case 'planning': return <FileText className="h-4 w-4" />;
      case 'generation': return <Zap className="h-4 w-4" />;
      case 'verification': return <CheckCircle2 className="h-4 w-4" />;
      case 'learning': return <Activity className="h-4 w-4" />;
      default: return <Loader2 className="h-4 w-4 animate-spin" />;
    }
  };
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'thinking': return 'bg-blue-100 text-blue-800';
      case 'acting': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };
  
  const getAgentColor = (agent: string) => {
    switch (agent) {
      case 'master': return 'bg-purple-500';
      case 'research': return 'bg-blue-500';
      case 'planning': return 'bg-green-500';
      case 'execution': return 'bg-orange-500';
      case 'verification': return 'bg-red-500';
      case 'learning': return 'bg-indigo-500';
      default: return 'bg-gray-500';
    }
  };
  
  const phases = [
    { name: 'analysis', label: 'Analysis', description: 'Understanding requirements' },
    { name: 'research', label: 'Research', description: 'Gathering information' },
    { name: 'planning', label: 'Planning', description: 'Structuring content' },
    { name: 'generation', label: 'Generation', description: 'Creating content' },
    { name: 'verification', label: 'Verification', description: 'Quality assurance' },
    { name: 'learning', label: 'Learning', description: 'Self-improvement' }
  ];
  
  const getCurrentPhaseIndex = () => {
    return phases.findIndex(phase => phase.name === currentPhase);
  };
  
  const progress = getCurrentPhaseIndex() >= 0 ? ((getCurrentPhaseIndex() + 1) / phases.length) * 100 : 0;
  
  return (
    <div className="space-y-6">
      {/* Session Header */}
      <Card className="border-purple-200 bg-gradient-to-r from-purple-50 to-pink-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-purple-600" />
            Agent Orchestration
            {sessionId && (
              <Badge variant="outline" className="ml-2">
                {sessionId.split('_')[1]}
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Progress</span>
              <span className="text-sm text-muted-foreground">
                {Math.round(progress)}% Complete
              </span>
            </div>
            <Progress value={progress} className="h-2" />
            
            {/* Phase Timeline */}
            <div className="flex items-center justify-between mt-4">
              {phases.map((phase, index) => (
                <div key={phase.name} className="flex flex-col items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-bold ${
                    index <= getCurrentPhaseIndex() ? 'bg-purple-500' : 'bg-gray-300'
                  }`}>
                    {index + 1}
                  </div>
                  <div className="text-xs text-center mt-1">
                    <div className="font-medium">{phase.label}</div>
                    <div className="text-gray-500">{phase.description}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Agent Activity Feed */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Agent Activity
            {isGenerating && (
              <Loader2 className="h-4 w-4 animate-spin ml-2" />
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-64">
            <div className="space-y-3">
              {agentStates.length === 0 ? (
                <div className="text-center text-gray-500 py-8">
                  {isGenerating ? 'Initializing agents...' : 'No activity yet'}
                </div>
              ) : (
                agentStates.map((state, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg border bg-gray-50">
                    <div className={`w-3 h-3 rounded-full mt-1 ${getAgentColor(state.agent)}`} />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium capitalize">{state.agent}</span>
                        <Badge className={getStatusColor(state.status)} variant="secondary">
                          {state.status}
                        </Badge>
                        {getPhaseIcon(state.phase)}
                        <span className="text-xs text-gray-500">
                          {new Date(state.timestamp).toLocaleTimeString()}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700">{state.action}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-gray-500">Confidence:</span>
                        <Progress value={state.confidence * 100} className="h-1 w-16" />
                        <span className="text-xs text-gray-500">
                          {Math.round(state.confidence * 100)}%
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
} 