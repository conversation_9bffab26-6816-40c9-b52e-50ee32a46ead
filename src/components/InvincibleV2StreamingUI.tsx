'use client';

import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { 
  Activity, 
  Brain, 
  Search, 
  Globe, 
  Zap, 
  Shield, 
  Target, 
  Code, 
  Database,
  Eye,
  Cpu,
  Terminal,
  Wifi,
  TrendingUp,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  Clock,
  FileText,
  Network,
  BarChart3,
  Users,
  Sparkles,
  Crown,
  ArrowLeft,
  RotateCcw,
  Download,
  Share,
  Loader2,
  Lightbulb,
  MonitorSpeaker
} from 'lucide-react';

interface StreamEvent {
  type: string;
  data: any;
  timestamp: number;
}

interface AgentState {
  phase: string;
  agent: string;
  action: string;
  confidence: number;
  status: 'thinking' | 'acting' | 'completed' | 'failed';
  timestamp: number;
}

interface InvincibleV2Config {
  topic: string;
  customInstructions?: string;
  targetAudience?: string;
  contentLength?: number;
  tone?: string;
  keywords?: string[];
  autonomyLevel?: 'full' | 'guided' | 'supervised';
  confidenceThreshold?: number;
  enableWebSearch?: boolean;
  enableSelfImprovement?: boolean;
  enableVisualization?: boolean;
  enableMemoryConsolidation?: boolean;
}

interface InvincibleV2StreamingUIProps {
  config: InvincibleV2Config;
  onComplete?: (result: any) => void;
  onError?: (error: string) => void;
  onReset?: () => void;
  isSaving?: boolean;
}

const InvincibleV2StreamingUI: React.FC<InvincibleV2StreamingUIProps> = ({
  config,
  onComplete,
  onError,
  onReset,
  isSaving
}) => {
  const router = useRouter();
  const [isStreaming, setIsStreaming] = useState(false);
  const [currentPhase, setCurrentPhase] = useState<string>('');
  const [progress, setProgress] = useState(0);
  const [events, setEvents] = useState<StreamEvent[]>([]);
  const [agentStates, setAgentStates] = useState<AgentState[]>([]);
  const [activeAgents, setActiveAgents] = useState<string[]>([]);
  const [searchQueries, setSearchQueries] = useState<string[]>([]);
  const [researchSources, setResearchSources] = useState<string[]>([]);
  const [agentMetrics, setAgentMetrics] = useState<Record<string, any>>({});
  const [finalResult, setFinalResult] = useState<any>(null);
  const [error, setError] = useState<string>('');
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'error' | 'complete'>('connecting');
  const [confidenceScore, setConfidenceScore] = useState(0);
  const [improvements, setImprovements] = useState<string[]>([]);
  const eventsContainerRef = useRef<HTMLDivElement>(null);
  const eventSourceRef = useRef<EventSource | null>(null);

  // Auto-start streaming when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      startStreaming();
    }, 1000);
    
    return () => {
      clearTimeout(timer);
      if (eventSourceRef.current) {
        eventSourceRef.current.close();
      }
    };
  }, []);

  const startStreaming = () => {
    if (isStreaming) return;
    
    setIsStreaming(true);
    setProgress(0);
    setEvents([]);
    setAgentStates([]);
    setActiveAgents([]);
    setSearchQueries([]);
    setResearchSources([]);
    setAgentMetrics({});
    setFinalResult(null);
    setError('');
    setConnectionStatus('connecting');
    setConfidenceScore(0);
    setImprovements([]);

    // Build URL with query parameters for EventSource GET request
    const params = new URLSearchParams({
      topic: config.topic,
      contentLength: (config.contentLength || 3000).toString(),
      tone: config.tone || 'professional',
      targetAudience: config.targetAudience || 'general audience',
      customInstructions: config.customInstructions || '',
      autonomyLevel: config.autonomyLevel || 'full',
      confidenceThreshold: (config.confidenceThreshold || 0.95).toString(),
      enableWebSearch: config.enableWebSearch ? 'true' : 'false',
      enableSelfImprovement: config.enableSelfImprovement ? 'true' : 'false',
      enableVisualization: config.enableVisualization ? 'true' : 'false',
      enableMemoryConsolidation: config.enableMemoryConsolidation ? 'true' : 'false'
    });

    const eventSource = new EventSource(`/api/invincible-v2/stream?${params.toString()}`);
    eventSourceRef.current = eventSource;

    // Safe JSON parsing helper
    const safeParseJSON = (rawData: string) => {
      try {
        if (!rawData || rawData === 'undefined' || rawData === 'null') {
          console.warn('Received invalid SSE data:', rawData);
          return null;
        }
        return JSON.parse(rawData);
      } catch (error) {
        console.error('Failed to parse SSE data:', rawData, error);
        return null;
      }
    };

    // Connection established
    eventSource.onopen = () => {
      setConnectionStatus('connected');
      addEvent('connection', { message: 'Multi-Agent System Connected', timestamp: Date.now() });
    };

    // Handle different event types
    eventSource.addEventListener('session', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      if (data) {
        addEvent('session', data);
      }
    });

    eventSource.addEventListener('agent_state', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      if (data && data.state) {
        setAgentStates(prev => [...prev, data.state]);
        setCurrentPhase(data.state.phase);
        
        // Update active agents
        setActiveAgents(prev => {
          const newActive = [...prev];
          if (!newActive.includes(data.state.agent)) {
            newActive.push(data.state.agent);
          }
          return newActive;
        });
        
        addEvent('agent_state', data);
      }
    });

    eventSource.addEventListener('search_query', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      if (data && data.query) {
        setSearchQueries(prev => [...prev, data.query]);
        addEvent('search_query', data);
      }
    });

    eventSource.addEventListener('research_source', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      if (data && data.source) {
        setResearchSources(prev => [...prev, data.source]);
        addEvent('research_source', data);
      }
    });

    eventSource.addEventListener('agent_metrics', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      if (data) {
        setAgentMetrics(data);
        addEvent('agent_metrics', data);
      }
    });

    eventSource.addEventListener('confidence', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      if (data && typeof data.confidence === 'number') {
        setConfidenceScore(data.confidence);
        addEvent('confidence', data);
      }
    });

    eventSource.addEventListener('improvement', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      if (data && data.improvement) {
        setImprovements(prev => [...prev, data.improvement]);
        addEvent('improvement', data);
      }
    });

    eventSource.addEventListener('progress', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      if (data && typeof data.progress === 'number') {
        setProgress(data.progress);
        addEvent('progress', data);
      }
    });

    eventSource.addEventListener('content', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      if (data) {
        setFinalResult(data);
        addEvent('content', data);
      }
    });

    eventSource.addEventListener('complete', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      if (data) {
        setFinalResult(data);
        setProgress(100);
        setConnectionStatus('complete');
        setIsStreaming(false);
        addEvent('complete', data);
        onComplete?.(data);
      }
      eventSource.close();
    });

    eventSource.addEventListener('error', (e) => {
      const data = safeParseJSON((e as MessageEvent).data);
      const errorMessage = data?.error || 'Unknown error occurred';
      setError(errorMessage);
      setConnectionStatus('error');
      setIsStreaming(false);
      addEvent('error', data);
      onError?.(errorMessage);
      eventSource.close();
    });

    eventSource.onerror = (error) => {
      console.error('SSE Connection Error:', error);
      setError('Connection lost. Please try again.');
      setConnectionStatus('error');
      setIsStreaming(false);
      onError?.('Connection lost. Please try again.');
      eventSource.close();
    };
  };

  const addEvent = (type: string, data: any) => {
    const event: StreamEvent = {
      type,
      data,
      timestamp: Date.now()
    };
    
    setEvents(prev => [...prev, event]);
    
    // Auto-scroll to bottom
    setTimeout(() => {
      if (eventsContainerRef.current) {
        eventsContainerRef.current.scrollTop = eventsContainerRef.current.scrollHeight;
      }
    }, 100);
  };

  const getPhaseInfo = (phase: string) => {
    const phases = {
      'initializing': { 
        icon: Cpu, 
        label: 'Initializing', 
        color: 'text-blue-400',
        description: 'Starting multi-agent system...'
      },
      'analysis': { 
        icon: Brain, 
        label: 'Master Analysis', 
        color: 'text-purple-400',
        description: 'Understanding requirements and complexity...'
      },
      'research': { 
        icon: Search, 
        label: 'Research Phase', 
        color: 'text-green-400',
        description: 'Deep web research and competitive analysis...'
      },
      'planning': { 
        icon: Target, 
        label: 'Strategic Planning', 
        color: 'text-orange-400',
        description: 'Creating content strategy and structure...'
      },
      'generation': { 
        icon: FileText, 
        label: 'Content Generation', 
        color: 'text-pink-400',
        description: 'Writing superior content...'
      },
      'verification': { 
        icon: Shield, 
        label: 'Verification', 
        color: 'text-red-400',
        description: 'Quality assurance and fact-checking...'
      },
      'learning': { 
        icon: TrendingUp, 
        label: 'Learning Phase', 
        color: 'text-indigo-400',
        description: 'Self-improvement and optimization...'
      }
    };
    
    return phases[phase as keyof typeof phases] || { 
      icon: Activity, 
      label: 'Processing', 
      color: 'text-gray-400',
      description: 'Agent processing...'
    };
  };

  const getAgentInfo = (agent: string) => {
    const agents = {
      'master': { icon: Crown, color: 'text-purple-400', name: 'Master Agent' },
      'research': { icon: Search, color: 'text-blue-400', name: 'Research Agent' },
      'planning': { icon: Target, color: 'text-green-400', name: 'Planning Agent' },
      'execution': { icon: FileText, color: 'text-orange-400', name: 'Execution Agent' },
      'verification': { icon: Shield, color: 'text-red-400', name: 'Verification Agent' },
      'learning': { icon: TrendingUp, color: 'text-indigo-400', name: 'Learning Agent' }
    };
    
    return agents[agent as keyof typeof agents] || { 
      icon: Activity, 
      color: 'text-gray-400',
      name: 'System Agent'
    };
  };

  const getConnectionStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-400';
      case 'error': return 'text-red-400';
      case 'complete': return 'text-blue-400';
      default: return 'text-yellow-400';
    }
  };

  return (
    <div className="min-h-screen bg-black">
      {/* Enhanced Background */}
      <div className="fixed inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-950/20 via-black to-indigo-950/20" />
        <motion.div
          animate={{ opacity: [0.3, 0.6, 0.3] }}
          transition={{ duration: 4, repeat: Infinity }}
          className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-600/10 rounded-full blur-3xl"
        />
        <motion.div
          animate={{ opacity: [0.4, 0.7, 0.4] }}
          transition={{ duration: 6, repeat: Infinity }}
          className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-indigo-600/10 rounded-full blur-3xl"
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10">
        {/* Header */}
        <motion.header 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="border-b border-white/10 backdrop-blur-xl bg-black/50"
        >
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button 
                  onClick={onReset}
                  className="flex items-center space-x-2 text-gray-400 hover:text-white transition-colors group"
                >
                  <ArrowLeft className="w-5 h-5 group-hover:-translate-x-1 transition-transform" />
                  <span>Back to Configuration</span>
                </button>
                <div className="w-px h-6 bg-white/20" />
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-xl blur-lg opacity-60" />
                    <div className="relative bg-black rounded-xl p-2.5 border border-white/20">
                      <Brain className="w-6 h-6 text-white" />
                    </div>
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold bg-gradient-to-r from-purple-400 to-indigo-400 bg-clip-text text-transparent">
                      Invincible V2
                    </h1>
                    <p className="text-sm text-gray-500">Autonomous Generation in Progress</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className={`flex items-center space-x-2 text-sm ${getConnectionStatusColor()}`}>
                  <div className="w-2 h-2 bg-current rounded-full animate-pulse" />
                  <span>{connectionStatus === 'connected' ? 'Agent System Active' : 
                         connectionStatus === 'complete' ? 'Generation Complete' :
                         connectionStatus === 'error' ? 'Connection Error' : 'Connecting...'}</span>
                </div>
                {confidenceScore > 0 && (
                  <div className="text-sm text-purple-400">
                    Confidence: {Math.round(confidenceScore * 100)}%
                  </div>
                )}
              </div>
            </div>
          </div>
        </motion.header>

        {/* Main Dashboard */}
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Phase Progress & Agent Status */}
            <div className="lg:col-span-1 space-y-6">
              {/* Current Phase */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
              >
                <h3 className="text-lg font-semibold text-white mb-4">Current Phase</h3>
                {currentPhase ? (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      {React.createElement(getPhaseInfo(currentPhase).icon, {
                        className: `w-8 h-8 ${getPhaseInfo(currentPhase).color}`
                      })}
                      <div>
                        <div className="font-semibold text-white">{getPhaseInfo(currentPhase).label}</div>
                        <div className="text-sm text-gray-400">{getPhaseInfo(currentPhase).description}</div>
                      </div>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-400">Progress</span>
                        <span className="text-white">{Math.round(progress)}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <motion.div 
                          className="bg-gradient-to-r from-purple-500 to-indigo-500 h-2 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${progress}%` }}
                          transition={{ duration: 0.5 }}
                        />
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-gray-400 text-center py-4">
                    Waiting for agent initialization...
                  </div>
                )}
              </motion.div>

              {/* Active Agents */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
              >
                <h3 className="text-lg font-semibold text-white mb-4">Agent Network</h3>
                <div className="space-y-3">
                  {['master', 'research', 'planning', 'execution', 'verification', 'learning'].map((agent) => {
                    const agentInfo = getAgentInfo(agent);
                    const isActive = activeAgents.includes(agent);
                    const latestState = agentStates.filter(s => s.agent === agent).slice(-1)[0];
                    
                    return (
                      <div key={agent} className={`flex items-center justify-between p-3 rounded-lg border transition-all ${
                        isActive ? 'border-purple-500/50 bg-purple-900/20' : 'border-gray-700 bg-gray-800/30'
                      }`}>
                        <div className="flex items-center space-x-3">
                          {React.createElement(agentInfo.icon, {
                            className: `w-5 h-5 ${isActive ? agentInfo.color : 'text-gray-500'}`
                          })}
                          <span className={`font-medium ${isActive ? 'text-white' : 'text-gray-500'}`}>
                            {agentInfo.name}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          {latestState && (
                            <span className="text-xs text-gray-400">
                              {Math.round(latestState.confidence * 100)}%
                            </span>
                          )}
                          <div className={`w-2 h-2 rounded-full ${
                            isActive ? 'bg-green-400 animate-pulse' : 'bg-gray-600'
                          }`} />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </motion.div>

              {/* Research Progress */}
              {(searchQueries.length > 0 || researchSources.length > 0) && (
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
                >
                  <h3 className="text-lg font-semibold text-white mb-4">Research Progress</h3>
                  <div className="space-y-4">
                    {searchQueries.length > 0 && (
                      <div>
                        <div className="flex items-center space-x-2 mb-2">
                          <Search className="w-4 h-4 text-blue-400" />
                          <span className="text-sm text-gray-300">Search Queries</span>
                          <span className="text-xs text-blue-400">({searchQueries.length})</span>
                        </div>
                        <div className="max-h-24 overflow-y-auto space-y-1">
                          {searchQueries.slice(-3).map((query, index) => (
                            <div key={index} className="text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded">
                              "{query}"
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                    
                    {researchSources.length > 0 && (
                      <div>
                        <div className="flex items-center space-x-2 mb-2">
                          <Globe className="w-4 h-4 text-green-400" />
                          <span className="text-sm text-gray-300">Sources Found</span>
                          <span className="text-xs text-green-400">({researchSources.length})</span>
                        </div>
                        <div className="max-h-24 overflow-y-auto space-y-1">
                          {researchSources.slice(-3).map((source, index) => (
                            <div key={index} className="text-xs text-gray-400 bg-gray-800/50 px-2 py-1 rounded">
                              {source}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </div>

            {/* Right Column - Live Events & Results */}
            <div className="lg:col-span-2 space-y-6">
              {/* Live Event Stream */}
              <motion.div 
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
              >
                <h3 className="text-lg font-semibold text-white mb-4">Live Agent Activity</h3>
                <div 
                  ref={eventsContainerRef}
                  className="h-80 overflow-y-auto space-y-2 scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800"
                >
                  <AnimatePresence>
                    {events.length === 0 ? (
                      <div className="text-gray-400 text-center py-8">
                        <Terminal className="w-8 h-8 mx-auto mb-2 text-gray-600" />
                        <p>Waiting for agent events...</p>
                      </div>
                    ) : (
                      events.map((event, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          className="flex items-start space-x-3 p-3 rounded-lg bg-gray-800/50 border border-gray-700/50"
                        >
                          <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center space-x-2 mb-1">
                              <span className="text-xs text-purple-400 font-medium">
                                {event.type.toUpperCase()}
                              </span>
                              <span className="text-xs text-gray-500">
                                {new Date(event.timestamp).toLocaleTimeString()}
                              </span>
                            </div>
                            <div className="text-sm text-gray-300">
                              {event.type === 'agent_state' && event.data.state ? (
                                `${event.data.state.agent}: ${event.data.state.action}`
                              ) : event.type === 'search_query' ? (
                                `Searching: "${event.data.query}"`
                              ) : event.type === 'research_source' ? (
                                `Found source: ${event.data.source}`
                              ) : event.type === 'connection' ? (
                                event.data.message
                              ) : (
                                JSON.stringify(event.data).substring(0, 100) + '...'
                              )}
                            </div>
                          </div>
                        </motion.div>
                      ))
                    )}
                  </AnimatePresence>
                </div>
              </motion.div>

              {/* Self-Improvement Insights */}
              {improvements.length > 0 && (
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
                >
                  <h3 className="text-lg font-semibold text-white mb-4">Self-Improvement Insights</h3>
                  <div className="space-y-2">
                    {improvements.map((improvement, index) => (
                      <div key={index} className="flex items-start space-x-3 p-3 rounded-lg bg-green-900/20 border border-green-500/30">
                        <Lightbulb className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                        <span className="text-sm text-green-300">{improvement}</span>
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}

              {/* Results Display */}
              {finalResult && (
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-gradient-to-br from-gray-900/80 to-gray-800/60 backdrop-blur-xl border border-white/10 rounded-2xl p-6"
                >
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-white">Generation Complete</h3>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="w-5 h-5 text-green-400" />
                      <span className="text-green-400 font-medium">Success</span>
                    </div>
                  </div>
                  
                  {finalResult.article && (
                    <div className="space-y-4">
                      <div className="bg-gray-800/50 rounded-lg p-4">
                        <h4 className="font-semibold text-white mb-2">{finalResult.article.title}</h4>
                        <p className="text-gray-300 text-sm">{finalResult.article.metaDescription}</p>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-400">
                            {finalResult.article.wordCount || 0}
                          </div>
                          <div className="text-xs text-gray-400">Words</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-400">
                            {finalResult.article.seoScore || 0}
                          </div>
                          <div className="text-xs text-gray-400">SEO Score</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-400">
                            {Math.round((finalResult.confidence || 0) * 100)}%
                          </div>
                          <div className="text-xs text-gray-400">Confidence</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-400">
                            {finalResult.article.readabilityScore || 0}
                          </div>
                          <div className="text-xs text-gray-400">Readability</div>
                        </div>
                      </div>
                    </div>
                  )}
                </motion.div>
              )}

              {/* Error Display */}
              {error && (
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-900/50 border border-red-500/50 rounded-2xl p-6"
                >
                  <div className="flex items-center space-x-3">
                    <AlertCircle className="w-6 h-6 text-red-400 flex-shrink-0" />
                    <div>
                      <h3 className="text-lg font-semibold text-red-300">Generation Error</h3>
                      <p className="text-red-200">{error}</p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Saving Status */}
              {isSaving && (
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-blue-900/50 border border-blue-500/50 rounded-2xl p-6"
                >
                  <div className="flex items-center space-x-3">
                    <Loader2 className="w-6 h-6 text-blue-400 animate-spin flex-shrink-0" />
                    <div>
                      <h3 className="text-lg font-semibold text-blue-300">Saving Article</h3>
                      <p className="text-blue-200">Storing your autonomous creation...</p>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InvincibleV2StreamingUI; 