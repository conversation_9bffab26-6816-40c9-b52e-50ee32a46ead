/**
 * Type definitions for Invincible V2 Agent System
 */

export interface TaskContext {
  taskId: string;
  topic: string;
  contentLength?: number;
  tone?: string;
  targetAudience?: string;
  customInstructions?: string;
  language?: string;
  contentType?: string;
  urgency?: 'low' | 'medium' | 'high';
  qualityTarget?: 'standard' | 'premium' | 'exceptional';
}

export interface AgentState {
  agentType: string;
  status: 'ready' | 'thinking' | 'acting' | 'completed' | 'failed';
  context: Record<string, any>;
  memory: Record<string, any>;
  toolAccess: string[];
  currentAction?: AgentAction;
  observations: AgentObservation[];
}

export interface ReasoningTrace {
  timestamp: number;
  sessionId: string;
  agent: string;
  action: string;
  reasoning: string;
  confidence: number;
  observations: any[];
  toolInvocations?: ToolInvocation[];
  outcome?: 'success' | 'failure' | 'partial';
}

export interface MemoryEntry {
  id: string;
  type: 'episodic' | 'semantic' | 'procedural' | 'core' | 'tool' | 'improvement';
  content: any;
  timestamp: number;
  relevance: number;
  accessCount: number;
  lastAccessed: number;
  metadata: Record<string, any>;
}

export interface ToolInvocation {
  toolId: string;
  toolName: string;
  parameters: Record<string, any>;
  result: any;
  executionTime: number;
  success: boolean;
  error?: string;
}

export interface AgentAction {
  type: 'think' | 'search' | 'analyze' | 'generate' | 'verify' | 'learn';
  description: string;
  parameters: Record<string, any>;
  expectedOutcome: string;
  alternativeActions?: AgentAction[];
}

export interface AgentObservation {
  source: string;
  content: any;
  relevance: number;
  confidence: number;
  timestamp: number;
}

export interface LearningFeedback {
  sessionId: string;
  outcome: 'success' | 'failure' | 'partial';
  metrics: Record<string, number>;
  improvements: string[];
  patterns: Pattern[];
}

export interface Pattern {
  id: string;
  type: string;
  description: string;
  frequency: number;
  effectiveness: number;
  conditions: Record<string, any>;
}

export interface ResearchData {
  sources: ResearchSource[];
  insights: string[];
  statistics: Statistic[];
  competitorAnalysis: CompetitorData[];
  gaps: string[];
  opportunities: string[];
}

export interface ResearchSource {
  url: string;
  title: string;
  content: string;
  credibility: number;
  relevance: number;
  timestamp: number;
  keyPoints: string[];
}

export interface Statistic {
  value: string | number;
  unit?: string;
  source: string;
  context: string;
  year?: number;
}

export interface CompetitorData {
  url: string;
  title: string;
  wordCount: number;
  seoScore: number;
  strengths: string[];
  weaknesses: string[];
  uniqueElements: string[];
}

export interface GeneratedContent {
  title: string;
  content: string;
  metaDescription: string;
  wordCount: number;
  seoScore: number;
  readabilityScore: number;
  keywordUsage: KeywordUsage[];
  suggestions: ContentSuggestion[];
  
  // V2 specific fields
  researchDepth?: number;
  competitorSuperiority?: number;
  originalityScore?: number;
  completenessScore?: number;
  accuracyScore?: number;
  
  // Enhanced content elements
  tableOfContents?: TOCItem[];
  externalLinks?: ExternalLink[];
  internalLinks?: InternalLink[];
  visualElements?: VisualElement[];
  dataVisualizations?: DataVisualization[];
  citations?: Citation[];
  
  // Metadata
  topic?: string;
  generatedAt?: number;
  sessionId?: string;
  confidence?: number;
}

export interface KeywordUsage {
  keyword: string;
  frequency: number;
  density: number;
  positions: number[];
  context?: string[];
}

export interface ContentSuggestion {
  type: 'improvement' | 'warning' | 'optimization';
  section: string;
  suggestion: string;
  priority: 'low' | 'medium' | 'high';
  implementation?: string;
}

export interface TOCItem {
  id: string;
  title: string;
  level: number;
  wordCount: number;
  keywords: string[];
  children?: TOCItem[];
}

export interface ExternalLink {
  url: string;
  anchorText: string;
  context: string;
  authority: number;
  relevance: number;
  position: number;
}

export interface InternalLink {
  target: string;
  anchorText: string;
  context: string;
  relevance: number;
  position: number;
}

export interface VisualElement {
  type: 'image' | 'chart' | 'diagram' | 'infographic';
  description: string;
  altText: string;
  position: number;
  purpose: string;
  dataSource?: string;
}

export interface DataVisualization {
  type: 'table' | 'chart' | 'comparison';
  title: string;
  data: any;
  format: string;
  position: number;
  insights: string[];
}

export interface Citation {
  id: string;
  source: string;
  url?: string;
  author?: string;
  year?: number;
  title?: string;
  position: number;
  type: 'web' | 'academic' | 'report' | 'news';
}

// Reasoning Engine Types
export interface ReasoningContext {
  goal: string;
  constraints: string[];
  availableActions: string[];
  currentState: Record<string, any>;
  history: ReasoningStep[];
}

export interface ReasoningStep {
  thought: string;
  action: string;
  observation: string;
  reflection?: string;
  confidence: number;
  alternatives?: string[];
}

export interface UncertaintyNode {
  id: string;
  question: string;
  importance: number;
  resolved: boolean;
  resolution?: string;
  dependencies: string[];
}

// Tool Orchestrator Types
export interface Tool {
  id: string;
  name: string;
  description: string;
  category: string;
  parameters: ToolParameter[];
  execute: (params: Record<string, any>) => Promise<any>;
  cost: number;
  reliability: number;
}

export interface ToolParameter {
  name: string;
  type: string;
  required: boolean;
  description: string;
  default?: any;
  validation?: (value: any) => boolean;
}

// Memory System Types
export interface MemoryQuery {
  type?: string[];
  keywords?: string[];
  timeRange?: { start: number; end: number };
  relevanceThreshold?: number;
  limit?: number;
}

export interface MemoryStats {
  totalEntries: number;
  entriesByType: Record<string, number>;
  averageRelevance: number;
  accessPatterns: AccessPattern[];
  storageSize: number;
}

export interface AccessPattern {
  memoryType: string;
  frequency: number;
  lastAccess: number;
  averageRelevance: number;
}

// Learning System Types
export interface LearningObjective {
  id: string;
  description: string;
  metrics: string[];
  targetValue: number;
  currentValue: number;
  priority: number;
}

export interface ImprovementSuggestion {
  area: string;
  suggestion: string;
  expectedImpact: number;
  confidence: number;
  basedOn: string[];
}

export interface PerformanceMetrics {
  speed: number;
  accuracy: number;
  completeness: number;
  originality: number;
  userSatisfaction?: number;
  competitorBenchmark?: number;
} 