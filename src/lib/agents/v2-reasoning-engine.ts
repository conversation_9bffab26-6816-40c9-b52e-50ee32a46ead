/**
 * V2 Reasoning Engine - Advanced reasoning with uncertainty reduction
 */

import { 
  ReasoningContext, 
  ReasoningStep, 
  UncertaintyNode,
  AgentAction,
  AgentObservation 
} from './v2-types';
import { GeminiService } from '../gemini';

/**
 * Reasoning patterns inspired by WebSailor and other SOTA agents
 */
export class V2ReasoningEngine {
  private geminiService: GeminiService;
  private reasoningHistory: Map<string, ReasoningStep[]>;
  private uncertaintyGraph: Map<string, UncertaintyNode>;
  
  constructor(geminiService: GeminiService) {
    this.geminiService = geminiService;
    this.reasoningHistory = new Map();
    this.uncertaintyGraph = new Map();
  }
  
  /**
   * Think before acting - Core reasoning method
   */
  async think(context: ReasoningContext): Promise<ReasoningStep> {
    const thought = await this.generateThought(context);
    const action = await this.selectAction(context, thought);
    const expectedObservation = await this.predictObservation(action, context);
    
    const step: ReasoningStep = {
      thought,
      action: JSON.stringify(action),
      observation: '', // Will be filled after action execution
      confidence: await this.calculateConfidence(thought, action, context),
      alternatives: await this.generateAlternatives(context, action)
    };
    
    // Add to history
    const history = this.reasoningHistory.get(context.goal) || [];
    history.push(step);
    this.reasoningHistory.set(context.goal, history);
    
    return step;
  }
  
  /**
   * Reflect on observation and update reasoning
   */
  async reflect(
    step: ReasoningStep, 
    observation: AgentObservation,
    context: ReasoningContext
  ): Promise<string> {
    const reflection = await this.generateReflection(step, observation, context);
    
    // Update step with observation and reflection
    step.observation = JSON.stringify(observation);
    step.reflection = reflection;
    
    // Update uncertainty graph
    await this.updateUncertainty(step, observation);
    
    return reflection;
  }
  
  /**
   * Generate a thought based on context
   */
  private async generateThought(context: ReasoningContext): Promise<string> {
    const prompt = `You are an advanced reasoning agent. Given the current context, generate a thought about what to do next.

Goal: ${context.goal}

Constraints:
${context.constraints.map((c, i) => `${i + 1}. ${c}`).join('\n')}

Available Actions:
${context.availableActions.map((a, i) => `${i + 1}. ${a}`).join('\n')}

Current State:
${JSON.stringify(context.currentState, null, 2)}

Previous Steps:
${context.history.slice(-3).map((h, i) => 
  `Step ${i + 1}:
  Thought: ${h.thought}
  Action: ${h.action}
  Observation: ${h.observation}
  ${h.reflection ? `Reflection: ${h.reflection}` : ''}`
).join('\n\n')}

Generate a clear, specific thought about what to do next. Consider:
1. What information is still uncertain?
2. What action would reduce the most uncertainty?
3. Are we making progress toward the goal?
4. Should we try a different approach?

Thought:`;

    const response = await this.geminiService.generateContent(prompt, {
      temperature: 0.7,
      maxOutputTokens: 1000
    });
    
    return response.response.trim();
  }
  
  /**
   * Select the best action based on thought
   */
  private async selectAction(
    context: ReasoningContext, 
    thought: string
  ): Promise<AgentAction> {
    const prompt = `Based on this thought, select the best action to take.

Thought: ${thought}

Available Actions:
${context.availableActions.map((a, i) => `${i + 1}. ${a}`).join('\n')}

Current Goal: ${context.goal}

Select ONE action and provide:
1. Action type (from available actions)
2. Specific parameters for the action
3. What you expect to observe
4. Why this reduces uncertainty

Format as JSON:
{
  "type": "action_type",
  "description": "what this action does",
  "parameters": { ... },
  "expectedOutcome": "what we expect to observe"
}`;

    const response = await this.geminiService.generateContent(prompt, {
      temperature: 0.3,
      maxOutputTokens: 1000
    });
    
    try {
      return JSON.parse(response.response);
    } catch {
      // Fallback action
      return {
        type: 'think',
        description: 'Continue reasoning',
        parameters: {},
        expectedOutcome: 'Better understanding of the problem'
      };
    }
  }
  
  /**
   * Predict what we expect to observe
   */
  private async predictObservation(
    action: AgentAction,
    context: ReasoningContext
  ): Promise<string> {
    return action.expectedOutcome;
  }
  
  /**
   * Calculate confidence in the reasoning step
   */
  private async calculateConfidence(
    thought: string,
    action: AgentAction,
    context: ReasoningContext
  ): Promise<number> {
    // Factors affecting confidence
    let confidence = 0.5; // Base confidence
    
    // Clear goal alignment
    if (thought.toLowerCase().includes(context.goal.toLowerCase())) {
      confidence += 0.1;
    }
    
    // Action has clear parameters
    if (Object.keys(action.parameters).length > 0) {
      confidence += 0.1;
    }
    
    // Learning from history
    if (context.history.length > 0) {
      const avgHistoricalConfidence = context.history.reduce(
        (sum, h) => sum + h.confidence, 0
      ) / context.history.length;
      
      // If we're improving, boost confidence
      if (avgHistoricalConfidence < 0.7) {
        confidence += 0.1;
      }
    }
    
    // Uncertainty reduction
    const uncertaintyCount = Array.from(this.uncertaintyGraph.values())
      .filter(u => !u.resolved).length;
    
    if (uncertaintyCount < 5) {
      confidence += 0.2;
    } else if (uncertaintyCount < 10) {
      confidence += 0.1;
    }
    
    return Math.min(0.95, Math.max(0.1, confidence));
  }
  
  /**
   * Generate alternative actions
   */
  private async generateAlternatives(
    context: ReasoningContext,
    selectedAction: AgentAction
  ): Promise<string[]> {
    const alternatives: string[] = [];
    
    // Filter out the selected action
    const otherActions = context.availableActions.filter(
      a => !a.includes(selectedAction.type)
    );
    
    // Take top 3 alternatives
    return otherActions.slice(0, 3);
  }
  
  /**
   * Generate reflection based on observation
   */
  private async generateReflection(
    step: ReasoningStep,
    observation: AgentObservation,
    context: ReasoningContext
  ): Promise<string> {
    const prompt = `Reflect on this reasoning step and observation.

Thought: ${step.thought}
Action: ${step.action}
Expected: ${JSON.parse(step.action).expectedOutcome}
Observed: ${observation.content}

Questions to consider:
1. Did the observation match expectations?
2. What new information was gained?
3. What uncertainties were resolved?
4. What new questions arose?
5. Are we closer to the goal?

Provide a brief, insightful reflection:`;

    const response = await this.geminiService.generateContent(prompt, {
      temperature: 0.5,
      maxOutputTokens: 500
    });
    
    return response.response.trim();
  }
  
  /**
   * Update uncertainty graph based on observations
   */
  private async updateUncertainty(
    step: ReasoningStep,
    observation: AgentObservation
  ): Promise<void> {
    // Extract uncertainties from the observation
    const uncertainties = await this.extractUncertainties(
      observation.content
    );
    
    // Update existing uncertainties
    for (const uncertainty of uncertainties) {
      const existing = this.uncertaintyGraph.get(uncertainty.id);
      
      if (existing) {
        // Check if resolved
        if (observation.confidence > 0.8 && uncertainty.resolved) {
          existing.resolved = true;
          existing.resolution = observation.content;
        }
      } else {
        // Add new uncertainty
        this.uncertaintyGraph.set(uncertainty.id, uncertainty);
      }
    }
    
    // Remove resolved uncertainties that are old
    const now = Date.now();
    for (const [id, node] of this.uncertaintyGraph) {
      if (node.resolved && observation.timestamp - now > 3600000) {
        this.uncertaintyGraph.delete(id);
      }
    }
  }
  
  /**
   * Extract uncertainties from content
   */
  private async extractUncertainties(content: string): Promise<UncertaintyNode[]> {
    const prompt = `Extract any uncertainties or open questions from this content:

${content}

For each uncertainty, provide:
1. A unique ID
2. The question or uncertainty
3. Its importance (0-1)
4. Whether it seems resolved
5. Any dependencies on other uncertainties

Format as JSON array of uncertainty nodes.`;

    try {
      const response = await this.geminiService.generateContent(prompt, {
        temperature: 0.3,
        maxOutputTokens: 1000
      });
      
      return JSON.parse(response.response);
    } catch {
      return [];
    }
  }
  
  /**
   * Get current uncertainty level
   */
  getUncertaintyLevel(): number {
    const unresolved = Array.from(this.uncertaintyGraph.values())
      .filter(u => !u.resolved);
    
    if (unresolved.length === 0) return 0;
    
    const totalImportance = unresolved.reduce(
      (sum, u) => sum + u.importance, 0
    );
    
    return totalImportance / unresolved.length;
  }
  
  /**
   * Get most important unresolved uncertainty
   */
  getMostImportantUncertainty(): UncertaintyNode | null {
    const unresolved = Array.from(this.uncertaintyGraph.values())
      .filter(u => !u.resolved)
      .sort((a, b) => b.importance - a.importance);
    
    return unresolved[0] || null;
  }
  
  /**
   * Monte Carlo Tree Search for complex decisions
   */
  async monteCarloSearch(
    context: ReasoningContext,
    simulations: number = 100
  ): Promise<AgentAction> {
    const root = {
      state: context.currentState,
      visits: 0,
      value: 0,
      children: new Map<string, any>(),
      action: null as AgentAction | null
    };
    
    for (let i = 0; i < simulations; i++) {
      await this.simulate(root, context);
    }
    
    // Select best action based on visit count
    let bestChild = null;
    let maxVisits = 0;
    
    for (const child of root.children.values()) {
      if (child.visits > maxVisits) {
        maxVisits = child.visits;
        bestChild = child;
      }
    }
    
    return bestChild?.action || {
      type: 'think',
      description: 'No clear action found',
      parameters: {},
      expectedOutcome: 'Need more information'
    };
  }
  
  /**
   * Simulate one MCTS iteration
   */
  private async simulate(node: any, context: ReasoningContext): Promise<number> {
    // Selection
    if (node.children.size > 0 && node.visits > 0) {
      const selected = this.selectBestChild(node);
      return await this.simulate(selected, context);
    }
    
    // Expansion
    if (node.visits > 0) {
      await this.expandNode(node, context);
      if (node.children.size > 0) {
        const child = Array.from(node.children.values())[0];
        return await this.simulate(child, context);
      }
    }
    
    // Rollout
    const value = await this.rollout(node, context);
    
    // Backpropagation
    node.visits++;
    node.value += value;
    
    return value;
  }
  
  /**
   * Select best child using UCB1
   */
  private selectBestChild(node: any): any {
    let bestChild = null;
    let bestScore = -Infinity;
    
    for (const child of node.children.values()) {
      const exploitation = child.value / (child.visits + 1);
      const exploration = Math.sqrt(
        2 * Math.log(node.visits + 1) / (child.visits + 1)
      );
      const score = exploitation + exploration;
      
      if (score > bestScore) {
        bestScore = score;
        bestChild = child;
      }
    }
    
    return bestChild;
  }
  
  /**
   * Expand node with possible actions
   */
  private async expandNode(node: any, context: ReasoningContext): Promise<void> {
    const thought = await this.generateThought({
      ...context,
      currentState: node.state
    });
    
    const actions = await this.generatePossibleActions(context, thought);
    
    for (const action of actions.slice(0, 3)) {
      const child = {
        state: this.applyAction(node.state, action),
        visits: 0,
        value: 0,
        children: new Map(),
        action
      };
      
      node.children.set(action.type, child);
    }
  }
  
  /**
   * Generate possible actions
   */
  private async generatePossibleActions(
    context: ReasoningContext,
    thought: string
  ): Promise<AgentAction[]> {
    const actions: AgentAction[] = [];
    
    for (const actionType of context.availableActions) {
      actions.push({
        type: actionType as 'think' | 'search' | 'analyze' | 'generate' | 'verify' | 'learn',
        description: `Execute ${actionType}`,
        parameters: {},
        expectedOutcome: `Result of ${actionType}`
      });
    }
    
    return actions;
  }
  
  /**
   * Apply action to state
   */
  private applyAction(state: any, action: AgentAction): any {
    // Simple state transition
    return {
      ...state,
      lastAction: action.type,
      actionCount: (state.actionCount || 0) + 1
    };
  }
  
  /**
   * Rollout simulation
   */
  private async rollout(node: any, context: ReasoningContext): Promise<number> {
    // Simulate to terminal state and evaluate
    let currentState = node.state;
    let steps = 0;
    const maxSteps = 10;
    
    while (steps < maxSteps && !this.isTerminal(currentState, context)) {
      // Random action
      const actionIndex = Math.floor(
        Math.random() * context.availableActions.length
      );
      const action: AgentAction = {
        type: context.availableActions[actionIndex] as 'think' | 'search' | 'analyze' | 'generate' | 'verify' | 'learn',
        description: 'Rollout action',
        parameters: {},
        expectedOutcome: 'Unknown'
      };
      
      currentState = this.applyAction(currentState, action);
      steps++;
    }
    
    // Evaluate final state
    return this.evaluateState(currentState, context);
  }
  
  /**
   * Check if state is terminal
   */
  private isTerminal(state: any, context: ReasoningContext): boolean {
    return state.actionCount >= 20 || state.goalReached === true;
  }
  
  /**
   * Evaluate state value
   */
  private evaluateState(state: any, context: ReasoningContext): number {
    let value = 0;
    
    // Progress toward goal
    if (state.goalReached) {
      value += 1;
    }
    
    // Efficiency bonus
    value += 0.5 / (state.actionCount + 1);
    
    // Uncertainty reduction
    const uncertaintyLevel = this.getUncertaintyLevel();
    value += (1 - uncertaintyLevel) * 0.3;
    
    return value;
  }
  
  /**
   * Analyze error for recovery
   */
  async analyzeError(error: any): Promise<any> {
    const errorInfo = {
      type: error.name || 'UnknownError',
      message: error.message || 'No error message',
      stack: error.stack || '',
      timestamp: Date.now()
    };
    
    const prompt = `Analyze this error and suggest recovery strategies:

Error Type: ${errorInfo.type}
Message: ${errorInfo.message}
Stack: ${errorInfo.stack?.split('\n').slice(0, 5).join('\n')}

Suggest:
1. Root cause
2. Severity (low/medium/high)
3. Recovery strategies
4. Preventive measures

Format as JSON.`;

    try {
      const response = await this.geminiService.generateContent(prompt, {
        temperature: 0.3,
        maxOutputTokens: 1000
      });
      
      return JSON.parse(response.response);
    } catch {
      return {
        rootCause: 'Unknown',
        severity: 'medium',
        recoveryStrategies: ['retry', 'fallback'],
        preventiveMeasures: ['better error handling']
      };
    }
  }
  
  /**
   * Clear reasoning history for a goal
   */
  clearHistory(goal: string): void {
    this.reasoningHistory.delete(goal);
    
    // Clear related uncertainties
    const related = Array.from(this.uncertaintyGraph.entries())
      .filter(([id, node]) => node.question.includes(goal));
    
    related.forEach(([id]) => this.uncertaintyGraph.delete(id));
  }
} 