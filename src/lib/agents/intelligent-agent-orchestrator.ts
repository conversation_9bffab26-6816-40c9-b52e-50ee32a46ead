/**
 * Intelligent Agent Orchestrator
 * A meta-intelligence system that selects and orchestrates the best agent approach
 * for each specific task, adding additional intelligence layers and capabilities.
 */

import { GeminiService } from '../gemini';
import { TaskContext, GeneratedContent } from './types';
import { InvincibleAgent, InvincibleConfig, InvincibleResult } from './invincible-agent';

interface IntelligentConfig {
  enableMetaIntelligence?: boolean;
  enableAdaptiveAgentSelection?: boolean;
  enableContinuousLearning?: boolean;
  enableQualityAssurance?: boolean;
  maxRetries?: number;
  qualityThreshold?: number;
}

interface AgentRecommendation {
  agentType: 'invincible';
  reasoning: string;
  confidence: number;
  expectedQuality: number;
  estimatedTime: number;
  recommendations: string[];
}

interface ExecutionPlan {
  primaryAgent: 'invincible';
  fallbackAgent?: 'invincible';
  qualityGates: number[];
  adaptiveStrategies: string[];
  contingencyPlans: string[];
}

export class IntelligentAgentOrchestrator {
  private geminiService: GeminiService;
  private config: IntelligentConfig;
  private executionHistory: Map<string, any> = new Map();
  private performanceMetrics: Map<string, any> = new Map();

  constructor(config: IntelligentConfig = {}) {
    this.config = {
      enableMetaIntelligence: true,
      enableAdaptiveAgentSelection: true,
      enableContinuousLearning: true,
      enableQualityAssurance: true,
      maxRetries: 2,
      qualityThreshold: 85,
      ...config
    };
    
    this.geminiService = new GeminiService();
  }

  /**
   * Main orchestration method - intelligently selects and executes the best approach
   */
  async orchestrateExecution(context: TaskContext): Promise<InvincibleResult> {
    console.log('🎭 Starting Intelligent Agent Orchestration');
    console.log('🧠 Analyzing task and selecting optimal approach...');

    try {
      // Step 1: Analyze task and recommend agent approach
      const agentRecommendation = await this.analyzeAndRecommendAgent(context);
      console.log(`🎯 Recommended Agent: ${agentRecommendation.agentType} (${agentRecommendation.confidence}% confidence)`);

      // Step 2: Create execution plan
      const executionPlan = await this.createExecutionPlan(context, agentRecommendation);
      console.log(`📋 Execution Plan: ${executionPlan.primaryAgent} agent with ${executionPlan.qualityGates.length} quality gates`);

      // Step 3: Execute with intelligent orchestration
      const result = await this.executeWithOrchestration(context, executionPlan);

      // Step 4: Post-execution analysis and learning
      await this.captureExecutionInsights(context, result, agentRecommendation, executionPlan);

             return {
         ...result,
         // Add orchestration insights as additional metadata
         logs: [...(result.logs || []), 
           `🎭 Orchestration: Used ${executionPlan.primaryAgent} agent`,
           `📊 Quality: ${this.calculateQualityScore(result)}%`,
           `🧠 Intelligence: ${agentRecommendation.confidence}% confidence`
         ]
       };

    } catch (error) {
      console.error('❌ Orchestration failed:', error);
      
      // Fallback to basic agent
      console.log('🔄 Falling back to basic Invincible agent...');
      const fallbackAgent = new InvincibleAgent();
      return await fallbackAgent.execute(context);
    }
  }

  /**
   * Analyze task complexity and recommend the best agent approach
   */
  private async analyzeAndRecommendAgent(context: TaskContext): Promise<AgentRecommendation> {
    if (!this.config.enableAdaptiveAgentSelection) {
      return {
        agentType: 'invincible',
        reasoning: 'Default invincible agent selection',
        confidence: 85,
        expectedQuality: 88,
        estimatedTime: 10,
        recommendations: ['Use invincible agent for comprehensive approach']
      };
    }

    const analysisPrompt = `You are an expert AI system architect analyzing a content creation task to recommend the optimal agent approach.

📝 **TASK ANALYSIS:**
- Topic: "${context.topic}"
- Word Count: ${context.contentLength || 2000}
- Tone: ${context.tone || 'professional'}
- Target Audience: ${context.targetAudience || 'general users'}
- Custom Instructions: ${context.customInstructions ? 'Yes' : 'No'}
- Custom Instructions Detail: ${context.customInstructions || 'None'}

🤖 **AVAILABLE AGENT TYPES:**

1. **Invincible Agent:**
   - Linear 4-step process
   - Comprehensive research and analysis
   - Proven quality and reliability
   - Fast execution (5-15 minutes)
   - Best for: All content needs, scalable approach
   - Configurable for different complexity levels

🎯 **RECOMMENDATION CRITERIA:**

Consider:
- Topic complexity and depth required
- Quality vs speed trade-offs
- User's specific requirements
- Custom instruction complexity
- Target audience sophistication

Provide recommendation in JSON format:

{
  "taskComplexityAnalysis": {
    "complexityLevel": "low|medium|high|expert",
    "customInstructionComplexity": "none|simple|moderate|complex",
    "qualityRequirements": "standard|high|premium",
    "timeConstraints": "urgent|normal|flexible"
  },
  "agentType": "invincible",
  "reasoning": "detailed explanation of why this agent is best",
  "confidence": 0-100,
  "expectedQuality": 70-95,
  "estimatedTime": "minutes",
  "tradeoffAnalysis": {
    "speedVsQuality": "explanation",
    "complexityHandling": "how well it handles complexity",
    "customInstructionFit": "how well it handles custom requirements"
  },
  "recommendations": [
    "specific recommendation 1",
    "specific recommendation 2"
  ],
  "fallbackOptions": [
    "if primary fails, try this",
    "backup plan"
  ]
}

Analyze carefully and recommend the optimal approach.`;

    try {
      const response = await this.geminiService.generateContentWithThinking(
        analysisPrompt,
        8000,
        false,
        { temperature: 0.3, maxOutputTokens: 4000 }
      );

      const analysis = this.parseJSONResponse(response.response) || this.getDefaultRecommendation(context);
      
      console.log(`📊 Task Complexity: ${analysis.taskComplexityAnalysis?.complexityLevel || 'medium'}`);
      console.log(`⏱️ Estimated Time: ${analysis.estimatedTime || 15} minutes`);
      console.log(`🎯 Expected Quality: ${analysis.expectedQuality || 85}%`);
      
      return analysis;
    } catch (error) {
      console.log(`⚠️ Agent analysis failed, using intelligent defaults: ${error}`);
      return this.getDefaultRecommendation(context);
    }
  }

  /**
   * Create comprehensive execution plan
   */
  private async createExecutionPlan(context: TaskContext, recommendation: AgentRecommendation): Promise<ExecutionPlan> {
    const plan: ExecutionPlan = {
      primaryAgent: recommendation.agentType,
      qualityGates: [70, 80, this.config.qualityThreshold || 85],
      adaptiveStrategies: [
        'monitor_quality_continuously',
        'adjust_strategy_based_on_progress',
        'escalate_if_quality_issues'
      ],
      contingencyPlans: [
        'retry_with_modified_parameters_if_needed',
        'manual_review_trigger_if_quality_below_threshold'
      ]
    };

    // Adjust plan based on recommendation confidence
    if (recommendation.confidence < 80) {
      plan.adaptiveStrategies.push('closely_monitor_execution');
      plan.qualityGates = [60, 70, 80]; // Lower initial thresholds
    }

    return plan;
  }

  /**
   * Execute with intelligent orchestration and monitoring
   */
  private async executeWithOrchestration(context: TaskContext, plan: ExecutionPlan): Promise<InvincibleResult> {
    console.log(`🚀 Executing with ${plan.primaryAgent} agent...`);
    
    let result: InvincibleResult;
    let attempt = 1;

    while (attempt <= (this.config.maxRetries || 2)) {
      try {
        // Execute invincible agent
        const invincibleAgent = new InvincibleAgent({
          uniquenessLevel: 'high',
          enableNicheLearning: true,
          enableExternalLinking: true,
          enableTableGeneration: true
        });
        result = await invincibleAgent.execute(context);

        // Quality gate check
        const qualityScore = this.calculateQualityScore(result);
        console.log(`📊 Quality Score: ${qualityScore}%`);

        if (qualityScore >= (this.config.qualityThreshold || 85)) {
          console.log('✅ Quality threshold met!');
          break;
        }

        // If quality insufficient and we have a fallback agent
        if (attempt === 1 && plan.fallbackAgent && qualityScore < (this.config.qualityThreshold || 85)) {
          console.log(`🔄 Quality below threshold (${qualityScore}%), trying fallback agent...`);
          plan.primaryAgent = plan.fallbackAgent;
          attempt++;
          continue;
        }

        break;

      } catch (error) {
        console.error(`❌ Attempt ${attempt} failed:`, error);
        
        if (attempt < (this.config.maxRetries || 2)) {
          console.log('🔄 Retrying with adjusted parameters...');
          attempt++;
          
          // Adjust context for retry
          context = {
            ...context,
            customInstructions: (context.customInstructions || '') + '\n\nNote: Previous attempt failed, please focus on reliability and completeness.'
          };
        } else {
          throw error;
        }
      }
    }

    return result!;
  }

  /**
   * Calculate overall quality score from result
   */
  private calculateQualityScore(result: InvincibleResult): number {
    if (!result.success || !result.article) return 0;

    // Simple quality calculation based on available metrics
    let score = 70; // Base score

    // Word count appropriateness
    const wordCount = result.article.wordCount || 0;
    if (wordCount >= 1500 && wordCount <= 5000) score += 10;

    // SEO score
    if (result.article.seoScore) score += Math.min(result.article.seoScore * 0.15, 15);

    // Readability score  
    if (result.article.readabilityScore) score += Math.min(result.article.readabilityScore * 0.1, 10);

    // Uniqueness score
    if (result.uniquenessScore) score += Math.min(result.uniquenessScore * 10, 5);

    return Math.min(Math.round(score), 100);
  }

  /**
   * Capture execution insights for continuous learning
   */
  private async captureExecutionInsights(
    context: TaskContext,
    result: InvincibleResult,
    recommendation: AgentRecommendation,
    plan: ExecutionPlan
  ): Promise<void> {
    if (!this.config.enableContinuousLearning) return;

    const insights = {
      context: {
        topic: context.topic,
        wordCount: context.contentLength,
        hasCustomInstructions: !!context.customInstructions
      },
      recommendation,
      execution: {
        agentUsed: plan.primaryAgent,
        success: result.success,
        qualityAchieved: this.calculateQualityScore(result),
        executionTime: result.executionTime
      },
      performance: {
        recommendationAccuracy: this.calculateRecommendationAccuracy(recommendation, result),
        qualityPredictionAccuracy: this.calculateQualityPredictionAccuracy(recommendation, result),
        timePredictionAccuracy: this.calculateTimePredictionAccuracy(recommendation, result)
      },
      timestamp: Date.now()
    };

    // Store insights for future decision making
    const insightKey = `${context.topic}_${plan.primaryAgent}_${Date.now()}`;
    this.executionHistory.set(insightKey, insights);

    // Update performance metrics
    this.updatePerformanceMetrics(recommendation.agentType, insights.performance);

    console.log('📚 Execution insights captured for continuous learning');
  }

  /**
   * Calculate how accurate the agent recommendation was
   */
  private calculateRecommendationAccuracy(recommendation: AgentRecommendation, result: InvincibleResult): number {
    const qualityAchieved = this.calculateQualityScore(result);
    const qualityExpected = recommendation.expectedQuality;
    
    // Calculate accuracy based on how close the achieved quality was to expected
    const qualityAccuracy = Math.max(0, 100 - Math.abs(qualityAchieved - qualityExpected));
    
    // Bonus for success
    const successBonus = result.success ? 20 : 0;
    
    return Math.min(qualityAccuracy + successBonus, 100);
  }

  /**
   * Calculate quality prediction accuracy
   */
  private calculateQualityPredictionAccuracy(recommendation: AgentRecommendation, result: InvincibleResult): number {
    const actual = this.calculateQualityScore(result);
    const predicted = recommendation.expectedQuality;
    return Math.max(0, 100 - Math.abs(actual - predicted));
  }

  /**
   * Calculate time prediction accuracy
   */
  private calculateTimePredictionAccuracy(recommendation: AgentRecommendation, result: InvincibleResult): number {
    if (!result.executionTime) return 50;
    
    const actualMinutes = result.executionTime / (1000 * 60);
    const predictedMinutes = typeof recommendation.estimatedTime === 'string' 
      ? parseInt(recommendation.estimatedTime) || 15 
      : recommendation.estimatedTime;
    
    return Math.max(0, 100 - Math.abs(actualMinutes - predictedMinutes) * 5);
  }

  /**
   * Update performance metrics for agent types
   */
  private updatePerformanceMetrics(agentType: string, performance: any): void {
    const current = this.performanceMetrics.get(agentType) || {
      totalExecutions: 0,
      averageAccuracy: 0,
      averageQualityAccuracy: 0,
      averageTimeAccuracy: 0
    };

    current.totalExecutions++;
    current.averageAccuracy = (current.averageAccuracy * (current.totalExecutions - 1) + performance.recommendationAccuracy) / current.totalExecutions;
    current.averageQualityAccuracy = (current.averageQualityAccuracy * (current.totalExecutions - 1) + performance.qualityPredictionAccuracy) / current.totalExecutions;
    current.averageTimeAccuracy = (current.averageTimeAccuracy * (current.totalExecutions - 1) + performance.timePredictionAccuracy) / current.totalExecutions;

    this.performanceMetrics.set(agentType, current);
  }

  /**
   * Get default recommendation if analysis fails
   */
  private getDefaultRecommendation(context: TaskContext): AgentRecommendation {
    const hasCustomInstructions = !!context.customInstructions;
    const isComplex = (context.contentLength || 2000) > 3000 || hasCustomInstructions;
    
    return {
      agentType: 'invincible',
      reasoning: isComplex 
        ? 'Complex task detected, using enhanced invincible agent configuration'
        : 'Standard task, using reliable invincible agent for efficiency',
      confidence: 85,
      expectedQuality: isComplex ? 88 : 82,
      estimatedTime: isComplex ? 15 : 10,
      recommendations: [
        isComplex ? 'Use enhanced invincible approach for quality' : 'Use standard approach for efficiency',
        'Monitor quality throughout execution',
        'Apply comprehensive post-processing'
      ]
    };
  }

  /**
   * Parse JSON response with error handling
   */
  private parseJSONResponse(response: string): any {
    try {
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.log('⚠️ JSON parsing failed:', error);
    }
    return null;
  }

  /**
   * Get performance insights for analysis
   */
  getPerformanceInsights(): any {
    return {
      agentMetrics: Object.fromEntries(this.performanceMetrics),
      totalExecutions: this.executionHistory.size,
      latestInsights: Array.from(this.executionHistory.values()).slice(-5)
    };
  }
} 