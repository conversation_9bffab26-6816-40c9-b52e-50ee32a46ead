/**
 * V2 Tool Orchestrator - Dynamic tool discovery and management
 */

import { Tool, ToolParameter, ToolInvocation } from './v2-types';
import { TavilySearchService } from '../search';
import { NodeWebScraperService } from '../web-scraper';
import { GeminiService } from '../gemini';

/**
 * Tool Categories:
 * - Web Search: Real-time information gathering
 * - Analysis: Data processing and insights
 * - SEO: Search engine optimization
 * - GEO: Geographic optimization
 * - External Links: Authority linking
 * - Data: Statistical and visualization
 * - Visualization: Charts and graphics
 */
export class V2ToolOrchestrator {
  private tools: Map<string, Tool>;
  private toolUsage: Map<string, number>;
  private toolPerformance: Map<string, { success: number; failure: number; avgTime: number }>;
  private config: any;
  
  // Core services
  private searchService: TavilySearchService;
  private scraperService: NodeWebScraperService;
  private geminiService: GeminiService;
  
  constructor(config: any) {
    this.config = config;
    this.tools = new Map();
    this.toolUsage = new Map();
    this.toolPerformance = new Map();
    
    // Initialize core services
    this.searchService = new TavilySearchService();
    this.scraperService = new NodeWebScraperService();
    this.geminiService = new GeminiService();
    
    // Register built-in tools
    this.registerBuiltInTools();
    
    // Start tool discovery
    if (config.enableToolDiscovery) {
      this.startToolDiscovery();
    }
  }
  
  /**
   * Register built-in tools
   */
  private registerBuiltInTools(): void {
    // Web Search Tool
    this.registerTool({
      id: 'web_search',
      name: 'Web Search',
      description: 'Search the web for real-time information',
      category: 'search',
      parameters: [
        {
          name: 'query',
          type: 'string',
          required: true,
          description: 'Search query'
        },
        {
          name: 'limit',
          type: 'number',
          required: false,
          description: 'Number of results',
          default: 10
        }
      ],
      execute: async (params) => {
        return await this.searchService.search(params.query, params.limit || 10);
      },
      cost: 0.1,
      reliability: 0.95
    });
    
    // Web Scraper Tool
    this.registerTool({
      id: 'web_scraper',
      name: 'Web Scraper',
      description: 'Extract content from web pages',
      category: 'extraction',
      parameters: [
        {
          name: 'url',
          type: 'string',
          required: true,
          description: 'URL to scrape'
        }
      ],
      execute: async (params) => {
        return await this.scraperService.scrapeUrl(params.url);
      },
      cost: 0.2,
      reliability: 0.9
    });
    
    // Content Analyzer Tool
    this.registerTool({
      id: 'content_analyzer',
      name: 'Content Analyzer',
      description: 'Analyze content for insights and patterns',
      category: 'analysis',
      parameters: [
        {
          name: 'content',
          type: 'string',
          required: true,
          description: 'Content to analyze'
        },
        {
          name: 'analysisType',
          type: 'string',
          required: false,
          description: 'Type of analysis',
          default: 'comprehensive'
        }
      ],
      execute: async (params) => {
        return await this.analyzeContent(params.content, params.analysisType);
      },
      cost: 0.3,
      reliability: 0.92
    });
    
    // SEO Optimizer Tool
    this.registerTool({
      id: 'seo_optimizer',
      name: 'SEO Optimizer',
      description: 'Optimize content for search engines',
      category: 'seo',
      parameters: [
        {
          name: 'content',
          type: 'string',
          required: true,
          description: 'Content to optimize'
        },
        {
          name: 'keywords',
          type: 'array',
          required: true,
          description: 'Target keywords'
        }
      ],
      execute: async (params) => {
        return await this.optimizeSEO(params.content, params.keywords);
      },
      cost: 0.4,
      reliability: 0.88
    });
    
    // GEO Optimizer Tool
    this.registerTool({
      id: 'geo_optimizer',
      name: 'GEO Optimizer',
      description: 'Optimize content for geographic targeting',
      category: 'geo',
      parameters: [
        {
          name: 'content',
          type: 'string',
          required: true,
          description: 'Content to optimize'
        },
        {
          name: 'locations',
          type: 'array',
          required: true,
          description: 'Target locations'
        }
      ],
      execute: async (params) => {
        return await this.optimizeGEO(params.content, params.locations);
      },
      cost: 0.3,
      reliability: 0.85
    });
    
    // External Link Finder Tool
    this.registerTool({
      id: 'link_finder',
      name: 'External Link Finder',
      description: 'Find authoritative external links',
      category: 'linking',
      parameters: [
        {
          name: 'topic',
          type: 'string',
          required: true,
          description: 'Topic for links'
        },
        {
          name: 'count',
          type: 'number',
          required: false,
          description: 'Number of links',
          default: 5
        }
      ],
      execute: async (params) => {
        return await this.findExternalLinks(params.topic, params.count);
      },
      cost: 0.2,
      reliability: 0.9
    });
    
    // Data Visualizer Tool
    this.registerTool({
      id: 'data_visualizer',
      name: 'Data Visualizer',
      description: 'Create data visualizations',
      category: 'visualization',
      parameters: [
        {
          name: 'data',
          type: 'object',
          required: true,
          description: 'Data to visualize'
        },
        {
          name: 'type',
          type: 'string',
          required: false,
          description: 'Visualization type',
          default: 'auto'
        }
      ],
      execute: async (params) => {
        return await this.createVisualization(params.data, params.type);
      },
      cost: 0.5,
      reliability: 0.87
    });
    
    // Table Generator Tool
    this.registerTool({
      id: 'table_generator',
      name: 'Table Generator',
      description: 'Generate comparison tables and data tables',
      category: 'formatting',
      parameters: [
        {
          name: 'data',
          type: 'array',
          required: true,
          description: 'Table data'
        },
        {
          name: 'headers',
          type: 'array',
          required: false,
          description: 'Table headers'
        }
      ],
      execute: async (params) => {
        return await this.generateTable(params.data, params.headers);
      },
      cost: 0.1,
      reliability: 0.95
    });
    
    // Fact Checker Tool
    this.registerTool({
      id: 'fact_checker',
      name: 'Fact Checker',
      description: 'Verify facts and claims',
      category: 'verification',
      parameters: [
        {
          name: 'claim',
          type: 'string',
          required: true,
          description: 'Claim to verify'
        },
        {
          name: 'context',
          type: 'string',
          required: false,
          description: 'Additional context'
        }
      ],
      execute: async (params) => {
        return await this.checkFact(params.claim, params.context);
      },
      cost: 0.3,
      reliability: 0.91
    });
  }
  
  /**
   * Register a new tool
   */
  registerTool(tool: Tool): void {
    this.tools.set(tool.id, tool);
    this.toolUsage.set(tool.id, 0);
    this.toolPerformance.set(tool.id, {
      success: 0,
      failure: 0,
      avgTime: 0
    });
  }
  
  /**
   * Execute a tool
   */
  async executeTool(toolId: string, parameters: Record<string, any>): Promise<ToolInvocation> {
    const startTime = Date.now();
    const tool = this.tools.get(toolId);
    
    if (!tool) {
      throw new Error(`Tool ${toolId} not found`);
    }
    
    // Validate parameters
    for (const param of tool.parameters) {
      if (param.required && !(param.name in parameters)) {
        throw new Error(`Missing required parameter: ${param.name}`);
      }
      
      if (param.validation && !param.validation(parameters[param.name])) {
        throw new Error(`Invalid parameter value for: ${param.name}`);
      }
    }
    
    // Apply defaults
    for (const param of tool.parameters) {
      if (param.default !== undefined && !(param.name in parameters)) {
        parameters[param.name] = param.default;
      }
    }
    
    // Execute tool
    try {
      const result = await tool.execute(parameters);
      const executionTime = Date.now() - startTime;
      
      // Update usage stats
      this.updateToolStats(toolId, true, executionTime);
      
      return {
        toolId,
        toolName: tool.name,
        parameters,
        result,
        executionTime,
        success: true
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      // Update usage stats
      this.updateToolStats(toolId, false, executionTime);
      
      return {
        toolId,
        toolName: tool.name,
        parameters,
        result: null,
        executionTime,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }
  
  /**
   * Get recommended tools for a task
   */
  async getRecommendedTools(taskDescription: string): Promise<Tool[]> {
    const prompt = `Given this task: "${taskDescription}"
    
Which of these tools would be most useful? Return tool IDs in order of relevance.

Available tools:
${Array.from(this.tools.values()).map(t => 
  `- ${t.id}: ${t.description} (${t.category})`
).join('\n')}

Return as JSON array of tool IDs.`;

    try {
      const response = await this.geminiService.generateContent(prompt, {
        temperature: 0.3,
        maxOutputTokens: 1000
      });
      
      const toolIds = JSON.parse(response.response);
      return toolIds
        .map((id: string) => this.tools.get(id))
        .filter((tool: Tool | undefined): tool is Tool => tool !== undefined);
    } catch {
      // Fallback to category-based recommendation
      return this.getToolsByCategory(taskDescription);
    }
  }
  
  /**
   * Get tools by category
   */
  private getToolsByCategory(description: string): Tool[] {
    const desc = description.toLowerCase();
    const categories: string[] = [];
    
    if (desc.includes('search') || desc.includes('find')) {
      categories.push('search');
    }
    if (desc.includes('analyze') || desc.includes('understand')) {
      categories.push('analysis');
    }
    if (desc.includes('seo') || desc.includes('ranking')) {
      categories.push('seo');
    }
    if (desc.includes('location') || desc.includes('geo')) {
      categories.push('geo');
    }
    if (desc.includes('link') || desc.includes('external')) {
      categories.push('linking');
    }
    if (desc.includes('visual') || desc.includes('chart')) {
      categories.push('visualization');
    }
    
    return Array.from(this.tools.values())
      .filter(tool => categories.includes(tool.category));
  }
  
  /**
   * Dynamic tool discovery
   */
  private async startToolDiscovery(): Promise<void> {
    // Periodically discover new tools or update existing ones
    setInterval(async () => {
      await this.discoverNewTools();
    }, 60 * 60 * 1000); // Every hour
  }
  
  /**
   * Discover new tools
   */
  private async discoverNewTools(): Promise<void> {
    try {
      // Search for new AI tools and APIs
      const searchResults = await this.searchService.search(
        'new AI content generation tools API 2025',
        5
      );
      
      // Analyze and potentially integrate new tools
      for (const result of searchResults.items) {
        await this.analyzeToolCandidate(result);
      }
    } catch (error) {
      console.error('Tool discovery error:', error);
    }
  }
  
  /**
   * Analyze a potential tool candidate
   */
  private async analyzeToolCandidate(candidate: any): Promise<void> {
    // Check if it's a valid tool
    // This would involve more sophisticated analysis in production
    const isValidTool = candidate.link.includes('api') || 
                       candidate.link.includes('tool') ||
                       candidate.snippet.toLowerCase().includes('integration');
    
    if (isValidTool) {
      console.log(`Discovered potential tool: ${candidate.title}`);
      // In production, this would analyze the tool and potentially integrate it
    }
  }
  
  /**
   * Update tool statistics
   */
  private updateToolStats(toolId: string, success: boolean, executionTime: number): void {
    const stats = this.toolPerformance.get(toolId);
    if (!stats) return;
    
    if (success) {
      stats.success++;
    } else {
      stats.failure++;
    }
    
    // Update average time
    const totalCalls = stats.success + stats.failure;
    stats.avgTime = (stats.avgTime * (totalCalls - 1) + executionTime) / totalCalls;
    
    // Update usage count
    const usage = this.toolUsage.get(toolId) || 0;
    this.toolUsage.set(toolId, usage + 1);
  }
  
  /**
   * Get tool usage statistics
   */
  getUsageStats(): Record<string, number> {
    const stats: Record<string, number> = {};
    
    this.toolUsage.forEach((count, toolId) => {
      stats[toolId] = count;
    });
    
    return stats;
  }
  
  /**
   * Get tool performance statistics
   */
  getPerformanceStats(): Record<string, any> {
    const stats: Record<string, any> = {};
    
    this.toolPerformance.forEach((perf, toolId) => {
      const total = perf.success + perf.failure;
      stats[toolId] = {
        ...perf,
        successRate: total > 0 ? perf.success / total : 0,
        totalCalls: total
      };
    });
    
    return stats;
  }
  
  /**
   * Optimize content for SEO
   */
  async optimizeForSEO(content: any, context: any): Promise<any> {
    return await this.executeTool('seo_optimizer', {
      content: content.content || content,
      keywords: context.keywords || [context.topic]
    });
  }
  
  /**
   * Optimize content for GEO
   */
  async optimizeForGEO(content: any, context: any): Promise<any> {
    return await this.executeTool('geo_optimizer', {
      content: content.content || content,
      locations: context.locations || ['global']
    });
  }
  
  /**
   * Add external links
   */
  async addExternalLinks(content: any, research: any): Promise<any> {
    const topic = research.topic || 'general';
    const linkResult = await this.executeTool('link_finder', {
      topic,
      count: this.config.maxExternalLinks || 8
    });
    
    if (linkResult.success && linkResult.result) {
      return this.integrateLinks(content, linkResult.result);
    }
    
    return content;
  }
  
  /**
   * Generate visualizations
   */
  async generateVisualizations(content: any, plan: any): Promise<any> {
    const visualizations = [];
    
    // Extract data points for visualization
    const dataPoints = this.extractDataPoints(content, plan);
    
    for (const data of dataPoints) {
      const vizResult = await this.executeTool('data_visualizer', {
        data: data.content,
        type: data.suggestedType || 'auto'
      });
      
      if (vizResult.success) {
        visualizations.push(vizResult.result);
      }
    }
    
    return visualizations;
  }
  
  // Tool implementation methods
  
  private async analyzeContent(content: string, analysisType: string): Promise<any> {
    const prompt = `Analyze this content for ${analysisType} insights:
    
${content}

Provide analysis including:
- Key themes and topics
- Writing style and tone
- Target audience
- Strengths and weaknesses
- Improvement suggestions

Return as structured JSON.`;

    const response = await this.geminiService.generateContent(prompt, {
      temperature: 0.3,
      maxOutputTokens: 4000
    });
    
    try {
      return JSON.parse(response.response);
    } catch {
      return { analysis: response.response };
    }
  }
  
  private async optimizeSEO(content: string, keywords: string[]): Promise<any> {
    // SEO optimization logic
    return {
      optimizedContent: content,
      seoImprovements: [
        'Added keyword variations',
        'Optimized headings',
        'Improved meta description'
      ],
      keywordDensity: this.calculateKeywordDensity(content, keywords)
    };
  }
  
  private async optimizeGEO(content: string, locations: string[]): Promise<any> {
    // GEO optimization logic
    return {
      optimizedContent: content,
      geoEnhancements: [
        'Added location-specific content',
        'Included regional references',
        'Optimized for local search'
      ],
      targetedLocations: locations
    };
  }
  
  private async findExternalLinks(topic: string, count: number): Promise<any> {
    const searchResults = await this.searchService.search(
      `${topic} authoritative sources research data`,
      count * 2
    );
    
    const links = searchResults.items
      .filter(item => this.isAuthoritativeSource(item.link))
      .slice(0, count)
      .map(item => ({
        url: item.link,
        title: item.title,
        snippet: item.snippet,
        authority: this.calculateAuthority(item.link)
      }));
    
    return links;
  }
  
  private async createVisualization(data: any, type: string): Promise<any> {
    // Visualization creation logic
    return {
      type: type === 'auto' ? this.determineVisualizationType(data) : type,
      data,
      description: 'Data visualization',
      altText: 'Chart showing data trends'
    };
  }
  
  private async generateTable(data: any[], headers?: string[]): Promise<any> {
    // Table generation logic
    const tableHeaders = headers || (data.length > 0 ? Object.keys(data[0]) : []);
    
    return {
      type: 'table',
      headers: tableHeaders,
      rows: data,
      markdown: this.generateMarkdownTable(tableHeaders, data)
    };
  }
  
  private async checkFact(claim: string, context?: string): Promise<any> {
    const searchQuery = `fact check "${claim}" ${context || ''}`;
    const results = await this.searchService.search(searchQuery, 5);
    
    // Analyze results for fact verification
    const verification = {
      claim,
      status: 'unverified',
      confidence: 0,
      sources: [] as any[]
    };
    
    // Simple fact checking logic
    for (const result of results.items) {
      if (result.snippet.toLowerCase().includes('true') || 
          result.snippet.toLowerCase().includes('confirmed')) {
        verification.status = 'verified';
        verification.confidence += 0.2;
        verification.sources.push(result.link);
      } else if (result.snippet.toLowerCase().includes('false') || 
                 result.snippet.toLowerCase().includes('debunked')) {
        verification.status = 'disputed';
        verification.confidence -= 0.2;
        verification.sources.push(result.link);
      }
    }
    
    verification.confidence = Math.max(0, Math.min(1, verification.confidence));
    
    return verification;
  }
  
  // Helper methods
  
  private calculateKeywordDensity(content: string, keywords: string[]): Record<string, number> {
    const density: Record<string, number> = {};
    const words = content.toLowerCase().split(/\s+/).length;
    
    keywords.forEach(keyword => {
      const regex = new RegExp(keyword.toLowerCase(), 'gi');
      const matches = content.match(regex) || [];
      density[keyword] = (matches.length / words) * 100;
    });
    
    return density;
  }
  
  private isAuthoritativeSource(url: string): boolean {
    const authoritative = [
      '.gov', '.edu', '.org',
      'wikipedia.org', 'britannica.com',
      'nature.com', 'science.org',
      'harvard.edu', 'stanford.edu',
      'mit.edu', 'oxford.ac.uk'
    ];
    
    return authoritative.some(domain => url.includes(domain));
  }
  
  private calculateAuthority(url: string): number {
    if (url.includes('.gov')) return 0.95;
    if (url.includes('.edu')) return 0.9;
    if (url.includes('.org')) return 0.85;
    if (url.includes('wikipedia.org')) return 0.8;
    return 0.7;
  }
  
  private integrateLinks(content: any, links: any[]): any {
    // Logic to integrate links into content
    return {
      ...content,
      externalLinks: links
    };
  }
  
  private extractDataPoints(content: any, plan: any): any[] {
    // Extract data suitable for visualization
    return [];
  }
  
  private determineVisualizationType(data: any): string {
    // Determine best visualization type based on data
    if (Array.isArray(data)) return 'bar';
    if (typeof data === 'object') return 'pie';
    return 'line';
  }
  
  private generateMarkdownTable(headers: string[], data: any[]): string {
    let markdown = `| ${headers.join(' | ')} |\n`;
    markdown += `| ${headers.map(() => '---').join(' | ')} |\n`;
    
    data.forEach(row => {
      const values = headers.map(h => row[h] || '');
      markdown += `| ${values.join(' | ')} |\n`;
    });
    
    return markdown;
  }
  
  /**
   * Convenience method for search execution
   */
  async executeSearch(query: string, limit: number = 10): Promise<any> {
    try {
      return await this.searchService.search(query, limit);
    } catch (error) {
      console.error('Search execution error:', error);
      return {
        items: [],
        error: error instanceof Error ? error.message : 'Search failed'
      };
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    // Cleanup any resources
    if (this.scraperService && typeof this.scraperService.close === 'function') {
      await this.scraperService.close();
    }
  }
} 