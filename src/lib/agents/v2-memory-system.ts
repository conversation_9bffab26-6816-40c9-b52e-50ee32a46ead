/**
 * V2 Memory System - Multi-type memory for autonomous learning
 */

import { MemoryEntry, MemoryQuery, MemoryStats, AccessPattern } from './v2-types';
import { KnowledgeBase } from '../knowledge-base';

/**
 * Memory Types:
 * - Core Memory: Fundamental knowledge and rules
 * - Episodic Memory: Past article generation experiences
 * - Semantic Memory: Domain knowledge and facts
 * - Procedural Memory: How-to knowledge and patterns
 * - Tool Memory: Available tools and their usage
 * - Improvement Memory: Learning and optimization history
 */
export class V2MemorySystem {
  private memories: Map<string, MemoryEntry[]>;
  private accessLog: AccessPattern[];
  private sessionId: string;
  private persistencePath: string;
  
  // Memory indices for fast retrieval
  private typeIndex: Map<string, Set<string>>;
  private keywordIndex: Map<string, Set<string>>;
  private temporalIndex: Map<number, Set<string>>;
  
  constructor(sessionId: string) {
    this.sessionId = sessionId;
    this.persistencePath = `./memory/${sessionId}`;
    
    this.memories = new Map();
    this.accessLog = [];
    
    this.typeIndex = new Map();
    this.keywordIndex = new Map();
    this.temporalIndex = new Map();
    
    this.initializeMemoryTypes();
    this.loadPersistentMemory();
  }
  
  /**
   * Initialize memory type stores
   */
  private initializeMemoryTypes(): void {
    const memoryTypes = [
      'core', 'episodic', 'semantic', 
      'procedural', 'tool', 'improvement'
    ];
    
    memoryTypes.forEach(type => {
      this.memories.set(type, []);
      this.typeIndex.set(type, new Set());
    });
  }
  
  /**
   * Store a memory entry
   */
  async store(entry: Omit<MemoryEntry, 'id' | 'timestamp' | 'accessCount' | 'lastAccessed'>): Promise<string> {
    const id = this.generateMemoryId();
    const fullEntry: MemoryEntry = {
      ...entry,
      id,
      timestamp: Date.now(),
      accessCount: 0,
      lastAccessed: Date.now()
    };
    
    // Add to appropriate memory type
    const memories = this.memories.get(entry.type) || [];
    memories.push(fullEntry);
    this.memories.set(entry.type, memories);
    
    // Update indices
    this.updateIndices(fullEntry);
    
    // Trigger consolidation if needed
    if (memories.length > 1000) {
      await this.consolidateMemories(entry.type);
    }
    
    return id;
  }
  
  /**
   * Store memory (alias for store method to match MemoryAgent expectations)
   */
  async storeMemory(entry: Omit<MemoryEntry, 'id' | 'timestamp' | 'accessCount' | 'lastAccessed'>): Promise<string> {
    return await this.store(entry);
  }
  
  /**
   * Retrieve memories based on query
   */
  async retrieve(query: MemoryQuery): Promise<MemoryEntry[]> {
    let candidates = this.getAllMemories();
    
    // Filter by type
    if (query.type && query.type.length > 0) {
      candidates = candidates.filter(m => query.type!.includes(m.type));
    }
    
    // Filter by keywords
    if (query.keywords && query.keywords.length > 0) {
      candidates = this.filterByKeywords(candidates, query.keywords);
    }
    
    // Filter by time range
    if (query.timeRange) {
      candidates = candidates.filter(m => 
        m.timestamp >= query.timeRange!.start && 
        m.timestamp <= query.timeRange!.end
      );
    }
    
    // Calculate relevance scores
    const scoredCandidates = await this.scoreRelevance(candidates, query);
    
    // Filter by relevance threshold
    if (query.relevanceThreshold) {
      scoredCandidates.filter(sc => sc.relevance >= query.relevanceThreshold!);
    }
    
    // Sort by relevance and recency
    scoredCandidates.sort((a, b) => {
      const relevanceDiff = b.relevance - a.relevance;
      if (Math.abs(relevanceDiff) > 0.1) return relevanceDiff;
      return b.timestamp - a.timestamp;
    });
    
    // Apply limit
    const results = scoredCandidates.slice(0, query.limit || 100);
    
    // Update access statistics
    results.forEach(memory => {
      memory.accessCount++;
      memory.lastAccessed = Date.now();
      this.logAccess(memory.type);
    });
    
    return results;
  }
  
  /**
   * Get memories relevant to a topic
   */
  async getRelevantMemories(topic: string, limit: number = 10): Promise<MemoryEntry[]> {
    const keywords = this.extractKeywords(topic);
    
    return this.retrieve({
      keywords,
      relevanceThreshold: 0.5,
      limit
    });
  }
  
  /**
   * Store research findings
   */
  async storeResearch(research: any): Promise<void> {
    // Store main research as semantic memory
    await this.store({
      type: 'semantic',
      content: research,
      relevance: 0.9,
      metadata: {
        topic: research.topic,
        sources: research.sources?.length || 0,
        insights: research.insights?.length || 0
      }
    });
    
    // Store individual insights
    if (research.insights) {
      for (const insight of research.insights) {
        await this.store({
          type: 'semantic',
          content: insight,
          relevance: 0.8,
          metadata: {
            topic: research.topic,
            source: 'research_insight'
          }
        });
      }
    }
    
    // Store competitor data
    if (research.competitorAnalysis && typeof research.competitorAnalysis === 'object') {
      // Handle competitorAnalysis as object with topCompetitors array
      if (Array.isArray(research.competitorAnalysis.topCompetitors)) {
        for (const competitor of research.competitorAnalysis.topCompetitors) {
          await this.store({
            type: 'episodic',
            content: competitor,
            relevance: 0.85,
            metadata: {
              topic: research.topic,
              source: 'competitor_analysis',
              url: competitor.url || 'unknown'
            }
          });
        }
      }
      
      // Store common themes
      if (Array.isArray(research.competitorAnalysis.commonThemes)) {
        for (const theme of research.competitorAnalysis.commonThemes) {
          await this.store({
            type: 'episodic',
            content: theme,
            relevance: 0.8,
            metadata: {
              topic: research.topic,
              source: 'competitor_themes',
              type: 'theme'
            }
          });
        }
      }
      
      // Store content gaps
      if (Array.isArray(research.competitorAnalysis.contentGaps)) {
        for (const gap of research.competitorAnalysis.contentGaps) {
          await this.store({
            type: 'episodic',
            content: gap,
            relevance: 0.9,
            metadata: {
              topic: research.topic,
              source: 'content_gaps',
              type: 'opportunity'
            }
          });
        }
      }
      
      // Store advantages
      if (Array.isArray(research.competitorAnalysis.advantages)) {
        for (const advantage of research.competitorAnalysis.advantages) {
          await this.store({
            type: 'episodic',
            content: advantage,
            relevance: 0.7,
            metadata: {
              topic: research.topic,
              source: 'competitive_advantages',
              type: 'advantage'
            }
          });
        }
      }
    } else if (Array.isArray(research.competitorAnalysis)) {
      // Handle legacy array format
      for (const competitor of research.competitorAnalysis) {
        await this.store({
          type: 'episodic',
          content: competitor,
          relevance: 0.85,
          metadata: {
            topic: research.topic,
            source: 'competitor_analysis',
            url: competitor.url || 'unknown'
          }
        });
      }
    }
  }
  
  /**
   * Store learning outcomes
   */
  async storeLearnings(learnings: any): Promise<void> {
    await this.store({
      type: 'improvement',
      content: learnings,
      relevance: 0.95,
      metadata: {
        sessionId: this.sessionId,
        improvements: learnings.improvements?.length || 0,
        patterns: learnings.patterns?.length || 0
      }
    });
    
    // Store individual patterns as procedural memory
    if (learnings.patterns) {
      for (const pattern of learnings.patterns) {
        await this.store({
          type: 'procedural',
          content: pattern,
          relevance: pattern.effectiveness || 0.7,
          metadata: {
            patternType: pattern.type,
            frequency: pattern.frequency
          }
        });
      }
    }
  }
  
  /**
   * Get successful patterns
   */
  async getSuccessfulPatterns(minEffectiveness: number = 0.7): Promise<any[]> {
    const proceduralMemories = await this.retrieve({
      type: ['procedural'],
      relevanceThreshold: minEffectiveness
    });
    
    return proceduralMemories
      .map(m => m.content)
      .filter(p => p.effectiveness >= minEffectiveness);
  }
  
  /**
   * Get writing patterns
   */
  async getWritingPatterns(): Promise<any[]> {
    const patterns = await this.retrieve({
      type: ['procedural'],
      keywords: ['writing', 'style', 'pattern', 'technique']
    });
    
    return patterns.map(m => m.content);
  }
  
  /**
   * Consolidate memories to prevent bloat
   */
  private async consolidateMemories(type: string): Promise<void> {
    const memories = this.memories.get(type) || [];
    
    // Remove low-relevance, old, rarely accessed memories
    const threshold = Date.now() - (30 * 24 * 60 * 60 * 1000); // 30 days
    const consolidated = memories.filter(m => 
      m.relevance > 0.3 || 
      m.timestamp > threshold || 
      m.accessCount > 5
    );
    
    // Merge similar memories
    const merged = await this.mergeSimilarMemories(consolidated);
    
    this.memories.set(type, merged);
    
    // Rebuild indices
    this.rebuildIndices();
  }
  
  /**
   * Merge similar memories
   */
  private async mergeSimilarMemories(memories: MemoryEntry[]): Promise<MemoryEntry[]> {
    // Group by similarity
    const groups: MemoryEntry[][] = [];
    const processed = new Set<string>();
    
    for (const memory of memories) {
      if (processed.has(memory.id)) continue;
      
      const similar = memories.filter(m => 
        m.id !== memory.id && 
        !processed.has(m.id) &&
        this.calculateSimilarity(memory, m) > 0.8
      );
      
      if (similar.length > 0) {
        groups.push([memory, ...similar]);
        processed.add(memory.id);
        similar.forEach(s => processed.add(s.id));
      }
    }
    
    // Merge groups
    const merged: MemoryEntry[] = [];
    
    for (const group of groups) {
      const mergedEntry = this.mergeMemoryGroup(group);
      merged.push(mergedEntry);
    }
    
    // Add non-grouped memories
    memories.forEach(m => {
      if (!processed.has(m.id)) {
        merged.push(m);
      }
    });
    
    return merged;
  }
  
  /**
   * Calculate similarity between memories
   */
  private calculateSimilarity(a: MemoryEntry, b: MemoryEntry): number {
    // Simple similarity based on content overlap
    const aStr = JSON.stringify(a.content).toLowerCase();
    const bStr = JSON.stringify(b.content).toLowerCase();
    
    const aWords = new Set(aStr.split(/\s+/));
    const bWords = new Set(bStr.split(/\s+/));
    
    const intersection = new Set([...aWords].filter(x => bWords.has(x)));
    const union = new Set([...aWords, ...bWords]);
    
    return intersection.size / union.size;
  }
  
  /**
   * Merge a group of similar memories
   */
  private mergeMemoryGroup(group: MemoryEntry[]): MemoryEntry {
    // Sort by relevance and access count
    group.sort((a, b) => {
      const relevanceDiff = b.relevance - a.relevance;
      if (Math.abs(relevanceDiff) > 0.1) return relevanceDiff;
      return b.accessCount - a.accessCount;
    });
    
    const primary = group[0];
    
    return {
      ...primary,
      accessCount: group.reduce((sum, m) => sum + m.accessCount, 0),
      metadata: {
        ...primary.metadata,
        mergedFrom: group.map(m => m.id),
        mergedCount: group.length
      }
    };
  }
  
  /**
   * Update memory indices
   */
  private updateIndices(memory: MemoryEntry): void {
    // Type index
    const typeSet = this.typeIndex.get(memory.type) || new Set();
    typeSet.add(memory.id);
    this.typeIndex.set(memory.type, typeSet);
    
    // Keyword index
    const keywords = this.extractKeywords(JSON.stringify(memory.content));
    keywords.forEach(keyword => {
      const keywordSet = this.keywordIndex.get(keyword) || new Set();
      keywordSet.add(memory.id);
      this.keywordIndex.set(keyword, keywordSet);
    });
    
    // Temporal index (by day)
    const day = Math.floor(memory.timestamp / (24 * 60 * 60 * 1000));
    const daySet = this.temporalIndex.get(day) || new Set();
    daySet.add(memory.id);
    this.temporalIndex.set(day, daySet);
  }
  
  /**
   * Rebuild all indices
   */
  private rebuildIndices(): void {
    this.typeIndex.clear();
    this.keywordIndex.clear();
    this.temporalIndex.clear();
    
    this.getAllMemories().forEach(memory => {
      this.updateIndices(memory);
    });
  }
  
  /**
   * Get all memories
   */
  private getAllMemories(): MemoryEntry[] {
    const all: MemoryEntry[] = [];
    
    this.memories.forEach(memories => {
      all.push(...memories);
    });
    
    return all;
  }
  
  /**
   * Filter memories by keywords
   */
  private filterByKeywords(memories: MemoryEntry[], keywords: string[]): MemoryEntry[] {
    const memoryIds = new Set<string>();
    
    keywords.forEach(keyword => {
      const ids = this.keywordIndex.get(keyword.toLowerCase());
      if (ids) {
        ids.forEach(id => memoryIds.add(id));
      }
    });
    
    return memories.filter(m => memoryIds.has(m.id));
  }
  
  /**
   * Score relevance of memories
   */
  private async scoreRelevance(
    memories: MemoryEntry[], 
    query: MemoryQuery
  ): Promise<MemoryEntry[]> {
    // Simple relevance scoring
    return memories.map(memory => {
      let score = memory.relevance;
      
      // Boost recent memories
      const age = Date.now() - memory.timestamp;
      const ageFactor = Math.exp(-age / (7 * 24 * 60 * 60 * 1000)); // 7 day half-life
      score *= (0.5 + 0.5 * ageFactor);
      
      // Boost frequently accessed memories
      const accessFactor = Math.min(1, memory.accessCount / 10);
      score *= (0.8 + 0.2 * accessFactor);
      
      return {
        ...memory,
        relevance: score
      };
    });
  }
  
  /**
   * Extract keywords from text
   */
  private extractKeywords(text: string): string[] {
    // Simple keyword extraction
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(w => w.length > 3);
    
    // Remove common words
    const stopWords = new Set([
      'this', 'that', 'these', 'those', 'which', 'where',
      'when', 'what', 'with', 'from', 'into', 'through'
    ]);
    
    return [...new Set(words.filter(w => !stopWords.has(w)))];
  }
  
  /**
   * Generate unique memory ID
   */
  private generateMemoryId(): string {
    return `mem_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
  
  /**
   * Log memory access
   */
  private logAccess(type: string): void {
    const existing = this.accessLog.find(a => a.memoryType === type);
    
    if (existing) {
      existing.frequency++;
      existing.lastAccess = Date.now();
    } else {
      this.accessLog.push({
        memoryType: type,
        frequency: 1,
        lastAccess: Date.now(),
        averageRelevance: 0
      });
    }
  }
  
  /**
   * Get memory statistics
   */
  getAccessStats(): MemoryStats {
    const stats: MemoryStats = {
      totalEntries: 0,
      entriesByType: {},
      averageRelevance: 0,
      accessPatterns: this.accessLog,
      storageSize: 0
    };
    
    let totalRelevance = 0;
    
    this.memories.forEach((memories, type) => {
      stats.entriesByType[type] = memories.length;
      stats.totalEntries += memories.length;
      
      memories.forEach(m => {
        totalRelevance += m.relevance;
        stats.storageSize += JSON.stringify(m).length;
      });
    });
    
    if (stats.totalEntries > 0) {
      stats.averageRelevance = totalRelevance / stats.totalEntries;
    }
    
    return stats;
  }
  
  /**
   * Load persistent memory
   */
  private async loadPersistentMemory(): Promise<void> {
    try {
      // Load from file system or database
      // Implementation depends on storage strategy
      console.log(`Loading memory for session: ${this.sessionId}`);
    } catch (error) {
      console.error('Failed to load persistent memory:', error);
    }
  }
  
  /**
   * Persist memory to storage
   */
  async persist(): Promise<void> {
    try {
      // Save to file system or database
      // Implementation depends on storage strategy
      console.log(`Persisting memory for session: ${this.sessionId}`);
    } catch (error) {
      console.error('Failed to persist memory:', error);
    }
  }
} 