/**
 * V2 Learning System - Self-improvement through reinforcement learning
 */

import { 
  LearningFeedback, 
  Pattern, 
  LearningObjective,
  ImprovementSuggestion,
  PerformanceMetrics
} from './v2-types';
import { V2MemorySystem } from './v2-memory-system';

/**
 * Learning System that enables the agent to improve over time
 */
export class V2LearningSystem {
  private memorySystem: V2MemorySystem;
  private patterns: Map<string, Pattern>;
  private objectives: Map<string, LearningObjective>;
  private performanceHistory: PerformanceMetrics[];
  private feedbackHistory: LearningFeedback[];
  
  // Learning parameters
  private learningRate: number = 0.1;
  private explorationRate: number = 0.2;
  private patternThreshold: number = 3; // Min occurrences to become a pattern
  
  constructor(memorySystem: V2MemorySystem) {
    this.memorySystem = memorySystem;
    this.patterns = new Map();
    this.objectives = new Map();
    this.performanceHistory = [];
    this.feedbackHistory = [];
    
    this.initializeObjectives();
  }
  
  /**
   * Initialize learning objectives
   */
  private initializeObjectives(): void {
    const defaultObjectives: LearningObjective[] = [
      {
        id: 'speed',
        description: 'Reduce article generation time',
        metrics: ['executionTime'],
        targetValue: 300000, // 5 minutes
        currentValue: 600000, // 10 minutes
        priority: 0.7
      },
      {
        id: 'quality',
        description: 'Improve article quality scores',
        metrics: ['seoScore', 'readabilityScore'],
        targetValue: 95,
        currentValue: 85,
        priority: 0.9
      },
      {
        id: 'confidence',
        description: 'Increase confidence in generated content',
        metrics: ['confidence'],
        targetValue: 0.95,
        currentValue: 0.8,
        priority: 0.8
      },
      {
        id: 'efficiency',
        description: 'Reduce tool invocations',
        metrics: ['toolInvocations'],
        targetValue: 20,
        currentValue: 40,
        priority: 0.6
      }
    ];
    
    defaultObjectives.forEach(obj => {
      this.objectives.set(obj.id, obj);
    });
  }
  
  /**
   * Learn from execution results
   */
  async learn(feedback: LearningFeedback): Promise<void> {
    this.feedbackHistory.push(feedback);
    
    // Extract patterns from successful executions
    if (feedback.outcome === 'success') {
      await this.extractAndStorePatterns(feedback);
    }
    
    // Update objectives based on metrics
    this.updateObjectives(feedback.metrics);
    
    // Analyze failures for improvement
    if (feedback.outcome === 'failure') {
      await this.analyzeFailure(feedback);
    }
    
    // Adjust learning parameters
    this.adjustLearningParameters(feedback);
    
    // Store learning in memory
    await this.memorySystem.storeLearnings({
      feedback,
      patterns: Array.from(this.patterns.values()),
      objectives: Array.from(this.objectives.values()),
      timestamp: Date.now()
    });
  }
  
  /**
   * Extract patterns from successful execution
   */
  private async extractAndStorePatterns(feedback: LearningFeedback): Promise<void> {
    for (const pattern of feedback.patterns) {
      const existing = this.patterns.get(pattern.id);
      
      if (existing) {
        // Update existing pattern
        existing.frequency++;
        existing.effectiveness = 
          (existing.effectiveness * (existing.frequency - 1) + pattern.effectiveness) / 
          existing.frequency;
      } else {
        // Add new pattern
        this.patterns.set(pattern.id, pattern);
      }
      
      // Store as procedural memory if frequent enough
      if (pattern.frequency >= this.patternThreshold) {
        await this.memorySystem.store({
          type: 'procedural',
          content: pattern,
          relevance: pattern.effectiveness,
          metadata: {
            source: 'learning_system',
            patternType: pattern.type
          }
        });
      }
    }
  }
  
  /**
   * Update objectives based on performance
   */
  private updateObjectives(metrics: Record<string, number>): void {
    for (const [objId, objective] of this.objectives) {
      let totalProgress = 0;
      let metricCount = 0;
      
      for (const metric of objective.metrics) {
        if (metric in metrics) {
          const progress = this.calculateProgress(
            objective.currentValue,
            metrics[metric],
            objective.targetValue
          );
          totalProgress += progress;
          metricCount++;
        }
      }
      
      if (metricCount > 0) {
        const avgProgress = totalProgress / metricCount;
        
        // Update current value using exponential moving average
        objective.currentValue = 
          objective.currentValue * (1 - this.learningRate) + 
          metrics[objective.metrics[0]] * this.learningRate;
        
        // Adjust priority based on progress
        if (avgProgress < 0.5) {
          objective.priority = Math.min(1, objective.priority * 1.1);
        } else if (avgProgress > 0.8) {
          objective.priority = Math.max(0.1, objective.priority * 0.9);
        }
      }
    }
  }
  
  /**
   * Calculate progress toward objective
   */
  private calculateProgress(
    current: number, 
    actual: number, 
    target: number
  ): number {
    const improvement = (actual - current) / (target - current);
    return Math.max(0, Math.min(1, improvement));
  }
  
  /**
   * Analyze failure for improvement opportunities
   */
  private async analyzeFailure(feedback: LearningFeedback): Promise<void> {
    // Look for anti-patterns
    const antiPatterns: Pattern[] = [];
    
    for (const pattern of feedback.patterns) {
      if (pattern.effectiveness < 0.3) {
        antiPatterns.push({
          ...pattern,
          id: `anti_${pattern.id}`,
          description: `Avoid: ${pattern.description}`,
          effectiveness: 1 - pattern.effectiveness
        });
      }
    }
    
    // Store anti-patterns
    for (const antiPattern of antiPatterns) {
      this.patterns.set(antiPattern.id, antiPattern);
    }
    
    // Generate improvement suggestions
    const improvements = await this.generateImprovements(feedback, antiPatterns);
    
    // Store in memory
    await this.memorySystem.store({
      type: 'improvement',
      content: {
        failure: feedback,
        antiPatterns,
        improvements
      },
      relevance: 0.9,
      metadata: {
        source: 'failure_analysis',
        sessionId: feedback.sessionId
      }
    });
  }
  
  /**
   * Generate improvement suggestions
   */
  private async generateImprovements(
    feedback: LearningFeedback,
    antiPatterns: Pattern[]
  ): Promise<ImprovementSuggestion[]> {
    const suggestions: ImprovementSuggestion[] = [];
    
    // Analyze metrics
    for (const [metric, value] of Object.entries(feedback.metrics)) {
      const objective = Array.from(this.objectives.values())
        .find(obj => obj.metrics.includes(metric));
      
      if (objective && value > objective.targetValue * 1.5) {
        suggestions.push({
          area: metric,
          suggestion: `Reduce ${metric} by optimizing related processes`,
          expectedImpact: 0.3,
          confidence: 0.7,
          basedOn: [`High ${metric}: ${value}`]
        });
      }
    }
    
    // Analyze anti-patterns
    for (const antiPattern of antiPatterns) {
      suggestions.push({
        area: antiPattern.type,
        suggestion: antiPattern.description,
        expectedImpact: antiPattern.effectiveness,
        confidence: 0.8,
        basedOn: [`Pattern frequency: ${antiPattern.frequency}`]
      });
    }
    
    return suggestions;
  }
  
  /**
   * Adjust learning parameters based on performance
   */
  private adjustLearningParameters(feedback: LearningFeedback): void {
    // Adjust exploration rate
    if (feedback.outcome === 'success') {
      // Reduce exploration when successful
      this.explorationRate = Math.max(0.05, this.explorationRate * 0.95);
    } else {
      // Increase exploration when failing
      this.explorationRate = Math.min(0.5, this.explorationRate * 1.1);
    }
    
    // Adjust learning rate based on consistency
    const recentFeedback = this.feedbackHistory.slice(-10);
    const successRate = recentFeedback.filter(f => f.outcome === 'success').length / 
                       recentFeedback.length;
    
    if (successRate > 0.8) {
      // Slow down learning when performing well
      this.learningRate = Math.max(0.01, this.learningRate * 0.9);
    } else if (successRate < 0.5) {
      // Speed up learning when struggling
      this.learningRate = Math.min(0.3, this.learningRate * 1.1);
    }
  }
  
  /**
   * Get improvement suggestions
   */
  async getImprovementSuggestions(): Promise<string[]> {
    const suggestions: string[] = [];
    
    // Based on objectives
    for (const objective of this.objectives.values()) {
      const progress = (objective.targetValue - objective.currentValue) / 
                      objective.targetValue;
      
      if (progress > 0.2) {
        suggestions.push(
          `Focus on ${objective.description} - currently at ${
            Math.round(progress * 100)
          }% of target`
        );
      }
    }
    
    // Based on patterns
    const topPatterns = Array.from(this.patterns.values())
      .filter(p => p.effectiveness > 0.7)
      .sort((a, b) => b.effectiveness - a.effectiveness)
      .slice(0, 3);
    
    for (const pattern of topPatterns) {
      suggestions.push(
        `Apply pattern: ${pattern.description} (${
          Math.round(pattern.effectiveness * 100)
        }% effective)`
      );
    }
    
    return suggestions;
  }
  
  /**
   * Get confidence boost based on learning
   */
  getConfidenceBoost(topic: string): number {
    // Check if we have successful patterns for similar topics
    const relevantPatterns = Array.from(this.patterns.values())
      .filter(p => 
        p.effectiveness > 0.7 && 
        p.conditions.topic?.includes(topic.toLowerCase())
      );
    
    if (relevantPatterns.length === 0) return 0;
    
    // Calculate boost based on pattern effectiveness
    const avgEffectiveness = relevantPatterns.reduce(
      (sum, p) => sum + p.effectiveness, 0
    ) / relevantPatterns.length;
    
    return avgEffectiveness * 0.1; // Max 10% boost
  }
  
  /**
   * Get planning adjustments based on learning
   */
  getPlanningAdjustments(): any {
    return {
      explorationRate: this.explorationRate,
      learningRate: this.learningRate,
      patterns: Array.from(this.patterns.values())
        .filter(p => p.effectiveness > 0.6),
      objectives: Array.from(this.objectives.values())
        .sort((a, b) => b.priority - a.priority)
    };
  }
  
  /**
   * Learn from planning issues
   */
  async learnFromPlanningIssues(issues: string[]): Promise<void> {
    // Create anti-patterns from issues
    for (const issue of issues) {
      const pattern: Pattern = {
        id: `issue_${Date.now()}_${Math.random()}`,
        type: 'planning_issue',
        description: issue,
        frequency: 1,
        effectiveness: 0.2, // Low effectiveness indicates avoidance
        conditions: {
          context: 'planning',
          issue: true
        }
      };
      
      this.patterns.set(pattern.id, pattern);
    }
  }
  
  /**
   * Log correction failure
   */
  async logCorrectinFailure(issues: any[], verification: any): Promise<void> {
    const pattern: Pattern = {
      id: `correction_failure_${Date.now()}`,
      type: 'correction_failure',
      description: 'Failed to correct content issues',
      frequency: 1,
      effectiveness: 0.1,
      conditions: {
        issues: issues.map(i => i.type),
        verification: verification
      }
    };
    
    this.patterns.set(pattern.id, pattern);
    
    // Store in memory for future reference
    await this.memorySystem.store({
      type: 'improvement',
      content: {
        type: 'correction_failure',
        issues,
        verification,
        pattern
      },
      relevance: 0.8,
      metadata: {
        source: 'correction_failure',
        timestamp: Date.now()
      }
    });
  }
  
  /**
   * Analyze performance
   */
  async analyzePerformance(
    content: any,
    trace: any[],
    metrics: Map<string, any>
  ): Promise<PerformanceMetrics> {
    const performance: PerformanceMetrics = {
      speed: this.calculateSpeed(trace),
      accuracy: this.calculateAccuracy(content),
      completeness: this.calculateCompleteness(content),
      originality: content.originalityScore || 0.8,
      userSatisfaction: undefined,
      competitorBenchmark: content.competitorSuperiority || 0.85
    };
    
    this.performanceHistory.push(performance);
    
    return performance;
  }
  
  /**
   * Extract learnings from execution history (overload for MemoryAgent)
   */
  async extractLearnings(
    executionHistory: any[],
    content: any
  ): Promise<any>;
  
  /**
   * Extract learnings from performance
   */
  async extractLearnings(
    performance: PerformanceMetrics,
    content: any,
    context: any
  ): Promise<any>;

  async extractLearnings(
    firstParam: any[] | PerformanceMetrics,
    content: any,
    context?: any
  ): Promise<any> {
    // Handle different overloads
    if (Array.isArray(firstParam)) {
      // Called from MemoryAgent with execution history
      const executionHistory = firstParam;
      
      // Extract performance metrics from execution history
      const performance = this.extractPerformanceFromHistory(executionHistory);
      
      const learnings = {
        performance,
        patterns: this.extractPatternsFromContent(content, { executionHistory }),
        insights: this.generateInsights(performance),
        improvements: await this.generateImprovements({
          sessionId: 'memory_extraction',
          outcome: performance.accuracy > 0.8 ? 'success' : 'partial',
          metrics: performance as any,
          improvements: [],
          patterns: []
        }, []),
        significance: this.calculateSignificance(performance, content)
      };
      
      return learnings;
    } else {
      // Original method with performance metrics
      const performance = firstParam;
      
      const learnings = {
        performance,
        patterns: this.extractPatternsFromContent(content, context),
        insights: this.generateInsights(performance),
        improvements: await this.generateImprovements({
          sessionId: context.taskId,
          outcome: performance.accuracy > 0.8 ? 'success' : 'partial',
          metrics: performance as any,
          improvements: [],
          patterns: []
        }, [])
      };
      
      return learnings;
    }
  }
  
  /**
   * Adjust behavior based on learnings
   */
  async adjustBehavior(learnings: any, config: any): Promise<void> {
    // Update configuration based on learnings
    if (learnings.performance.speed > 600000) {
      // If too slow, increase parallelism
      config.parallelismLevel = Math.min(16, config.parallelismLevel + 2);
    }
    
    if (learnings.performance.accuracy < 0.8) {
      // If accuracy is low, increase confidence threshold
      config.confidenceThreshold = Math.min(0.98, config.confidenceThreshold + 0.02);
    }
    
    // Store adjustment as pattern
    const adjustmentPattern: Pattern = {
      id: `adjustment_${Date.now()}`,
      type: 'config_adjustment',
      description: 'Configuration adjustment based on performance',
      frequency: 1,
      effectiveness: 0.5, // Will be updated based on results
      conditions: {
        performance: learnings.performance,
        adjustments: {
          parallelismLevel: config.parallelismLevel,
          confidenceThreshold: config.confidenceThreshold
        }
      }
    };
    
    this.patterns.set(adjustmentPattern.id, adjustmentPattern);
  }
  
  // Helper methods
  
  private calculateSpeed(trace: any[]): number {
    if (trace.length === 0) return 0;
    
    const startTime = trace[0].timestamp;
    const endTime = trace[trace.length - 1].timestamp;
    
    return endTime - startTime;
  }
  
  private calculateAccuracy(content: any): number {
    return content.accuracyScore || 0.85;
  }
  
  private calculateCompleteness(content: any): number {
    return content.completenessScore || 0.9;
  }
  
  private extractPatternsFromContent(content: any, context: any): Pattern[] {
    const patterns: Pattern[] = [];
    
    // Extract successful writing patterns
    if (content.seoScore > 90) {
      patterns.push({
        id: `seo_success_${Date.now()}`,
        type: 'seo_optimization',
        description: 'Successful SEO optimization approach',
        frequency: 1,
        effectiveness: content.seoScore / 100,
        conditions: {
          topic: context.topic,
          wordCount: content.wordCount
        }
      });
    }
    
    return patterns;
  }
  
  private generateInsights(performance: PerformanceMetrics): string[] {
    const insights: string[] = [];
    
    if (performance.speed < 300000) {
      insights.push('Achieved target speed for content generation');
    }
    
    if (performance.accuracy > 0.9) {
      insights.push('High accuracy achieved in content generation');
    }
    
    if (performance.competitorBenchmark && performance.competitorBenchmark > 0.9) {
      insights.push('Successfully surpassed competitor benchmarks');
    }
    
    return insights;
  }

  /**
   * Extract performance metrics from execution history
   */
  private extractPerformanceFromHistory(executionHistory: any[]): PerformanceMetrics {
    return {
      accuracy: 0.8, // Default values - could be improved with actual analysis
      speed: executionHistory.length,
      completeness: 0.85,
      originality: 0.9
    };
  }

  /**
   * Calculate significance of learnings
   */
  private calculateSignificance(performance: PerformanceMetrics, content: any): number {
    const factors = [
      performance.accuracy || 0,
      performance.completeness || 0,
      content.competitorSuperiority || 0,
      content.seoScore ? content.seoScore / 100 : 0
    ];
    
    return factors.reduce((sum, factor) => sum + factor, 0) / factors.length;
  }
  
  /**
   * Persist learning data
   */
  async persist(): Promise<void> {
    // Save patterns and objectives
    const learningData = {
      patterns: Array.from(this.patterns.entries()),
      objectives: Array.from(this.objectives.entries()),
      performanceHistory: this.performanceHistory.slice(-100),
      feedbackHistory: this.feedbackHistory.slice(-100),
      parameters: {
        learningRate: this.learningRate,
        explorationRate: this.explorationRate,
        patternThreshold: this.patternThreshold
      }
    };
    
    await this.memorySystem.store({
      type: 'core',
      content: learningData,
      relevance: 1.0,
      metadata: {
        source: 'learning_system',
        version: '2.0'
      }
    });
  }
} 